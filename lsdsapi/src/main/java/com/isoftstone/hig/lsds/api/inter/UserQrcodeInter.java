package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.entity.UserQrcode;
import com.isoftstone.hig.lsds.api.filter.UserQrcodeFilter;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 用户和货源码的关系
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@Api(value = "用户和货源码的关系", tags = "用户和货源码的关系")
@FeignClient(name = "lsds", path = "/lsds/userQrcode")
public interface UserQrcodeInter
{

    /**
     * 查询用户和货源码的关系
     *
     * @param userQrcodeFilter 用户和货源码的关系ID
     * @return 用户和货源码的关系
     */
    @PostMapping("/getById")
    @ApiOperation(value = "根据id查询")
    ResultMode<UserQrcode> getById(@RequestBody UserQrcodeFilter userQrcodeFilter);

    /**
     * 查询用户和货源码的关系列表
     *
     * @param userQrcodeFilter 用户和货源码的关系
     * @return 用户和货源码的关系集合
     */
    @PostMapping("/listByEntity")
    @ApiOperation(value = "查询列表")
    ResultMode<List<UserQrcode>> listByEntity(@RequestBody UserQrcodeFilter userQrcodeFilter);


    /**
     * 新增用户和货源码的关系
     *
     * @param userQrcode 用户和货源码的关系
     * @return 结果
     */
    @PostMapping(value = "/save")
    @ApiOperation(value = "新增")
    ResultMode save(@RequestBody UserQrcode userQrcode);

    /**
     * 修改用户和货源码的关系
     *
     * @param userQrcodeFilter 用户和货源码的关系
     * @return 结果
     */
    @PostMapping(value = "/updateById")
    @ApiOperation(value = "更新")
    ResultMode updateById(@RequestBody UserQrcodeFilter userQrcodeFilter);



    /**
     * 批量删除用户和货源码的关系
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @PostMapping(value = "/removeByIds")
    @ApiOperation(value = "批量删除")
    ResultMode removeByIds(String[] ids);

    /**
     * 删除用户和货源码的关系信息
     *
     * @param id 用户和货源码的关系ID
     * @return 结果
     */
    @PostMapping(value = "/removeById")
    @ApiOperation(value = "删除")
    ResultMode removeById(String id);

    /**
     * 分页查询用户和货源码的关系列表
     *
     * @param pageinfo 用户和货源码的关系
     * @return 用户和货源码的关系集合
     */
    @PostMapping(value = "/page")
    @ApiOperation(value = "分页查询")
    ResultMode<UserQrcode> page(@RequestBody PagingInfo<UserQrcodeFilter> pageinfo);
}
