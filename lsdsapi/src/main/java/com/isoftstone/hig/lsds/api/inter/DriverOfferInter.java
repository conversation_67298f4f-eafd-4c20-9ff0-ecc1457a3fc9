package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.DriverOffer;
import com.isoftstone.hig.lsds.api.filter.DriverOfferFilter;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 司机报价
 *
 * <AUTHOR>
 * @date 2021-05-28
 */

@Api(value = "司机报价", tags = "司机报价")
@FeignClient(name = "lsds", path = "/lsds/driverOffer")
public interface DriverOfferInter {

    /**
     * 查询司机报价
     *
     * @param driverOfferFilter 司机报价ID
     * @return 司机报价
     */
    @PostMapping("/getById")
    @ApiOperation(value = "根据id查询")
    ResultMode<DriverOffer> getById(@RequestBody DriverOfferFilter driverOfferFilter);

    /**
     * 查询司机报价列表
     *
     * @param driverOfferFilter 司机报价
     * @return 司机报价集合
     */
    @PostMapping("/listByEntity")
    @ApiOperation(value = "查询列表")
    ResultMode<List<DriverOffer>> listByEntity(@RequestBody DriverOfferFilter driverOfferFilter);

    /**
     * 新增司机报价
     *
     * @param driverOffer 司机报价
     * @return 结果
     */
    @PostMapping(value = "/save")
    @ApiOperation(value = "新增")
    ResultMode save(@RequestBody DriverOffer driverOffer);

    /**
     * 修改司机报价
     *
     * @param offer 司机报价
     * @return 结果
     */
    @PostMapping(value = "/updateById")
    @ApiOperation(value = "更新")
    ResultMode updateById(@RequestBody DriverOffer offer);

    /**
     * 批量删除司机报价
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @PostMapping(value = "/removeByIds")
    @ApiOperation(value = "批量删除")
    ResultMode removeByIds(String[] ids);

    /**
     * 删除司机报价信息
     *
     * @param id 司机报价ID
     * @return 结果
     */
    @PostMapping(value = "/removeById")
    @ApiOperation(value = "删除")
    ResultMode removeById(String id);

    /**
     * 分页查询司机报价列表
     *
     * @param pageinfo 司机报价
     * @return 司机报价集合
     */
    @PostMapping(value = "/page")
    @ApiOperation(value = "分页查询")
    ResultMode<DriverOffer> page(@RequestBody PagingInfo<DriverOfferFilter> pageinfo);

    /**
     * @Description 司机货源报价
     * <AUTHOR>
     * @Date 2021/5/31 9:50
     * @Version 1.0
     * @Param
     * @Return
     */
    @PostMapping(value = "/sourceQuotation")
    @ApiOperation(value = "司机货源报价")
    ResultMode<DriverOffer> sourceQuotation(@RequestBody DriverOfferFilter offerFilter);

    /**
     * 司机货源报价（新方法）
     */
    @PostMapping(value = "/goodsQuote")
    @ApiOperation(value = "司机货源/撮合货源-司机报价")
    ResultMode<DriverOffer> goodsQuote(@Validated(DriverOfferFilter.Quote.class)
                                       @RequestBody DriverOfferFilter offerFilter);




    /**
     * @Description 指定司机发布货源生成预报价单
     * <AUTHOR>
     * @Date 2021/5/31 9:50
     * @Version 1.0
     * @Param
     * @Return
     */
    @PostMapping(value = "/generateDriverOffer")
    @ApiOperation(value = "指定司机发布货源生成预报价单")
    ResultMode generateDriverOffer(@RequestBody DriverGoods goods);

    /**
     * @Description 获取司机货源报价记录
     * <AUTHOR>
     * @Date 2021/5/31 9:50
     * @Version 1.0
     * @Param
     * @Return
     */
    @PostMapping(value = "/getDriverOfferList")
    @ApiOperation(value = "获取司机货源报价记录")
    ResultMode<DriverOffer> getDriverOfferList(@RequestBody PagingInfo<DriverOfferFilter> pageInfo);

    @PostMapping(value = "/getDriverGoodsPriceByGoodsId")
    @ApiOperation(value = "通过货源号和发货方指定的发货方开票价重新计算出的发货方基价,司机基价,司机开票价")
    ResultMode<DriverGoods> getDriverGoodsPriceByGoodsId(@RequestBody DriverGoods goods);



    /**
     * <AUTHOR>
     * @since 2020-1-19
     * 根据运单号获取报价对象
     * @param tmsWaybillFilter
     * @return
     */
/*    @ApiOperation(value = "根据运单号获取报价对象")
    @PostMapping("/getTractorPlateCarIdByWaybillId")
    ResultMode<DriverOffer> getTractorPlateCarIdByWaybillId(@RequestBody TmsWaybillFilter tmsWaybillFilter);*/

    @ApiOperation(value = "根据运单号获取报价对象")
    @PostMapping("/getDriverOfferByWaybillId")
    ResultMode<DriverOffer> getDriverOfferByWaybillId(@RequestBody DriverOfferFilter filter);

    /**
     * <AUTHOR>
     * @since 2022-06-11
     * 根据订单id查找的报价
     * @param filter
     * @return
     */
    @ApiOperation(value = "根据运单号获取报价对象")
    @PostMapping("/getDriverOfferByOrderId")
    ResultMode<DriverOffer> getDriverOfferByOrderId(@RequestBody DriverOfferFilter filter) ;

    /**
     * 根绝waybillIds集合查询接单重量数据
     * <AUTHOR>
     * 创建时间 2023/08/11 19:27
     * @param waybillIds 查询条件
     */
    @ApiOperation(value = "根绝waybillIds集合查询接单重量数据", tags = {"查询接单重量"}, notes = "根绝waybillIds集合查询接单重量数据")
    @RequestMapping(value = "/getDriverOfferInfoByQuery", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<DriverOffer> getDriverOfferInfoByQuery(@RequestBody List<String> waybillIds);


    /**
     * 查询报价信息
     * @return
     */
    @PostMapping("/getDriverOfferInfo")
    ResultMode<DriverOffer> getDriverOfferInfo(@RequestBody DriverOfferFilter filter);

    /**
     * 批量查询报价
     *
     * @param goodsIds 查询条件
     */
    @ApiOperation(value = "批量查询报价")
    @RequestMapping(value = "/queryDriverOfferByGoodsIds", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<DriverOffer> queryDriverOfferByGoodsIds(@RequestBody List<String> goodsIds);



    /**
     * 分页查询显示未接单车辆
     *
     * @param pageInfo 司机报价
     * @return 司机报价集合
     */
    @PostMapping(value = "/getUnacceptedOrdersByGoodsId")
    @ApiOperation(value = "分页查询")
    ResultMode<DriverOffer> getUnacceptedOrdersByGoodsId(@RequestBody PagingInfo<DriverOfferFilter> pageInfo);

    /**
     * 批量查询司机报价
     */
    @PostMapping(value = "/getByIds")
    @ApiOperation(value = "批量查询司机报价")
    ResultMode<DriverOffer> getByIds(@RequestBody List<String> ids);

}
