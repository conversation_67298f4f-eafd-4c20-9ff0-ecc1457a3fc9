package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.entity.LsdsGoodsAttention;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsAttentionFilter;
import com.isoftstone.hig.lsds.api.mvcvo.GetGoodsAttentionListVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsAttentionCreateVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsAttentionVO;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <功能描述/>

 *
 * <AUTHOR> href="mailto:<EMAIL>">rong<PERSON>jun</A>
 * @version 1.0
 * @since 2023/3/3 13:12
 */
@Api(value = "货源关注接口", tags = {"货源关注接口"})

@FeignClient(name = "lsds", path = "/lsds/goodsAttention")
public interface LsdsGoodsAttentionInter {

    /**
     * 查询货源关注信息
     *
     * @param pageInfo 页面信息
     * @return {@link ResultMode}<{@link LsdsGoodsAttentionVO}>
     */
    @RequestMapping("/pagelist")
     ResultMode<LsdsGoodsAttentionVO> pagelist(@RequestBody PagingInfo<LsdsGoodsAttentionFilter> pageInfo);

    @GetMapping("/getInfo")
     ResultMode getInfo(@PathVariable("attentionId") String attentionId);

    /**
     * 新增货源关注信息
     *
     * @param lsdsGoodsAttention
     * @return {@link ResultMode}
     */
    @RequestMapping("/addGoodsAttention")
     ResultMode addGoodsAttention(@RequestBody LsdsGoodsAttentionCreateVo lsdsGoodsAttention);

    @PutMapping("/edit")
     ResultMode edit(@RequestBody LsdsGoodsAttention lsdsGoodsAttention);

    @DeleteMapping("/remove")
     ResultMode remove(@PathVariable String[] attentionIds);

    /**
     * 长期关注
     *
     * @param lsdsGoodsAttention
     * @return {@link ResultMode}
     */
    @RequestMapping("/longTermAttentionGoods")
     ResultMode longTermAttentionGoods(@RequestBody LsdsGoodsAttention lsdsGoodsAttention);

    /**
     * 取消关注
     *
     * @param lsdsGoodsAttention
     * @return {@link ResultMode}
     */
    @RequestMapping("/cancelGoodsAttention")
     ResultMode cancelGoodsAttention(@RequestBody LsdsGoodsAttention lsdsGoodsAttention);

    /**
     * 查询货源关注列表
     *
     * @param getGoodsAttentionListVo
     * @return {@link ResultMode}
     */
    @PostMapping("/getGoodsAttentionInfoList")
    ResultMode<LsdsGoodsAttention> getGoodsAttentionInfoList(@RequestBody GetGoodsAttentionListVo getGoodsAttentionListVo);
}
