package com.isoftstone.hig.lsds.api.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 货源关注对象 lsds_goods_attention
 *
 * <AUTHOR>
 * @date 2023-03-03
 */


@ApiModel(description = "货源关注表")
@Data(staticConstructor = "of")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class LsdsGoodsAttention implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 关注id */
    private String attentionId;

    /** 司机id */
    @ApiModelProperty(value = "司机id")
    private String driverId;

    @ApiModelProperty(value = "司机手机号")
    private String driverPhone;


    /** 司机姓名 */
    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    /** 订单id */
    @ApiModelProperty(value = "订单id")
    private String orderId;

    /** 货源码code */
    @ApiModelProperty(value = "货源码code")
    private String qrCodeId;

    /** 货源id */
    @ApiModelProperty(value = "货源id")
    private String goodsId;

    /** 货源类型(0司机货源、1绑码订单) */
    @ApiModelProperty(value = "货源类型(0司机货源、1绑码订单)")
    private String goodsType;

    /** 运输类型(110.公路整车, 111.公路集卡) */
    @ApiModelProperty(value = "运输类型(110.公路整车, 111.公路集卡)")
    private String transportType;

    /** 货运类型:1-传统模式(默认)、 2-网络模式 */
    @ApiModelProperty(value = "货运类型:1-传统模式(默认)、 2-网络模式")
    private String freightType;

    /** 关注来源类型(10-装货,20-卸货,30-签收,40-提柜,50-还柜,60-集卡卸货,70-集卡还柜,80-报价,90-接单,200-货源大厅) */
    @ApiModelProperty(value = "关注来源类型(10-装货,20-卸货,30-签收,40-提柜,50-还柜,60-集卡卸货,70-集卡还柜,80-报价,90-接单,200-货源大厅)")
    private String attentionSource;

    /** 关注来源运单id */
    @ApiModelProperty(value = "关注来源运单id")
    private String waybillId;

    /** 关注类型(10-长期关注，20-非长期关注) */
    @ApiModelProperty(value = "关注类型(10-长期关注，20-非长期关注)")
    private String attentionType;

    /** 删除标记(默认0，1已删除) */
    @ApiModelProperty(value = "删除标记(默认0，1已删除)")
    private String deleteFlag;

    /** 关注时间 */
    @ApiModelProperty(value = "关注时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date attentionTime;

    /** 扩展字段1 */
    @ApiModelProperty(value = "扩展字段1")
    private String item1;


    @ApiModelProperty(value = "扩展字段2")
    private String item2;


    @ApiModelProperty(value = "扩展字段3")
    private String item3;

    @ApiModelProperty(value = "扩展字段4")
    private String item4;

    /** 关注时间 */
    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyDate;

    @ApiModelProperty(value = "关注用户id")
    private String userId;

    @ApiModelProperty(value = "关注用户id")
    private List<String> goodsIds;

    @ApiModelProperty(value = "关注用户id")
    private List<String> orderIds;


    /**
     * 优享服务状态：10使用 20未使用
     */
    private String premiumServStatus;


    public LsdsGoodsAttention() {
    }

}
