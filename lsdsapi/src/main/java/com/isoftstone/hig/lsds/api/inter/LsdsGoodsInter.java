
package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.command.BidOpeningCommand;
import com.isoftstone.hig.lsds.api.dto.GoodsStatisticsDTO;
import com.isoftstone.hig.lsds.api.dto.LsdsGoodsBidOpeningDTO;
import com.isoftstone.hig.lsds.api.entity.LsdsGoods;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsOffer;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsPlan;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsRecord;
import com.isoftstone.hig.lsds.api.filter.BidOpeningFilter;
import com.isoftstone.hig.lsds.api.filter.CreateWinBidFileFilter;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsFilter;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsRecordFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsVo;
import com.isoftstone.hig.lsds.api.mvcvo.PlanTransferToGoodsVo;
import com.isoftstone.hig.lsds.api.mvcvo.WebsiteGoods;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 创建描述：货源编号  货源信息表控制器 类的后面Controller其实可以不用，只是为了容易区分
 *
 * <AUTHOR>
 * 创建时间 2019/11/15 14:56:33
 */
@Api(value = "货源管理操作接口", tags = {"货源管理操作接口"})
@FeignClient(name = "lsds", path = "/lsds/LsdsGoods")
public interface LsdsGoodsInter {
    /**
     * 新增下游询价货源
     * 创建者：cgb
     * 创建时间：2020/1/15 14:56:33
     *
     * @param lsdsGoodsVo 货源vo
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "新增下游询价货源", tags = {"货源管理操作接口"}, notes = "新增下游询价货源")
    @RequestMapping(value = "/lsdsGoodsDownAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsDownAdd(@RequestBody @ApiParam LsdsGoodsVo lsdsGoodsVo) throws Exception;


    @ApiOperation(value = "承运商货源自动审核", tags = {"承运商货源自动审核"}, notes = "承运商货源自动审核")
    @RequestMapping(value = "/lsdsGoodsAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsAddWithAutoCheck(@RequestBody @ApiParam LsdsGoodsVo vo);

    @ApiOperation(value = "计划单转换货源", tags = {"计划单转换货源"}, notes = "计划单转换货源")
    @RequestMapping(value = "/planTransferToGoods", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> planTransferToGoods(@RequestBody @ApiParam PlanTransferToGoodsVo vo);

    /**
     * 根据货源编号  货源信息表主键删除记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param goodsId 货源编号
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "删除货源", tags = {"货源管理操作接口"}, notes = "删除货源")
    @RequestMapping(value = "/lsdsGoodsDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsDel(@RequestParam String goodsId);

    /**
     * 根据货源编号  货源信息表实体修改记录，内部以ID主键修改
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param lsdsGoodsVo 货源信息表实体
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "更新货源", tags = {"货源管理操作接口"}, notes = "更新货源")
    @RequestMapping(value = "/lsdsGoodsUpdate", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsUpdate(@RequestBody @ApiParam LsdsGoodsVo lsdsGoodsVo) throws Exception;

    /**
     * 货源编号  货源信息表分页查询
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoods>}
     */
    @ApiOperation(value = "分页查询货源编号", tags = {"货源管理操作接口"}, notes = "分页查询货源编号")
    @RequestMapping(value = "/lsdsGoodsPaging", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> lsdsGoodsPaging(@RequestBody @ApiParam PagingInfo<LsdsGoodsFilter> pageInfo);


    /**
     * 分页查询
     * 创建者：cgb
     * 创建时间：2019/12/2
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoods>}
     */
    @ApiOperation(value = "分页查询货源信息", tags = {"货源管理操作接口"}, notes = "分页查询货源信息")
    @RequestMapping(value = "/getGoodsListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> getGoodsListPage(@RequestBody @ApiParam PagingInfo<LsdsGoodsFilter> pageInfo);
    /**
     * 查询待审核的货源条数
     * 创建者：zmf
     * 创建时间：2022/01/14
     * @return {@code Map<String,String> 1为货源管理,2为司机货源}
     */
    @ApiOperation(value = "查询待审核的货源条数", tags = {"货源管理操作接口"}, notes = "查询待审核的货源条数")
    @RequestMapping(value = "/getGoodsListAuditData", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.GET)
    Map<String,String> getGoodsListAuditData();

    /**
     * 分页查询货源操作记录列表
     * 创建者：cgb
     * 创建时间：2020/6/9
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoods>}
     */
    @ApiOperation(value = "分页查询货源操作记录列表", tags = {"货源管理操作接口"}, notes = "分页查询货源操作记录列表")
    @RequestMapping(value = "/getGoodsRecordListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoodsRecord> getGoodsRecordListPage(@RequestBody @ApiParam PagingInfo<LsdsGoodsRecordFilter> pageInfo);


    /**
     * 根据货源编号获取货源主信息
     * 创建者：cgb
     * 创建时间：2019/11/20
     *
     * @param goodsId 货源ID
     * @return {@code ResultMode<LsdsGoods>}
     */
    @ApiOperation(value = "根据货源编号获取货源主信息", tags = {"货源管理操作接口"}, notes = "根据货源编号获取货源主信息")
    @RequestMapping(value = "/lsdsGoodsGet", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> lsdsGoodsGet(@RequestParam String goodsId);


    /**
     * 根据货源ID查询货源详细信息（包含货源主信息、货源分段信息、承运商信息）
     * 创建者：cgb
     * 创建时间：2019/11/20
     *
     * @param goodsId 货源ID
     * @return {@code ResultMode<LsdsGoodsVo>}
     */
    @ApiOperation(value = "根据货源编号获取货源详细信息", tags = {"根据货源编号获取货源详细信息"}, notes = "根据货源编号获取货源详细信息")
    @RequestMapping(value = "/lsdsGoodsGetDetails", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoodsVo> lsdsGoodsGetDetails(@RequestParam String goodsId);


    /**
     * 根据货源ID查询货源详细信息（包含货源主信息、货源分段信息、承运商信息）
     * 创建者：cgb
     * 创建时间：2019/11/20
     *
     * @param lsdsGoods
     * @return {@code ResultMode<LsdsGoodsVo>}
     */
    @ApiOperation(value = "根据货源编号获取货源详细信息", tags = {"根据货源编号获取货源详细信息"}, notes = "根据货源编号获取货源详细信息")
    @RequestMapping(value = "/lsdsGoodsGetDetailsForDown", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoodsVo> lsdsGoodsGetDetailsForDown(@RequestBody @ApiParam LsdsGoods lsdsGoods);


    /**
     * 货源概况信息查询
     * 创建者：cgb
     *
     * @param goodsId 创建时间：2019/11/27
     * @param goodsId 货源ID
     * @return {@code ResultMode<Map<String, Object>>}
     */
    @ApiOperation(value = "货源概况信息查询", tags = {"货源概况信息查询"}, notes = "货源概况信息查询")
    @RequestMapping(value = "/lsdsGoodsOverview", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<Map<String, Object>> lsdsGoodsOverview(@RequestParam("goodsId") String goodsId);


    /**
     * 关闭货源
     * 创建者：cgb
     * 创建时间：2019/12/3
     *
     * @param goodsId 货源ID
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "关闭货源", tags = {"关闭货源"}, notes = "关闭货源")
    @RequestMapping(value = "/lsdsGoodsClose", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsClose(@RequestParam("goodsId") String goodsId);


    /**
     * 进行下一轮报价
     * 创建者：cgb
     * 创建时间：2019/11/27
     *
     * @param lsdsGoodsVo 货源发布VO实体类
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "进行下一轮报价", tags = {"进行下一轮报价"}, notes = "进行下一轮报价")
    @RequestMapping(value = "/offerToNetRound", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> offerToNetRound(@RequestBody @ApiParam LsdsGoodsVo lsdsGoodsVo);




    /**
     * 下单成功后 更新报价状态与货源交易状态
     * 创建者：cgb
     * 创建时间：2019/11/30
     *
     * @param lsdsGoodsVo
     * @return boolean
     */
    @ApiOperation(value = "下单成功后更新报价状态与货源交易状态", tags = {"下单成功后更新报价状态与货源交易状态"}, notes = "下单成功后更新报价状态与货源交易状态")
    @RequestMapping(value = "/lsdsGoodsUpdateAfterOrder", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsUpdateAfterOrder(@RequestBody @ApiParam LsdsGoodsVo lsdsGoodsVo);


    /**
     * 货源详情页面报价列表
     * 创建者: cgb
     * 创建时间: 2019/12/13
     *
     * @param pageInfo
     * @returns ResultMode<Map < String, Object>>
     */
    @ApiOperation(value = "货源详情页面报价列表", tags = {"货源详情页面报价列表"}, notes = "货源详情页面报价列表")
    @RequestMapping(value = "/getOfferMapListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<Map<String, Object>> getOfferMapListPage(@RequestBody @ApiParam PagingInfo<LsdsGoodsFilter> pageInfo);


    /**
     * 报价运输方案
     *
     * @param offerId
     * @return {@code  ResultMode<LsdsGoodsPlan>}
     * <AUTHOR>
     * 创建时间 2019/12/2
     */
    @ApiOperation(value = "报价运输方案", tags = {"报价运输方案"}, notes = "报价运输方案")
    @RequestMapping(value = "/getPlanListByOfferId", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoodsPlan> getPlanListByOfferId(@RequestParam String offerId);


    /**
     * 根据报价offerId查询报价信息
     *
     * @param offerIdArr
     * @return {@code  ResultMode<LsdsGoodsOffer>}
     * <AUTHOR>
     * 创建时间 2019/12/2
     */
    @ApiOperation(value = "根据报价offerId查询报价信息", tags = {"根据报价offerId查询报价信息"}, notes = "根据报价offerId查询报价信息")
    @RequestMapping(value = "/getOfferListById", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoodsOffer> getOfferListById(@RequestBody String[] offerIdArr);


    /**
     * 首页货源大厅（展示5条发布中货源信息：公开询价的货源）
     *
     * @return {@code  ResultMode<LsdsGoods>}
     * <AUTHOR>
     * 创建时间 2019/12/21
     */
    @ApiOperation(value = "首页根据报价offerId查询报价信息", tags = {"首页根据报价offerId查询报价信息"}, notes = "首页根据报价offerId查询报价信息")
    @RequestMapping(value = "/getGoodsListAuditedForHomePage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> getGoodsListAuditedForHomePage();

    /**
     * 首页货源大厅（展示5条已成交货源信息）
     *
     * @return {@code  ResultMode<LsdsGoods>}
     * <AUTHOR>
     * 创建时间 2019/12/21
     */
    @ApiOperation(value = "首页货源大厅（展示5条已成交货源信息）", tags = {"首页货源大厅（展示5条已成交货源信息）"}, notes = "首页货源大厅（展示5条已成交货源信息）")
    @RequestMapping(value = "/getGoodsListFinishedForHomePage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> getGoodsListFinishedForHomePage();


    /**
     * 首页货源信息分页查询
     * 创建者：cgb
     * 创建时间：2019/12/31
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoods>}
     */
    @ApiOperation(value = "首页货源信息分页查询", tags = {"首页货源信息分页查询"}, notes = "首页货源信息分页查询")
    @RequestMapping(value = "/getHomeGoodsListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<WebsiteGoods> getHomeGoodsListPage(@RequestBody @ApiParam PagingInfo<LsdsGoodsFilter> pageInfo);


    /**
     * 查询工作台发货方货源统计信息
     * 创建者：cgb
     *
     * @param lsdsGoods 货源实体
     * @return {@code ResultMode<Map<String, Object>>}
     */
    @ApiOperation(value = "查询工作台发货方货源统计信息", tags = {"查询工作台发货方货源统计信息"}, notes = "查询工作台发货方货源统计信息")
    @RequestMapping(value = "/lsdsGoodsSenderView", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<Map<String, String>> lsdsGoodsSenderView(@RequestBody @ApiParam LsdsGoods lsdsGoods);


    /**
     * 导出我要发货-货源列表信息
     *
     * @param lsdsGoodsFilter 查询条件
     * <AUTHOR>
     * 创建时间 2020/6/10
     */
    @ApiOperation(value = "导出我要发货-货源列表信息", tags = {"导出我要发货-货源列表信息"}, notes = "导出我要发货-货源列表信息")
    @RequestMapping(value = "/exportLsdsGoodsList", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> exportLsdsGoodsList(@RequestBody @ApiParam LsdsGoodsFilter lsdsGoodsFilter) throws IOException;


    /**
     * 返回首页货源大厅、最新车源、最新货源信息
     *
     * <AUTHOR>
     * 创建时间 2020/7/8
     */
    @ApiOperation(value = "返回首页货源大厅、最新车源、最新货源信息", tags = {"返回首页货源大厅、最新车源、最新货源信息"}, notes = "返回首页货源大厅、最新车源、最新货源信息")
    @RequestMapping(value = "/getGoodsCarInfoForHomeList", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoodsVo> getGoodsCarInfoForHomeList();

/*    @GetMapping(value = "/getGoodsListByGoodIds")
    ResultMode<LsdsGoods> getGoodsListByGoodIds(@RequestBody PlatformQrCodeFilter platformQrCodeFilter);*/

    /**
     * 根据货源ID查询基价；货物ID
     * @param goodsId
     * @return
     */
    @GetMapping(value = "/getGoodsBasePriceById")
    ResultMode<LsdsGoods> getGoodsBasePriceById(@RequestParam String goodsId);

    /**
     * 获取货源信息
     * 创建者：liupeng
     * 创建时间：2022/08/13
     *
     * @param goodsId 货源ID
     * @return {@code ResultMode<LsdsGoods>}
     */
    @ApiOperation(value = "获取货源信息", tags = {"货源管理操作接口"}, notes = "获取货源信息")
    @RequestMapping(value = "/lsdsGoodsInfo", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> lsdsGoodsInfo(@RequestParam String goodsId);

/*    @RequestMapping(value = "/getGoodsInfo", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> getGoodsInfo(@RequestBody PlatformQrCodeFilter platformQrCodeFilter);*/

    @RequestMapping(value = "/getGoodsInfoByQuery", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> getGoodsInfoByQuery(@RequestParam("goodsIds") List<String> goodsIds);

    @GetMapping(value = "/getGoodsInfoContainAddress")
    ResultMode<LsdsGoods> getGoodsInfoContainAddress(@RequestParam("goodsId") String goodsId);

    @ApiOperation(value = "统计常用地址已产生的货源数", tags = {"货源管理操作接口"}, notes = "统计常用地址已产生的货源数")
    @RequestMapping(value = "/countAddressInUse", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<Integer> countAddressInUse(@RequestParam("lineId") String lineId);

    @ApiOperation(value = "统计常用路线已产生的货源数", tags = {"货源管理操作接口"}, notes = "统计常用路线已产生的货源数")
    @RequestMapping(value = "/countLineInUse", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<Integer> countLineInUse(@RequestBody List<String> lineIds);

    @RequestMapping(value = "/getLsdsGoodsListByGoodsIdList", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> getLsdsGoodsListByGoodsIdList(@RequestParam("goodsIds") List<String> goodsIds);

    /**
     * 开标
     */
    @PostMapping(value = "/bidOpening")
    ResultMode bidOpening(@RequestBody @Validated BidOpeningCommand command);

    /**
     * 查询货源开标状态
     */
    @PostMapping(value = "/queryGoodsBidOpening")
    ResultMode<LsdsGoodsBidOpeningDTO> queryGoodsBidOpening(@RequestBody @Validated BidOpeningFilter filter);

    /**
     * 生成中标文件
     * @return
     */
    @PostMapping(value = "/createWinBidFile")
    ResultMode createWinBidFile(@RequestBody @Validated CreateWinBidFileFilter filter);

    @ApiOperation("4pl数据统计")
    @PostMapping("/platformStatistics")
    ResultMode<GoodsStatisticsDTO> platformStatistics(@RequestBody LsdsGoodsFilter filter);
}
