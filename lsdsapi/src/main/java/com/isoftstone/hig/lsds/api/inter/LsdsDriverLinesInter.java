package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.entity.LsdsDriverLines;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 司机常跑路线
 * <AUTHOR>
 */

@Api(value = "司机常跑路线", tags = "司机常跑路线")
@FeignClient(name = "lsds", path = "/lsds/driverLines")
public interface LsdsDriverLinesInter {

    /**
     * APP司机端：装货/提柜异步触发，添加常跑路线
     * @param lsdsDriverLines
     * @return 结果
     */
    @PostMapping(value = "/save")
    @ApiOperation(value = "新增")
    ResultMode save(@RequestBody LsdsDriverLines lsdsDriverLines);

    /**
     * APP司机端：签收/还柜异步触发：作业数量的累加
     * @param lsdsDriverLines
     * @return 结果
     */
    @PostMapping(value = "/updateByDriverIdAndCityCode")
    @ApiOperation(value = "更新")
    ResultMode updateByDriverIdAndCityCode(@RequestBody LsdsDriverLines lsdsDriverLines);

}
