package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.command.ReceiptPayCheckCommand;
import com.isoftstone.hig.lsds.api.mvcvo.PrepayReqVO;
import com.isoftstone.hig.lsds.api.mvcvo.PrepayResVO;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <功能描述/>
 * 预付费接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">ronglijun</A>
 * @version 1.0
 * @since 2022/5/7 16:03
 */

@Api(value = "预付费接口", tags = "预付费接口")
@FeignClient(name = "lsds", path = "/lsds/prepayment")
public interface PrepayInter {

    @ApiOperation(value = "判断设置的预付费是否超限额接口", tags = {"判断设置的预付费是否超限额接口"}, notes = "判断设置的预付费是否超限额接口")
    @RequestMapping(value = "/prepayCheck", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<PrepayResVO> prepayCheck(@RequestBody @ApiParam PrepayReqVO prepayReqVO);

    @ApiOperation(value = "回单付金额上限校验")
    @RequestMapping(value = "/receiptPayCheck", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<PrepayResVO> receiptPayCheck(@RequestBody @Validated ReceiptPayCheckCommand command);

}
