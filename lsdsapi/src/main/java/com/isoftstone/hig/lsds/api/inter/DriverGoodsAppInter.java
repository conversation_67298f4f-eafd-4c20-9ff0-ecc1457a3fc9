package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.DriverGoodsVo;
import com.isoftstone.hig.lsds.api.filter.DriverGoodsFilter;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 司机货源
 *
 * <AUTHOR>
 * @date 2021-05-28
 */

@Api(value = "司机货源App", tags = "司机货源App")
@FeignClient(name = "lsds", path = "/lsds/driverGoodsApp")
public interface DriverGoodsAppInter
{

    /**
     * 查询司机货源列表
     *
     * @param driverGoodsFilter 司机货源
     * @return 司机货源集合
     */
    @PostMapping("/viewGoods")
    @ApiOperation(value = "查看货源")
    ResultMode<DriverGoodsVo> viewGoods(@RequestBody DriverGoodsFilter driverGoodsFilter);

    /**
     * 分页查询司机货源列表
     *
     * @param driverGoodsFilter 司机货源
     * @return 司机货源集合
     */
    @PostMapping(value = "/homeData")
    @ApiOperation(value = "首页数据查询")
    ResultMode<DriverGoods> homeData(@RequestBody DriverGoodsFilter driverGoodsFilter);
}
