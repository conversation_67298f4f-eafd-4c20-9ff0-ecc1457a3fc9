package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.mvcvo.GoodsRecommendReqVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsAttentionVO;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 货源推荐
 *
 * <AUTHOR>
 * @date 2023/03/03 09:55:18
 */

@FeignClient(name = "lsds", path = "/lsds/goodsRecommend")
public interface GoodsRecommendInter {

    /**
     * 获取货源推荐信息
     *
     * @param recommendReq
     * @return {@link ResultMode}<{@link LsdsGoodsAttentionVO}>
     */
    @RequestMapping("/getGoodsRecommendInfo")
    ResultMode<LsdsGoodsAttentionVO> getGoodsRecommendInfo(@RequestBody GoodsRecommendReqVo recommendReq);

}
