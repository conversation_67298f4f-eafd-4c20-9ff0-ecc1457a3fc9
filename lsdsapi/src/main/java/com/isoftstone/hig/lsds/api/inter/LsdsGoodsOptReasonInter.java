package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.command.OptReasonAddCommand;
import com.isoftstone.hig.lsds.api.command.OptReasonDelCommand;
import com.isoftstone.hig.lsds.api.command.OptReasonUpdSortCommand;
import com.isoftstone.hig.lsds.api.dto.LsdsGoodsOptReasonDTO;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsOptReasonFilter;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 货源审核原因
 * <AUTHOR>
 */
@Api(value = "货源审核原因", tags = {"货源审核原因操作接口"})
@FeignClient(name = "lsds", path = "/lsds/optReason")
public interface LsdsGoodsOptReasonInter {

    /**
     * 分页查询
     */
    @ApiOperation(value = "分页查询")
    @PostMapping(value = "/queryList")
    ResultMode<LsdsGoodsOptReasonDTO> queryList(@RequestBody @Validated PagingInfo<LsdsGoodsOptReasonFilter> pagingInfo);

    /**
     * 新增
     */
    @ApiOperation(value = "新增")
    @PostMapping(value = "/add")
    ResultMode add(@RequestBody @Validated OptReasonAddCommand command);

    /**
     * 更新排序
     */
    @ApiOperation(value = "更新排序")
    @PostMapping(value = "/updateSort")
    ResultMode updateSort(@RequestBody @Validated List<OptReasonUpdSortCommand> commandList);

    /**
     * 删除
     */
    @ApiOperation(value = "删除")
    @PostMapping(value = "/delete")
    ResultMode delete(@RequestBody @Validated OptReasonDelCommand command);

}
