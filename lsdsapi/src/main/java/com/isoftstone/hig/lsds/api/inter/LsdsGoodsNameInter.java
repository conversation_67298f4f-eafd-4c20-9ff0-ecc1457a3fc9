package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.entity.LsdsGoodsName;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsNameRelation;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsNameFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsNameCreateVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsNameVo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 * 货物名称管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 */
@Api(value = "货物名称操作接口", tags = {"货物名称操作接口"})
@FeignClient(name = "lsds", path = "/lsds/lsdsGoodsName")
public interface LsdsGoodsNameInter {

    /**
     * 查询货品名称列表
     *
     * @param pageInfo 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    @ApiOperation(value = "查询货品名称列表")
    @RequestMapping(value = "/getGoodsNameListPage", method = RequestMethod.POST)
    ResultMode<LsdsGoodsNameVo> getGoodsNameListPage(@RequestBody PagingInfo<LsdsGoodsNameFilter> pageInfo);

    /**
     * 查询货物名称关系
     *
     * @param pageInfo 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    @ApiOperation(value = "查询货物名称关系")
    @RequestMapping(value = "/getGoodsNameRelationListPage", method = RequestMethod.POST)
    ResultMode<LsdsGoodsNameRelation> getGoodsNameRelationListPage(@RequestBody PagingInfo<LsdsGoodsNameRelation> pageInfo);

    /**
     * 新增货物名称
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @ApiOperation(value = "新增货物名称")
    @RequestMapping(value = "/addGoodsName", method = RequestMethod.POST)
    ResultMode addGoodsName(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo);

    /**
     * 新增货物名称-- 智慧场站专用
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @ApiOperation(value = "新增货物名称-- 智慧场站专用")
    @RequestMapping(value = "/addGoodsNameByStation", method = RequestMethod.POST)
    ResultMode addGoodsNameByStation(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo);

    /**
     * 解除
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @ApiOperation(value = "解除")
    @RequestMapping(value = "/removeGoodsName", method = RequestMethod.POST)
    ResultMode removeGoodsName(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo);

    /**
     * 删除
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/deleteGoodsName", method = RequestMethod.POST)
    ResultMode deleteGoodsName(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo);

    /**
     * 禁用
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @ApiOperation(value = "禁用")
    @RequestMapping(value = "/disableGoodsName", method = RequestMethod.POST)
    ResultMode disableGoodsName(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo);

    /**
     * 启用
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @ApiOperation(value = "启用")
    @RequestMapping(value = "/enableGoodsName", method = RequestMethod.POST)
    ResultMode enableGoodsName(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo);

    /**
     * 审核
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @ApiOperation(value = "审核")
    @RequestMapping(value = "/auditGoodsName", method = RequestMethod.POST)
    ResultMode auditGoodsName(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo);

    /**
     * 调整平台分类
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @ApiOperation(value = "调整平台分类")
    @RequestMapping(value = "/editPlatformType", method = RequestMethod.POST)
    ResultMode editPlatformType(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo);

    /**
     * 导出货物名称
     *
     * @param goodsNameFilter 货物名称
     * @return {@link ResultMode}
     */
    @ApiOperation(value = "导出货物名称")
    @RequestMapping(value = "/exportGoodsName", method = RequestMethod.POST)
    void exportGoodsName(@RequestBody LsdsGoodsNameFilter goodsNameFilter);

    /**
     * 按公司id查询货品名称下拉列表
     *
     * @param goodsNameFilter 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    @ApiOperation(value = "按公司id查询货品名称下拉列表")
    @RequestMapping(value = "/findGoodsNameDropdownList", method = RequestMethod.POST)
    ResultMode<LsdsGoodsNameVo> findGoodsNameDropdownList(@RequestBody LsdsGoodsNameFilter goodsNameFilter);

    /**
     * 服务伙伴创建-按公司id查询货品名称下拉列表
     *
     * @param goodsNameFilter 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    @ApiOperation(value = "按公司id查询货品名称下拉列表")
    @RequestMapping(value = "/findGoodsNameListForPartner", method = RequestMethod.POST)
    ResultMode<LsdsGoodsNameVo> findGoodsNameListForPartner(@RequestBody LsdsGoodsNameFilter goodsNameFilter);

    /**
     * 查询货品名称列表
     *
     * @param goodsNameFilter 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    @ApiOperation(value = "查询货品名称列表")
    @RequestMapping(value = "/findGoodsNameList", method = RequestMethod.POST)
    ResultMode<LsdsGoodsNameVo> findGoodsNameList(@RequestBody LsdsGoodsNameFilter goodsNameFilter);

    /**
     * 按名称查询货品名称
     *
     * @param goodsNameFilter 货物名称信息
     * @return {@link LsdsGoodsName}
     */
    @ApiOperation(value = "按名称查询货品名称")
    @RequestMapping(value = "/findGoodsNameByName", method = RequestMethod.POST)
    ResultMode<LsdsGoodsNameVo> findGoodsNameByName(@RequestBody LsdsGoodsNameFilter goodsNameFilter);

    /**
     * 增加货物关联关系
     *
     * @param goodsNameVo 货物信息
     * @return {@link LsdsGoodsName}
     */
    @ApiOperation(value = "增加货物关联关系")
    @RequestMapping(value = "/addGoodsNameRelation", method = RequestMethod.POST)
    ResultMode<String> addGoodsNameRelation(@RequestBody LsdsGoodsNameVo goodsNameVo);


    /**
     * 批量导入货物名称
     *
     * @param file 货物名称文件
     * @return 处理结果
     */
    @PostMapping(value = "/batchImportGoodsName", consumes = {"multipart/form-data"},
        produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "批量导入货物名称", notes = "批量导入货物名称")
    @ResponseBody
    ResultMode batchImportGoodsName(@RequestParam("file") MultipartFile file);

    /**
     * 查询货品名称列表
     *
     * @param pageInfo 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    /**
     * @Description: 根据companyId和货物名称查询货物列表
     * @Author: Mindy
     * @Date: 2023/11/20
     * @Param:
     * @return:
     */
    @ApiOperation(value = "根据companyId和货物名称查询货物列表")
    @RequestMapping(value = "/getGoodsNameList", method = RequestMethod.POST)
    ResultMode<LsdsGoodsNameVo> getGoodsNameList(@RequestBody PagingInfo<LsdsGoodsNameFilter> pagingInfo);

    @ApiOperation(value = "批量按名称查询货品名称")
    @RequestMapping(value = "/findBatchGoodsNameByName", method = RequestMethod.POST)
    ResultMode<LsdsGoodsNameVo> findBatchGoodsNameByName(@RequestBody LsdsGoodsNameFilter goodsNameFilter);
}
