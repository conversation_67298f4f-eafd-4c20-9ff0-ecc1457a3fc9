package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.filter.IdentifyCodeRelFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsIdentifyCodeRelVO;
import com.isoftstone.hig.lsds.api.util.ValidatedGroup;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "货源识别码绑定关系", tags = "货源识别码绑定关系")
@FeignClient(name = "lsds", path = "/lsds/identifyCodeRel")
public interface LsdsIdentifyCodeRelInter {


    @PostMapping("/bindIdentifyCodeRel")
    ResultMode<LsdsIdentifyCodeRelVO> queryBindIdentifyCodeRel(@RequestBody @Validated IdentifyCodeRelFilter filter);


    @PostMapping("/modify")
    ResultMode<Boolean> updateById(@RequestBody @Validated({ValidatedGroup.Update.class}) IdentifyCodeRelFilter filter);
}
