package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.command.LsdsMatchmakingConfigAddCommand;
import com.isoftstone.hig.lsds.api.command.LsdsMatchmakingConfigUpdateCommand;
import com.isoftstone.hig.lsds.api.dto.LsdsMatchmakingConfigDTO;
import com.isoftstone.hig.lsds.api.dto.LsdsMatchmakingRecordDTO;
import com.isoftstone.hig.lsds.api.query.LsdsMatchmakingConfigQuery;
import com.isoftstone.hig.lsds.api.query.LsdsMatchmakingRecordQuery;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年11月14日 19:11
 */
@Api("撮合管理")
@FeignClient(name = "lsds", path = "/lsds/matchmaking")
public interface LsdsMatchmakingInter {

    @ApiOperation("新增撮合配置")
    @PostMapping("/addConfig")
    ResultMode addConfig(@Validated @RequestBody LsdsMatchmakingConfigAddCommand command);

    @ApiOperation("修改撮合配置")
    @PostMapping("/updateConfig")
    ResultMode updateConfig(@Validated @RequestBody LsdsMatchmakingConfigUpdateCommand command);

    @ApiOperation("查询撮合配置")
    @PostMapping("/queryConfig")
    ResultMode<LsdsMatchmakingConfigDTO> queryConfig(@Validated @RequestBody LsdsMatchmakingConfigQuery query);

    @ApiOperation("查询撮合配置操作记录")
    @PostMapping("/queryConfigRecord")
    ResultMode<LsdsMatchmakingRecordDTO> queryConfigRecord(@Validated @RequestBody PagingInfo<LsdsMatchmakingRecordQuery> query);
}
