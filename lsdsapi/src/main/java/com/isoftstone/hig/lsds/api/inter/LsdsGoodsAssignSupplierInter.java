//=========================================================
//===       此类是由代码工具生成，框架开发者
//===       框架开发者Create By: 李健华
//===       Create Date: 2019/11/15 14:56:33
//=========================================================
package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.entity.LsdsGoodsAssignSupplier;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsAssignSupplierFilter;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

//引用的包

/**
 * @ApiImplicitParams：用在请求的方法上，包含一组参数说明
 * @ApiImplicitParam：对单个参数的说明 name：参数名
 * value：参数的汉字说明、解释
 * required：参数是否必须传
 * paramType：参数放在哪个地方
 * · header --> 请求参数的获取：@RequestHeader
 * · query --> 请求参数的获取：@RequestParam
 * · path（用于restful接口）--> 请求参数的获取：@PathVariable
 * · body（请求体）-->  @RequestBody User user
 * · form（不常用）
 * dataType：参数类型，默认String，其它值dataType="Integer"
 * defaultValue：参数的默认值
 * @Api 的tags2.6以前不能使用中文，不然生成的ui出来无法点击方方名称展开，只能点击全部显示。 2.9.1版本可以支持
 * 这里只用value可以点击，但一起与tags使用就不行
 */


/**
 * 创建描述：货物指定物流供应商控制器 类的后面Controller其实可以不用，只是为了容易区分
 *
 * <AUTHOR>
 * 创建时间 2019/11/15 14:56:33
 */
@Api(value = "货物指定物流供应商控制器", tags = {"货物指定物流供应商操作接口"})

@FeignClient(name = "lsds", path = "/lsds/LsdsGoodsAssignSupplier")
public interface LsdsGoodsAssignSupplierInter {
    /**
     * 根据实体货物指定物流供应商添加记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param model 货物指定物流供应商实体类
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "增加货物指定物流供应商", tags = {"货物指定物流供应商操作接口"}, notes = "增加货物指定物流供应商")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "model", value = "货物指定物流供应商实体类", required = true, paramType = "body", dataType = "LsdsGoodsAssignSupplier")
    })
    @RequestMapping(value = "/lsdsGoodsAssignSupplierAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsAssignSupplierAdd(@RequestBody LsdsGoodsAssignSupplier model);

    /**
     * 根据货物指定物流供应商主键删除记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "删除货物指定物流供应商", tags = {"货物指定物流供应商操作接口"}, notes = "根据主键删除货物指定物流供应商")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "goodsAssignSupplierId", value = "goodsAssignSupplierId主键", required = true, paramType = "query")
    })
    @RequestMapping(value = "/lsdsGoodsAssignSupplierDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsAssignSupplierDel(@RequestParam String goodsAssignSupplierId);

    /**
     * 根据货物指定物流供应商实体修改记录，内部以ID主键修改
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param model 货物指定物流供应商实体类
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "更新货物指定物流供应商", tags = {"货物指定物流供应商操作接口"}, notes = "根据主键更新货物指定物流供应商")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "model", value = "货物指定物流供应商实体类", required = true, paramType = "body", dataType = "LsdsGoodsAssignSupplier")
    })
    @RequestMapping(value = "/lsdsGoodsAssignSupplierUpdate", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsAssignSupplierUpdate(@RequestBody LsdsGoodsAssignSupplier model);

    /**
     * 货物指定物流供应商分页查询
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoodsAssignSupplier>}
     */
    @ApiOperation(value = "分页查询货物指定物流供应商", tags = {"货物指定物流供应商操作接口"}, notes = "分页查询货物指定物流供应商")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageInfo", value = "货物指定物流供应商实体类", required = true, paramType = "body", dataType = "PagingInfo<LsdsGoodsAssignSupplierFilter>")
    })
    @RequestMapping(value = "/lsdsGoodsAssignSupplierPaging", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoodsAssignSupplier> lsdsGoodsAssignSupplierPaging(@RequestBody PagingInfo<LsdsGoodsAssignSupplierFilter> pageInfo);


    /**
     * 查询货源承运企业
     * @param pageInfo
     * @return
     */
    @RequestMapping(value = "/getLsdsGoodsAssignSupplier", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoodsAssignSupplier> getLsdsGoodsAssignSupplier(@RequestBody PagingInfo<LsdsGoodsAssignSupplierFilter> pageInfo);


}
