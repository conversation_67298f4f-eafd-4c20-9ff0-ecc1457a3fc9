package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.command.GoodsAliasCommand;
import com.isoftstone.hig.lsds.api.command.IdCommand;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年11月18日 19:40
 */

@FeignClient(name = "lsds", path = "/lsds/goods/alias")
public interface LsdsGoodsAliasInter {

    @PostMapping("/add")
    ResultMode add(@RequestBody @Validated GoodsAliasCommand command);

    @PostMapping("/queryList")
    ResultMode queryList();

    @PostMapping("/delete")
    ResultMode delete(@RequestBody @Validated IdCommand command);
}
