package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.entity.LsdsGoodsDeductible;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsCompensationRulesFilter;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.Api;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Api(value = "赔偿规则接口", tags = "免赔规则相关接口")
@FeignClient(name = "lsds", path = "/lsds/compensationRules")
public interface LsdsCompensationRulesInter {

    @PostMapping(value="/getLsdsGoodsCompensationRulesById")
     ResultMode<LsdsGoodsDeductible> getLsdsGoodsCompensationRulesById(@RequestBody LsdsGoodsCompensationRulesFilter filter);
}
