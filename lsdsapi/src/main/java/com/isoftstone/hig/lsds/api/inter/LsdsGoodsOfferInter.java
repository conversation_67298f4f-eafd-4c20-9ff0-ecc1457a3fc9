//=========================================================
//===       此类是由代码工具生成，框架开发者
//===       框架开发者Create By: 李健华
//===       Create Date: 2019/11/15 14:56:33
//=========================================================
package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.entity.LsdsGoods;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsOffer;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsFilter;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsOfferFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsVo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;
import java.util.Map;

//引用的包

/**
 * @ApiImplicitParams：用在请求的方法上，包含一组参数说明
 * @ApiImplicitParam：对单个参数的说明 name：参数名
 * value：参数的汉字说明、解释
 * required：参数是否必须传
 * paramType：参数放在哪个地方
 * · header --> 请求参数的获取：@RequestHeader
 * · query --> 请求参数的获取：@RequestParam
 * · path（用于restful接口）--> 请求参数的获取：@PathVariable
 * · body（请求体）-->  @RequestBody User user
 * · form（不常用）
 * dataType：参数类型，默认String，其它值dataType="Integer"
 * defaultValue：参数的默认值
 * @Api 的tags2.6以前不能使用中文，不然生成的ui出来无法点击方方名称展开，只能点击全部显示。 2.9.1版本可以支持
 * 这里只用value可以点击，但一起与tags使用就不行
 */


/**
 * 创建描述：对货源报价ID 货源报价 我准备要接单进行承运进行报价。货主（老板）审核评估你的价格OK了再进行下单给你承运.控制器 类的后面Controller其实可以不用，只是为了容易区分
 *
 * <AUTHOR>
 * 创建时间 2019/11/15 14:56:33
 */
@Api(value = "货源报价", tags = {"货源报价"})
@FeignClient(name = "lsds", path = "/lsds/LsdsGoodsOffer")
public interface LsdsGoodsOfferInter {
    /**
     * 根据实体对货源报价ID 货源报价 我准备要接单进行承运进行报价。货主（老板）审核评估你的价格OK了再进行下单给你承运.添加记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     * @param lsdsGoodsVo 货源信息表实体
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "新增报价", tags = {"新增报价"}, notes = "新增报价")
    @RequestMapping(value = "/lsdsGoodsOfferAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsOfferAdd(@RequestBody @ApiParam LsdsGoodsVo lsdsGoodsVo);

    /**
     * 平台3pl新增货源报价信息
     * 创建者：cgb
     * 创建时间：2020/4/22
     * @param lsdsGoodsVo 货源信息表实体
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "新增下游货源报价", tags = {"新增下游货源报价"}, notes = "新增下游货源报价")
    @RequestMapping(value = "/lsdsGoodsOfferAddDown", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsOfferAddDown(@RequestBody @ApiParam LsdsGoodsVo lsdsGoodsVo);

    /**
     * 根据对货源报价ID 货源报价 我准备要接单进行承运进行报价。货主（老板）审核评估你的价格OK了再进行下单给你承运.主键删除记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param offerId offerId主键
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "删除货源报价", tags = {"删除货源报价"}, notes = "删除货源报价")
    @RequestMapping(value = "/lsdsGoodsOfferDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsOfferDel(@RequestParam("offerId") String offerId);

    /**
     * 根据对货源报价ID 货源报价 我准备要接单进行承运进行报价。货主（老板）审核评估你的价格OK了再进行下单给你承运.实体修改记录，内部以ID主键修改
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param lsdsGoodsOffer 货源报价实体类
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "更新货源报价", tags = {"更新货源报价"}, notes = "更新货源报价")
    @RequestMapping(value = "/lsdsGoodsOfferUpdate", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsOfferUpdate(@RequestBody @ApiParam LsdsGoodsOffer lsdsGoodsOffer);

    /**
     * 承运人货源报价列表
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoodsOffer>}
     */
    @ApiOperation(value = "货源报价列表", tags = {"货源报价列表"}, notes = "货源报价列表")
    @RequestMapping(value = "/lsdsGoodsOfferPaging", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoodsOffer> lsdsGoodsOfferPaging(@RequestBody @ApiParam PagingInfo<LsdsGoodsOfferFilter> pageInfo);

    /**
     * 承运人货源报价列表
     * 创建者：cgb
     * 创建时间：2019/12/2
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoods>}
     */
    @ApiOperation(value = "货源管理列表", tags = {"货源管理列表"}, notes = "货源管理列表")
    @RequestMapping(value = "/getGoodsListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> getGoodsListPage(@RequestBody @ApiParam PagingInfo<LsdsGoodsFilter> pageInfo);


    /**
     * App货源列表(找货)
     * 创建者：cgb
     * 创建时间：2020/1/16
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoods>}
     */
    @ApiOperation(value = "货源管理列表", tags = {"货源报价"}, notes = "货源管理列表")
    @RequestMapping(value = "/getGoodsListPageForApp", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> getGoodsListPageForApp(@RequestBody @ApiParam PagingInfo<LsdsGoodsFilter> pageInfo);


    /**
     * 承运人货源报价列表
     * 创建者：cgb
     * 创建时间：2019/12/2
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoods>}
     */
    @ApiOperation(value = "报价管理列表", tags = {"报价管理列表"}, notes = "报价管理列表")
    @RequestMapping(value = "/getGoodsOfferListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> getGoodsOfferListPage(@RequestBody @ApiParam PagingInfo<LsdsGoodsFilter> pageInfo);


    /**
     * 根据货源ID查询货源详细信息（包含货源主信息、货源分段信息、承运商信息）
     * 创建者：cgb
     * 创建时间：2019/11/25
     * @param lsdsGoods 货源信息表实体类
     * @return {@code ResultMode<LsdsGoodsVo>}
     */
    @ApiOperation(value = "根据货源编号获取货源详细信息", tags = {"根据货源编号获取货源详细信息"}, notes = "根据货源编号获取货源详细信息")
    @RequestMapping(value = "/lsdsGoodsGetDetails", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoodsVo> lsdsGoodsGetDetails(@RequestBody @ApiParam LsdsGoods lsdsGoods);

    /**
     * 根据货源ID查询货源详细信息（包含货源主信息、货源分段信息、承运商信息）
     * 创建者：cgb
     * 创建时间：2019/11/25
     * @param lsdsGoods 货源信息表实体类
     * @return {@code ResultMode<LsdsGoodsVo>}
     */
    @ApiOperation(value = "根据货源编号获取下游货源详细信息", tags = {"根据货源编号获取下游货源详细信息"}, notes = "根据货源编号获取下游货源详细信息")
    @RequestMapping(value = "/lsdsGoodsGetDetailsDown", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoodsVo> lsdsGoodsGetDetailsDown(@RequestBody @ApiParam LsdsGoods lsdsGoods);

    /**
     * 根据货源ID查询货源详细信息（包含货源主信息、货源分段信息、承运商信息）
     * 创建者：cgb
     * 创建时间：2019/11/25
     * @param lsdsGoods 货源信息表实体类
     * @return {@code ResultMode<LsdsGoods>}
     */
    @ApiOperation(value = "多条件获取货源详细信息", tags = {"多条件获取货源详细信息"}, notes = "多条件获取货源详细信息")
    @RequestMapping(value = "/lsdsGoodsDetails", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> lsdsGoodsDetails(@RequestBody @ApiParam LsdsGoods lsdsGoods);

    /**
     * 进行下一轮报价
     * 创建者：cgb
     * 创建时间：2019/11/27
     * @param lsdsGoodsVo 货源信息表实体类
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "进行下一轮报价", tags = {"进行下一轮报价"}, notes = "进行下一轮报价")
    @RequestMapping(value = "/offerToNetRound", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> offerToNetRound(@RequestBody @ApiParam LsdsGoodsVo lsdsGoodsVo);


    /**
     * 下游询价时，平台3pl使用下游承运商的报价作为自己给发货人的报价
     * 创建者: cgb
     * 创建时间: 2019/12/17
     * @param lsdsGoodsVo
     * @returns ResultMode<String>
     */
    @ApiOperation(value = "下游询价时（平台3pl）", tags = {"下游询价时（平台3pl）"}, notes = "下游询价时（平台3pl）")
    @RequestMapping(value = "/lsdsGoodsOfferToSender", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsGoodsOfferToSender(@RequestBody @ApiParam LsdsGoodsVo lsdsGoodsVo);

    /**
     * 根据货源编号获取货源主信息
     * 创建者：cgb
     * 创建时间：2019/11/20
     * @param goodsId 货源ID
     * @return {@code ResultMode<LsdsGoods>}
     */
    @ApiOperation(value = "根据货源编号获取货源主信息", tags = {"货源报价"}, notes = "根据货源编号获取货源主信息")
    @RequestMapping(value = "/lsdsGoodsGet", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> lsdsGoodsGet(@RequestBody String goodsId);

    /**
     * 查询工作台承运方货源统计信息
     * 创建者：cgb
     * @param lsdsGoods 货源实体
     * @return {@code ResultMode<Map<String, Object>>}
     */
    @ApiOperation(value = "查询工作台承运方货源统计信息", tags = {"查询工作台承运方货源统计信息"}, notes = "查询工作台承运方货源统计信息")
    @RequestMapping(value = "/lsdsGoodsSupplierView", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<Map<String, String>> lsdsGoodsSupplierView(@RequestBody @ApiParam LsdsGoods lsdsGoods);

    /**
     * 查询工作台货源大厅货源列表信息
     * 创建者：cgb
     * 创建时间：2020/3/24
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoods>}
     */
    @ApiOperation(value = "查询工作台货源大厅货源列表信息", tags = {"查询工作台货源大厅货源列表信息"}, notes = "查询工作台货源大厅货源列表信息")
    @RequestMapping(value = "/getBenchGoodsListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoods> getBenchGoodsListPage(@RequestBody @ApiParam PagingInfo<LsdsGoodsFilter> pageInfo);

    /**
     * 导出我要承运-报价列表信息
     * <AUTHOR>
     * 创建时间 2020/6/12
     * @param lsdsGoodsFilter 查询条件
     */
    @ApiOperation(value = "导出我要承运-报价列表信息", tags = {"货源报价"}, notes = "导出我要承运-报价列表信息")
    @RequestMapping(value = "/exportLsdsGoodsOfferList", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> exportLsdsGoodsOfferList(@RequestBody @ApiParam LsdsGoodsFilter lsdsGoodsFilter) throws IOException;

    @ApiOperation(value = "根据报价id查询成交报价信息", tags = {"货源报价"}, notes = "根据货源号查询成交报价信息")
    @RequestMapping(value = "/getOfferByOfferId", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsGoodsOffer> getOfferByOfferId(@RequestParam("offerId") String offerId);

}
