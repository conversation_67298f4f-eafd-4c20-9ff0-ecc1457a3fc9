package com.isoftstone.hig.lsds.api.util;

import com.isoftstone.hig.lsds.api.exception.ResultStatus;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;

public class Result<T> extends ResultMode {

    /**
     * 返回成功
     *
     * @return ResultMode 结果实体
     */
    public static ResultMode success() {
        return new ResultMode<String>();
    }

    /**
     * 返回成功
     *
     * @return ResultMode 结果实体
     */
    public static ResultMode success(String str) {
        ResultMode resultMode = new ResultMode();
        resultMode.getModel().add(str);
        return resultMode;
    }

    /**
     * 返回失败
     *
     * @param status 状态
     * @return ResultMode 结果实体
     */
    public static ResultMode error(ResultStatus status) {
        ResultMode<String> resultMode = new ResultMode<>();
        resultMode.setErrCode(String.valueOf(status.getCode()));
        resultMode.setErrMsg(status.getDesc());
        resultMode.setSucceed(false);
        return resultMode;
    }

    /**
     * 返回失败
     *
     * @param status 状态
     * @param msg    信息
     * @return ResultMode 结果实体
     */
    public static ResultMode error(ResultStatus status, String msg) {
        ResultMode<String> resultMode = new ResultMode<>();
        resultMode.setErrCode(String.valueOf(status.getCode()));
        resultMode.setErrMsg(status.getDesc());
        resultMode.setSucceed(false);
        resultMode.getModel().add(msg);
        return resultMode;
    }

}
