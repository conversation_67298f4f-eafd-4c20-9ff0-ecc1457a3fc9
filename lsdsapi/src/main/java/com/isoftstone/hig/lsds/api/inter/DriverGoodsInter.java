package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.dto.DriverGoodsExtendDTO;
import com.isoftstone.hig.lsds.api.dto.GoodsStatisticsDTO;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.filter.DriverGoodsFilter;
import com.isoftstone.hig.lsds.api.mvcvo.DriverGoodsHotCar;
import com.isoftstone.hig.lsds.api.mvcvo.PlanTransferToDriverGoodsVo;
import com.isoftstone.hig.lsds.api.query.DriverGoodsExtendQuery;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

/**
 * 司机货源
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@Api(value = "司机货源", tags = "司机货源")
@FeignClient(name = "lsds", path = "/lsds/driverGoods")
public interface DriverGoodsInter {

    /**
     * 查询司机货源
     *
     * @param driverGoodsFilter 司机货源ID
     * @return 司机货源
     */
    @PostMapping("/getById")
    @ApiOperation(value = "根据id查询")
    ResultMode<DriverGoods> getById(@RequestBody DriverGoodsFilter driverGoodsFilter);

    /**
     * 查询司机货源列表
     *
     * @param driverGoodsFilter 司机货源
     * @return 司机货源集合
     */
    @PostMapping("/listByEntity")
    @ApiOperation(value = "查询列表")
    ResultMode<DriverGoods> listByEntity(@RequestBody DriverGoodsFilter driverGoodsFilter);

    /**
     * 通过货源IDS查询绑定的货源列表
     *
     * @param pageInfo 货源IDS
     * @return {@code ResultMode<DriverGoods>}
     */
/*    @ApiOperation(value = "通过货源IDS查询绑定的订单列表", tags = {"司机货源"}, notes = "通过货源IDS查询货源列表")
    @PostMapping("/getBindSupplyListByIds")
    ResultMode<DriverGoods> getBindSupplyListByIds(@RequestBody @ApiParam PlatformQrCodeFilter pageInfo);*/

    /**
     * 新增司机货源
     *
     * @param driverGoods 司机货源
     * @return 结果
     */
    @PostMapping(value = "/save")
    @ApiOperation(value = "新增")
    ResultMode save(@RequestBody DriverGoods driverGoods)  throws Exception;

    @ApiOperation(value = "计划单转换货源", tags = {"计划单转换货源"}, notes = "计划单转换货源")
    @RequestMapping(value = "/planTransferToGoods", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode planTransferToGoods(@RequestBody @ApiParam PlanTransferToDriverGoodsVo vo);

    @ApiOperation(value = "报价计划单转换货源", tags = {"报价计划单转换货源"}, notes = "计划单转换货源")
    @RequestMapping(value = "/offerPlanTransferToGoods", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode offerPlanTransferToGoods(@RequestBody @ApiParam PlanTransferToDriverGoodsVo vo);

    /**
     * 修改司机货源
     *
     * @param driverGoods 司机货源
     * @return 结果
     */
    @PostMapping(value = "/updateById")
    @ApiOperation(value = "更新")
    ResultMode updateById(@RequestBody DriverGoods driverGoods);

    /**
     * 批量删除司机货源
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @PostMapping(value = "/removeByIds")
    @ApiOperation(value = "批量删除")
    ResultMode removeByIds(String[] ids);

    /**
     * 删除司机货源信息
     *
     * @param id 司机货源ID
     * @return 结果
     */
    @PostMapping(value = "/removeById")
    @ApiOperation(value = "删除")
    ResultMode removeById(String id);

    /**
     * 删除司机货源信息feign接口
     *
     * @param id 司机货源ID
     * @return 结果
     */
    @PostMapping(value = "/removeByIdFeign/{id}")
    @ApiOperation(value = "删除")
    ResultMode removeByIdFeign(@PathVariable("id") String id);

    /**
     * 分页查询司机货源列表
     *
     * @param pageinfo 司机货源
     * @return 司机货源集合
     */
    @PostMapping(value = "/page")
    @ApiOperation(value = "分页查询")
    ResultMode<DriverGoods> page(@RequestBody PagingInfo<DriverGoodsFilter> pageinfo);

    /**
     * 货主移动端-分页查询司机货源列表
     *
     * @param pageinfo 司机货源
     * <AUTHOR>
     * @return 司机货源集合
     */
    @PostMapping(value = "/pageShipper")
    @ApiOperation(value = "货主移动端-分页查询司机货源列表")
    ResultMode<DriverGoods> pageShipper(@RequestBody PagingInfo<DriverGoodsFilter> pageinfo);


    /**
     * 分页查询司机货源列表
     *
     * @param pageinfo 司机货源
     * @return 司机货源集合
     */
    @PostMapping(value = "/pageBindQr")
    @ApiOperation(value = "分页查询")
    ResultMode<DriverGoods> pageBindQr(@RequestBody PagingInfo<DriverGoodsFilter> pageinfo);



    /**
     * 导出司机货源列表信息
     *
     * @param driverGoodsFilter 查询条件
     * <AUTHOR>
     */
    @ApiOperation(value = "导出司机货源列表信息")
    @RequestMapping(value = "/exportDriverGoods", method = RequestMethod.POST)
    void exportDriverGoods(@RequestBody @ApiParam DriverGoodsFilter driverGoodsFilter) throws IOException;

    /**
     * 导出司机货源运单信息
     *
     * @param driverGoodsFilter 查询条件
     * <AUTHOR>
     */
    @ApiOperation(value = "导出司机货源运单信息")
    @RequestMapping(value = "/exportWayBill", method = RequestMethod.POST)
    void exportWayBill(@RequestBody @ApiParam DriverGoodsFilter driverGoodsFilter) throws IOException;

    /**
     * @return
     * <AUTHOR>
     * @Description 关闭货源
     * @Date 2021/6/17
     * @Param
     **/
    @RequestMapping(value = "/closeDriverGoods", method = RequestMethod.POST)
    @ApiOperation(value = "关闭货源")
    ResultMode closeDriverGoods(@RequestBody @ApiParam DriverGoodsFilter driverGoodsFilter);


    /**
     * 根据货源id查询
     *
     * @param driverGoodsFilter 司机货源ID
     * @return 司机货源
     */
    @PostMapping("/getByGoodsId")
    @ApiOperation(value = "根据货源id查询")
    ResultMode<DriverGoods> getByGoodsId(@RequestBody DriverGoodsFilter driverGoodsFilter);


    @ApiOperation(value = "通过货源ID获取客户信息", tags = {"货源报价"}, notes = "通过货源ID获取客户信息")
    @RequestMapping(value = "/getDriverGoodsListByGoodsIdList", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    List<DriverGoods> getDriverGoodsListByGoodsIdList(@RequestBody List<String> goodsIdList);


    @PostMapping("/getGoodsBasePriceById")
    ResultMode<DriverGoods>  getGoodsBasePriceById(@RequestParam String goodsId);


    @ApiOperation(value = "常用线路的运输里程和耗时为空数据修复", tags = {"常用线路的运输里程和耗时为空数据修复"}, notes = "常用线路的运输里程和耗时为空数据修复")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "model", value = "常用线路的运输里程和耗时为空数据修复", required = true, paramType = "body")
    })
    @RequestMapping(value = "/commonLineTransportmileageRepair", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
     ResultMode<String> commonLineTransportmileageRepair();


    /**
     * 通过业务id获取金额取整方式
     *
     * @param busiIdList
     * @return {@link ResultMode}<{@link Integer}>
     */
    @RequestMapping(value = "/getRoundingMode", method = RequestMethod.POST)
    ResultMode<Map<String,Integer>> getRoundingMode(@RequestBody List<String> busiIdList);

    /**
     * 根据货源编号  司机货源信息表主键删除记录
     * 创建者：柳鹏
     * 创建时间：2019/11/15 14:56:33
     *
     * @param goodsId 货源编号
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "删除司机货源", tags = {"司机货源管理操作接口"}, notes = "删除司机货源")
    @RequestMapping(value = "/driverGoodsDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> driverGoodsDel(@RequestParam String goodsId);

    /**
     * 推荐运力列表
     * @param driverGoodsHotCar
     * @return
     */
    @RequestMapping(value = "/queryHotCar", method = RequestMethod.POST)
    ResultMode<DriverGoodsHotCar> queryHotCar(@RequestBody DriverGoodsHotCar driverGoodsHotCar);

    @ApiOperation("4pl数据统计")
    @PostMapping("/platformStatistics")
    ResultMode<GoodsStatisticsDTO> platformStatistics(@RequestBody DriverGoodsFilter filter);

    /**
     * 查询司机货源扩展信息
     */
    @PostMapping("/getDriverGoodsExtendInfo")
    @ApiOperation(value = "根据货源id查询司机货源扩展信息")
    ResultMode<DriverGoodsExtendDTO> getDriverGoodsExtendInfo(@RequestBody @Valid DriverGoodsExtendQuery query);
}
