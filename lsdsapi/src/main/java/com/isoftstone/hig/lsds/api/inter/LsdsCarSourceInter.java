
package com.isoftstone.hig.lsds.api.inter;

import com.isoftstone.hig.lsds.api.entity.LsdsCarSource;
import com.isoftstone.hig.lsds.api.filter.LsdsCarSourceFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsCarSourceVo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 创建描述：车源 控制器 类的后面Controller其实可以不用，只是为了容易区分
 *
 * <AUTHOR>
 * 创建时间 2019/11/15 14:56:33
 */
@Api(value = "车源", tags = {"车源"})
@FeignClient(name = "lsds", path = "/lsds/LsdsCarSource")
public interface LsdsCarSourceInter {
    /**
     * 根据实体车源 添加记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     * @param lsdsCarSourceVo 车源 实体类
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "增加车源 ", tags = {"车源"}, notes = "增加车源")
    @RequestMapping(value = "/lsdsCarSourceAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsCarSourceAdd(@RequestBody @ApiParam LsdsCarSourceVo lsdsCarSourceVo);

    /**
     * 根据车源 主键删除记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     * @param carSourceId 车源 主键
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "删除车源 ", tags = {"车源"}, notes = "根据主键删除车源 ")
    @RequestMapping(value = "/lsdsCarSourceDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsCarSourceDel(@RequestParam("carSourceId") String carSourceId);

    /**
     * 根据车源 实体修改记录，内部以ID主键修改
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     * @param lsdsCarSource 车源 实体类
     * @return {@code ResultMode<String>}
     */
    @ApiOperation(value = "更新车源 ", tags = {"车源"}, notes = "根据主键更新车源 ")
    @RequestMapping(value = "/lsdsCarSourceUpdate", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<String> lsdsCarSourceUpdate(@RequestBody @ApiParam LsdsCarSource lsdsCarSource);

    /**
     * 车源 分页查询
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsCarSource>}
     */
    @ApiOperation(value = "分页查询车源 ", tags = {"车源"}, notes = "分页查询车源 ")
    @RequestMapping(value = "/lsdsCarSourcePaging", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsCarSource> lsdsCarSourcePaging(@RequestBody @ApiParam PagingInfo<LsdsCarSourceFilter> pageInfo);

    /**
     * 根据车源编号获取车源详情
     * 创建者：cgb
     * 创建时间：2019/11/25
     *
     * @param carSourceId 车源编号
     * @return {@code ResultMode<LsdsCarSource>}
     */
    @ApiOperation(value = "根据车源编号获取车源详情", tags = {"车源"}, notes = "lsdsCarSource")
    @RequestMapping(value = "/lsdsCarSourceGet", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsCarSource> lsdsCarSourceGet(@RequestParam("carSourceId") String carSourceId);


    /**
     * 根据车牌号查询车源信息（批量）
     * 创建者：cgb
     * 创建时间：2019/12/19
     * @param strList
     * @return {@code  ResultMode<LsdsCarSource>}
     */
    @ApiOperation(value = "根据车牌号查询车源信息（批量） ", tags = {"车源"}, notes = "根据车牌号查询车源信息（批量）")
    @RequestMapping(value = "/getCarSourceListByNum", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsCarSource> getCarSourceListByNum(@RequestBody List<String> strList);


    /**
     * 查询车源信息
     * 创建者: cgb
     * 创建时间: 2019/12/21
     * @return {@code  ResultMode<LsdsCarSource>}
     */
    @ApiOperation(value = "查询车源信息", tags = {"车源"}, notes = "查询车源信息")
    @RequestMapping(value = "/getCarSourceListForHomePage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsCarSource> getCarSourceListForHomePage();


    /**
     * 车源信息分页查询
     * 创建者：cgb
     * 创建时间：2019/12/21
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @ApiOperation(value = "车源信息分页查询", tags = {"车源"}, notes = "车源信息分页查询")
    @RequestMapping(value = "/getCarSourceListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    ResultMode<LsdsCarSource> getCarSourceListPage(@RequestBody @ApiParam PagingInfo<LsdsCarSourceFilter> pageInfo);

    /**
     * <AUTHOR>
     * @since 2021-2-4
     * 物流信息车源列表查询
     * @param lsdsCarSourceFilterPagingInfo
     * @return
     */
    @ApiOperation(value = "物流信息车源列表查询", tags = {"车源"}, notes = "物流信息车源列表查询")
    @RequestMapping(value = "/findLsdsCarSource",produces ={"application/json;charset=UTF-8"},method = RequestMethod.POST)
    ResultMode<LsdsCarSource> findLsdsCarSource(@RequestBody @Autowired PagingInfo<LsdsCarSourceFilter> lsdsCarSourceFilterPagingInfo);

}
