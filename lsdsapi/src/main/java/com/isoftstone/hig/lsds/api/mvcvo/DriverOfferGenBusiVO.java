package com.isoftstone.hig.lsds.api.mvcvo;

import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.DriverOffer;
import com.wanlianyida.framework.lgicommon.entity.TokenInfo;
import lombok.Data;

/**
 * 报价单和货源处理VO
 *
 * <AUTHOR>
 */
@Data
public class DriverOfferGenBusiVO {
    /**
     * 报价单
     */
    private DriverOffer driverOffer;
    /**
     * 货源单
     */
    private DriverGoods goods;
    /**
     * token
     */
    private TokenInfo tokenInfo;

    public DriverOfferGenBusiVO(DriverOffer driverOffer, DriverGoods goods, TokenInfo tokenInfo) {
        this.driverOffer = driverOffer;
        this.goods = goods;
        this.tokenInfo = tokenInfo;
    }
}


