package com.wanlianyida.lsds.domain.service;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.isoftstone.hig.common.model.PagingProceduresInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.LogHelper;
import com.isoftstone.hig.common.utils.SQLUtils;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsAssignSupplier;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsAssignSupplierFilter;
import com.wanlianyida.lsds.infrastructure.repository.mapper.GoodsAssignSupplierMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 服务公共类主要实现业务规则，处理业务逻辑 可以再进行创建多个，这主要为了方便一起生成
 *
 * <AUTHOR>
 */
@Service
public class LsdsGoodsAssignSupplierDomainService {

    /**
     * 分页获取货物指定物流供应商记录ID 信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @param token    预留要传递的token，服务端进行判断权限功能
     * @return {@code  ResultMode<LsdsGoodsAssignSupplier>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货物指定物流供应商记录ID 实体类LsdsGoodsAssignSupplier列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsGoodsAssignSupplier> pagingLsdsGoodsAssignSupplier(PagingInfo<LsdsGoodsAssignSupplierFilter> pageInfo, String token) {

        ResultMode<LsdsGoodsAssignSupplier> returnmodel = new ResultMode<LsdsGoodsAssignSupplier>();
        try {
            //过滤SQL参数
            SQLUtils.filterParam(pageInfo.filterModel);

            GoodsAssignSupplierMapper dao = SpringContextUtil.getBeanByClass(GoodsAssignSupplierMapper.class);
            PagingProceduresInfo filter = new PagingProceduresInfo();
            filter.setTables("lsds_goods_assign_supplier");
            filter.setFields("goods_assign_supplier_id,goods_id,company_id,company_name,company_code,company_linker,company_phone_number,sort_node,create_by,create_date,modify_by,modify_date,item1,item2,item3,item4");
            filter.setFilter("");
            filter.setCountTotal(pageInfo.getCountTotal());
            filter.setPageNumber(pageInfo.currentPage);
            filter.setPageSize(pageInfo.pageLength);
            StringBuilder sbwhere = new StringBuilder(" 1 = 1 ");
            LsdsGoodsAssignSupplierFilter queryModel = pageInfo.filterModel;
            if (StringUtils.isNotEmpty(queryModel.getGoodsAssignSupplierId())) {
                sbwhere.append(" and goods_assign_supplier_id like '%" + queryModel.getGoodsAssignSupplierId() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getGoodsId())) {
                sbwhere.append(" and goods_id like '%" + queryModel.getGoodsId() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getCompanyId())) {
                sbwhere.append(" and company_id like '%" + queryModel.getCompanyId() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getCompanyName())) {
                sbwhere.append(" and company_name like '%" + queryModel.getCompanyName() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getCompanyCode())) {
                sbwhere.append(" and company_code like '%" + queryModel.getCompanyCode() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getCompanyLinker())) {
                sbwhere.append(" and company_linker like '%" + queryModel.getCompanyLinker() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getCompanyPhoneNumber())) {
                sbwhere.append(" and company_phone_number like '%" + queryModel.getCompanyPhoneNumber() + "%'");
            }
            if (queryModel.getSortNode() != 0) {
                sbwhere.append(" and sort_node =" + queryModel.getSortNode());
            }
            if (StringUtils.isNotEmpty(queryModel.getCreateBy())) {
                sbwhere.append(" and create_by like '%" + queryModel.getCreateBy() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getModifyBy())) {
                sbwhere.append(" and modify_by like '%" + queryModel.getModifyBy() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getItem1())) {
                sbwhere.append(" and item1 like '%" + queryModel.getItem1() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getItem2())) {
                sbwhere.append(" and item2 like '%" + queryModel.getItem2() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getItem3())) {
                sbwhere.append(" and item3 like '%" + queryModel.getItem3() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getItem4())) {
                sbwhere.append(" and item4 like '%" + queryModel.getItem4() + "%'");
            }
            filter.setFilter(sbwhere.toString());
            filter.setPk("goods_assign_supplier_id");
            List<LsdsGoodsAssignSupplier> list = dao.pagingLsdsGoodsAssignSupplier(filter);
            returnmodel.setTotal(filter.getTotal());
            returnmodel.setModel(list);

        } catch (Exception ex) {
            String errMsg = "分页获取货物指定物流供应商记录ID 信息异常:" + ex.getMessage();
            returnmodel.setErrMsg(errMsg);
            LogHelper.writeError(errMsg, ex);
        }
        return returnmodel;
    }
}
