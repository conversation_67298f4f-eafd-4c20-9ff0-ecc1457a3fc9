package com.wanlianyida.lsds.domain.service.publish.paramverif;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.platform.api.entity.PlatformCmSensitiveWord;
import com.isoftstone.hig.platform.api.filter.PlatformCmSensitiveWordFilter;
import com.wanlianyida.hig.rms.api.common.IdUtils;
import com.wanlianyida.hig.rms.api.enums.RmsEnum;
import com.wanlianyida.hig.rms.api.po.DriverGoodsPublishRuleParma;
import com.wanlianyida.hig.rms.api.po.platform.HazardousChemicalVO;
import com.wanlianyida.lsds.application.model.command.publish.LossDeductibleCommand;
import com.wanlianyida.lsds.application.model.command.publish.PublishGoodsSourceCommand;
import com.wanlianyida.lsds.infrastructure.enums.GoodsBizModelTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsPublishTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsReleaseTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsSourceEntryTypeEnum;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.RmsExchangeService;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 通用参数验证
 */
@Slf4j
@Service
public abstract class CommonParamVerif {

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private RmsExchangeService rmsExchangeService;

    /**
     * 司机货源和企业货源公共参数校验
     */
    public ResultMode checkCommonParam(PublishGoodsSourceCommand command) {
        //1、字段合法性校验
        ResultMode checkResult = checkFieldValidity(command);
        if (!checkResult.isSucceed()) {
            return checkResult;
        }

        //2、敏感词过滤
        ResultMode filterResultMode = filterSensitiveWord(command);
        if (!filterResultMode.getSucceed()) {
            return filterResultMode;
        }

        //3、风控校验网络货运货物是否危化品
        String errTipMsg = hazardousChemicalsCalculate(command);
        if (StrUtil.isNotBlank(errTipMsg)) {
            return ResultMode.fail(errTipMsg);
        }

        //4、亏涨吨校验
        LossDeductibleCommand lossDeductible = command.getLossDeductible();
        if (ObjectUtil.isEmpty(lossDeductible)) {
            return ResultMode.fail("请输入亏吨免赔参数", CommonStatusCodeEnum.USER_ERROR_PARAM_NOTNULL.getCode());
        }
        if (lossDeductible.getDeductibleType() != 0 && lossDeductible.getDeductibleValue() == null) {
            return ResultMode.fail("请输入亏吨免赔参数", CommonStatusCodeEnum.USER_ERROR_PARAM_NOTNULL.getCode());
        }

        return ResultMode.success();
    }

    /**
     * 字段合法性校验
     */
    private ResultMode checkFieldValidity(PublishGoodsSourceCommand command) {
        //枚举合法性
        if (!GoodsPublishTypeEnum.isTypeExist(command.getPublisherType())) {
            return ResultMode.fail("发布主体类型参数错误");
        }
        if (!GoodsBizModelTypeEnum.isTypeExist(command.getBizModelType())) {
            return ResultMode.fail("业务模式类型参数错误");
        }
        if (!GoodsReleaseTypeEnum.isTypeExist(command.getReleaseType())) {
            return ResultMode.fail("发布类型参数错误");
        }
        if (!GoodsSourceEntryTypeEnum.isTypeExist(command.getSourceEntryType())) {
            return ResultMode.fail("来源入口类型参数错误");
        }

        return ResultMode.success();
    }

    /**
     * 过滤敏感词
     */
    public ResultMode<String> filterSensitiveWord(PublishGoodsSourceCommand command) {
        ResultMode<String> resultModel = new ResultMode<String>();
        try {
            resultModel.setSucceed(true);
            Map<String, String> map = new HashMap<String, String>();
            map.put("出发地简称", command.getGoodsLine().getSendAddrShortName());
            map.put("出发地联系人", command.getGoodsLine().getSendLinker());
            map.put("出发地详细地址 ", command.getGoodsLine().getSendAddrDetail());
            map.put("目的地简称", command.getGoodsLine().getReceiveAddrShortName());
            map.put("目的地联系人", command.getGoodsLine().getReceiveLinker());
            map.put("目的详细地址 ", command.getGoodsLine().getReceiveAddrDetail());
            map.put("销售合同号", command.getSalesContractNumber());
            map.put("货物名称", command.getGoodsName());
            map.put("货物描述", command.getGoodsDesc());
            map.put("运输要求", command.getOtherRemark());
            if (ObjectUtil.isNotNull(command.getEnterpriseGoods()) && CollUtil.isNotEmpty(command.getEnterpriseGoods().getGoodsSplitList())) {
                command.getEnterpriseGoods().getGoodsSplitList().stream().forEach(e -> {
                    map.put(e.getSortNode() + "-运输拆分-出发地简称", e.getSendAddrShortName());
                    map.put(e.getSortNode() + "-运输拆分-出发地详细地址 ", e.getSendAddrDetail());
                    map.put(e.getSortNode() + "-运输拆分-目的地简称", e.getReceiveAddrShortName());
                    map.put(e.getSortNode() + "-运输拆分-目的详细地址 ", e.getReceiveAddrDetail());
                });
            }
            Map<String, String> returnMap = sensitiveWordValidMatch(map);
            if (!MapUtils.isEmpty(returnMap)) {
                resultModel.setErrMsg("敏感词校验不通过:" + returnMap);
                resultModel.getModel().add("敏感词校验不通过:" + returnMap);
                resultModel.setErrCode(CommonStatusCodeEnum.BUSS_ERROR_BCOM0500.getCode());
                resultModel.setSucceed(false);
            }
            return resultModel;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultModel;
    }

    public Map<String, String> sensitiveWordValidMatch(Map<String, String> words) {
        Map<String, String> resultMap = new HashMap<>();
        PagingInfo<PlatformCmSensitiveWordFilter> pageInfo = new PagingInfo<>();
        pageInfo.currentPage = 1;
        pageInfo.pageLength = 999999999;
        pageInfo.filterModel = new PlatformCmSensitiveWordFilter();

        List<PlatformCmSensitiveWord> res = platformExchangeService.getAllPlatformCmSensitiveWord();
        if (!CollectionUtils.isEmpty(words) && !CollectionUtils.isEmpty(res)) {
            for (Map.Entry<String, String> entry : words.entrySet()) {
                String sensitiveWord = entry.getValue();
                if (!org.springframework.util.StringUtils.isEmpty(sensitiveWord)) {
                    res.forEach(item -> {
                        if (sensitiveWord.equals(item.getSenWord())) {
                            resultMap.put(entry.getKey(), entry.getValue());
                            return;
                        }
                    });
                }
            }
        }
        return resultMap;
    }

    /**
     * 风控校验网络货运货物是否危化品
     */
    private String hazardousChemicalsCalculate(PublishGoodsSourceCommand command) {
        try {
            HazardousChemicalVO hazardousChemicalVO = new HazardousChemicalVO();
            hazardousChemicalVO.setGoodsName(command.getGoodsName());
            hazardousChemicalVO.setGoodsDesc(command.getGoodsDesc());
            hazardousChemicalVO.setFreightType(command.getFreightType());
            DriverGoodsPublishRuleParma ruleParma = new DriverGoodsPublishRuleParma();
            ruleParma.setBussSceneType(RmsEnum.BussItemEnum.LSDS_DRIVER_GOODS_PUBLISH.getCode());
            ruleParma.setRiskSceneType(RmsEnum.RiskItemEnum.PLATFORM_HAZARDOUS_CHEMICALS.getCode() + "|");
            ruleParma.setBizId(IdUtils.generateShardingId() + StrUtil.toString(IdUtil.generateId()));
            ruleParma.setBussId(JwtUtil.getInstance().getUserBaseIdByToken());
            ruleParma.setHazardousChemicalVO(hazardousChemicalVO);
            // 调风控接口
            return rmsExchangeService.driverGoodsPublishCalculate(ruleParma);
        } catch (Exception e) {
            log.error("校验异常：", e);
            return "网络货运货物危化品校验异常";
        }
    }

}
