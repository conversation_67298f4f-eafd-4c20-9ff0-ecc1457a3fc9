package com.wanlianyida.lsds.domain.service.audit;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.util.StringUtil;
import com.isoftstone.hig.annotations.LogPrintPoint;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.DateUtils;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.lsds.api.entity.*;
import com.isoftstone.hig.lsds.api.enums.BidOpeningStatusEnum;
import com.isoftstone.hig.lsds.api.enums.GoodsSourceTypeEnum;
import com.isoftstone.hig.lsds.api.enums.OfferStatusEnum;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.platform.api.entity.PlatformUmCompany;
import com.isoftstone.hig.platform.api.entity.PlatformUmCompanyMain;
import com.isoftstone.hig.platform.api.entity.PlatformUmLogininfo;
import com.wanlianyida.lsds.domain.assembler.GoodsSourceOperatorAssembler;
import com.wanlianyida.lsds.domain.model.bo.audit.GoodsSourceAuditBO;
import com.wanlianyida.lsds.domain.model.bo.audit.GoodsSourceAuditSaveBO;
import com.wanlianyida.lsds.domain.model.bo.audit.LsdsGoodsAuditBO;
import com.wanlianyida.lsds.domain.model.entity.GoodsSourceEntity;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.domain.service.BizDomainService;
import com.wanlianyida.lsds.domain.service.LsdsGoodsRuleDomainService;
import com.wanlianyida.lsds.infrastructure.enums.DealStatusEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsAuditTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.StatusCodeEnum;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.exchange.AmountRoundingModeExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import com.wanlianyida.lsds.infrastructure.util.MessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 企业货源
 */
@Slf4j
@Service("LsdsGoodsAudit")
public class LsdsGoodsAuditDomainService extends BizDomainService<GoodsSourceAuditBO, GoodsSourceAuditSaveBO,String> {

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private AmountRoundingModeExchangeService amountRoundingModeService;

    @Resource
    private LsdsKafkaSender lsdsKafkaSender;

    @Resource
    private MessageUtils messageUtils;

    @Resource
    private TaskExecutor asynTaskExecutor;

    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;

    @Resource
    private LsdsGoodsRuleDomainService lsdsGoodsRuleDomainService;


    @Override
    public ResultMode checkParam(GoodsSourceAuditBO bo) {
        LsdsGoodsAuditBO lsdsGoodsAudit = bo.getLsdsGoodsAudit();
        if(ObjUtil.isNull(lsdsGoodsAudit)){
            return ResultMode.fail("企业货源审核参数为空");
        }
        LsdsGoods oldLsdsGoods = goodsOperatorRepository.selectLsdsGoodsByGoodsId(lsdsGoodsAudit.getGoodsId());
        if(ObjUtil.isNull(oldLsdsGoods)){
            return ResultMode.fail(lsdsGoodsAudit.getGoodsId()+":货源不存在");
        }
        if(StrUtil.equals(oldLsdsGoods.getDealStatus(), LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode())){
            return ResultMode.fail(lsdsGoodsAudit.getGoodsId()+":货源状态已审核");
        }

        if (LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus4.getCode().equals(lsdsGoodsAudit.getDealStatus())) {
            if (StringUtils.isEmpty(lsdsGoodsAudit.getAuditRecordStatusMark())) {
               return ResultMode.fail("审核不通过原因不能为空字符");
            }
        }
        //初始化参数
        this.initParams(lsdsGoodsAudit,oldLsdsGoods);

        ResultMode success = ResultMode.success();
        String auditType = lsdsGoodsAudit.getAuditType();
        if(StrUtil.equals(auditType,GoodsAuditTypeEnum.TYPE_10.getType())) {
            //平台企业审核通过
            boolean flag = platformExchangeService.checkPlatformIs3PL(lsdsGoodsAudit.getCompanyId());
            if(flag){
                lsdsGoodsAudit.setDealStatus(DealStatusEnum.RELEASE.getCode());
            }else {
                //非平台企业走自动审核规则校验
                Map<String,String> model = new HashMap<>();
                model.put("companyId",lsdsGoodsAudit.getCompanyId());
                model.put("mainBodyId",lsdsGoodsAudit.getNetworkMainBodyId());
                model.put("bizType",Constants.GOODS_AUTO_CHECK);
                boolean autoAudit = platformExchangeService.judgeAutoAudit(model);
                if(autoAudit){
                    //自动货源审核
                    return ResultMode.success("自动审核开启");
                }
                return ResultMode.fail("自动审核未开启");
            }
        }
        else if (StrUtil.equals(auditType,GoodsAuditTypeEnum.TYPE_20.getType())){
            //4pl审核
            //设置原先的经营主体id 为网络交易主体id
            lsdsGoodsAudit.setOperationMainBodyId(lsdsGoodsAudit.getTransactionContractingBodyId());
            success = this.checkManualAuditParam(bo);
        }else if ( StrUtil.equals(auditType,GoodsAuditTypeEnum.TYPE_30.getType())) {
            if (!platformExchangeService.isTransactionBody(lsdsGoodsAudit.getCompanyId())) {
                return ResultMode.fail("下游询价非交易主体不处理");
            }
            lsdsGoodsAudit.setDealStatus(DealStatusEnum.RELEASE.getCode());
        }
        return success;
    }

    /**
     * 出刷化参数
     * @param lsdsGoodsAudit
     * @param oldLsdsGoods
     */
    private void initParams(LsdsGoodsAuditBO lsdsGoodsAudit, LsdsGoods oldLsdsGoods) {
        lsdsGoodsAudit.setCompanyId(oldLsdsGoods.getCompanyId());
        lsdsGoodsAudit.setCompanyShortName(oldLsdsGoods.getCompanyShortName());
        lsdsGoodsAudit.setCarrierCompanyId(oldLsdsGoods.getCarrierCompanyId());
        lsdsGoodsAudit.setCarrierName(oldLsdsGoods.getCarrierName());
        lsdsGoodsAudit.setClearMonthDay(oldLsdsGoods.getClearMonthDay());
        lsdsGoodsAudit.setChargeType(oldLsdsGoods.getChargeType());

        lsdsGoodsAudit.setModifyBy(oldLsdsGoods.getCreateBy());
        lsdsGoodsAudit.setOfferPrice(oldLsdsGoods.getOfferPrice());
        lsdsGoodsAudit.setExOffEnquiryTypeBaseOpenTicket(oldLsdsGoods.getExOffEnquiryTypeBaseOpenTicket());
        lsdsGoodsAudit.setEnquiryRange(oldLsdsGoods.getEnquiryRange());
        lsdsGoodsAudit.setReleaseType(oldLsdsGoods.getReleaseType());
        lsdsGoodsAudit.setGoodsSourceType(oldLsdsGoods.getGoodsSourceType());
        lsdsGoodsAudit.setSendAddrShortName(oldLsdsGoods.getSendAddrShortName());
        lsdsGoodsAudit.setReceiveAddrShortName(oldLsdsGoods.getReceiveAddrShortName());
        lsdsGoodsAudit.setSubmitBy(oldLsdsGoods.getSubmitBy());
        lsdsGoodsAudit.setSendPhoneNumber(oldLsdsGoods.getSendPhoneNumber());
        lsdsGoodsAudit.setGoodsAddressId(oldLsdsGoods.getGoodsAddressId());
        lsdsGoodsAudit.setOfferCurrentRounds(oldLsdsGoods.getOfferCurrentRounds());

        PlatformUmLogininfo platformUmLogininfo = platformExchangeService.getLoginInfoByUserBaseId(lsdsGoodsAudit.getUserId());
        if(ObjUtil.isNotNull(platformUmLogininfo)){
            lsdsGoodsAudit.setUserName(platformUmLogininfo.getUsername());
            lsdsGoodsAudit.setUserLoginName(platformUmLogininfo.getLoginName());
        }
        // 兼容V27.4审核隐藏按xx上浮，默认为按单价上浮，值为0
        String feeClearType = lsdsGoodsAudit.getFeeClearType();
        if(StrUtil.isBlank(feeClearType)){
            lsdsGoodsAudit.setFeeClearType("2");
        }
        if(lsdsGoodsAudit.getFeeClearValue() == null){
            lsdsGoodsAudit.setFeeClearValue(BigDecimal.ZERO);
        }

        lsdsGoodsAudit.setOldLsdsGoods(oldLsdsGoods);
    }


    @LogPrintPoint
    @Override
    public GoodsSourceAuditSaveBO dataAssembly(GoodsSourceAuditBO bo) {
        LsdsGoodsAuditBO lsdsGoodsAudit = bo.getLsdsGoodsAudit();

        String auditType = lsdsGoodsAudit.getAuditType();

        List<String> errors = new ArrayList<>();
        if(StrUtil.equals(auditType,GoodsAuditTypeEnum.TYPE_10.getType())) {
            ResultMode resultMode = this.processAutoAudit(bo, errors);
            if(!resultMode.isSucceed()){
                String errMsg = resultMode.getErrMsg();
                errors.add(errMsg);
            }
        }

        //审核记录状态【1-审核通过,2-审核不通过】
        String dealStatus = lsdsGoodsAudit.getDealStatus();

        List<LsdsGoodsOffer> lsdsGoodsOffers = null;
        if(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus)){
            lsdsGoodsOffers = this.processAutoAuditData(lsdsGoodsAudit);
        }else if(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus4.getCode().equals(dealStatus)){
            lsdsGoodsAudit.setRecordStatus(LsdsEnum.LsdsgoodsRecordStatusEnum.recordStatus2.getCode());
        }
        //操作日志
        LsdsGoodsRecord lsdsGoodsRecord = GoodsSourceOperatorAssembler.buildDriverGoodsRecord(lsdsGoodsAudit,errors);
        //是否是平台企业
        boolean platform = false;
        GoodsSourceEntity goodsSourceEntity = null;
        LsdsGoods updateLsdsGoods = null;
        DriverGoodsExtend upDriverGoodsExtend = null;
        if(StrUtil.equalsAny(dealStatus,LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode(),
            LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus4.getCode())){
            //是否是平台企业
            platform = isPlatform(lsdsGoodsAudit);
            //set 默认值
            this.setDefaultData(lsdsGoodsAudit);

            goodsSourceEntity = GoodsSourceOperatorAssembler.buildLsdsGoodsSourceEntity2(lsdsGoodsAudit);
            updateLsdsGoods = GoodsSourceOperatorAssembler.buildUpdateLsdsGoods(lsdsGoodsAudit);
            upDriverGoodsExtend = GoodsSourceOperatorAssembler.buildUpdateDriverGoodsExtend(lsdsGoodsAudit);
        }
        return GoodsSourceOperatorAssembler.buildGoodsSourceAuditSaveBO2(updateLsdsGoods,goodsSourceEntity,lsdsGoodsOffers,lsdsGoodsRecord,platform,upDriverGoodsExtend);
    }

    /**
     * 是否是平台企业
     * @param lsdsGoodsAudit
     * @return
     */
    private boolean isPlatform(LsdsGoodsAuditBO lsdsGoodsAudit) {
        PlatformUmCompany platformUmCompany = platformExchangeService.getCompanyByModel(lsdsGoodsAudit.getCompanyId());
        if(ObjUtil.isNotNull(platformUmCompany) && "1".equals(platformUmCompany.getPlatformFlag())){
            return true;
        }
        return false;
    }

    /**
     * 处理审核通过
     * @param lsdsGoodsAudit
     * @return
     */
    private List<LsdsGoodsOffer> processAutoAuditData(LsdsGoodsAuditBO lsdsGoodsAudit) {

        lsdsGoodsAudit.setRecordStatus(LsdsEnum.LsdsgoodsRecordStatusEnum.recordStatus1.getCode());
        lsdsGoodsAudit.setCurrentOfferStartDate(DateUtils.getDateTime());

        //金额取整
        LsdsGoods lsdsGoodsQuery = LsdsGoods.of();
        lsdsGoodsQuery.setGoodsId(lsdsGoodsAudit.getGoodsId());
        lsdsGoodsQuery.setExOffEnquiryTypeBaseOpenTicket(lsdsGoodsAudit.getExOffEnquiryTypeBaseOpenTicket());
        lsdsGoodsQuery.setOfferPrice(lsdsGoodsAudit.getOfferPrice());
        lsdsGoodsQuery.setEnquiryTypeBasePrice(lsdsGoodsAudit.getEnquiryTypeBasePrice());
        lsdsGoodsQuery.setEnquiryTypeBaseOpenTicket(lsdsGoodsAudit.getEnquiryTypeBaseOpenTicket());
        amountRoundingModeService.setLsdsGoodsScale(lsdsGoodsQuery);

        BigDecimal exOffEnquiryTypeBaseOpenTicket = lsdsGoodsQuery.getExOffEnquiryTypeBaseOpenTicket();
        BigDecimal enquiryTypeBasePrice = lsdsGoodsQuery.getEnquiryTypeBasePrice();
        BigDecimal enquiryTypeBaseOpenTicket = lsdsGoodsQuery.getEnquiryTypeBaseOpenTicket();
        BigDecimal offerPrice = lsdsGoodsQuery.getOfferPrice();
        lsdsGoodsAudit.setExOffEnquiryTypeBaseOpenTicket(exOffEnquiryTypeBaseOpenTicket);
        lsdsGoodsAudit.setOfferPrice(offerPrice);
        lsdsGoodsAudit.setEnquiryTypeBasePrice(enquiryTypeBasePrice);
        lsdsGoodsAudit.setEnquiryTypeBaseOpenTicket(enquiryTypeBaseOpenTicket);

        LsdsGoodsAssignSupplier supplier = new LsdsGoodsAssignSupplier();
        supplier.setGoodsId(lsdsGoodsAudit.getGoodsId());
        supplier.setOfferRound(lsdsGoodsAudit.getOfferCurrentRounds());
        List<LsdsGoodsAssignSupplier> supplierList = goodsOperatorRepository.getPreviousSupplierList(supplier);
        return generateSupplierOffer(lsdsGoodsAudit, supplierList);

    }

    /**
     * 复制默认值
     * @param lsdsGoodsAudit
     */
    private void setDefaultData(LsdsGoodsAuditBO lsdsGoodsAudit) {
        lsdsGoodsAudit.setModifyBy(lsdsGoodsAudit.getUserId());
        lsdsGoodsAudit.setModifyDate(DateUtils.getDateTime());
        //设置审核时间
        lsdsGoodsAudit.setAuditDate(new Date());
    }


    @Override
    public void saveRep(GoodsSourceAuditSaveBO bo) {
        goodsOperatorRepository.handleGoodsAudit(bo);
    }

    @Override
    public void asyOper(GoodsSourceAuditBO paramBo,GoodsSourceAuditSaveBO bo) {
        LsdsGoodsAuditBO lsdsGoodsAudit = paramBo.getLsdsGoodsAudit();
        asynTaskExecutor.execute(()-> {
            this.sendMQ(lsdsGoodsAudit);
        });

        asynTaskExecutor.execute(()->{
            this.sendMsg(lsdsGoodsAudit,bo);
        });


    }

    /**
     * 发生消息
     * @param lsdsGoodsAudit
     * @param bo
     */
    private void sendMsg(LsdsGoodsAuditBO lsdsGoodsAudit, GoodsSourceAuditSaveBO bo) {
        String dealStatus = lsdsGoodsAudit.getDealStatus();
        Integer releaseType = lsdsGoodsAudit.getReleaseType();
        List<LsdsGoodsAssignSupplier> supplierList = bo.getSupplierList();
        boolean platform = bo.isPlatform();
        if((LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus) && !(platform && ( "3".equals(releaseType) || "1".equals(releaseType) ) ))
            || LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus4.getCode().equals(dealStatus)){

            Map<String, String> param = new HashMap();
            String phoneNumber = lsdsGoodsAudit.getSendPhoneNumber();
            String userId = lsdsGoodsAudit.getSubmitBy();
            String companyId = lsdsGoodsAudit.getCompanyId();
            String templateId = "";
            if(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus)){
                templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_AUDIT_PASS.getCode();
            }else{
                templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_AUDIT_NO_PASS.getCode();
            }
            param.put("goodsNo",lsdsGoodsAudit.getGoodsId());
            param.put("sendShortName",lsdsGoodsAudit.getSendAddrShortName());
            param.put("receiveShortName",lsdsGoodsAudit.getReceiveAddrShortName());
            //发送站内信
            String finalTemplateId = templateId;
            messageUtils.sendMsg(phoneNumber, userId, companyId, finalTemplateId, param);
        }
        //货源审核通过，指定了承运商的发送站内信给相关承运商
        if(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus)){
            if (null != supplierList && supplierList.size() > 0) {
                for(LsdsGoodsAssignSupplier las: supplierList){
                    Map<String, String> param = new HashMap<String, String>();
                    String phoneNumber = las.getCompanyPhoneNumber();
                    String companyId = las.getCompanyId();
                    String templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_ASSIGN_SUPPLIER.getCode();
                    param.put("goodsNo",lsdsGoodsAudit.getGoodsId());
                    //发送站内信
                    messageUtils.sendMsg(phoneNumber,"",companyId,templateId,param);
                }
            }
        }
    }

    /**
     * 发送消息 mq
     * @param lsdsGoodsAudit
     */
    private void sendMQ(LsdsGoodsAuditBO lsdsGoodsAudit) {
        //广播通知货源状态变更
//        lsdsGoodsAudit.setDealStatus(lsdsGoodsAudit.getOldLsdsGoods().getDealStatus());
        GoodsKafkaNotice goodsKafkaNotice = BeanUtil.copyProperties(lsdsGoodsAudit, GoodsKafkaNotice.class);
        log.info("lsdsGoodsAudit#消息发送：{}",JSONUtil.toJsonStr(goodsKafkaNotice));
        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD,JSONUtil.toJsonStr(goodsKafkaNotice));
    }


    /**
     * 生成报价数据
     * @param lsdsGoodsAudit
     * @param supplierList
     * @return
     */
    public List<LsdsGoodsOffer> generateSupplierOffer(LsdsGoodsAuditBO lsdsGoodsAudit, List<LsdsGoodsAssignSupplier> supplierList) {
        Integer offerCurrentRounds = lsdsGoodsAudit.getOfferCurrentRounds();
        if (ObjUtil.hasNull(offerCurrentRounds) || IterUtil.isEmpty(supplierList)) {
            return null;
        }
        //第一轮、指定物流公司
        if (offerCurrentRounds == 1 &&
            !StrUtil.equals(lsdsGoodsAudit.getEnquiryRange(), LsdsEnum.LsdsgoodsEnquiryRangeEnum.enquiryrange1.getCode())) {
            return null;
        }
        List<LsdsGoodsOffer> offerList = new ArrayList<>();
        //查询报价人是否是交易签约主体
        List<String> companyIdList = supplierList.stream().map(LsdsGoodsAssignSupplier::getCompanyId).collect(Collectors.toList());
        Map<String, Boolean> map = platformExchangeService.getTransactionBody(companyIdList);

        String now = DateUtils.getDateTime();
        for (LsdsGoodsAssignSupplier supplier : supplierList) {
            LsdsGoodsOffer offer = new LsdsGoodsOffer();
            String offerId = String.valueOf( IdUtil.generateId());
            offer.setOfferId(offerId);
            offer.setGoodsAddressId(lsdsGoodsAudit.getGoodsAddressId());
            offer.setOfferRound(offerCurrentRounds);
            offer.setGoodsId(lsdsGoodsAudit.getGoodsId());
            offer.setCompanyId(supplier.getCompanyId());
            offer.setCompanyShortName(supplier.getCompanyShortName());
            offer.setTransportationType(lsdsGoodsAudit.getTransportationType());
            offer.setEnquiryTypeBasePrice(lsdsGoodsAudit.getEnquiryTypeBasePrice());
            offer.setEnquiryTypeBaseTaxRate(lsdsGoodsAudit.getEnquiryTypeBaseTaxRate());
            offer.setEnquiryTypeBaseOpenTicket(lsdsGoodsAudit.getEnquiryTypeBaseOpenTicket());
            offer.setHasPlan("21");
            offer.setStatus(1);
            offer.setReleaseDate(lsdsGoodsAudit.getReleaseDate());
            offer.setArriveDate(lsdsGoodsAudit.getArriveDate());
            offer.setSortNode(1);

            offer.setCreateBy(lsdsGoodsAudit.getUserId());
            offer.setCreateDate(now);
            offer.setModifyBy(lsdsGoodsAudit.getUserId());
            offer.setModifyDate(now);

            offer.setOfferStatus(OfferStatusEnum.NONE.getCode());
            offer.setBidOpeningStatus(BidOpeningStatusEnum.TO_BE.getCode());
            if (MapUtil.isNotEmpty(map) && ObjUtil.isNotNull(map.get(supplier.getCompanyId()))
                && map.get(supplier.getCompanyId())) {
                offer.setContractFlag(1);
                //判断是向下游询价货源
                if (lsdsGoodsAudit.getReleaseType() != 2 && !StrUtil.equals(lsdsGoodsAudit.getGoodsSourceType(), GoodsSourceTypeEnum.BID_OPENING.getCode())) {
                    offer.setDownOfferFlag(1);
                } else {
                    offer.setDownOfferFlag(0);
                }
            } else {
                offer.setContractFlag(0);
                offer.setDownOfferFlag(0);
            }

            offerList.add(offer);
        }
        return offerList;

    }


    /**
     * 手动审核
     * @param bo
     * @return
     */
    private ResultMode checkManualAuditParam(GoodsSourceAuditBO bo) {
        LsdsGoodsAuditBO lsdsGoodsAudit = bo.getLsdsGoodsAudit();
        if (LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(lsdsGoodsAudit.getDealStatus())) {
            if (StrUtil.isEmpty(lsdsGoodsAudit.getOperationMainBodyId())) {
                return ResultMode.fail("经营主体ID不能为空字符");
            }
            if (StrUtil.isEmpty(lsdsGoodsAudit.getBodyName())) {
                return ResultMode.fail("经营主体名称不能为空字符");
            }
            if (StrUtil.isEmpty(lsdsGoodsAudit.getOtherClearType())) {
                return ResultMode.fail("结算方式不能为空字符");
            }
            if (!ObjUtil.isEmpty(lsdsGoodsAudit.getUnitPrice()) && (lsdsGoodsAudit.getUnitPrice()).compareTo(BigDecimal.ZERO) < 0) {
                return ResultMode.fail("单价值必须大于等于零！");
            }
        }
        if (StringUtil.isNotEmpty(lsdsGoodsAudit.getCompanyId()) && platformExchangeService.isTransactionBody(lsdsGoodsAudit.getCompanyId())
            && LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus2.getCode().equals(lsdsGoodsAudit.getDealStatus())) {
            lsdsGoodsAudit.setDealStatus(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode());
        }
        return ResultMode.success();
    }

    /**
     * 自动审核 参数校验
     * @param bo
     * @param errors
     * @return
     */
    private ResultMode processAutoAudit(GoodsSourceAuditBO bo, List<String> errors) {
        LsdsGoodsAuditBO lsdsGoodsAudit = bo.getLsdsGoodsAudit();
        if (!StrUtil.equals(DealStatusEnum.TODO_AUDIT.getCode(), lsdsGoodsAudit.getDealStatus())) {
            return ResultMode.fail("非待审核状态");
        }
        errors = lsdsGoodsRuleDomainService.check(bo.getLsdsGoodsAudit().getOldLsdsGoods());
        if(CollUtil.isNotEmpty(errors)){
            lsdsGoodsAudit.setAutoAuditRemark("货源自动审核失败");
            String errMsg = String.join(",", errors);
            log.info("checkAutoAuditParam#没满足自动审核规则校验：{}",errMsg);
            return ResultMode.fail("308", errMsg+"不满足条件,不能自动审核！！");
        }
        String companyId = lsdsGoodsAudit.getCompanyId();
        List<PlatformUmCompanyMain> platformUmCompanyMains = platformExchangeService.selectCompanyMains(companyId,"1");
        if(CollUtil.isEmpty(platformUmCompanyMains)){
            throw new LsdsWlydException("未设置默认交易签约主体!");
        }
        if(platformUmCompanyMains.size()>1){
            return ResultMode.fail(StatusCodeEnum.TRANSACTION_SIGN_NOT_ONLY.getTipMsg());
        }
        lsdsGoodsAudit.setTransactionContractingBodyId(IterUtil.getFirst(platformUmCompanyMains).getMainId());

        List<PlatformUmCompanyMain> newtCompanyMains = platformExchangeService.selectCompanyMains(companyId,"2");
        if(CollUtil.isEmpty(newtCompanyMains)){
            throw new LsdsWlydException("未查找到网络货运主体");
        }
        if(newtCompanyMains.size()>1){
            return ResultMode.fail(StatusCodeEnum.NETWORK_MAIN_BODY_NOT_ONLY.getTipMsg());
        }
        lsdsGoodsAudit.setNetworkMainBodyId(IterUtil.getFirst(newtCompanyMains).getMainId());
        lsdsGoodsAudit.setDealStatus(DealStatusEnum.RELEASE.getCode());
        lsdsGoodsAudit.setAutoAuditRemark("货源自动审核成功");
        return ResultMode.success();
    }

}
