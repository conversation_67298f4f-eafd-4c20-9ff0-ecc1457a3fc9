package com.wanlianyida.lsds.domain.service.operate;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.LsdsIdentifyCodeRel;
import com.isoftstone.hig.lsds.api.filter.IdentifyCodeRelFilter;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsAttentionCancelFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsIdentifyCodeRelVO;
import com.isoftstone.hig.qrs.api.constants.QrsEnum;
import com.isoftstone.hig.qrs.api.dto.QrsIdentifyCodeGoodsDTO;
import com.isoftstone.hig.qrs.api.vo.IdentifyCodeGoodsNotice;
import com.wanlianyida.lsds.application.service.LsdsGoodsAttentionAppService;
import com.wanlianyida.lsds.domain.model.bo.GoodsIdentifyCodeBO;
import com.wanlianyida.lsds.domain.model.bo.GoodsOperateBO;
import com.wanlianyida.lsds.domain.model.bo.GoodsUpdateBO;
import com.wanlianyida.lsds.domain.model.entity.GoodsSourceEntity;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.domain.service.LsdsIdentifyCodeRelDomainService;
import com.wanlianyida.lsds.infrastructure.enums.GoodsOperateTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.IdentityCodeOptTypeEnum;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 货源码相关操作
 */
@Slf4j
@Service
public class GoodsCodeOperateHandler implements GoodsOperateStrategy{


    @Resource
    private LsdsIdentifyCodeRelDomainService lsdsIdentifyCodeRelService;

    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;

    @Override
    public ResultMode operate(GoodsOperateBO goodsOperateBO) {
        GoodsIdentifyCodeBO goodsIdentifyCodeBO = goodsOperateBO.getGoodsIdentifyCodeBO();
        if(ObjUtil.isNull(goodsIdentifyCodeBO)){
            return ResultMode.fail("参数为空");
        }
        String type = goodsIdentifyCodeBO.getType();
        if(StrUtil.equals(type, IdentityCodeOptTypeEnum.TYPE_10.getType())){
            this.doHandleBind(goodsIdentifyCodeBO.getIdentifyCodeGoodsNotices());
        }else {
            this.doHandleUnBind(goodsIdentifyCodeBO.getQrsIdentifyCodeGoodsDTOS());
        }
        return ResultMode.success();
    }

    /**
     * 逻辑删除订单识别码关系
     * @param dtos
     */
    private void doHandleUnBind(List<QrsIdentifyCodeGoodsDTO> dtos) {

        List<String> updateGoodsSources = new ArrayList<>();
        List<LsdsIdentifyCodeRel> insertIdentifyCodeRels = new ArrayList<>();
        List<QrsIdentifyCodeGoodsDTO> collect = dtos.stream()
            .filter(item -> StrUtil.equals(QrsEnum.BizOrderTypeEnum.GOODS.getCode(), item.getBizOrderType())).collect(Collectors.toList());
        for (QrsIdentifyCodeGoodsDTO item : collect) {
            LsdsIdentifyCodeRel record = new LsdsIdentifyCodeRel();
            record.setGoodsId(item.getBizOrderNo());
            record.setIdentifyCode(item.getIdentifyCode());
            record.setIdentifyCodeName(item.getIdentifyCodeName());
            record.setIdentifyCodeType(item.getIdentifyCodeType());
            record.setCompanyId(item.getBindCompanyId());
            record.setDelFlag("21");
            insertIdentifyCodeRels.add(record);
            //自动取消货源关注
            LsdsGoodsAttentionCancelFilter attentionCancelFilter = new LsdsGoodsAttentionCancelFilter();
            attentionCancelFilter.setOperType("3");
            attentionCancelFilter.setOrderId(item.getBizOrderNo());
            attentionCancelFilter.setQrCodeId(item.getIdentifyCode());
            LsdsGoodsAttentionAppService lsdsGoodsAttentionServiceTran = SpringUtil.getBean(LsdsGoodsAttentionAppService.class);
            lsdsGoodsAttentionServiceTran.autoCancelGoodsAttention(attentionCancelFilter);
            //收款码解绑不解绑货源码
            if(StrUtil.equals(item.getIdentifyCodeType(), QrsEnum.IdentifyCodeTypeEnum.COMPANY_GOODS_CODE.getCode())){
                updateGoodsSources.add(item.getBizOrderNo());
            }
        }

        GoodsUpdateBO goodsUpdateBO = new GoodsUpdateBO();
        goodsUpdateBO.setUnbindGoodsIds(updateGoodsSources);
        goodsUpdateBO.setUpdateIdentifyCodeRels(insertIdentifyCodeRels);
        goodsOperatorRepository.updateByGoodsId(goodsUpdateBO);
    }

    /**
     * 绑定操作
     * @param dtos
     */
    private void doHandleBind(List<IdentifyCodeGoodsNotice> dtos) {

        List<GoodsSourceEntity> updateGoodsSources = new ArrayList<>();
        List<DriverGoods> updatDriverGoods = new ArrayList<>();
        List<LsdsIdentifyCodeRel> insertIdentifyCodeRels = new ArrayList<>();
        List<IdentifyCodeGoodsNotice> collect = dtos.stream()
            .filter(item -> StrUtil.equals(QrsEnum.BizOrderTypeEnum.GOODS.getCode(), item.getBizOrderType())).collect(Collectors.toList());
        for (IdentifyCodeGoodsNotice item : collect) {
            IdentifyCodeRelFilter rel = new IdentifyCodeRelFilter();
            rel.setGoodsId(item.getBizOrderNo());
            rel.setIdentifyCode(item.getIdentifyCode());
            rel.setIdentifyCodeName(item.getIdentifyCodeName());
            rel.setCompanyId(item.getBindCompanyId());
            rel.setIdentifyCodeType(item.getIdentifyCodeType());
            List<LsdsIdentifyCodeRelVO> voList = lsdsIdentifyCodeRelService.queryBindIdentifyCodeRel(rel);
            if (IterUtil.isEmpty(voList)) {
                LsdsIdentifyCodeRel codeRel = BeanUtil.copyProperties(rel, LsdsIdentifyCodeRel.class);
                codeRel.setCreatorId("system");
                codeRel.setUpdaterId("system");
                codeRel.setUserBaseId(item.getUserBaseId());
                codeRel.setPayeePhone(item.getPayeePhone());
                log.info("新增司机货源与货源码关系 入参：{}", JSONUtil.toJsonStr(rel));
                setInsertDefaultValue(codeRel);
                insertIdentifyCodeRels.add(codeRel);

                GoodsSourceEntity entity = new GoodsSourceEntity();
                entity.setGoodsId(item.getBizOrderNo());
                entity.setGoodsCodeNumber(item.getIdentifyCode());
                entity.setGoodsCodeName(item.getIdentifyCodeName());
                entity.setUpdatedDate(new Date());
                updateGoodsSources.add(entity);

                DriverGoods lsdsGoods = new DriverGoods();
                lsdsGoods.setId(item.getBizOrderNo());
                lsdsGoods.setGoodsId(item.getBizOrderNo());
                lsdsGoods.setGoodsCodeNumber(item.getIdentifyCode());
                lsdsGoods.setGoodsCodeName(item.getIdentifyCodeName());
                updatDriverGoods.add(lsdsGoods);
            }
        }
        GoodsUpdateBO goodsUpdateBO = new GoodsUpdateBO();
        goodsUpdateBO.setUpdateGoodsSourceList(updateGoodsSources);
        goodsUpdateBO.setUpdateDriverGoodsList(updatDriverGoods);
        goodsUpdateBO.setInsertIdentifyCodeRels(insertIdentifyCodeRels);
        goodsOperatorRepository.updateByGoodsId(goodsUpdateBO);
    }

    @Override
    public GoodsOperateTypeEnum getOperateType() {
        return GoodsOperateTypeEnum.IDENTIFY_CODE;
    }

    private void setInsertDefaultValue(LsdsIdentifyCodeRel record) {
        record.setId(IdUtil.generateId());
        record.setDelFlag(QrsEnum.DelFlagEnum.NORMAL.getCode());
        record.setCreatedDate(Opt.ofNullable(record.getCreatedDate()).orElseGet(DateUtil::date));
        record.setCreatorId(Opt.ofBlankAble(record.getCreatorId()).orElseGet(() -> JwtUtil.getInstance().getUserBaseIdByToken()));
        record.setUpdatedDate(Opt.ofNullable(record.getUpdatedDate()).orElseGet(DateUtil::date));
        record.setUpdaterId(Opt.ofBlankAble(record.getUpdaterId()).orElseGet(() -> JwtUtil.getInstance().getUserBaseIdByToken()));
    }
}
