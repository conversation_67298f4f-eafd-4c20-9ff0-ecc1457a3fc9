package com.wanlianyida.lsds.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.isoftstone.hig.annotations.LogPrintPoint;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.DateUtils;
import com.isoftstone.hig.lsds.api.entity.*;
import com.isoftstone.hig.lsds.api.enums.GoodsKindEnum;
import com.isoftstone.hig.lsds.api.enums.LsdsDealStatusEnum;
import com.isoftstone.hig.lsds.api.enums.OfferStatusEnum;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.platform.api.entity.PlatformCmOperationMainBody;
import com.isoftstone.hig.platform.api.entity.PlatformUmUserbaseinfo;
import com.isoftstone.hig.platform.api.enums.PlatformEnum;
import com.isoftstone.hig.platform.api.mvcvo.PlatformPricingTypeConfigVo;
import com.isoftstone.hig.tms.api.enums.PricingMethodEnum;
import com.wanlianyida.lsds.application.service.DriverOfferAppService;
import com.wanlianyida.lsds.application.service.LsdsGoodsAttentionAppService;
import com.wanlianyida.lsds.domain.assembler.GoodsSourceOperatorAssembler;
import com.wanlianyida.lsds.domain.model.bo.GoodsCreateOrderStatusChangeBO;
import com.wanlianyida.lsds.domain.model.bo.GoodsCreateOrderStatusChangeSaveBO;
import com.wanlianyida.lsds.domain.model.bo.SyncOrderToLsdsBO;
import com.wanlianyida.lsds.domain.model.entity.GoodsSourceEntity;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.infrastructure.enums.GoodsDealStatusEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsPublishTypeEnum;
import com.wanlianyida.lsds.infrastructure.exchange.AmountRoundingModeExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import com.wanlianyida.lsds.infrastructure.util.TokenInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Service
public class CreateOrderStatusDomainService extends BizDomainService<GoodsCreateOrderStatusChangeBO, GoodsCreateOrderStatusChangeSaveBO, String> {

    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private DriverOfferAppService driverOfferService;

    @Resource
    private LsdsKafkaSender lsdsKafkaSender;

    @Resource
    private LsdsGoodsAttentionAppService goodsAttentionServiceTran;

    @Resource
    private AmountRoundingModeExchangeService amountRoundingModeService;

    @Resource
    private TaskExecutor asynTaskExecutor;

    @Override
    public ResultMode<String> checkParam(GoodsCreateOrderStatusChangeBO goodsCreateOrderStatusChangeBO) {
        //参数校验
        if(ObjUtil.isNull(goodsCreateOrderStatusChangeBO)){
            return ResultMode.fail("状态更新操作参数为空");
        }
        String publisherType = goodsCreateOrderStatusChangeBO.getPublisherType();
        if(StrUtil.isEmpty(publisherType)){
            return ResultMode.fail("未知货源");
        }
        return ResultMode.success();
    }

    @LogPrintPoint
    @Override
    public GoodsCreateOrderStatusChangeSaveBO dataAssembly(GoodsCreateOrderStatusChangeBO goodsCreateOrderStatusChangeBO) {

        GoodsCreateOrderStatusChangeSaveBO saveBO = new GoodsCreateOrderStatusChangeSaveBO();
        String publisherType = goodsCreateOrderStatusChangeBO.getPublisherType();
        saveBO.setPublisherType(publisherType);
        if(StrUtil.equals(publisherType, GoodsPublishTypeEnum.COMPANY.getType())){
            this.buildGoodsStatusChangeData(goodsCreateOrderStatusChangeBO,saveBO);
        }else {
            this.buildDriverGoods(goodsCreateOrderStatusChangeBO,saveBO);
        }
        return saveBO;
    }

    @Override
    public void saveRep(GoodsCreateOrderStatusChangeSaveBO saveBO) {
        goodsOperatorRepository.handleGoodsStatusChange(saveBO);
    }

    @Override
    public void asyOper(GoodsCreateOrderStatusChangeBO paramBo,GoodsCreateOrderStatusChangeSaveBO saveBO) {

        asynTaskExecutor.execute(()->{
            this.sendDriverGoods(paramBo);
        });
        asynTaskExecutor.execute(()->{
            this.sendLsdsGoods(saveBO);
        });

    }

    /**
     * 企业货源消息处理
     * @param saveBO
     */
    private void sendLsdsGoods(GoodsCreateOrderStatusChangeSaveBO saveBO) {
        log.info("sendLsdsGoods#消息发送：{}",JSONUtil.toJsonStr(saveBO));
        if(!StrUtil.equals(saveBO.getPublisherType(),GoodsPublishTypeEnum.COMPANY.getType())){
            return;
        }
        List<LsdsGoods> updateLsdsGoodsList = saveBO.getUpdateLsdsGoodsList();
        for (LsdsGoods lsdsGoods : updateLsdsGoodsList) {
            //通知货源状态变更
            GoodsKafkaNotice goodsKafkaNotice = BeanUtil.copyProperties(lsdsGoods, GoodsKafkaNotice.class);
            String message = JSONUtil.toJsonStr(goodsKafkaNotice);
            log.info("sendLsdsGoods#消息发送:{}", message);
            lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, message);
        }
    }

    /**
     * 司机货源消息处理
     * @param paramBo
     */
    private void sendDriverGoods(GoodsCreateOrderStatusChangeBO paramBo) {
        log.info("sendDriverGoods#消息发送{}",JSONUtil.toJsonStr(paramBo));
        if(!StrUtil.equals(paramBo.getPublisherType(),GoodsPublishTypeEnum.DRIVER.getType())){
            return;
        }
        DriverOffer offer = paramBo.getDriverOffer();
        DriverGoods goods = paramBo.getGoods();
        //1、货源状态变更的异步处理
        String statusMsg = JSONUtil.toJsonStr(BeanUtil.copyProperties(goods, GoodsKafkaNotice.class));
        log.info("sendDriverGoods#货源状态变更消息发送：{}",statusMsg);
        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, statusMsg);

        //2、司机接单时,取消上次货源关注信息
        log.info("sendDriverGoods#取消上次货源关注信息:{}",JSONUtil.toJsonStr(offer));
        goodsAttentionServiceTran.sendCancelGoodsAttentionMsg(offer.getDriverId(), offer.getWaybillId(), offer.getGoodsId());

        //3、发送站内信给发布货源操作人
        log.info("sendGoodsMsgToShipper#发送站内信给发布货源操作人:{}",JSONUtil.toJsonStr(offer));
        driverOfferService.sendGoodsMsgToShipper(goods, offer.getWaybillId());
        driverOfferService.sendOrderMsgToShipper(goods, offer.getOrderId(), offer.getWaybillId());
    }

    /**
     * 更新数据构建
     *
     * @param goodsCreateOrderStatusChangeBO
     * @param saveBO
     */
    private void buildGoodsStatusChangeData(GoodsCreateOrderStatusChangeBO goodsCreateOrderStatusChangeBO, GoodsCreateOrderStatusChangeSaveBO saveBO) {
        SyncOrderToLsdsBO syncOrderToLsdsBO = goodsCreateOrderStatusChangeBO.getSyncOrderToLsds();

        //处理close合同生成订单无订单号问题
        this.processPoc(syncOrderToLsdsBO,saveBO);

        //处理报价单
        this.processOffer(syncOrderToLsdsBO,saveBO);
    }

    /**
     * 处理报价单
     * @param syncOrderToLsdsBO
     * @param saveBO
     */
    private void processOffer(SyncOrderToLsdsBO syncOrderToLsdsBO, GoodsCreateOrderStatusChangeSaveBO saveBO) {
        List<LsdsGoods> updateLsdsGoods = new ArrayList<>();
        List<GoodsSourceEntity> upgoods = new ArrayList<>();
        List<LsdsGoodsOffer> updateLsdsGoodsOffer = new ArrayList<>();
        List<LsdsGoodsRecord> lsdsGoodsRecords = new ArrayList<>();
        List<UpdateGoodsOfferStatusEntity> updateGoodsOfferStatusList = new ArrayList<>();

        if (StrUtil.isNotBlank(syncOrderToLsdsBO.getGoodsId()) && null != syncOrderToLsdsBO.getOfferId()) {

            LsdsGoods lsdsGoods = goodsOperatorRepository.selectLsdsGoodsByGoodsId(syncOrderToLsdsBO.getGoodsId());
            LsdsGoods tempLsdsGoods = GoodsSourceOperatorAssembler.buildUpdateLsdsGoods2(syncOrderToLsdsBO);
            GoodsSourceEntity updateGoods = GoodsSourceOperatorAssembler.buildUpdateGoodSource2(syncOrderToLsdsBO);
            updateLsdsGoods.add(tempLsdsGoods);
            upgoods.add(updateGoods);

            //更新对应下单的报价信息状态
            this.updateOfferInfo(syncOrderToLsdsBO,updateLsdsGoodsOffer,updateGoodsOfferStatusList);

            //通过oms消息传入的userBaseId获取用户信息
            PlatformUmUserbaseinfo platformUmUserbaseinfo = platformExchangeService.getUserInfoByUserId(syncOrderToLsdsBO.getUserBaseId());
            //同步货源操作记录
            LsdsGoodsRecord lgr = GoodsSourceOperatorAssembler.buildCommonOfferRecord(lsdsGoods.getGoodsId(),platformUmUserbaseinfo.getCompanyId(),"5",
                "货源成交",null,platformUmUserbaseinfo.getUserBaseId(),platformUmUserbaseinfo.getLoginName(),platformUmUserbaseinfo.getUsername());
            lsdsGoodsRecords.add(lgr);

            //更新子货源信息
            this.updateChildGoodsInfo(lsdsGoods,syncOrderToLsdsBO,updateLsdsGoodsOffer,
                updateGoodsOfferStatusList,updateLsdsGoods,upgoods,lsdsGoodsRecords);
        }
        saveBO.setUpdateGoodsOffer(updateLsdsGoodsOffer);
        saveBO.setSaveLgrsRecordList(lsdsGoodsRecords);
        saveBO.setUpdateLsdsGoodsList(updateLsdsGoods);
        saveBO.setUpdateGoodsOfferStatusList(updateGoodsOfferStatusList);
        saveBO.setUpdateGoodsSourceList(upgoods);
    }

    /**
     * 更新子货源信息
     * @param lsdsGoods
     * @param syncOrderToLsdsBO
     * @param updateLsdsGoodsOffer
     * @param updateGoodsOfferStatusList
     * @param updateLsdsGoods
     * @param upgoods
     * @param lsdsGoodsRecords
     */
    private void updateChildGoodsInfo(LsdsGoods lsdsGoods, SyncOrderToLsdsBO syncOrderToLsdsBO, List<LsdsGoodsOffer> updateLsdsGoodsOffer,
                                      List<UpdateGoodsOfferStatusEntity> updateGoodsOfferStatusList,
                                      List<LsdsGoods> updateLsdsGoods, List<GoodsSourceEntity> upgoods,
                                      List<LsdsGoodsRecord> lsdsGoodsRecords) {
        String userBaseId = syncOrderToLsdsBO.getUserBaseId();
        //下游询价，更新子货源及子报价状态为已成交
        if(null !=lsdsGoods && !StringUtils.isEmpty(lsdsGoods.getChildGoodsId())){

            LsdsGoods tempGoods = LsdsGoods.of();
            GoodsSourceEntity updateGoodsSource = new GoodsSourceEntity();

            tempGoods.setGoodsId(lsdsGoods.getChildGoodsId())
                .setDealStatus(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus5.getCode());

            updateGoodsSource.setGoodsId(lsdsGoods.getChildGoodsId());
            updateGoodsSource.setDealStatus(GoodsDealStatusEnum.TRADED.getType());
            updateGoodsSource.setUpdatedDate(new Date());
            updateGoodsSource.setCurrentRoundOfferCount(0);

            //更新对应子货源的报价信息状态
            if(null == syncOrderToLsdsBO.getOfferId() || syncOrderToLsdsBO.getOfferId().length<1) {
                return;
            }
            String [] offerIdArr = syncOrderToLsdsBO.getOfferId();
            String  platformCompanyIdStr=getPlatformCompanyIdStr();
            LsdsGoodsOffer lsdsGoodsOffer= new LsdsGoodsOffer();
            lsdsGoodsOffer.setOfferIdArr(offerIdArr);
            lsdsGoodsOffer.setPlatformCompanyId(platformCompanyIdStr);
            List<LsdsGoodsOffer> offerList = goodsOperatorRepository.getOfferListById(lsdsGoodsOffer);

            //按金额取整方式设置基价
            setBasePriceScale(offerList);

            List<String> childOfferIdList = offerList.stream().filter(p -> !StringUtils.isEmpty(p.getChildOfferId())).map(LsdsGoodsOffer::getChildOfferId).collect(Collectors.toList());
            if(null != childOfferIdList && childOfferIdList.size() > 0){
                tempGoods.setDealStatus(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus5.getCode());
                updateGoodsSource.setDealStatus(GoodsDealStatusEnum.TRADED.getType());
            }else{
                tempGoods.setDealStatus(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus7.getCode());
                updateGoodsSource.setDealStatus(GoodsDealStatusEnum.CANCELED.getType());
            }

            updateLsdsGoods.add(tempGoods);
            upgoods.add(updateGoodsSource);

            List<LsdsGoodsOffer> childOffers = GoodsSourceOperatorAssembler.buildLsdsGoodsOffer(childOfferIdList,userBaseId);
            if(CollUtil.isNotEmpty(childOffers)){
                updateLsdsGoodsOffer.addAll(childOffers);
            }

            //更新未中标的报价为未成交
            UpdateGoodsOfferStatusEntity updateEntity = UpdateGoodsOfferStatusEntity
                .buildUpdateGoodsOfferStatus(lsdsGoods.getChildGoodsId(), userBaseId, 1, OfferStatusEnum.UNSETTLED.getCode());
            updateGoodsOfferStatusList.add(updateEntity);

            LsdsGoodsRecord tempRec = GoodsSourceOperatorAssembler.buildCommonOfferRecord(lsdsGoods.getChildGoodsId(),"system","5",
                "货源成交",null,"system","system","系统");
            lsdsGoodsRecords.add(tempRec);
        }
    }

    /**
     * 更新报价信息
     * @param syncOrderToLsdsBO
     * @param updateLsdsGoodsOffer
     * @param updateGoodsOfferStatusList
     */
    private void updateOfferInfo(SyncOrderToLsdsBO syncOrderToLsdsBO, List<LsdsGoodsOffer> updateLsdsGoodsOffer, List<UpdateGoodsOfferStatusEntity> updateGoodsOfferStatusList) {
        if (null == syncOrderToLsdsBO.getOfferId() || syncOrderToLsdsBO.getOfferId().length<1) {
            return;
        }
        String now = DateUtils.getDateTime();
        String userBaseId = syncOrderToLsdsBO.getUserBaseId();
        String[] offerId = syncOrderToLsdsBO.getOfferId();
        for (int i = 0; i < offerId.length; i++) {
            LsdsGoodsOffer offer = new LsdsGoodsOffer();
            offer.setOfferId(offerId[i]);
            offer.setModifyDate(now);
            offer.setModifyBy(userBaseId);
            offer.setStatus(2);
            offer.setOfferStatus(OfferStatusEnum.TRADED.getCode());
            offer.setShowWinBids(Integer.valueOf(Opt.ofNullable(syncOrderToLsdsBO.getShowWinBids()).orElse(20).toString()));
            updateLsdsGoodsOffer.add(offer);
        }
        //更新未中标的报价为未成交
        UpdateGoodsOfferStatusEntity updateEntity = UpdateGoodsOfferStatusEntity
            .buildUpdateGoodsOfferStatus(syncOrderToLsdsBO.getGoodsId(), userBaseId, 1, OfferStatusEnum.UNSETTLED.getCode());
        updateGoodsOfferStatusList.add(updateEntity);

    }

    /**
     * 处理close合同生成订单无订单号问题
     * @param syncOrderToLsdsBO
     * @param saveBO
     */
    private void processPoc(SyncOrderToLsdsBO syncOrderToLsdsBO, GoodsCreateOrderStatusChangeSaveBO saveBO) {
        List<LsdsGoods> updateLsdsGoods = new ArrayList<>();
        List<GoodsSourceEntity> upgoods = new ArrayList<>();
        //处理close合同生成订单无订单号问题
        if (StrUtil.isNotBlank(syncOrderToLsdsBO.getGoodsId()) &&  null == syncOrderToLsdsBO.getOfferId()) {
            if (StrUtil.contains(syncOrderToLsdsBO.getParentOrderId(),"POC")){
                LsdsGoods tempLsdsGoods = GoodsSourceOperatorAssembler.buildUpdateLsdsGoods2(syncOrderToLsdsBO);
                GoodsSourceEntity updateGoods = GoodsSourceOperatorAssembler.buildUpdateGoodSource2(syncOrderToLsdsBO);
                updateLsdsGoods.add(tempLsdsGoods);
                upgoods.add(updateGoods);
            }
        }
        saveBO.setUpdateLsdsGoodsList(updateLsdsGoods);
        saveBO.setUpdateGoodsSourceList(upgoods);
    }


    /**
     * 司机货源下单成功后更新数据
     * @param goodsCreateOrderStatusChangeBO
     * @param saveBO
     */
    private void buildDriverGoods(GoodsCreateOrderStatusChangeBO goodsCreateOrderStatusChangeBO, GoodsCreateOrderStatusChangeSaveBO saveBO) {
        DriverOffer offer = goodsCreateOrderStatusChangeBO.getDriverOffer();
        TokenInfo tokenInfo = goodsCreateOrderStatusChangeBO.getTokenInfo();
        DriverGoods goods = goodsCreateOrderStatusChangeBO.getGoods();
        GoodsSourceEntity updateGoodsSource = new GoodsSourceEntity();
        updateGoodsSource.setGoodsId(goods.getGoodsId());

        List<DriverOffer> updateDriverOfferList = new ArrayList<>();

        tokenInfo = TokenInfoUtils.getTokenInfoIfNull(tokenInfo);
        BigDecimal remainQuantity = goods.getRemainingQuantity();
        if (offer.getSubQuantity() != null) {
            remainQuantity = remainQuantity.subtract(offer.getSubQuantity());
        } else {
            //按照配置减（报价下单使用这个逻辑）（报价下单新接口废弃）
            remainQuantity = getRemainQuantity(offer, goods);
        }
        if (remainQuantity.compareTo(BigDecimal.ZERO) < 0) {
            remainQuantity = BigDecimal.ZERO;
        }
        //如果货源剩余数量小于等于报价接单数量，则表示所有货物已处理完成，将剩余数量改为0，货源状态改为已成交
        if (remainQuantity.compareTo(BigDecimal.ZERO) == 0) {
            goods.setDealStatus(Constants.GOODS_STATUS_DEAL);
            updateGoodsSource.setDealStatus(Constants.GOODS_STATUS_DEAL);
            updateGoodsSource.setGoodsStatus(LsdsDealStatusEnum.LsdsDriverGoodsDealStatusEnum.getGoodsStatusByDealStatus(Constants.GOODS_STATUS_DEAL));
            updateGoodsSource.setCurrentRoundOfferCount(0);
        }
        //如果货源剩余数量大于报价接单数量，则需扣减剩余数量，货源状态改为部分成交
        else {
            goods.setDealStatus(Constants.GOODS_STATUS_PART_DEAL);
            updateGoodsSource.setDealStatus(Constants.GOODS_STATUS_PART_DEAL);
            updateGoodsSource.setGoodsStatus(LsdsDealStatusEnum.LsdsDriverGoodsDealStatusEnum.getGoodsStatusByDealStatus(Constants.GOODS_STATUS_PART_DEAL));
        }
        goods.setRemainingQuantity(remainQuantity);
        goods.setModifyBy(tokenInfo.getUserBaseId());
        goods.setModifyName(tokenInfo.getUsername());
        goods.setModifyDate(new Date());

        updateGoodsSource.setGoodsId(goods.getGoodsId());
        updateGoodsSource.setRemainingQuantity(remainQuantity);
        updateGoodsSource.setUpdaterId(tokenInfo.getUserBaseId());
        updateGoodsSource.setUpdaterName(tokenInfo.getUsername());
        updateGoodsSource.setUpdatedDate(new Date());


        //1、存储报价单
        //1.1询价范围为指定司机、询价方式为公开询价 修改报价单
        if (Constants.ENQUIRY_RANGE_DRIVERS.equals(goods.getEnquiryRange()) || StrUtil.equals(goods.getEnquiryType(), Constants.ENQUIRY_TYPE_OPEN)) {
            updateDriverOfferList.add(offer);
        }
        //撮合货源-报价单
        else if (GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(goods.getGoodsKind())) {
            //查询是否存在报价单
            DriverOffer driverOffer = goodsOperatorRepository.selectDriverOfferById(offer.getId());
            if (ObjUtil.isNull(driverOffer)) {
                saveBO.setSaveDriverOffer(offer);
                //撮合货源-没有议价，不需要更新报价次数
                updateGoodsSource.setCurrentRoundOfferCount(-1);
            } else {
                updateDriverOfferList.add(offer);
            }
        }
        //1.2询价范围为平台司机 新增报价单
        else if (Constants.ENQUIRY_RANGE_PLATFORM_DRIVERS.equals(goods.getEnquiryRange()) || Constants.ENQUIRY_RANGE_OWN_DRIVERS.equals(goods.getEnquiryRange())) {
            saveBO.setSaveDriverOffer(offer);
        }
        //2、货源状态为已成交时，剩下为未确认的报价单状态更新为未成交
        if (Constants.GOODS_STATUS_DEAL.equals(goods.getDealStatus())) {
            List<DriverOffer> list = goodsOperatorRepository.getDriverOfferByGoodsId(goods.getGoodsId());
            if (CollUtil.isNotEmpty(list)) {
                for (DriverOffer obj : list) {
                    obj.setOfferStatus(Constants.OFFER_STATUS_NO_DEAL);
                    obj.setModifyDate(new Date());
                    updateDriverOfferList.add(offer);
                }
            }
        }

        String cont = "货源成交";
        String remark = "";
        //撮合货源-议价下单
        if (GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(goods.getGoodsKind())
            && offer.getReceivingOrdersUnitPrice().compareTo(goods.getEnquiryTypeBasePrice()) != 0) {
            cont = "议价下单";
            remark = String.format("运单号%s更改了(接单单价)，更改前：%s，更改后：%s",
                offer.getWaybillId(), goods.getEnquiryTypeBasePrice(), offer.getReceivingOrdersUnitPrice());
        }
        LsdsGoodsRecord lgr = GoodsSourceOperatorAssembler.buildCommonOfferRecord(goods.getGoodsId(),tokenInfo.getCompanyId(),
            "5",cont,remark,tokenInfo.getUserBaseId(),tokenInfo.getLoginName(),tokenInfo.getUsername());

        saveBO.setUpdateDriverGoods(goods);
        saveBO.setUpdateGoodsSourceList(Arrays.asList(updateGoodsSource));
        saveBO.setUpdateDriverOfferList(updateDriverOfferList);
        saveBO.setSaveLgrsRecordList(Arrays.asList(lgr));

    }

    /**
     * 扣减数据计算
     *
     * @param offer
     * @param goods
     * @return
     */
    private BigDecimal getRemainQuantity(DriverOffer offer, DriverGoods goods) {
        BigDecimal remainQuantity = BigDecimal.ZERO;
        if (StrUtil.equals(offer.getTotalQuantityUnits(), goods.getTotalQuantityUnits())) {
            //单位一直直接扣
            remainQuantity = goods.getRemainingQuantity().subtract(offer.getReceivingOrdersQuantity());
        } else {
            //单位不一直取配置扣
            List<PlatformPricingTypeConfigVo> platformPricingTypeConfigVos = platformExchangeService.queryPricingTypeConfigEnum(PlatformEnum.EnableStatusEnum.ENABLE.getCode());
            Map<String, List<PlatformPricingTypeConfigVo>> groupByPricingType = platformPricingTypeConfigVos
                .stream().collect(Collectors.groupingBy(PlatformPricingTypeConfigVo::getPricingType));
            PlatformPricingTypeConfigVo platformPricingTypeConfigVo = IterUtil.getFirst(groupByPricingType.get(goods.getTotalQuantityUnits()));
            if (StrUtil.equals(platformPricingTypeConfigVo.getQuantityValueMethod(), PricingMethodEnum.TEN.getCode())) {
                remainQuantity = goods.getRemainingQuantity().subtract(offer.getReceivingOrdersWeight());
            } else if (StrUtil.equals(platformPricingTypeConfigVo.getQuantityValueMethod(), PricingMethodEnum.TWENTY.getCode())) {
                remainQuantity = goods.getRemainingQuantity().subtract(new BigDecimal(platformPricingTypeConfigVo.getFixedValue()));
            } else {
                remainQuantity = goods.getRemainingQuantity().subtract(BigDecimal.ONE);
            }
        }
        return remainQuantity;
    }

    /**
     * 获取平台公司id值封装
     * @return  String
     */
    public String  getPlatformCompanyIdStr() {
        List<PlatformCmOperationMainBody> listBody=platformExchangeService.getAllTransaction();
        String platformCompanyId="";
        for (int i=0;i<listBody.size();i++){
            platformCompanyId=platformCompanyId+listBody.get(i).getCompanyId()+",";
        }
        platformCompanyId=platformCompanyId.substring(0,platformCompanyId.length()-1);
        return platformCompanyId;
    }

    private void setBasePriceScale(List<LsdsGoodsOffer> offerList){
        if(IterUtil.isEmpty(offerList)){
            return;
        }

        Set<String> busiIdSet = new HashSet<>();
        for(LsdsGoodsOffer goodsOffer:offerList){
            if(!StrUtil.isEmpty(goodsOffer.getGoodsId())){
                busiIdSet.add(goodsOffer.getGoodsId());
            }
        }
        Integer roundingMode = null;
        Map<String, Integer> roundingModeMap = amountRoundingModeService.getRoundingMode(new ArrayList<>(busiIdSet));
        for(LsdsGoodsOffer goodsOffer:offerList){
            roundingMode = BigDecimal.ROUND_UP;
            if(!StrUtil.isEmpty(goodsOffer.getGoodsId())){
                roundingMode = roundingModeMap.get(goodsOffer.getGoodsId());
            }
            if(roundingMode==null){
                roundingMode = BigDecimal.ROUND_UP;
            }
            if(goodsOffer.getEnquiryTypeBasePrice()!=null){
                goodsOffer.setEnquiryTypeBasePrice(goodsOffer.getEnquiryTypeBasePrice().setScale(2,roundingMode));
            }
            if(goodsOffer.getEnquiryTypeBaseOpenTicket()!=null){
                goodsOffer.setEnquiryTypeBaseOpenTicket(goodsOffer.getEnquiryTypeBaseOpenTicket().setScale(2,roundingMode));
            }
            if(goodsOffer.getTotalQuantity()!=null && goodsOffer.getEnquiryTypeBaseOpenTicket()!=null){
                goodsOffer.setExFreightPrice(goodsOffer.getTotalQuantity().multiply(goodsOffer.getEnquiryTypeBaseOpenTicket()).setScale(2,roundingMode ));
            }
        }
    }

}
