package com.wanlianyida.lsds.domain.service.operate;

import cn.hutool.extra.spring.SpringUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.lsds.domain.model.bo.GoodsOperateBO;
import com.wanlianyida.lsds.infrastructure.enums.GoodsOperateTypeEnum;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月14日 14:50
 */
@Service
public class GoodsOperateExecutor implements InitializingBean {

    private Map<GoodsOperateTypeEnum, GoodsOperateStrategy> mapping;

    public ResultMode operate(GoodsOperateBO goodsOperateBO, GoodsOperateTypeEnum operateType) {
        GoodsOperateStrategy strategy = mapping.get(operateType);
        return strategy.operate(goodsOperateBO);
    }

    @Override
    public void afterPropertiesSet() {
        mapping = new HashMap<>();
        Map<String, GoodsOperateStrategy> beansOfType = SpringUtil.getBeansOfType(GoodsOperateStrategy.class);
        for (GoodsOperateStrategy strategy : beansOfType.values()) {
            mapping.put(strategy.getOperateType(), strategy);
        }
    }
}
