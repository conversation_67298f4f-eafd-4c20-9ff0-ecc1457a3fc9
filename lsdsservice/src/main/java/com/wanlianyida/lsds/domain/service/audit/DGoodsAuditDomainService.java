package com.wanlianyida.lsds.domain.service.audit;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.isoftstone.hig.annotations.LogPrintPoint;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.UtilityClass;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.eval.api.entity.EvalDriverInfo;
import com.isoftstone.hig.lsds.api.entity.*;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.platform.api.entity.PlatformCmOperationMainBody;
import com.isoftstone.hig.platform.api.entity.PlatformUmCompanyMain;
import com.isoftstone.hig.platform.api.entity.PlatformUmLogininfo;
import com.isoftstone.hig.platform.api.filter.PlatformCmOperationMainBodyFilter;
import com.isoftstone.hig.tcs.api.mvcvo.TcsDriverVO;
import com.wanlianyida.lsds.application.service.DriverGoodsRuleAppService;
import com.wanlianyida.lsds.application.service.DriverOfferAppService;
import com.wanlianyida.lsds.domain.assembler.GoodsSourceOperatorAssembler;
import com.wanlianyida.lsds.domain.model.bo.audit.DGoodsAuditBO;
import com.wanlianyida.lsds.domain.model.bo.audit.GoodsSourceAuditBO;
import com.wanlianyida.lsds.domain.model.bo.audit.GoodsSourceAuditSaveBO;
import com.wanlianyida.lsds.domain.model.entity.GoodsSourceEntity;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.domain.service.BizDomainService;
import com.wanlianyida.lsds.infrastructure.enums.DriverDealStatusEnum;
import com.wanlianyida.lsds.infrastructure.enums.EnquiryTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.FreightTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsAuditTypeEnum;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.exchange.EvalExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.TcsExchangeService;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import com.wanlianyida.lsds.infrastructure.util.MessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.isoftstone.hig.lsds.api.util.Constants.MATCHMAKING_GOODS_CHANGE_TOPIC;

/**
 * 司机货源审核
 */
@Slf4j
@Service("DGoodsAudit")
public class DGoodsAuditDomainService extends BizDomainService<GoodsSourceAuditBO, GoodsSourceAuditSaveBO,String> {


    @Resource
    private DriverOfferAppService driverOfferService;

    @Resource
    private TcsExchangeService tcsExchangeService;

    @Resource
    private EvalExchangeService evalExchangeService;

    @Resource
    private LsdsKafkaSender lsdsKafkaSender;

    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private TaskExecutor asynTaskExecutor;

    @Resource
    private MessageUtils messageUtils;

    @Resource
    private DriverGoodsRuleAppService driverGoodsRuleAppService;

    @Override
    @LogPrintPoint(ret = true)
    public ResultMode checkParam(GoodsSourceAuditBO paramBo) {
        if(ObjUtil.isNull(paramBo.getDGoodsAudit())){
            return ResultMode.fail("司机货源审核参数为空");
        }
        DGoodsAuditBO bo= paramBo.getDGoodsAudit();
        //公共参数校验
        ResultMode resultMode = this.commonCheckParams(bo);
        if(!resultMode.isSucceed()){
            return resultMode;
        }

        String auditType = bo.getAuditType();
        if(StrUtil.equals(auditType,GoodsAuditTypeEnum.TYPE_10.getType())) {
            //自动审核逻辑,调用接口，入参（companyId,maniBodyId）
            Map<String, String> model = new HashMap<>();
            model.put("companyId", bo.getCompanyId());
            model.put("mainBodyId", bo.getNetworkMainBodyId());
            model.put("bizType", Constants.DRIVER_GOODS_AUTO_CHECK);
            Boolean autoAudit = platformExchangeService.judgeAutoAudit(model);
            if(!autoAudit){
                Boolean transactionBody = platformExchangeService.isTransactionBody(bo.getCompanyId());
                if(transactionBody){
                    log.info("交易签约主体自动审核通过");
                    return ResultMode.success();
                }
                log.info("司机货源审核自动审核没开启:{}",JSONUtil.toJsonStr(model));
                return ResultMode.fail("没开启自动审核");
            }
        }
        //待审核、审核通过校验
        if(StrUtil.equalsAny(bo.getDealStatus(),DriverDealStatusEnum.TODO_AUDIT.getCode(),DriverDealStatusEnum.RELEASE.getCode())){
            return this.passCheckParams(bo);
        }else {
            //审核驳回校验
            return this.notPassCheckParams(bo);
        }
    }


    /**
     * 审核驳回校验
     * @param bo
     * @return
     */
    private ResultMode notPassCheckParams(DGoodsAuditBO bo) {
        return ResultMode.success();
    }


    /**
     * 通用参数校验
     * @param bo
     * @return
     */
    private ResultMode commonCheckParams(DGoodsAuditBO bo) {
        if(StrUtil.isBlank(bo.getAuditType())){
            return ResultMode.fail("审核类型为空");
        }
        DriverGoods oldDriverGoods = goodsOperatorRepository.selectDriverGoods(bo.getGoodsId());
        if(ObjUtil.isNull(oldDriverGoods)){
            return ResultMode.fail(bo.getGoodsId()+":司机货源不存在");
        }

        if(StrUtil.equals(oldDriverGoods.getDealStatus(),DriverDealStatusEnum.RELEASE.getCode())){
            return ResultMode.fail(bo.getGoodsId()+":已审核通过");
        }

        if(!StrUtil.equalsAny(bo.getDealStatus(),DriverDealStatusEnum.TODO_AUDIT.getCode(),
            bo.getDealStatus(),DriverDealStatusEnum.RELEASE.getCode(),
            bo.getDealStatus(),DriverDealStatusEnum.AUDIT_NOT_PASS.getCode())){
            return ResultMode.fail(bo.getGoodsId()+":状态不是 20,30,40");

        }

        //初始必须参数
        this.initParams(bo,oldDriverGoods);

        return ResultMode.success();

    }

    /**
     * 审核通过参数校验
     * @param bo
     * @return
     */
    @LogPrintPoint(ret = true)
    private ResultMode passCheckParams(DGoodsAuditBO bo) {
        if (StrUtil.equals(bo.getFreightType(),Constants.FREIGHT_TYPE_NETWORK)){
            if(ObjUtil.isNull(bo.getFreightSurvivalRate())){
                return ResultMode.fail("网络货运主体运费留存率不能为空!");
            }
            if(StrUtil.isBlank(bo.getNetworkMainBodyName()) || StrUtil.isBlank(bo.getNetworkMainBodyId())) {
                return ResultMode.fail("网络货运主体不能为空!");
            }
            if(ObjUtil.isNull(bo.getFreightSurvivalRate())){
                return ResultMode.fail("税率为空!");
            }
        }
        if(StrUtil.isBlank(bo.getFeeClearType())){
            return ResultMode.fail("费率上浮类型不能为空!");
        }
        if(ObjUtil.isNull(bo.getFeeClearValue())){
            return ResultMode.fail("费用设置值为空!");
        }
        if(StrUtil.isBlank(bo.getOtherClearType())){
            return ResultMode.fail("结算方式为空!");
        }
        return ResultMode.success();
    }

    /**
     * 初始化参数
     * @param bo
     * @param oldDriverGoods
     */
    private void initParams(DGoodsAuditBO bo, DriverGoods oldDriverGoods) {
        bo.setCompanyId(oldDriverGoods.getCompanyId());
        bo.setEnquiryType(oldDriverGoods.getEnquiryType());
        bo.setEnquiryRange(oldDriverGoods.getEnquiryRange());
        bo.setDriverIds(oldDriverGoods.getDriverIds());
        bo.setGoodsKind(oldDriverGoods.getGoodsKind());
        bo.setTransportationType(oldDriverGoods.getTransportationType());
        bo.setRoundingMode(oldDriverGoods.getRoundingMode());
        bo.setFreightType(oldDriverGoods.getFreightType());
        bo.setFreightSurvivalRate(oldDriverGoods.getFreightSurvivalRate());

        bo.setStartSiteCityName(oldDriverGoods.getStartSiteCityName());
        bo.setEndSiteCityName(oldDriverGoods.getEndSiteCityName());
        bo.setCreateBy(oldDriverGoods.getCreateBy());
        bo.setEnquiryTypeBaseTaxRate(oldDriverGoods.getEnquiryTypeBaseTaxRate());


        PlatformUmLogininfo platformUmLogininfo = platformExchangeService.getLoginInfoByUserBaseId(bo.getUserId());
        if(ObjUtil.isNotNull(platformUmLogininfo)){
            bo.setUserName(platformUmLogininfo.getUsername());
            bo.setUserLoginName(platformUmLogininfo.getLoginName());
        }

        bo.setOldDriverGoods(oldDriverGoods);
    }

    @LogPrintPoint
    @Override
    public GoodsSourceAuditSaveBO dataAssembly(GoodsSourceAuditBO bo) {
        DGoodsAuditBO dGoodsAudit = bo.getDGoodsAudit();
        String auditType = dGoodsAudit.getAuditType();

        List<String> errors = new ArrayList<>();
        if(StrUtil.equals(auditType,GoodsAuditTypeEnum.TYPE_10.getType())) {
            //先走自动审核规矩
            this.processAutoAuditData(bo,errors);
            //交易签约主体审核
            this.transactionBodyAutoAudit(dGoodsAudit,errors);

        }
        //构建操作记录
        LsdsGoodsRecord lsdsGoodsRecord = GoodsSourceOperatorAssembler.buildLsdsGoodsRecord(dGoodsAudit,errors);
        log.info("司机货源审核status:{}",dGoodsAudit.getDealStatus());
        List<DriverOffer> driverOfferList = null;
        GoodsSourceEntity updateGoodsSourceEntity = null;
        DriverGoods updateDriverGoods = null;
        DriverGoodsExtend updateDriverGoodsExtend = null;
        if(StrUtil.equalsAny(dGoodsAudit.getDealStatus(),DriverDealStatusEnum.RELEASE.getCode(),DriverDealStatusEnum.AUDIT_NOT_PASS.getCode())){
            driverOfferList = this.audit(bo);
            //构建updateGoodsSourceEntity
            updateGoodsSourceEntity = GoodsSourceOperatorAssembler.buildDriverGoodsSourceEntity(dGoodsAudit);
            //构建 updateDriverGoods
            updateDriverGoods = GoodsSourceOperatorAssembler.buildUpdateDriverGoods(dGoodsAudit);
            updateDriverGoodsExtend = GoodsSourceOperatorAssembler.buildUpdateDriverGoodsExtend1(dGoodsAudit);
        }
        return GoodsSourceOperatorAssembler.buildGoodsSourceAuditSaveBO(updateDriverGoods,updateGoodsSourceEntity,lsdsGoodsRecord,driverOfferList,updateDriverGoodsExtend);
    }


    @Override
    public void saveRep(GoodsSourceAuditSaveBO bo) {
        goodsOperatorRepository.handleGoodsAudit(bo);
    }

    /**
     * 异步操作
     * @param bo
     */
    @Override
    public void asyOper(GoodsSourceAuditBO paramBo,GoodsSourceAuditSaveBO bo) {
        asynTaskExecutor.execute(()->{
            this.sendMessage(paramBo);
        });
        asynTaskExecutor.execute(()->{
            this.sendMQ(paramBo);
        });
    }

    /**
     * 广播通知货源状态变更
     * @param paramBo
     */
    private void sendMQ(GoodsSourceAuditBO paramBo) {
        DGoodsAuditBO dGoodsAudit = paramBo.getDGoodsAudit();
        GoodsKafkaNotice goodsKafkaNotice = BeanUtil.copyProperties(dGoodsAudit, GoodsKafkaNotice.class);
        // 审核通过的woa可能要推送渠道货源线索：发布货源时自动审核通过的也走这里
        if (StrUtil.equals(dGoodsAudit.getDealStatus(), Constants.GOODS_STATUS_RELEASED)) {
            goodsKafkaNotice.setSaveWoaGoodsClue(true);
            goodsKafkaNotice.setPushWoaGoodsClue(true);
            log.info("save#发布后自动审核通过，推送到woa->{}", JSONUtil.toJsonStr(goodsKafkaNotice));
        } else {
            goodsKafkaNotice.setSaveWoaGoodsClue(true);
            log.info("save#发布后自动审核不通过，推送到woa->{}", JSONUtil.toJsonStr(goodsKafkaNotice));
        }
        String statusChangeMessage = JSONUtil.toJsonStr(goodsKafkaNotice);
        log.info("goods_status_upd#货源状态变更：{}",statusChangeMessage);
        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, statusChangeMessage);

        //通知撮合
        String goodsId = dGoodsAudit.getGoodsId();
        log.info("matchmaking_goods_change_topic#通知撮合：{}",goodsId);
        lsdsKafkaSender.send(MATCHMAKING_GOODS_CHANGE_TOPIC, goodsId);
    }


    /**
     *  发送站内信给发货方
     * @param paramBo
     */
    private void sendMessage(GoodsSourceAuditBO paramBo) {
        DGoodsAuditBO dGoodsAudit = paramBo.getDGoodsAudit();
        Map<String, String> param = new HashMap();
        String phoneNumber = "";
        String userId = dGoodsAudit.getCreateBy();
        String companyId = dGoodsAudit.getCompanyId();
        String templateId = "";
        if (Constants.GOODS_STATUS_RELEASED.equals(dGoodsAudit.getDealStatus())) {
            templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_AUDIT_PASS.getCode();
        } else {
            templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_AUDIT_NO_PASS.getCode();
        }
        param.put("goodsNo", dGoodsAudit.getGoodsId());
        param.put("sendShortName", dGoodsAudit.getStartSiteCityName());
        param.put("receiveShortName", dGoodsAudit.getEndSiteCityName());
        log.info("sendMessage#消息通知：userId:{},companyId:{},templateId:{},param:{}",userId,companyId,templateId,param);
        messageUtils.sendMsg(phoneNumber, userId, companyId, templateId, param);
    }


    /**
     * 审核
     * @param bo
     */
    private List<DriverOffer> audit(GoodsSourceAuditBO bo) {
        DGoodsAuditBO dGoodsAudit = bo.getDGoodsAudit();
        dGoodsAudit.setAuditDate(new Date());
        //设置创建和修改用户信息
        dGoodsAudit.setModifyBy(dGoodsAudit.getUserId());
        dGoodsAudit.setModifyName(dGoodsAudit.getUserName());

        if (!StrUtil.equals(dGoodsAudit.getDealStatus(),Constants.GOODS_STATUS_RELEASED)) {
            return null;
        }
        List<DriverOffer> driverOffers = null;
        //如果货源为指定单价，则需根据公式设置开票价，上浮后的价格
        if (StrUtil.equals(dGoodsAudit.getEnquiryType(), EnquiryTypeEnum.APPOINT.getType())) {

            DriverGoods queryDriverGoods = new DriverGoods();
            queryDriverGoods.setRoundingMode(dGoodsAudit.getRoundingMode());
            queryDriverGoods.setGoodsId(dGoodsAudit.getGoodsId());
            queryDriverGoods.setGoodsKind(dGoodsAudit.getGoodsKind());
            queryDriverGoods.setFreightType(dGoodsAudit.getFreightType());
            queryDriverGoods.setFreightSurvivalRate(dGoodsAudit.getFreightSurvivalRate());
            queryDriverGoods.setEnquiryTypeBaseTaxRate(dGoodsAudit.getEnquiryTypeBaseTaxRate());
            queryDriverGoods.setEnquiryTypeBasePrice(dGoodsAudit.getEnquiryTypeBasePrice());
            queryDriverGoods.setFeeClearValue(dGoodsAudit.getFeeClearValue());
            queryDriverGoods.setFeeClearType(dGoodsAudit.getFeeClearType());
            queryDriverGoods.setEnquiryType(dGoodsAudit.getEnquiryType());

            DriverGoods driverGoods = driverOfferService.calculatePrice(queryDriverGoods, true);

            dGoodsAudit.setEnquiryTypeBasePrice(driverGoods.getEnquiryTypeBasePrice());
            dGoodsAudit.setEnquiryTypeBaseOpenTicket(driverGoods.getEnquiryTypeBaseOpenTicket());
            dGoodsAudit.setFloatEnquiryTypeBasePrice(driverGoods.getFloatEnquiryTypeBasePrice());
            dGoodsAudit.setFloatEnquiryTypeBaseOpenTicket(driverGoods.getFloatEnquiryTypeBaseOpenTicket());
        }
        //如果是指定司机的货源，则需要创建对应的待报价和待确定数据给司机
        if (StrUtil.equals(dGoodsAudit.getEnquiryRange(),Constants.ENQUIRY_RANGE_DRIVERS)
            && StrUtil.isNotBlank(dGoodsAudit.getDriverIds())) {
            driverOffers = this.buildDriverOffer(bo);
        }
       return driverOffers;
    }

    /**
     * 构建报价对象
     * @param bo
     */
    public List<DriverOffer> buildDriverOffer(GoodsSourceAuditBO bo) {
        //指定司机id集合
        DGoodsAuditBO dGoodsAudit = bo.getDGoodsAudit();
        List<String> driverIds = Arrays.asList(dGoodsAudit.getDriverIds().split(","));

        //查询司机
        List<TcsDriverVO> tcsDriverVOS = tcsExchangeService.queryDriverByDriverIds(driverIds);
        Map<String, TcsDriverVO> tcsDriverVOMap = new HashMap<>();
        if(CollUtil.isNotEmpty(tcsDriverVOS)){
            tcsDriverVOMap = tcsDriverVOS.stream().collect(Collectors.toMap(TcsDriverVO::getDriverId, Function.identity()));
        }

        //查询司机评级
        Map<String, EvalDriverInfo> evalDriverInfoMap = new HashMap<>();
        List<EvalDriverInfo> evalDriverInfos = evalExchangeService.selectByDriverIds(driverIds);
        if(CollUtil.isNotEmpty(evalDriverInfos)){
            evalDriverInfoMap = evalDriverInfos.stream().collect(Collectors.toMap(EvalDriverInfo::getDriverId, Function.identity()));
        }

        List<DriverOffer> driverOffers = new ArrayList<>();
        for (String id : driverIds) {
            DriverOffer offer = new DriverOffer();
            offer.setGoodsId(dGoodsAudit.getGoodsId());
            offer.setId(UtilityClass.uuid());
            TcsDriverVO driver = tcsDriverVOMap.get(id);
            if (driver != null) {
                offer.setIdCardNo(driver.getLicenseNo());
                offer.setDriverId(driver.getDriverCode());
                offer.setDriverName(driver.getDriverName());
                offer.setContactPhoneNumber(driver.getDriverPhoneNumber());
                EvalDriverInfo evalDriverInfo = Opt.ofNullable(evalDriverInfoMap.get(id)).orElse(new EvalDriverInfo());
                Double driverGrade = Opt.ofNullable(evalDriverInfo.getEvalDriverLevel()).orElse(Double.valueOf(0));
                log.info("driverGrade:{}",driverGrade);
                offer.setDriverGrade(driverGrade.toString());
                //查询司机运营标识和是否实名状态
                offer.setOperateTag("2");
                offer.setIsRealAuthentication("1");
            }
            if (Constants.ENQUIRY_TYPE_OPEN.equals(dGoodsAudit.getEnquiryType())) {
                offer.setOfferStatus(Constants.OFFER_STATUS_WAIT_OFFER);
            }
            //询价类型为指定单价 需要结算浮动金额
            if (Constants.ENQUIRY_TYPE_ASSIGN.equals(dGoodsAudit.getEnquiryType())) {
                offer.setOfferStatus(Constants.OFFER_STATUS_WAIT_CONFIRM);
                offer.setEnquiryTypeBasePrice(dGoodsAudit.getEnquiryTypeBasePrice());
                offer.setEnquiryTypeBaseOpenTicket(dGoodsAudit.getEnquiryTypeBaseOpenTicket());
                offer.setFloatEnquiryTypeBaseOpenTicket(dGoodsAudit.getFloatEnquiryTypeBaseOpenTicket());
                offer.setFloatEnquiryTypeBasePrice(dGoodsAudit.getFloatEnquiryTypeBasePrice());
                offer.setEnquiryTypeBaseTaxRate(dGoodsAudit.getFreightSurvivalRate());
            }

            offer.setCreateBy(dGoodsAudit.getUserId());
            offer.setCreateName(dGoodsAudit.getUserName());
            offer.setCreateDate(new Date());

            driverOffers.add(offer);
        }
        return driverOffers;
    }

    /**
     * 处理自动审核
     * @param bo
     * @param errors
     * @return
     */
    private ResultMode processAutoAuditData(GoodsSourceAuditBO bo, List<String> errors){
        log.info("processAutoAuditData#自动审核:{}",JSONUtil.toJsonStr(bo));
        DGoodsAuditBO dGoodsAudit = bo.getDGoodsAudit();
        DriverGoods oldDriverGoods = dGoodsAudit.getOldDriverGoods();
        if (!Constants.GOODS_STATUS_AUDIT.equals(dGoodsAudit.getDealStatus())) {
            log.info("processAutoAuditData#只有待审核走自动审核规则校验");
            return ResultMode.fail("非待审核状态不走自动审核");
        }
        if(!StrUtil.equals(oldDriverGoods.getFreightType(), FreightTypeEnum.NETWORK_MODE.getCode())){
            return ResultMode.fail("传统货运不走自动审核");
        }

        errors = driverGoodsRuleAppService.check(dGoodsAudit.getOldDriverGoods());
        if(CollUtil.isNotEmpty(errors)){
            dGoodsAudit.setAutoAuditRemark("货源自动审核失败");
            log.info("processAutoAuditData#自动审核没通过:{}",JSONUtil.toJsonStr(errors));
            return ResultMode.fail(String.join(",",errors));
        }
        //审核通过
        PlatformUmCompanyMain companyMainFilter = new PlatformUmCompanyMain();
        companyMainFilter.setCompanyId(dGoodsAudit.getCompanyId());
        companyMainFilter.setMainId(dGoodsAudit.getNetworkMainBodyId());
        companyMainFilter.setMainType(Constants.FREIGHT_TYPE_NETWORK);
        PlatformUmCompanyMain companyMainModel = platformExchangeService.getCompanyMainModel(companyMainFilter);

        PagingInfo pagingInfo = new PagingInfo();
        PlatformCmOperationMainBodyFilter filter = new PlatformCmOperationMainBodyFilter();
        filter.setType("1"); //交易签约主体类型 1
        pagingInfo.setFilterModel(filter);
        List<PlatformCmOperationMainBody> platformCmOperationMainBodies = platformExchangeService.listMainBodyPage(pagingInfo);
        if(CollUtil.isEmpty(platformCmOperationMainBodies)){
            throw new LsdsWlydException("查询默认交易签约主体失败");
        }
        PlatformCmOperationMainBody platformCmOperationMainBody = platformCmOperationMainBodies.get(0);
        dGoodsAudit.setTransactionContractingBodyId(platformCmOperationMainBody.getOperationMainBodyId());
        dGoodsAudit.setDealStatus(Constants.GOODS_STATUS_RELEASED);
        dGoodsAudit.setFeeClearType(companyMainModel.getFeeClearType());
        dGoodsAudit.setFeeClearValue(companyMainModel.getFeeClearValue());
        dGoodsAudit.setAutoAuditRemark("货源自动审核成功");
        return ResultMode.success();
    }

    /**
     * 交易签约主体
     * @param dGoodsAudit
     * @param errors
     */
    private void transactionBodyAutoAudit(DGoodsAuditBO dGoodsAudit,List<String> errors){
        if (!StringUtils.equals(dGoodsAudit.getDealStatus(), DriverDealStatusEnum.TODO_AUDIT.getCode())) {
            log.info("transactionBodyAutoAudit#不是待审核不处理");
            return;
        }
        //查询当前登录用户是否为交易签约主体
        Boolean transactionBody = platformExchangeService.isTransactionBody(dGoodsAudit.getCompanyId());
        //如果是交易签约主体，则将数据进行自动审核，改为已审核状态
        if (!transactionBody) {
            return;
        }
        //获取企业维护的默认交易签约主体
        List<PlatformUmCompanyMain> platformUmCompanyMains = platformExchangeService.selectCompanyMains(dGoodsAudit.getCompanyId(), "1");
        if (CollectionUtils.isEmpty(platformUmCompanyMains)) {
            throw new LsdsWlydException("未设置默认交易签约主体");

        }
        //设置货源的默认交易签约主体
        dGoodsAudit.setTransactionContractingBodyId(platformUmCompanyMains.get(0).getMainId());
        //设置状态为已审核
        dGoodsAudit.setDealStatus(DriverDealStatusEnum.RELEASE.getCode());
        //设置上浮类型为按单价上浮
        dGoodsAudit.setFeeClearType(Constants.FEE_CLEAR_TYPE_UN_PRICE);
        //设置上浮单价为0
        dGoodsAudit.setFeeClearValue(new BigDecimal("0"));
        //如果自动审核不通，网络货运主体和交易签约主体的货源状态改为审核不通过 40
        if (!CollectionUtils.isEmpty(errors)) {
            dGoodsAudit.setDealStatus(DriverDealStatusEnum.TODO_AUDIT.getCode());
        }
    }
}
