package com.wanlianyida.lsds.domain.service.operate;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.lsds.domain.model.bo.GoodsOperateBO;
import com.wanlianyida.lsds.infrastructure.enums.GoodsOperateTypeEnum;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月14日 13:49
 */
public interface GoodsOperateStrategy {

    ResultMode<?> operate(GoodsOperateBO goodsOperateBO);

    GoodsOperateTypeEnum getOperateType();
}
