package com.wanlianyida.lsds.domain.service.operate;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.wanlianyida.lsds.domain.model.bo.GoodsOperateBO;
import com.wanlianyida.lsds.domain.model.bo.GoodsRefundBO;
import com.wanlianyida.lsds.domain.model.bo.GoodsUpdateBO;
import com.wanlianyida.lsds.domain.model.entity.GoodsSourceEntity;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.infrastructure.enums.GoodsDealStatusEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsOperateTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsPublishTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月16日 11:42
 */
@Slf4j
@Component
public class GoodsRefundHandler implements GoodsOperateStrategy {

    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;

    @Override
    public ResultMode<?> operate(GoodsOperateBO goodsOperateBO) {
        GoodsRefundBO goodsRefund = goodsOperateBO.getGoodsRefund();
        GoodsSourceEntity goodsSource = goodsOperatorRepository.queryByGoodsId(goodsRefund.getGoodsId());
        if (goodsSource == null) {
            log.info("【取消运单返还数量】货源单不存在，goodsId：{}", goodsRefund.getGoodsId());
            return null;
        }
        if (!GoodsDealStatusEnum.PUBLISHING.getType().equals(goodsSource.getDealStatus()) && !GoodsDealStatusEnum.PART_TRADED.getType().equals(goodsSource.getDealStatus())) {
            log.info("【取消运单返还数量】货源单状态异常，goodsId：{}", goodsRefund.getGoodsId());
            return null;
        }
        GoodsUpdateBO updateBO = null;

        if (GoodsPublishTypeEnum.DRIVER.getType().equals(goodsSource.getPublisherType())) {
            updateBO = assemblerDriverGoods(goodsOperateBO, goodsSource);
        }
        if (updateBO != null) {
            goodsOperatorRepository.updateByGoodsId(updateBO);
        }
        return null;
    }

    public GoodsUpdateBO assemblerDriverGoods(GoodsOperateBO goodsOperateBO, GoodsSourceEntity goodsSource) {
        GoodsRefundBO goodsRefund = goodsOperateBO.getGoodsRefund();
        // 校验货源单状态
        if (Constants.GOODS_STATUS_RELEASED.equals(goodsSource.getDealStatus()) || Constants.GOODS_STATUS_PART_DEAL.equals(goodsSource.getDealStatus())) {
            GoodsSourceEntity  update = new GoodsSourceEntity();
            update.setId(goodsSource.getId());
            update.setGoodsId(goodsSource.getGoodsId());
            BigDecimal backQuantity = goodsSource.getRemainingQuantity().add(goodsRefund.getTotalQuantity());
            BigDecimal reallyBack = backQuantity.compareTo(goodsSource.getTotalQuantity()) > 0 ? goodsSource.getTotalQuantity() : backQuantity;
            update.setRemainingQuantity(reallyBack);
            update.setUpdaterId(goodsRefund.getModifyBy());
            update.setUpdaterName(goodsRefund.getModifyName());
            update.setUpdatedDate(new Date());
            GoodsUpdateBO updateBO = new GoodsUpdateBO();
            updateBO.setGoodsSource(update);

            DriverGoods driverGoods = new DriverGoods();
            driverGoods.setId(goodsSource.getGoodsId());
            driverGoods.setGoodsId(goodsSource.getGoodsId());
            driverGoods.setRemainingQuantity(reallyBack);
            driverGoods.setModifyBy(goodsRefund.getModifyBy());
            driverGoods.setModifyName(goodsRefund.getModifyName());
            driverGoods.setModifyDate(new Date());
            updateBO.setDriverGoods(driverGoods);
            return updateBO;
        }
        return null;
    }

    @Override
    public GoodsOperateTypeEnum getOperateType() {
        return GoodsOperateTypeEnum.REFUND;
    }
}
