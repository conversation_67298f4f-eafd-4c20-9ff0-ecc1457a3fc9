package com.wanlianyida.lsds.domain.service;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.isoftstone.hig.common.model.PagingProceduresInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.LogHelper;
import com.isoftstone.hig.common.utils.SQLUtils;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.lsds.api.entity.LsdsGoods;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsFilter;
import com.wanlianyida.lsds.infrastructure.repository.mapper.GoodsMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 服务公共类主要实现业务规则，处理业务逻辑 可以再进行创建多个，这主要为了方便一起生成
 *
 * <AUTHOR>
 */
@Service
public class LsdsGoodsDomainService {

    /**
     * 分页获取货源编号  货源信息表信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @param token    预留要传递的token，服务端进行判断权限功能
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsGoods> pagingLsdsGoods(PagingInfo<LsdsGoodsFilter> pageInfo, String token) {

        ResultMode<LsdsGoods> returnmodel = new ResultMode<LsdsGoods>();
        try {
            //过滤SQL参数
            SQLUtils.filterParam(pageInfo.filterModel);

            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            PagingProceduresInfo filter = new PagingProceduresInfo();
            filter.setTables("lsds_goods");
            filter.setFields("goods_id,company_id,child_goods_id,goods_is_split,order_id,contract_id,contract_name,start_site_city_code,start_site_city_name,start_site_address,start_send_linker,start_send_phone_number,end_site_city_code,end_site_city_name,end_site_address,end_receive_linker,end_receive_phone_number,goods_type,goods_name,goods_desc,weight_sum,weight_unit,volume_sum,total_goods,goods_release_amount,goods_deal_amount,pack_type,transportation_type,enquiry_type,enquiry_type_base_price,enquiry_type_base_tax_rate,enquiry_type_base_open_ticket,delivery_type,enquiry_range,enquiry_offer_time,transport_is_options,assign_car_type,assign_car_length,assign_car_plate_number,release_date,arrive_date,validity_date,other_is_invoice,other_kui_tons_ratio,other_receipt_type,other_clear_type,other_remark,record_status,deal_status,audit_record_status_mark,release_type,source_sys,im_is_body,pick_up_type,pick_up_date,hope_arrival_time,on_time_is,insurance_price,cancel_explain,fee_clear_type,fee_clear_value,offer_current_rounds,offer_round,create_by,create_date,modify_by,modify_date,item1,item2,item3,item4");
            filter.setFilter("");
            filter.setCountTotal(pageInfo.getCountTotal());
            filter.setPageNumber(pageInfo.currentPage);
            filter.setPageSize(pageInfo.pageLength);
            StringBuilder sbwhere = new StringBuilder(" 1 = 1 ");
            LsdsGoodsFilter queryModel = pageInfo.filterModel;

            if (StringUtils.isNotEmpty(queryModel.getDealStatus())) {
                sbwhere.append(" and deal_status =" + queryModel.getRecordStatus());
            }

            if (StringUtils.isNotEmpty(queryModel.getTransportationType())) {
                sbwhere.append(" and transportation_type =" + queryModel.getTransportationType());
            }

            if (null != queryModel && StringUtils.isNotEmpty(queryModel.getContractId())) {
                sbwhere.append(" and contract_id like '%" + queryModel.getContractId() + "%'");
            }

            if (null != queryModel && StringUtils.isNotEmpty(queryModel.getGoodsName())) {
                sbwhere.append(" and goods_name like '%" + queryModel.getGoodsName() + "%'");
            }

            if (null != queryModel && StringUtils.isNotEmpty(queryModel.getStartAuditDate())) {
                sbwhere.append(" and create_date >= '" + queryModel.getStartAuditDate() + "'");
            }

            if (null != queryModel && StringUtils.isNotEmpty(queryModel.getEndAuditDate())) {
                sbwhere.append(" and create_date <= '" + queryModel.getEndAuditDate() + "'");
            }
            filter.setFilter(sbwhere.toString());
            filter.setPk("goods_id");
            //货源列表
            List<LsdsGoods> list = dao.pagingLsdsGoods(filter);
            returnmodel.setTotal(filter.getTotal());
            returnmodel.setModel(list);
        } catch (Exception ex) {
            String errMsg = "分页获取货源编号  货源信息表信息异常:" + ex.getMessage();
            returnmodel.setErrMsg(errMsg);
            returnmodel.setSucceed(false);
            LogHelper.writeError(errMsg, ex);
        }
        return returnmodel;
    }
}
