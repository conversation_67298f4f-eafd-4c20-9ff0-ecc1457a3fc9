package com.wanlianyida.lsds.domain.service;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.isoftstone.hig.common.model.PagingProceduresInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.LogHelper;
import com.isoftstone.hig.common.utils.SQLUtils;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsPlan;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsPlanFilter;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsGoodsPlanMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 服务公共类主要实现业务规则，处理业务逻辑 可以再进行创建多个，这主要为了方便一起生成
 *
 * <AUTHOR>
 */
@Service
public class LsdsGoodsPlanDomainService {

    /**
     * 分页获取方案ID信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @param token    预留要传递的token，服务端进行判断权限功能
     * @return {@code  ResultMode<LsdsGoodsPlan>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为方案ID实体类LsdsGoodsPlan列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsGoodsPlan> pagingLsdsGoodsPlan(PagingInfo<LsdsGoodsPlanFilter> pageInfo, String token) {

        ResultMode<LsdsGoodsPlan> returnmodel = new ResultMode<LsdsGoodsPlan>();
        try {
            //过滤SQL参数
            SQLUtils.filterParam(pageInfo.filterModel);

            LsdsGoodsPlanMapper dao = SpringContextUtil.getBeanByClass(LsdsGoodsPlanMapper.class);
            PagingProceduresInfo filter = new PagingProceduresInfo();
            filter.setTables("lsds_goods_plan");
            filter.setFields("plan_id,offer_split_id,start_site_city_code,start_site_city_name,start_site_address,end_site_city_code,end_site_city_name,end_site_address,transportation_type,expect_start_date,expect_end_date,create_by,create_date,modify_by,modify_date,item1,item2,item3,item4");
            filter.setFilter("");
            filter.setCountTotal(pageInfo.getCountTotal());
            filter.setPageNumber(pageInfo.currentPage);
            filter.setPageSize(pageInfo.pageLength);
            StringBuilder sbwhere = new StringBuilder(" 1 = 1 ");
            LsdsGoodsPlanFilter queryModel = pageInfo.filterModel;
            if (StringUtils.isNotEmpty(queryModel.getPlanId())) {
                sbwhere.append(" and plan_id like '%" + queryModel.getPlanId() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getOfferSplitId())) {
                sbwhere.append(" and offer_split_id like '%" + queryModel.getOfferSplitId() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getStartSiteCityCode())) {
                sbwhere.append(" and start_site_city_code like '%" + queryModel.getStartSiteCityCode() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getStartSiteCityName())) {
                sbwhere.append(" and start_site_city_name like '%" + queryModel.getStartSiteCityName() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getStartSiteAddress())) {
                sbwhere.append(" and start_site_address like '%" + queryModel.getStartSiteAddress() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getEndSiteCityCode())) {
                sbwhere.append(" and end_site_city_code like '%" + queryModel.getEndSiteCityCode() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getEndSiteCityName())) {
                sbwhere.append(" and end_site_city_name like '%" + queryModel.getEndSiteCityName() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getEndSiteAddress())) {
                sbwhere.append(" and end_site_address like '%" + queryModel.getEndSiteAddress() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getTransportationType())) {
                sbwhere.append(" and transportation_type =" + queryModel.getTransportationType());
            }
            if (StringUtils.isNotEmpty(queryModel.getCreateBy())) {
                sbwhere.append(" and create_by like '%" + queryModel.getCreateBy() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getModifyBy())) {
                sbwhere.append(" and modify_by like '%" + queryModel.getModifyBy() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getItem1())) {
                sbwhere.append(" and item1 like '%" + queryModel.getItem1() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getItem2())) {
                sbwhere.append(" and item2 like '%" + queryModel.getItem2() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getItem3())) {
                sbwhere.append(" and item3 like '%" + queryModel.getItem3() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getItem4())) {
                sbwhere.append(" and item4 like '%" + queryModel.getItem4() + "%'");
            }
            filter.setFilter(sbwhere.toString());
            filter.setPk("plan_id");
            List<LsdsGoodsPlan> list = dao.pagingLsdsGoodsPlan(filter);
            returnmodel.setTotal(filter.getTotal());
            returnmodel.setModel(list);

        } catch (Exception ex) {
            String errMsg = "分页获取方案ID信息异常:" + ex.getMessage();
            returnmodel.setErrMsg(errMsg);
            LogHelper.writeError(errMsg, ex);
        }
        return returnmodel;
    }
}
