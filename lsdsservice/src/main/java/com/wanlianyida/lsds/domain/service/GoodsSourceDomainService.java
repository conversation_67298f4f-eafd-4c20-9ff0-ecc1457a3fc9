package com.wanlianyida.lsds.domain.service;

import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.lsds.domain.model.bo.GoodsSourceDetailBO;
import com.wanlianyida.lsds.domain.model.bo.GoodsSourceListBO;
import com.wanlianyida.lsds.domain.model.condition.GoodsSourceListCondition;
import com.wanlianyida.lsds.domain.model.entity.GoodsSourceEntity;
import com.wanlianyida.lsds.domain.repository.GoodsDetailRepository;
import com.wanlianyida.lsds.domain.repository.GoodsListRepository;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.domain.service.detail.CommonDetailHandler;
import com.wanlianyida.lsds.infrastructure.factory.GoodsOperHandlerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月14日 10:53
 */
@Slf4j
@Service
public class GoodsSourceDomainService {

    @Resource
    private GoodsDetailRepository goodsDetailRepository;

    @Resource
    private GoodsListRepository goodsListRepository;
    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;
    /**
     * 查询货源详情
     */
    public GoodsSourceDetailBO queryDetail(String goodsId) {
        GoodsSourceEntity goodsSource = goodsDetailRepository.queryGoodsSourceByGoodsId(goodsId);
        if (ObjUtil.isNull(goodsSource)) {
            return null;
        }

        CommonDetailHandler handler = GoodsOperHandlerFactory.getHandler(CommonDetailHandler.class, goodsSource.getBizModelType());
        return handler.queryDetail(goodsSource);
    }

    public GoodsSourceEntity queryGoodsSourceByGoodsId(String goodsId) {
        return goodsDetailRepository.queryGoodsSourceByGoodsId(goodsId);
    }
    /**
     * 获取货源列表
     */
    public List<GoodsSourceListBO> queryList(PagingInfo<GoodsSourceListCondition> pageInfo) {
        return goodsListRepository.queryList(pageInfo);
    }

    /**
     * 获取货源总数
     */
    public int queryCount(PagingInfo<GoodsSourceListCondition> pageInfo) {
        return goodsListRepository.queryCount(pageInfo);
    }

    /**
     * 列表货源发布
     */
    public void publishOneClick(String goodsId) {
         goodsOperatorRepository.updateStatus(goodsId);
    }
}
