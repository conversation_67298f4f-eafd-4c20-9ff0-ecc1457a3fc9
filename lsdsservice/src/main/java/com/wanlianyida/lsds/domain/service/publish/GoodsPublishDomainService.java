package com.wanlianyida.lsds.domain.service.publish;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.wanlianyida.lsds.application.model.command.publish.PublishGoodsSourceAgainCommand;
import com.wanlianyida.lsds.application.model.command.publish.PublishGoodsSourceCommand;
import com.wanlianyida.lsds.application.model.dto.PublishGoodsSourceDTO;
import com.wanlianyida.lsds.domain.model.bo.GoodsPublishBO;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.domain.service.publish.async.GoodsPublishAsyncManager;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

import javax.annotation.Resource;

/**
 * 货源发布 Domain Service
 */
@Slf4j
public abstract class GoodsPublishDomainService {
    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;

    @Resource
    private GoodsPublishAsyncManager goodsPublishAsyncManager;

    /**
     * 发布货源（新增）
     */
    public ResultMode<PublishGoodsSourceDTO> publish(PublishGoodsSourceCommand command, TokenInfo tokenInfo) {

        //1、参数校验
        ResultMode checkParamResult = checkParam(command);
        if (!checkParamResult.isSucceed()) {
            return checkParamResult;
        }
        //2、数据封装
        GoodsPublishBO goodsPublishBO = new GoodsPublishBO();
        goodsPublishBO.setTokenInfo(tokenInfo);
        goodsPublishBO.setGoodsSourceCommand(command);
        goodsPublishBO.setNow(new Date());
        goodsPublishBO.setInsertFlag(true);
        dataAssembly(goodsPublishBO);

        //3、数据入库
        goodsOperatorRepository.insert(goodsPublishBO);

        //4、触发异步处理
        goodsPublishAsyncManager.executeAsyncProcessors(goodsPublishBO);

        return ResultMode.success(BeanUtil.toBean(goodsPublishBO.getGoodsSourceEntity(), PublishGoodsSourceDTO.class));
    }

    /**
     * 发布货源（修改）
     */
    public ResultMode<PublishGoodsSourceDTO> publishAgain(PublishGoodsSourceAgainCommand command, TokenInfo tokenInfo) {

        //1、参数校验
        ResultMode checkParamResult = checkParam(command);
        if (!checkParamResult.isSucceed()) {
            return checkParamResult;
        }
        //2、数据封装
        GoodsPublishBO goodsPublishBO = new GoodsPublishBO();
        goodsPublishBO.setGoodsSourceCommand(command);
        goodsPublishBO.setTokenInfo(tokenInfo);
        goodsPublishBO.setGoodsId(command.getGoodsId());
        goodsPublishBO.setNow(new Date());
        dataAssembly(goodsPublishBO);

        //3、数据入库
        goodsOperatorRepository.update(goodsPublishBO);
        //4、触发异步处理
        goodsPublishAsyncManager.executeAsyncProcessors(goodsPublishBO);
        return ResultMode.success(BeanUtil.toBean(goodsPublishBO.getGoodsSourceEntity(), PublishGoodsSourceDTO.class));
    }


    /**
     * 数据封装
     */
    public abstract void dataAssembly(GoodsPublishBO bo);


    /**
     * 数据校验
     */
    public abstract ResultMode checkParam(PublishGoodsSourceCommand command);

}
