package com.wanlianyida.lsds.domain.service.operate;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.lsds.api.entity.GoodsKafkaNotice;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.wanlianyida.lsds.domain.model.bo.GoodsOperateBO;
import com.wanlianyida.lsds.domain.model.entity.GoodsSourceEntity;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.infrastructure.enums.GoodsDealStatusEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsOperateTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsPublishTypeEnum;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 删除货源
 * @Date 2025年05月14日 14:46
 */
@Component
public class GoodsDeleteHandler implements GoodsOperateStrategy {

    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;
    @Resource
    private LsdsKafkaSender lsdsKafkaSender;

    @Override
    public ResultMode<?> operate(GoodsOperateBO goodsOperateBO) {
        GoodsSourceEntity goodsSource = goodsOperateBO.getGoodsSource();
        if (GoodsPublishTypeEnum.COMPANY.getType().equals(goodsSource.getPublisherType())) {
            boolean verify = goodsSource.getDealStatus().equals(GoodsDealStatusEnum.WAITING_PUBLISH.getType()) || goodsSource.getDealStatus().equals(GoodsDealStatusEnum.AUDIT_REJECT.getType()) || goodsSource.getDealStatus().equals(GoodsDealStatusEnum.EXPIRED.getType()) || goodsSource.getDealStatus().equals(GoodsDealStatusEnum.CANCELED.getType());
            if (!verify) {
                return ResultMode.fail("货源当前状态不允许删除！");
            }
        }
        boolean result = goodsOperatorRepository.deleteByGoodsId(goodsSource.getGoodsId());
        if (!result) {
            return ResultMode.fail("删除货源失败");
        }
        SpringUtil.getBean(this.getClass()).syncOperate(goodsSource.getGoodsId());
        // 企业货源
        if (GoodsPublishTypeEnum.COMPANY.getType().equals(goodsSource.getPublisherType())) {
            SpringUtil.getBean(this.getClass()).sendOmsMessage(goodsSource);
        }
        return ResultMode.success();
    }

    public void syncOperate(String goodsId) {
        // 广播通知货源状态变更
        GoodsKafkaNotice goodsKafkaNotice = new GoodsKafkaNotice();
        goodsKafkaNotice.setGoodsId(goodsId);
        goodsKafkaNotice.setDealStatus(Constants.GOODS_STATUS_DELETED);
        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(goodsKafkaNotice));
    }

    public void sendOmsMessage(GoodsSourceEntity goodsSource) {
        String topic = SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.LSDS_TO_OMS_ORDER_STATUS.getTopicName();
        List<Map<String, String>> mapList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("changeDealGoodsId", goodsSource.getGoodsId());
        map.put("orderId", goodsSource.getOrderId());
        //补传状态：推送订单归档失效消息
        map.put("dealStatus", LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus8.getCode());
        mapList.add(map);
        JSONObject json = new JSONObject();
        json.put("exlsdsOrderIds", mapList);
        lsdsKafkaSender.send(topic, json.toJSONString());
    }

    @Override
    public GoodsOperateTypeEnum getOperateType() {
        return GoodsOperateTypeEnum.DELETE;
    }
}
