package com.wanlianyida.lsds.domain.service.operate;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.LogHelper;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.lsds.api.entity.GoodsKafkaNotice;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsAttentionCancelFilter;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.msg.api.entity.MsgInfo;
import com.isoftstone.hig.msg.api.inter.SystemMsgInter;
import com.isoftstone.hig.platform.api.client.PlatformCommonInterClient;
import com.isoftstone.hig.platform.api.entity.PlatformUmCompany;
import com.isoftstone.hig.platform.api.entity.PlatformUmUserbaseinfo;
import com.isoftstone.hig.platform.api.filter.PlatformUmCompanyFilter;
import com.wanlianyida.lsds.domain.model.bo.GoodsExpiredDataBO;
import com.wanlianyida.lsds.domain.model.bo.GoodsOperateBO;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.infrastructure.enums.DealStatusEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsOperateTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsPublishTypeEnum;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 货源过期
 * @Date 2025年05月14日 14:46
 */
@Slf4j
@Component
public class GoodsExpireHandler implements GoodsOperateStrategy {

    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;
    @Resource
    private PlatformCommonInterClient platformCommonInterClient;
    @Resource
    private SystemMsgInter systemMsgInter;
    @Resource
    private LsdsKafkaSender kafkaSender;

    @Override
    public ResultMode<?> operate(GoodsOperateBO goodsOperateBO) {
        List<GoodsExpiredDataBO> goodsSourceList = goodsOperatorRepository.queryExpiredGoods();
        if (CollectionUtils.isEmpty(goodsSourceList)) {
            log.info("【货源过期处理】暂无需要处理的数据！");
            return ResultMode.success();
        }
        // 查询子货源
        List<String> goodsIdList = goodsSourceList.stream().map(GoodsExpiredDataBO::getChangeDealGoodsId).collect(Collectors.toList());
        List<GoodsExpiredDataBO> childGoodsSourceList = goodsOperatorRepository.queryExpiredChildGoods(goodsIdList);
        if (CollectionUtil.isNotEmpty(childGoodsSourceList)) {
            goodsSourceList.addAll(childGoodsSourceList);
        }
        Map<String, List<GoodsExpiredDataBO>> goodsSourceMapping = goodsSourceList.stream().collect(Collectors.groupingBy(GoodsExpiredDataBO::getPublisherType));
        goodsOperatorRepository.batchExpiredGoods(goodsSourceMapping);
        // 异步操作
        handlerCompanyGoods(goodsSourceMapping.get(GoodsPublishTypeEnum.COMPANY.getType()));
        handlerDriverGoods(goodsSourceMapping.get(GoodsPublishTypeEnum.DRIVER.getType()));
        return ResultMode.success();
    }

    /**
     * 处理企业货源
     */
    public void handlerCompanyGoods(List<GoodsExpiredDataBO> goodsSourceList) {
        if (CollectionUtils.isEmpty(goodsSourceList)) {
            return;
        }
        sendInnerMessage(goodsSourceList);
        sendOmsMessage(goodsSourceList);
    }

    /**
     * 处理司机货源
     */
    public void handlerDriverGoods(List<GoodsExpiredDataBO> goodsSourceList) {
        if (CollectionUtils.isEmpty(goodsSourceList)) {
            return;
        }
        sendInnerMessage(goodsSourceList);
    }

    private void sendInnerMessage(List<GoodsExpiredDataBO> goodsSourceList) {
        for (GoodsExpiredDataBO bo : goodsSourceList) {
            Map<String, String> param = new HashMap<>();
            String phoneNumber = bo.getSendPhoneNumber();
            String userId = bo.getCreateBy();
            String companyId = bo.getCompanyId();
            String templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_EXPIRED.getCode();
            param.put("goodsNo", bo.getChangeDealGoodsId());
            param.put("sendShortName", bo.getSendAddrShortName());
            param.put("receiveShortName", bo.getReceiveAddrShortName());
            if (GoodsPublishTypeEnum.COMPANY.getType().equals(bo.getPublisherType())) {
                //发送站内信
                sendMsg(phoneNumber, userId, companyId, templateId, param);
                // 发送异步消息
                sendNoticeMessage(bo.getChangeDealGoodsId(), DealStatusEnum.EXPIRED.getCode());
            } else {
                if ("20".equals(bo.getDealStatus()) || "30".equals(bo.getDealStatus())) {
                    //发送站内信
                    sendMsg(phoneNumber, userId, companyId, templateId, param);
                }
                // 发送异步消息
                sendNoticeMessage(bo.getChangeDealGoodsId(), Constants.GOODS_STATUS_EXPIRE);
                // 司机货源过期时,需要取消货源关注信息
                sendAttentionMessage(bo.getChangeDealGoodsId());
            }
        }
    }

    public void sendMsg(String phoneNumber, String userId, String companyId, String templateId, Map<String, String> param) {
        try {
            PlatformUmCompanyFilter companyFilter = new PlatformUmCompanyFilter();
            companyFilter.setCompanyId(companyId);
            ResultMode<PlatformUmCompany> companyResultMode = platformCommonInterClient.getCompanyAndAdminInfoByCompanyId(companyFilter);
            String parentUserId = companyResultMode.getModel().get(0).getExUserBaseId();
            Set<String> receiverList = new HashSet<>(2);
            if (!StringUtils.isEmpty(userId)) {
                receiverList.add(userId);
            }
            if (!StringUtils.isEmpty(parentUserId) && !parentUserId.equals(userId)) {
                receiverList.add(parentUserId);
            }
            //系统消息埋点,发送审核提交成功消息
            MsgInfo msgInfo = new MsgInfo();
            // 发送人 系统
            msgInfo.setSender("1");
            // 接收人的手机号
            //msgInfo.setPhone(phoneNumber);
            // 设置站内信接收人
            msgInfo.setReceiverList(receiverList);
            msgInfo.setTemplateId(templateId);
            //参数信息
            String paramStr = JSON.toJSONString(param);
            msgInfo.setTemplateParameter(paramStr);
            //将要发送的用户查询出手机号 begin by huchuyin
            Set<String> phoneList = new HashSet<String>();
            if (!CollectionUtils.isEmpty(receiverList)) {
                for (String receiverId : receiverList) {
                    try {
                        log.info("根据用户ID查询用户信息参数：{}", receiverId);
                        ResultMode<PlatformUmUserbaseinfo> resultMode = platformCommonInterClient.getUserBaseInfoByUserBaseId(receiverId);
                        log.info("根据用户ID查询用户信息参数：{}", JSON.toJSONString(resultMode));
                        if (resultMode != null && resultMode.getSucceed()
                            && !CollectionUtils.isEmpty(resultMode.getModel())) {
                            PlatformUmUserbaseinfo userbaseinfo = resultMode.getModel().get(0);
                            if (userbaseinfo != null) {
                                phoneList.add(userbaseinfo.getTelephone());
                            }
                        }
                    } catch (Exception e) {
                        LogHelper.writeError("根据用户ID查询用户信息异常", e);
                    }
                }
            }
            msgInfo.setPhoneList(phoneList);
            //将要发送的用户查询出手机号 end
            systemMsgInter.send(msgInfo);
        } catch (Exception e) {
            LogHelper.writeError("方法【sendMsg】发送消息出现异常：", e);
        }
    }

    private void sendOmsMessage(List<GoodsExpiredDataBO> goodsSourceList) {
        String topic = SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.LSDS_TO_OMS_ORDER_STATUS.getTopicName();
        JSONObject json = new JSONObject();
        json.put("exlsdsOrderIds", goodsSourceList);
        kafkaSender.send(topic, json.toJSONString());
        log.info("货源过期，通知oms系统成功 。内容如下：{}", json);
    }

    /**
     * 货源状态消息
     */
    private void sendNoticeMessage(String id, String dealStatus) {
        GoodsKafkaNotice goodsKafkaNotice = new GoodsKafkaNotice();
        goodsKafkaNotice.setGoodsId(id);
        goodsKafkaNotice.setDealStatus(dealStatus);
        kafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(goodsKafkaNotice));
    }

    /**
     * 关注货源消息
     */
    private void sendAttentionMessage(String goodsId) {
        LsdsGoodsAttentionCancelFilter attentionCancelFilter = new LsdsGoodsAttentionCancelFilter();
        attentionCancelFilter.setOperType("2");
        attentionCancelFilter.setGoodsId(goodsId);
        String topic = SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.AUTO_CANCEL_GOODS_ATTENTION.getTopicName();
        kafkaSender.send(topic, JSONUtil.toJsonStr(attentionCancelFilter));
    }

    @Override
    public GoodsOperateTypeEnum getOperateType() {
        return GoodsOperateTypeEnum.EXPIRE;
    }
}
