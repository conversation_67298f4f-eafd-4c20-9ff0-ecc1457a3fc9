package com.wanlianyida.lsds.domain.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.BeanConverter;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.lsds.api.command.LsdsMatchmakingConfigAddCommand;
import com.isoftstone.hig.lsds.api.command.LsdsMatchmakingConfigUpdateCommand;
import com.isoftstone.hig.lsds.api.dto.LsdsMatchmakingConfigDTO;
import com.isoftstone.hig.lsds.api.dto.LsdsMatchmakingRecordDTO;
import com.isoftstone.hig.lsds.api.entity.LsdsMatchmakingConfig;
import com.isoftstone.hig.lsds.api.entity.LsdsMatchmakingRecord;
import com.isoftstone.hig.lsds.api.enums.MatchmakingActionType;
import com.isoftstone.hig.lsds.api.enums.MatchmakingTrackService;
import com.isoftstone.hig.lsds.api.query.LsdsMatchmakingConfigQuery;
import com.isoftstone.hig.lsds.api.query.LsdsMatchmakingRecordQuery;
import com.isoftstone.hig.tms.api.entity.TmsEnum;
import com.isoftstone.hig.tms.api.entity.TmsOrder;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.exchange.TmsExchangeService;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsMatchmakingRecordMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.MatchmakingConfigMapper;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import static com.isoftstone.hig.lsds.api.util.Constants.MATCHMAKING_GOODS_CHANGE_TOPIC;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年11月14日 19:52
 */
@Slf4j
@Service
public class LsdsMatchmakingDomainService {

    @Resource
    private MatchmakingConfigMapper matchmakingConfigMapper;
    @Resource
    private LsdsMatchmakingRecordMapper matchmakingRecordMapper;
    @Resource
    private TmsExchangeService tmsExchangeService;

    @Autowired
    private LsdsKafkaSender lsdsKafkaSender;

    /**
     * 新增配置
     */
    public void addConfig(LsdsMatchmakingConfigAddCommand command) {
        LsdsMatchmakingConfigQuery query = new LsdsMatchmakingConfigQuery();
        query.setBusId(command.getBusId());
        JwtUtil jwtUtil = JwtUtil.getInstance();
        query.setCompanyId(jwtUtil.getCompanyIdByToken());
        if (matchmakingConfigMapper.queryConfig(query) != null) {
            throw new LsdsWlydException("当前单号已添加配置");
        }
        String busId = command.getBusId();
        // 订单校验
        if (command.getConfigType().equals(20)) {
            if (busId.startsWith("POX") || busId.startsWith("POZ")) {
                throw new LsdsWlydException("当前订单类型不支持撮合设置");
            }
            TmsOrder tmsOrder = tmsExchangeService.getTmsOrder(busId);
            if (tmsOrder == null) {
                throw new LsdsWlydException("当前订单不存在");
            }
            if (TmsEnum.TmsorderOrderStatusEnum.orderappointcarrier8.getCode().equals(tmsOrder.getOrderStatus()) || new Date().compareTo(tmsOrder.getOrderExpireDate()) > 0) {
                throw new LsdsWlydException("当前订单状态不允许操作");
            }
        }

        LsdsMatchmakingConfig save = BeanConverter.toBean(command, LsdsMatchmakingConfig.class);
        save.setPriceTax(command.getPrice());
        save.setTaxRatio(BigDecimal.ZERO);
        save.setCompanyId(jwtUtil.getCompanyIdByToken());
        save.setCreatorId(jwtUtil.getUserBaseIdByToken());
        save.setUpdaterId(jwtUtil.getCompanyIdByToken());
        Date now = new Date();
        save.setCreatedDate(now);
        save.setUpdatedDate(now);
        if (matchmakingConfigMapper.addConfig(save) == 0) {
            throw new LsdsWlydException("配置添加失败");
        }
        //添加完订单发消息
        if(!StrUtil.startWith(command.getBusId(),"DFQ")){
            lsdsKafkaSender.send(MATCHMAKING_GOODS_CHANGE_TOPIC,command.getBusId());
        }
    }

    /**
     * 修改配置
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateConfig(LsdsMatchmakingConfigUpdateCommand command) {
        LsdsMatchmakingConfigQuery query = new LsdsMatchmakingConfigQuery();
        query.setBusId(command.getBusId());
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        query.setCompanyId(tokenInfo.getCompanyId());
        LsdsMatchmakingConfigDTO matchmakingConfig = matchmakingConfigMapper.queryConfig(query);
        if (matchmakingConfig == null) {
            throw new LsdsWlydException("未查询到当前配置");
        }
        LsdsMatchmakingConfig save = BeanConverter.toBean(command, LsdsMatchmakingConfig.class);
        save.setCompanyId(tokenInfo.getCompanyId());
        save.setPriceTax(save.getPrice());
        save.setUpdaterId(tokenInfo.getUserBaseId());
        save.setUpdatedDate(new Date());
        save.setId(matchmakingConfig.getId());
        matchmakingConfigMapper.updateConfig(save);
        // 保存操作记录
        saveRecord(matchmakingConfig, save, tokenInfo);
    }

    private void saveRecord(LsdsMatchmakingConfigDTO matchmakingConfig, LsdsMatchmakingConfig save, TokenInfo tokenInfo) {
        StringBuilder builder = new StringBuilder();
        if (matchmakingConfig.getPrice().compareTo(save.getPrice()) != 0) {
            getRemark(matchmakingConfig.getPrice().toString(), save.getPrice().toString(), builder, "指定单价");
        }
        if (matchmakingConfig.getExpireDate().compareTo(save.getExpireDate()) != 0) {
            getRemark(DateUtil.format(matchmakingConfig.getExpireDate(), "yyyy-MM-dd"), DateUtil.format(save.getExpireDate(), "yyyy-MM-dd"), builder, "撮合有效期");
        }
        if (!matchmakingConfig.getLoadMobile().equals(save.getLoadMobile())) {
            getRemark(matchmakingConfig.getLoadName() + "/" + matchmakingConfig.getLoadMobile(), save.getLoadName() + "/" + save.getLoadMobile(), builder, "装货联系人");
        }
        if (!matchmakingConfig.getSettlementMobile().equals(save.getSettlementMobile())) {
            getRemark(matchmakingConfig.getSettlementName() + "/" + matchmakingConfig.getSettlementMobile(), save.getSettlementName() + "/" + save.getSettlementMobile(), builder, "结算联系人");
        }
        if (!matchmakingConfig.getTrackService().equals(save.getTrackService())) {
            getRemark(MatchmakingTrackService.getEnum(matchmakingConfig.getTrackService()).getDesc(), MatchmakingTrackService.getEnum(save.getTrackService()).getDesc(), builder, "运输跟踪服务");
        }
        if ((StrUtil.isBlank(matchmakingConfig.getTransportRequire()) && StrUtil.isNotBlank(save.getTransportRequire())) ||
            StrUtil.isNotBlank(matchmakingConfig.getTransportRequire()) && !matchmakingConfig.getTransportRequire().equals(save.getTransportRequire())) {
            getRemark(matchmakingConfig.getTransportRequire(), save.getTransportRequire(), builder, "运输要求");
        }
        LsdsMatchmakingRecord record = new LsdsMatchmakingRecord();
        record.setRemark(builder.toString());
        record.setMatchmakingId(save.getId());
        record.setActionType(MatchmakingActionType.UPDATE.getCode());
        record.setOperateUsername(tokenInfo.getLoginName());
        record.setOperateName(tokenInfo.getUsername());
        Date now = new Date();
        record.setOperateTime(now);
        record.setCreatedDate(now);
        record.setUpdatedDate(now);
        record.setCreatorId(tokenInfo.getUserBaseId());
        record.setUpdaterId(tokenInfo.getUserBaseId());
        matchmakingRecordMapper.save(record);
    }

    private void getRemark(String oldValue, String newValue, StringBuilder builder, String paramName) {
        if (StrUtil.isBlank(oldValue) && StrUtil.isBlank(newValue)) {
            return;
        }
        builder.append("更改了(");
        builder.append(paramName);
        builder.append(")");
        if (StrUtil.isNotBlank(oldValue)) {
            builder.append(", 更改前：");
            builder.append(oldValue);
        }
        builder.append(", 调整后：");
        builder.append(StrUtil.isNotBlank(newValue) ? newValue : "空");
        builder.append("; ");
    }

    /**
     * 查询配置
     */
    public LsdsMatchmakingConfigDTO queryConfig(LsdsMatchmakingConfigQuery query) {
        return matchmakingConfigMapper.queryConfig(query);
    }

    /**
     * 查询配置操作记录
     */
    public ResultMode<LsdsMatchmakingRecordDTO> queryConfigRecord(PagingInfo<LsdsMatchmakingRecordQuery> query) {
        Page<LsdsMatchmakingRecordDTO> page = PageHelper.startPage(query.currentPage, query.pageLength, query.getCountTotal());
        List<LsdsMatchmakingRecordDTO> result = matchmakingRecordMapper.queryConfigRecord(query.filterModel);
        return ResultMode.successPageList(result, (int) page.getTotal());
    }
}
