package com.wanlianyida.lsds.domain.service.operate;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.lsds.domain.model.bo.GoodsOperateBO;
import com.wanlianyida.lsds.domain.service.GoodsNextOfferDomainService;
import com.wanlianyida.lsds.infrastructure.enums.GoodsOperateTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 下一轮报价
 * @Date 2025年05月14日 14:46
 */
@Component
public class GoodsNextOfferHandler implements GoodsOperateStrategy {

    @Resource
    private GoodsNextOfferDomainService goodsNextOfferDomainService;

    @Override
    public ResultMode<?> operate(GoodsOperateBO goodsOperateBO) {
        return goodsNextOfferDomainService.handleBiz(goodsOperateBO.getGoodsNextOfferParamBO());
    }

    @Override
    public GoodsOperateTypeEnum getOperateType() {
        return GoodsOperateTypeEnum.NEXT_OFFER;
    }
}
