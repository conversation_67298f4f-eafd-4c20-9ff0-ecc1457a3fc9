package com.wanlianyida.lsds.domain.service.operate;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.GoodsKafkaNotice;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.wanlianyida.lsds.domain.model.bo.GoodsOperateBO;
import com.wanlianyida.lsds.domain.model.bo.GoodsSourceQuantityBO;
import com.wanlianyida.lsds.domain.model.bo.GoodsUpdateBO;
import com.wanlianyida.lsds.domain.model.entity.GoodsSourceEntity;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.infrastructure.enums.GoodsDealStatusEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsOperateTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsPublishTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsStatusEnum;
import com.wanlianyida.lsds.infrastructure.repository.mapper.DriverGoodsMapper;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月16日 10:53
 */
@Slf4j
@Service
public class GoodsAbatementHandler implements GoodsOperateStrategy {

    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;
    @Resource
    private DriverGoodsMapper driverGoodsMapper;
    @Resource
    private LsdsKafkaSender lsdsKafkaSender;

    @Override
    public ResultMode<?> operate(GoodsOperateBO goodsOperateBO) {
        GoodsSourceQuantityBO goodsSourceQuantity = goodsOperateBO.getGoodsSourceQuantity();
        GoodsSourceEntity goodsSource = goodsOperatorRepository.queryByGoodsId(goodsSourceQuantity.getGoodsId());
        if (goodsSource == null || !GoodsPublishTypeEnum.DRIVER.getType().equals(goodsSource.getPublisherType())) {
            log.info("非司机货源不处理！");
            return null;
        }
        GoodsUpdateBO updateBO = assemblerDriverGoods(goodsSourceQuantity, goodsSource);
        goodsOperatorRepository.updateByGoodsId(updateBO);

        SpringUtil.getBean(this.getClass()).asyncOperate(goodsSourceQuantity.getGoodsId());
        return null;
    }

    public GoodsUpdateBO assemblerDriverGoods(GoodsSourceQuantityBO goodsSourceQuantity, GoodsSourceEntity goodsSource) {
        GoodsUpdateBO updateBO = new GoodsUpdateBO();
        GoodsSourceEntity goodsSourceUpdate = new GoodsSourceEntity();
        BigDecimal updateSubQuantity = goodsSourceQuantity.getUpdateSubQuantity();
        BigDecimal subtract = goodsSource.getRemainingQuantity().subtract(updateSubQuantity);
        goodsSourceUpdate.setId(goodsSource.getId());
        goodsSourceUpdate.setGoodsId(goodsSource.getGoodsId());
        BigDecimal quantity = subtract.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : subtract;
        goodsSourceUpdate.setRemainingQuantity(quantity);
        goodsSourceUpdate.setUpdatedDate(new Date());
        updateBO.setGoodsSource(goodsSourceUpdate);
        DriverGoods driverGoods = new DriverGoods();
        driverGoods.setId(goodsSource.getGoodsId());
        driverGoods.setGoodsId(goodsSource.getGoodsId());
        driverGoods.setRemainingQuantity(quantity);
        if (subtract.compareTo(BigDecimal.ZERO) <= 0) {
            driverGoods.setDealStatus(Constants.GOODS_STATUS_DEAL);
            goodsSourceUpdate.setDealStatus(GoodsDealStatusEnum.TRADED.getType());
            goodsSourceUpdate.setGoodsStatus(GoodsStatusEnum.DEFAULT.getCode());
        }
        updateBO.setDriverGoods(driverGoods);
        return updateBO;
    }

    public void asyncOperate(String goodsId) {
        DriverGoods driverGoods = driverGoodsMapper.getByGoodsId(goodsId);
        GoodsKafkaNotice goodsKafkaNotice = new GoodsKafkaNotice();
        goodsKafkaNotice.setGoodsId(driverGoods.getGoodsId());
        goodsKafkaNotice.setDealStatus(driverGoods.getDealStatus());
        log.info("【货源数量扣减】消息发送：{}", goodsKafkaNotice);
        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(goodsKafkaNotice));
    }

    @Override
    public GoodsOperateTypeEnum getOperateType() {
        return GoodsOperateTypeEnum.ABATEMENT;
    }
}
