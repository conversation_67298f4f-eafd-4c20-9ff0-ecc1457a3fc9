package com.wanlianyida.lsds.domain.service.publish.paramverif;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.mvcvo.PrepayReqVO;
import com.isoftstone.hig.lsds.api.mvcvo.PrepayResVO;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.platform.api.entity.PlatformCmPlatformParameter;
import com.isoftstone.hig.qrs.api.constants.QrsEnum;
import com.isoftstone.hig.tms.api.common.valid.StringUtils;
import com.isoftstone.hig.tms.api.enums.AdvancePaymentFlagEnum;
import com.wanlianyida.lsds.application.model.command.publish.AssignSupplierCommand;
import com.wanlianyida.lsds.application.model.command.publish.DriverGoodsSourceCommand;
import com.wanlianyida.lsds.application.model.command.publish.IdentifyCodeCommand;
import com.wanlianyida.lsds.application.model.command.publish.PublishGoodsSourceCommand;
import com.wanlianyida.lsds.application.service.PrepayAppService;
import com.wanlianyida.lsds.infrastructure.enums.EnquiryRangeEnum;
import com.wanlianyida.lsds.infrastructure.enums.FreightTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.PremiumServStatusEnum;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 司机货源参数校验
 */
@Slf4j
@Component
public class DriverParamVerif extends CommonParamVerif {
    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private PrepayAppService prepayService;

    public ResultMode checkParam(PublishGoodsSourceCommand command) {
        //公共参数校验
        ResultMode resultMode = checkCommonParam(command);
        if (!resultMode.getSucceed()) {
            return resultMode;
        }

        DriverGoodsSourceCommand driverGoods = command.getDriverGoods();
        if (org.apache.commons.lang3.StringUtils.equals(command.getEnquiryRange(), Constants.ENQUIRY_RANGE_OWN_DRIVERS)) {
            if (IterUtil.isEmpty(driverGoods.getQrsCodeRelations())) {
                return ResultMode.fail("询价范围为自有司机,货源码为必填！");
            }
            IdentifyCodeCommand identifyCode = driverGoods.getQrsCodeRelations().stream().filter(item ->
                item.getIdentifyCodeType().equals(QrsEnum.IdentifyCodeTypeEnum.COMPANY_GOODS_CODE.getCode())).findFirst().orElse(null);
            if (identifyCode == null) {
                return ResultMode.fail("询价范围为自有司机,货源码为必填！");
            }
        }
        if (command.getFreightType().equals(Constants.FREIGHT_TYPE_NETWORK) && command.getFreightSurvivalRate() == null) {
            return ResultMode.fail("网络货运主体运费留存率不能为空!");
        }
        if (command.getFreightType().equals(Constants.FREIGHT_TYPE_NETWORK) && StringUtils.isBlank(command.getNetworkMainBodyName())) {
            return ResultMode.fail("网络货运主体名称不能为空!");
        }
        if (EnquiryRangeEnum.SPECIFIED_DRIVER.getCode().equals(command.getEnquiryRange()) && CollUtil.isEmpty(command.getAssignSupplierList())) {
            return ResultMode.fail("指定司机数据不能为空!");
        }
        for (AssignSupplierCommand supplier : command.getAssignSupplierList()) {
            if (supplier == null || StringUtils.isBlank(supplier.getDriverId())) {
                return ResultMode.fail("指定司机数据不能为空!");
            }
        }
        if (!PremiumServStatusEnum.isTypeExist(command.getDriverGoods().getPremiumServStatus())) {
            return ResultMode.fail("优享服务状态参数错误");
        }
        //非短倒货源的进行预付费校验
        if (org.apache.commons.lang3.StringUtils.equals(driverGoods.getAdvancePaymentFlag(), AdvancePaymentFlagEnum.USE.getCode())) {
            if (driverGoods != null && StrUtil.isNotBlank(command.getTransportationType()) && !StrUtil.equals(command.getTransportationType(), UtilityEnum.TransportationAllTypeEnum.INVERT_SHORT.getLsdsCode())) {
                //预付费的逻辑
                if ("0".equalsIgnoreCase(driverGoods.getAdvancePaymentFlag())) {
                    List<String> paraCodesDefaultLoad = new ArrayList();
                    paraCodesDefaultLoad.add("1052");
                    List<PlatformCmPlatformParameter> platformCmPlatformParameters = platformExchangeService.getByParaCodes(paraCodesDefaultLoad);
                    if (!org.springframework.util.CollectionUtils.isEmpty(platformCmPlatformParameters)) {
                        if ("1".equalsIgnoreCase(platformCmPlatformParameters.get(0).getParaValue())) {
                            PrepayResVO prepayResVO = checkPrepay(command);
                            if (null != prepayResVO) {
                                return ResultMode.fail("预付费用超额,最大值为" + prepayResVO.getPrepayMax() + ",请重新输入");
                            }
                        }
                    }
                }
            }
        }
        //公开询价、传统货运、非公路整车不支持使用优享服务
        if (StrUtil.equals(command.getEnquiryType(), LsdsEnum.LsdsgoodsEnquiryTypeEnum.enquirytype1.getCode())
            || StrUtil.equals(command.getFreightType(), FreightTypeEnum.TRADITIONAL_MODE.getCode())
            || !StrUtil.equals(command.getTransportationType(), "110")) {
            if (StrUtil.equals(driverGoods.getPremiumServStatus(), "10")) {
                return ResultMode.fail("公开询价、传统货运、非公路整车不支持使用优享服务");
            }
        }
        // TODO: 2025/5/24
        //短倒最小里程校验

        return ResultMode.success();
    }

    /**
     * 预付费参数校验
     */
    public PrepayResVO checkPrepay(PublishGoodsSourceCommand command) {
        PrepayReqVO prepayReqVO = PrepayReqVO.of();
        //结算类型【radio:10-按柜,20-按重量,30-按车】
        prepayReqVO.setChargeType(command.getTotalQuantityUnits());
        //基价
        prepayReqVO.setEnquiryTypeBasePrice(command.getEnquiryTypeBasePrice());
        //预付费金额
        if (command.getDriverGoods().getAdvancePayment() != null) {
            prepayReqVO.setPrePayMoney(command.getDriverGoods().getAdvancePayment().toString());
        }
        //询价方式【radio:10-公开询价,20-指定单价】
        prepayReqVO.setEnquiryType(command.getEnquiryType());
        if ("10".equalsIgnoreCase(command.getEnquiryType())) {
            prepayReqVO.setPlaceOrderOptFlag("20");
        }
        ResultMode<PrepayResVO> prepayResVOResultMode = prepayService.prepayCheck(prepayReqVO);
        if (!CollectionUtils.isEmpty(prepayResVOResultMode.getModel())) {
            if (prepayResVOResultMode.getModel().get(0).getOverLimit()) {
                return prepayResVOResultMode.getModel().get(0);
            }
        }
        return null;
    }
}
