package com.wanlianyida.lsds.domain.service;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.isoftstone.hig.lsds.api.entity.UserQrcode;
import com.isoftstone.hig.lsds.api.filter.UserQrcodeFilter;
import com.wanlianyida.lsds.infrastructure.repository.mapper.UserQrcodeMapper;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * 用户和货源码的关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@Service
public class UserQrcodeDomainService
{
    @Resource
    private UserQrcodeMapper userQrcodeMapper;

    /**
     * 查询用户和货源码的关系
     *
     * @param id 用户和货源码的关系ID
     * @return 用户和货源码的关系
     */

    public UserQrcode getById(String id)
    {
        UserQrcode userQrcode = userQrcodeMapper.getById(id);
        return userQrcode;
    }

    /**
     * 查询用户和货源码的关系列表
     *
     * @param userQrcodeFilter 用户和货源码的关系
     * @return 用户和货源码的关系
     */

    public List<UserQrcode> listByEntity(UserQrcodeFilter userQrcodeFilter)
    {
        List<UserQrcode> userQrcodes = userQrcodeMapper.listByEntity(userQrcodeFilter);
        return userQrcodes;
    }

    /**
     * 新增用户和货源码的关系
     *
     * @param userQrcode 用户和货源码的关系
     * @return 结果
     */

    public int save(UserQrcode userQrcode)
    {
        return userQrcodeMapper.save(userQrcode);
    }

    /**
     * 修改用户和货源码的关系
     *
     * @param userQrcodeFilter 用户和货源码的关系
     * @return 结果
     */

    public int updateById(UserQrcodeFilter userQrcodeFilter)
    {
        return userQrcodeMapper.modifyById(userQrcodeFilter);
    }

    /**
     * 删除用户和货源码的关系对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */

    public int removeByIds(String[] ids)
    {
        return userQrcodeMapper.removeByIds(ids);
    }

    /**
     * 删除用户和货源码的关系信息
     *
     * @param id 用户和货源码的关系ID
     * @return 结果
     */

    public int removeById(String id)
    {
        return userQrcodeMapper.removeById(id);
    }
    /**
     * 分页查询用户和货源码的关系列表
     *
     * @param pageInfo 用户和货源码的关系
     * @return 用户和货源码的关系
     */

    public List<UserQrcode> page(PagingInfo<UserQrcodeFilter> pageInfo)
    {
        List<UserQrcode> list = userQrcodeMapper.listByEntity(pageInfo.getFilterModel());
        return list;
    }

}
