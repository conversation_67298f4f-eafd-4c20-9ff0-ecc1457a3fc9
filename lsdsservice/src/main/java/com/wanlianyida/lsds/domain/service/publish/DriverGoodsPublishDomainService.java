package com.wanlianyida.lsds.domain.service.publish;

import com.isoftstone.hig.common.constants.PrefixCodeConstants;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.lsds.application.model.command.publish.PublishGoodsSourceCommand;
import com.wanlianyida.lsds.domain.assembler.PublishGoodsSourceCommonAssembler;
import com.wanlianyida.lsds.domain.assembler.PublishGoodsSourcePersonalizeAssembler;
import com.wanlianyida.lsds.domain.model.bo.GoodsPublishBO;
import com.wanlianyida.lsds.domain.model.entity.*;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.domain.service.publish.paramverif.DriverParamVerif;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.GoodsDeductibleBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * 司机货源审核
 */
@Slf4j
@Service("DriverGoodsPublish")
public class DriverGoodsPublishDomainService extends GoodsPublishDomainService {

    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;

    @Resource
    private DriverParamVerif driverParamVerif;

    @Override
    public ResultMode checkParam(PublishGoodsSourceCommand command) {
        return driverParamVerif.checkParam(command);
    }


    @Override
    public void dataAssembly(GoodsPublishBO bo) {
        //1、货源信息
        if (bo.getInsertFlag()) {
            bo.setGoodsId(goodsOperatorRepository.generateGoodsId(PrefixCodeConstants.GOODS_D));
        }
        GoodsSourceEntity goodsSourceEntity = PublishGoodsSourceCommonAssembler.generateGoodsSourceEntity(bo);
        //2、货源扩展信息
        GoodsExtendEntity extendEntity = PublishGoodsSourceCommonAssembler.generateGoodsExtendEntity(bo);
        //3、货源线路信息
        List<GoodsAddressEntity> lineEntityList = new ArrayList<>();
        GoodsAddressEntity lineEntity = PublishGoodsSourceCommonAssembler.generateGoodsAddressEntity(bo);
        goodsSourceEntity.setGoodsAddressId(lineEntity.getGoodsAddressId());
        lineEntityList.add(lineEntity);
        //4、货源指定司机列表
        List<GoodsAssignSupplierEntity> goodsAssignEntityList = PublishGoodsSourceCommonAssembler.generateGoodsAssignSupplierEntitieList(bo);
        //5、货源附件列表
        List<GoodsAttachmentEntity> goodsAttachmentEntityList = PublishGoodsSourceCommonAssembler.generateGoodsAttachmentEntitieList(bo);
        //6、老的司机货源信息
        DriverGoodsEntity driverGoodsEntity = new DriverGoodsEntity();
        //6.1三张货源表公有属性设置
        PublishGoodsSourceCommonAssembler.generateGoodsBaseEntity(driverGoodsEntity, goodsSourceEntity, lineEntity.getGoodsAddressId());
        //6.2两张老货源表公有属性设置
        PublishGoodsSourceCommonAssembler.generateGoodsEntity(driverGoodsEntity, bo);
        //6.3老司机货源个性化属性设置
        PublishGoodsSourcePersonalizeAssembler.generateDriverGoodsEntity(driverGoodsEntity, goodsSourceEntity, bo);
        //7、货源操作记录信息
        GoodsRecordEntity goodsRecordEntity = PublishGoodsSourceCommonAssembler.generateGoodsRecordEntity(bo);
        //8、撮合配置信息
        MatchmakingConfigEntity matchmakingConfigEntity = PublishGoodsSourcePersonalizeAssembler.generateMatchmakingConfigEntity(bo);
        //9、司机货源识别码和收款码关系
        List<IdentifyCodeRelEntity> identifyCodeRelEntityList = PublishGoodsSourcePersonalizeAssembler.generateIdentifyCodeRelEntityList(bo);
        //10、亏涨吨
        GoodsDeductibleBO goodsDeductible = PublishGoodsSourceCommonAssembler.generateLsdsGoodsDeductible(bo);
        //11、抹零方式
        String zeroRoundingMethod = bo.getGoodsSourceCommand().getDriverGoods().getZeroRoundingMethod();
        //12、金额取整方式
        Integer roundingMode = bo.getGoodsSourceCommand().getRoundingMode();
        //13、分次支付信息
        PartPayEntity partPayEntity = new PartPayEntity(bo.getGoodsId(), bo.getGoodsSourceCommand().getDriverGoods().getAdvancePayment(), bo.getGoodsSourceCommand().getDriverGoods().getReceiptAmount());
        //14、保险信息
        InsuranceEntity insuranceEntity = PublishGoodsSourcePersonalizeAssembler.generateInsuranceEntity(bo);

        bo.setGoodsSourceEntity(goodsSourceEntity);
        bo.setGoodsExtendEntity(extendEntity);
        bo.setDriverGoodsEntity(driverGoodsEntity);
        bo.setGoodsRecordEntity(goodsRecordEntity);

        bo.setLineEntityList(lineEntityList);
        bo.setGoodsAssignSupplierEntityList(goodsAssignEntityList);
        bo.setGoodsAttachmentEntityList(goodsAttachmentEntityList);
        bo.setMatchmakingConfigEntity(matchmakingConfigEntity);
        bo.setIdentifyCodeRelEntityList(identifyCodeRelEntityList);

        bo.setLsdsGoodsDeductible(goodsDeductible);
        bo.setRoundingMode(roundingMode);
        bo.setZeroRoundingMethod(zeroRoundingMethod);
        bo.setPartPayEntity(partPayEntity);
        bo.setInsuranceEntity(insuranceEntity);

    }


}
