package com.wanlianyida.lsds.domain.repository;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.lsds.domain.model.bo.GoodsSourceListBO;
import com.wanlianyida.lsds.domain.model.condition.GoodsSourceListCondition;

import java.util.List;

/**
 * 货源列表
 */
public interface GoodsListRepository {

    /**
     * 获取货源列表
     */
    List<GoodsSourceListBO> queryList(PagingInfo<GoodsSourceListCondition> pageInfo);


    /**
     * 获取货源总数
     */
    int queryCount(PagingInfo<GoodsSourceListCondition> pageInfo);

}
