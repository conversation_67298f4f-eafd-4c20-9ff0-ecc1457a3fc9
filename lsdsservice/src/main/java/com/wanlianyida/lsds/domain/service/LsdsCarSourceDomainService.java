package com.wanlianyida.lsds.domain.service;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.isoftstone.hig.common.model.PagingProceduresInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.LogHelper;
import com.isoftstone.hig.common.utils.SQLUtils;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.lsds.api.entity.LsdsCarSource;
import com.isoftstone.hig.lsds.api.filter.LsdsCarSourceFilter;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsCarSourceMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 服务公共类主要实现业务规则，处理业务逻辑 可以再进行创建多个，这主要为了方便一起生成
 *
 * <AUTHOR>
 */
@Service
public class LsdsCarSourceDomainService {

    /**
     * 分页获取车源ID 信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @param token    预留要传递的token，服务端进行判断权限功能
     * @return {@code  ResultMode<LsdsCarSource>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为车源ID 实体类LsdsCarSource列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsCarSource> pagingLsdsCarSource(PagingInfo<LsdsCarSourceFilter> pageInfo, String token) {

        ResultMode<LsdsCarSource> returnmodel = new ResultMode<LsdsCarSource>();
        try {
            //过滤SQL参数
            SQLUtils.filterParam(pageInfo.filterModel);

            LsdsCarSourceMapper dao = SpringContextUtil.getBeanByClass(LsdsCarSourceMapper.class);
            PagingProceduresInfo filter = new PagingProceduresInfo();
            filter.setTables("lsds_car_source");
            filter.setFields("car_source_id,company_id,start_site_city_name,start_site_city_code,start_site_address,end_site_city_name,end_site_city_code,end_site_address,linker,linker_phone_number,assign_car_type,assign_car_length,assign_car_plate_number,shape_long,shape_width,shape_high,energy_type,standard_trans_weight,standard_drag_weight,begin_order_date,end_order_date,expect_arrive_date,car_valid_date,car_status,create_by,create_date,modify_by,modify_date,item1,item2,item3,item4");
            filter.setFilter("");
            filter.setCountTotal(pageInfo.getCountTotal());
            filter.setPageNumber(pageInfo.currentPage);
            filter.setPageSize(pageInfo.pageLength);
            StringBuilder sbwhere = new StringBuilder(" 1 = 1 ");
            LsdsCarSourceFilter queryModel = pageInfo.filterModel;

            if (null != queryModel && !StringUtils.isEmpty(queryModel.getAssignCarPlateNumber())) {
                sbwhere.append(" and assign_car_plate_number like '%" + queryModel.getAssignCarPlateNumber() + "%'");
            }
            if (null != queryModel && !StringUtils.isEmpty(queryModel.getAssignCarLength())) {
                sbwhere.append(" and assign_car_length like '%" + queryModel.getAssignCarLength() + "%'");
            }
            if (null != queryModel && !StringUtils.isEmpty(queryModel.getAssignCarType())) {
                sbwhere.append(" and assign_car_type = " + queryModel.getAssignCarType());
            }
            if (null != queryModel && !StringUtils.isEmpty(queryModel.getStartCreateDate())) {
                sbwhere.append(" and create_date  >= " + queryModel.getStartCreateDate());
            }
            if (null != queryModel && !StringUtils.isEmpty(queryModel.getEndCreateDate())) {
                sbwhere.append(" and create_date <= " + queryModel.getEndCreateDate());
            }

            filter.setFilter(sbwhere.toString());
            filter.setPk("car_source_id");
            List<LsdsCarSource> list = dao.pagingLsdsCarSource(filter);
            returnmodel.setTotal(filter.getTotal());
            returnmodel.setModel(list);

        } catch (Exception ex) {
            String errMsg = "分页获取车源ID 信息异常:" + ex.getMessage();
            returnmodel.setErrMsg(errMsg);
            LogHelper.writeError(errMsg, ex);
        }
        return returnmodel;
    }
}
