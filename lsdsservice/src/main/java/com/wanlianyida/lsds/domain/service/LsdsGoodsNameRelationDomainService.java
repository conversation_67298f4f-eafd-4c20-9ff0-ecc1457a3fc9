package com.wanlianyida.lsds.domain.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.UtilityClass;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsNameRelation;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsNameCreateVo;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsGoodsNameRelationMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

/**
 * <p>
 * 货物名称关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class LsdsGoodsNameRelationDomainService {

    @Resource
    private LsdsGoodsNameRelationMapper goodsNameRelationMapper;


    /**
     * 查询货物名称关系
     *
     * @param pageInfo 页面信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameRelation}>
     */
    public ResultMode<LsdsGoodsNameRelation> getGoodsNameRelationListPage(PagingInfo<LsdsGoodsNameRelation> pageInfo){
        //设置分页参数
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);

        LsdsGoodsNameRelation goodsNameFilter = pageInfo.getFilterModel();
        List<LsdsGoodsNameRelation>  goodsNameRelationList = goodsNameRelationMapper.findGoodsNameRelationList(goodsNameFilter);

        PageInfo<LsdsGoodsNameRelation> pageInfoList = new PageInfo<>(goodsNameRelationList);
        Long total = pageInfoList.getTotal();
        ResultMode<LsdsGoodsNameRelation> returnModel = new ResultMode<>();
        returnModel.setTotal(total.intValue());
        returnModel.setModel(goodsNameRelationList);
        return returnModel;
    }

    /**
     * 新增货品名称关系
     *
     * @param goodsName 货品名称
     * @return {@link ResultMode}
     */
    public ResultMode addGoodsNameRelation(LsdsGoodsNameCreateVo goodsName){

        goodsNameRelationMapper.countGoodsNameByGoodsNameId(goodsName.getCompanyId(),goodsName.getGoodsNameId());

        LsdsGoodsNameRelation goodsNameRelation = new LsdsGoodsNameRelation();
        goodsNameRelation.setGoodsNameRelationId(UtilityClass.uuid());
        goodsNameRelation.setGoodsNameId(goodsName.getGoodsNameId());
        goodsNameRelation.setCompanyId(goodsName.getCompanyId());
        goodsNameRelation.setCompanyName(goodsName.getCompanyName());
        goodsNameRelation.setPackType(goodsName.getPackType());
        goodsNameRelation.setGoodsDesc(goodsName.getGoodsDesc());
        goodsNameRelation.setDeleteFlag(LsdsEnum.GoodsDeleteFlagEnum.NOT_DELETED_FLAG.getCode());
        goodsNameRelation.setCreateName(goodsName.getCreateName());
        goodsNameRelation.setCreateDate(new Date());
        goodsNameRelation.setModifyName(goodsName.getCreateName());
        goodsNameRelation.setModifyDate(new Date());
        goodsNameRelationMapper.insertGoodsNameRelation(goodsNameRelation);
        return ResultMode.success();
    }


    /**
     * 查询货品名称记录
     *
     * @param companyId   企业id
     * @param goodsNameId 货品名称id
     * @return {@link List}<{@link LsdsGoodsNameRelation}>
     */
    public int countGoodsNameByGoodsNameId(String companyId,String goodsNameId){
        return  goodsNameRelationMapper.countGoodsNameByGoodsNameId(companyId,goodsNameId);
    }

    /**
     * 解除
     *
     * @param goodsNameVo 货品名称
     * @return {@link ResultMode}
     */
    public ResultMode removeGoodsNameRelation(LsdsGoodsNameCreateVo goodsNameVo){
        LsdsGoodsNameRelation goodsNameRelation = new LsdsGoodsNameRelation();
        goodsNameRelation.setGoodsNameRelationId(goodsNameVo.getGoodsNameRelationId());
        goodsNameRelation.setDeleteFlag(LsdsEnum.GoodsDeleteFlagEnum.DELETED_FLAG.getCode());
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        if(tokenInfo!=null){
            goodsNameRelation.setModifyName(tokenInfo.getUsername());
        }
        goodsNameRelationMapper.updateGoodsNameRelation(goodsNameRelation);
        return ResultMode.success();
    }

    /**
     * 按货品名称id删除
     *
     * @param goodsNameRelation 商品名称关系
     * @return int
     */
    public ResultMode deleteGoodsNameRelationByGoodsNameId(LsdsGoodsNameRelation goodsNameRelation) {
        goodsNameRelationMapper.deleteGoodsNameRelationByGoodsNameId(goodsNameRelation);
        return ResultMode.success();
    }

    public void batchAddGoodsName(List<LsdsGoodsNameRelation> goodsNameRelationList) {
        goodsNameRelationMapper.batchAddGoodsName(goodsNameRelationList);
    }
}
