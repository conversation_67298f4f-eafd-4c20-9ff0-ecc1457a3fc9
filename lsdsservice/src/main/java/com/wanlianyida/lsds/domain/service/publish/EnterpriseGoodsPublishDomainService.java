package com.wanlianyida.lsds.domain.service.publish;

import com.isoftstone.hig.common.constants.PrefixCodeConstants;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.lsds.application.model.command.publish.PublishGoodsSourceCommand;
import com.wanlianyida.lsds.domain.assembler.PublishGoodsSourceCommonAssembler;
import com.wanlianyida.lsds.domain.assembler.PublishGoodsSourcePersonalizeAssembler;
import com.wanlianyida.lsds.domain.model.bo.GoodsPublishBO;
import com.wanlianyida.lsds.domain.model.entity.*;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.domain.service.publish.paramverif.EnterpriseParamVerif;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.GoodsDeductibleBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * 企业货源
 */
@Slf4j
@Service("EnterpriseGoodsPublish")
public class EnterpriseGoodsPublishDomainService extends GoodsPublishDomainService {

    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;

    @Resource
    private EnterpriseParamVerif enterpriseParamVerif;

    @Override
    public ResultMode checkParam(PublishGoodsSourceCommand command) {
        return enterpriseParamVerif.checkParam(command);
    }

    @Override
    public void dataAssembly(GoodsPublishBO bo) {
        //1、货源信息
        if (bo.getInsertFlag()) {
            bo.setGoodsId(goodsOperatorRepository.generateGoodsId(PrefixCodeConstants.GOODS_C));
        }
        GoodsSourceEntity goodsSourceEntity = PublishGoodsSourceCommonAssembler.generateGoodsSourceEntity(bo);
        //2、货源扩展信息
        GoodsExtendEntity extendEntity = PublishGoodsSourceCommonAssembler.generateGoodsExtendEntity(bo);
        //3、货源主线路信息
        List<GoodsAddressEntity> lineEntityList = new ArrayList<>();
        GoodsAddressEntity lineEntity = PublishGoodsSourceCommonAssembler.generateGoodsAddressEntity(bo);
        goodsSourceEntity.setGoodsAddressId(lineEntity.getGoodsAddressId());
        lineEntityList.add(lineEntity);
        //4、货源分段线路信息和拆单信息
        List<GoodsSplitEntity> splitEntityList = new ArrayList<>();
        PublishGoodsSourcePersonalizeAssembler.generateSplitLine(bo, lineEntityList, splitEntityList);
        //5、货源指定供应商列表
        List<GoodsAssignSupplierEntity> goodsAssignEntityList = PublishGoodsSourceCommonAssembler.generateGoodsAssignSupplierEntitieList(bo);
        //6、货源附件列表
        List<GoodsAttachmentEntity> goodsAttachmentEntityList = PublishGoodsSourceCommonAssembler.generateGoodsAttachmentEntitieList(bo);
        //7、老的企业货源信息
        EnterpriseGoodsEntity enterpriseGoodsEntity = new EnterpriseGoodsEntity();
        //7.1三张货源表公有属性设置
        PublishGoodsSourceCommonAssembler.generateGoodsBaseEntity(enterpriseGoodsEntity, goodsSourceEntity, lineEntity.getGoodsAddressId());
        //7.2两张老货源表公有属性设置
        PublishGoodsSourceCommonAssembler.generateGoodsEntity(enterpriseGoodsEntity, bo);
        //7.3老企业货源个性化属性设置
        PublishGoodsSourcePersonalizeAssembler.generateEnterpriseGoodsEntity(enterpriseGoodsEntity, bo);
        //8、货源操作记录信息
        GoodsRecordEntity goodsRecordEntity = PublishGoodsSourceCommonAssembler.generateGoodsRecordEntity(bo);
        //9、亏涨吨
        GoodsDeductibleBO goodsDeductible = PublishGoodsSourceCommonAssembler.generateLsdsGoodsDeductible(bo);
        //10、金额取整方式
        Integer roundingMode = bo.getGoodsSourceCommand().getRoundingMode();
        //11、下游询价-更新父货源的子货源号
        EnterpriseGoodsEntity enterpriseGoodsUpd = PublishGoodsSourcePersonalizeAssembler.buildParentGoodsForDownstreamUpdate(bo);

        bo.setGoodsSourceEntity(goodsSourceEntity);
        bo.setGoodsExtendEntity(extendEntity);
        bo.setEnterpriseGoodsEntity(enterpriseGoodsEntity);
        bo.setEnterpriseGoodsUpd(enterpriseGoodsUpd);
        bo.setGoodsRecordEntity(goodsRecordEntity);


        bo.setLineEntityList(lineEntityList);
        bo.setGoodsAssignSupplierEntityList(goodsAssignEntityList);
        bo.setGoodsAttachmentEntityList(goodsAttachmentEntityList);
        bo.setSplitEntityList(splitEntityList);

        bo.setLsdsGoodsDeductible(goodsDeductible);
        bo.setRoundingMode(roundingMode);

    }

}
