package com.wanlianyida.lsds.domain.service.publish.paramverif;

import cn.hutool.core.collection.CollUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.tms.api.common.valid.StringUtils;
import com.wanlianyida.lsds.application.model.command.publish.AssignSupplierCommand;
import com.wanlianyida.lsds.application.model.command.publish.PublishGoodsSourceCommand;
import com.wanlianyida.lsds.infrastructure.enums.EnquiryRangeEnum;
import org.springframework.stereotype.Component;

/**
 * 企业货源参数校验
 */
@Component
public class EnterpriseParamVerif extends CommonParamVerif {
    public ResultMode checkParam(PublishGoodsSourceCommand command) {
        //公共参数校验
        ResultMode resultMode = checkCommonParam(command);
        if (!resultMode.getSucceed()) {
            return resultMode;
        }
        if (EnquiryRangeEnum.INVITE_CARRIER.getCode().equals(command.getEnquiryRange()) && CollUtil.isEmpty(command.getAssignSupplierList())) {
            return ResultMode.fail("邀请承运商数据不能为空!");
        }
        for (AssignSupplierCommand supplier : command.getAssignSupplierList()) {
            if (supplier == null || StringUtils.isBlank(supplier.getCompanyId())) {
                return ResultMode.fail("邀请承运商数据不能为空!");
            }
        }
        return ResultMode.success();
    }
}
