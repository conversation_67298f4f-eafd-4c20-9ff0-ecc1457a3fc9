package com.wanlianyida.lsds.domain.service;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.isoftstone.hig.common.model.PagingProceduresInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.LogHelper;
import com.isoftstone.hig.common.utils.SQLUtils;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsSplit;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsSplitFilter;
import com.wanlianyida.lsds.infrastructure.repository.mapper.GoodsSplitMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 服务公共类主要实现业务规则，处理业务逻辑 可以再进行创建多个，这主要为了方便一起生成
 *
 * <AUTHOR>
 */
@Service
public class LsdsGoodsSplitDomainService {

    /**
     * 分页获取货物拆单id 货源拆单信息表信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @param token    预留要传递的token，服务端进行判断权限功能
     * @return {@code  ResultMode<LsdsGoodsSplit>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货物拆单id 货源拆单信息表实体类LsdsGoodsSplit列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsGoodsSplit> pagingLsdsGoodsSplit(PagingInfo<LsdsGoodsSplitFilter> pageInfo, String token) {

        ResultMode<LsdsGoodsSplit> returnmodel = new ResultMode<LsdsGoodsSplit>();
        try {
            //过滤SQL参数
            SQLUtils.filterParam(pageInfo.filterModel);

            GoodsSplitMapper dao = SpringContextUtil.getBeanByClass(GoodsSplitMapper.class);
            PagingProceduresInfo filter = new PagingProceduresInfo();
            filter.setTables("lsds_goods_split");
            filter.setFields("goods_split_id,goods_id,split_type,start_site_city_code,start_site_city_name,start_site_address,start_send_linker,start_send_phone_number,end_site_city_code,end_site_city_name,end_site_address,end_receive_linker,end_receive_phone_number,transportation_type,split_amount,status,release_date,arrive_date,remark,sort_node,create_by,create_date,modify_by,modify_date");
            filter.setFilter("");
            filter.setCountTotal(pageInfo.getCountTotal());
            filter.setPageNumber(pageInfo.currentPage);
            filter.setPageSize(pageInfo.pageLength);
            StringBuilder sbwhere = new StringBuilder(" 1 = 1 ");
            LsdsGoodsSplitFilter queryModel = pageInfo.filterModel;
            if (StringUtils.isNotEmpty(queryModel.getGoodsSplitId())) {
                sbwhere.append(" and goods_split_id like '%" + queryModel.getGoodsSplitId() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getGoodsId())) {
                sbwhere.append(" and goods_id like '%" + queryModel.getGoodsId() + "%'");
            }
            if (queryModel.getSplitType() != 0) {
                sbwhere.append(" and split_type =" + queryModel.getSplitType());
            }
            if (StringUtils.isNotEmpty(queryModel.getStartSiteCityCode())) {
                sbwhere.append(" and start_site_city_code like '%" + queryModel.getStartSiteCityCode() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getStartSiteCityName())) {
                sbwhere.append(" and start_site_city_name like '%" + queryModel.getStartSiteCityName() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getStartSiteAddress())) {
                sbwhere.append(" and start_site_address like '%" + queryModel.getStartSiteAddress() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getStartSendLinker())) {
                sbwhere.append(" and start_send_linker like '%" + queryModel.getStartSendLinker() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getStartSendPhoneNumber())) {
                sbwhere.append(" and start_send_phone_number like '%" + queryModel.getStartSendPhoneNumber() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getEndSiteCityCode())) {
                sbwhere.append(" and end_site_city_code like '%" + queryModel.getEndSiteCityCode() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getEndSiteCityName())) {
                sbwhere.append(" and end_site_city_name like '%" + queryModel.getEndSiteCityName() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getEndSiteAddress())) {
                sbwhere.append(" and end_site_address like '%" + queryModel.getEndSiteAddress() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getEndReceiveLinker())) {
                sbwhere.append(" and end_receive_linker like '%" + queryModel.getEndReceiveLinker() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getEndReceivePhoneNumber())) {
                sbwhere.append(" and end_receive_phone_number like '%" + queryModel.getEndReceivePhoneNumber() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getTransportationType())) {
                sbwhere.append(" and transportation_type =" + queryModel.getTransportationType());
            }
            if (StringUtils.isNotEmpty(queryModel.getSplitAmount())) {
                sbwhere.append(" and split_amount like '%" + queryModel.getSplitAmount() + "%'");
            }
            if (queryModel.getStatus() != 0) {
                sbwhere.append(" and status =" + queryModel.getStatus());
            }
            if (StringUtils.isNotEmpty(queryModel.getRemark())) {
                sbwhere.append(" and remark like '%" + queryModel.getRemark() + "%'");
            }
            if (queryModel.getSortNode() != 0) {
                sbwhere.append(" and sort_node =" + queryModel.getSortNode());
            }
            if (StringUtils.isNotEmpty(queryModel.getCreateBy())) {
                sbwhere.append(" and create_by like '%" + queryModel.getCreateBy() + "%'");
            }
            if (StringUtils.isNotEmpty(queryModel.getModifyBy())) {
                sbwhere.append(" and modify_by like '%" + queryModel.getModifyBy() + "%'");
            }
            filter.setFilter(sbwhere.toString());
            filter.setPk("goods_split_id");
            List<LsdsGoodsSplit> list = dao.pagingLsdsGoodsSplit(filter);
            returnmodel.setTotal(filter.getTotal());
            returnmodel.setModel(list);

        } catch (Exception ex) {
            String errMsg = "分页获取货物拆单id 货源拆单信息表信息异常:" + ex.getMessage();
            returnmodel.setErrMsg(errMsg);
            LogHelper.writeError(errMsg, ex);
        }
        return returnmodel;
    }
}
