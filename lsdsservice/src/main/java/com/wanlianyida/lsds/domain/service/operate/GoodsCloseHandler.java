package com.wanlianyida.lsds.domain.service.operate;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.lsds.api.entity.GoodsKafkaNotice;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.wanlianyida.lsds.domain.model.bo.GoodsOperateBO;
import com.wanlianyida.lsds.domain.model.entity.GoodsSourceEntity;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.infrastructure.enums.GoodsOperateTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsPublishTypeEnum;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 关闭货源
 * @Date 2025年05月14日 14:46
 */
@Component
public class GoodsCloseHandler implements GoodsOperateStrategy {

    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;
    @Resource
    private LsdsKafkaSender lsdsKafkaSender;

    @Override
    public ResultMode<?> operate(GoodsOperateBO goodsOperateBO) {
        GoodsSourceEntity goodsSource = goodsOperateBO.getGoodsSource();
        if (StrUtil.isNotBlank(goodsSource.getParentGoodsId())) {
            return ResultMode.fail("下游询价货源不允许直接关闭！");
        }
        // 查询子货源
        List<GoodsSourceEntity> goodsList =goodsOperatorRepository.queryChildGoods(goodsSource.getGoodsId());
        if (CollectionUtil.isEmpty(goodsList)) {
            goodsList = new ArrayList<>();
        }
        goodsList.add(goodsSource);
        goodsOperatorRepository.updateByGoodsId(GoodsOperateAssembler.createGoodsClose(goodsSource));
        for (GoodsSourceEntity goodsSourceEntity : goodsList) {
            SpringUtil.getBean(this.getClass()).asyncOperate(goodsSourceEntity);
            SpringUtil.getBean(this.getClass()).asyncCompanyOperate(goodsSourceEntity);
        }
        return ResultMode.success();
    }

    public void asyncOperate(GoodsSourceEntity goodsSourceEntity) {
        // 广播通知货源状态变更
        GoodsKafkaNotice goodsKafkaNotice = new GoodsKafkaNotice();
        goodsKafkaNotice.setGoodsId(goodsSourceEntity.getGoodsId());
        if (GoodsPublishTypeEnum.DRIVER.getType().equals(goodsSourceEntity.getPublisherType())) {
            goodsKafkaNotice.setDealStatus(Constants.GOODS_STATUS_CLOSED);
        } else {
            goodsKafkaNotice.setDealStatus(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus7.getCode());
        }
        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(goodsKafkaNotice));
    }

    public void asyncCompanyOperate(GoodsSourceEntity goodsSource) {
        if (StrUtil.isNotBlank(goodsSource.getOrderId())) {
            String topic = SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.LSDS_TO_OMS_ORDER_STATUS.getTopicName();
            List<Map<String, String>> mapList = new ArrayList<>();
            Map<String, String> map = new HashMap<>();
            map.put("changeDealGoodsId", goodsSource.getGoodsId());
            map.put("orderId", goodsSource.getOrderId());
            //补传状态：推送订单归档失效消息
            map.put("dealStatus", LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus7.getCode());
            mapList.add(map);
            JSONObject json = new JSONObject();
            json.put("exlsdsOrderIds", mapList);
            lsdsKafkaSender.send(topic, json.toJSONString());
        }

    }

    @Override
    public GoodsOperateTypeEnum getOperateType() {
        return GoodsOperateTypeEnum.CLOSE;
    }
}
