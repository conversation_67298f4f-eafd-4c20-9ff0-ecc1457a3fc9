package com.wanlianyida.lsds.domain.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.UtilityClass;
import com.isoftstone.hig.lsds.api.entity.LsdsOperationRecord;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsOperationRecordMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

/**
 * <p>
 * 操作记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class LsdsOperationRecordDomainService {

    @Resource
    private LsdsOperationRecordMapper operationRecordMapper;


    /**
     * 查询操作记录
     *
     * @param pageInfo 页面信息
     * @return {@link ResultMode}<{@link LsdsOperationRecord}>
     */
    public ResultMode<LsdsOperationRecord> getOperationRecordListPage(PagingInfo<LsdsOperationRecord> pageInfo){
        //设置分页参数
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);

        LsdsOperationRecord operationRecordFilter = pageInfo.getFilterModel();
        List<LsdsOperationRecord> recordList = operationRecordMapper.findOperationRecordList(operationRecordFilter);

        PageInfo<LsdsOperationRecord> pageInfoList = new PageInfo<>(recordList);
        Long total = pageInfoList.getTotal();
        ResultMode<LsdsOperationRecord> returnModel = new ResultMode<>();
        returnModel.setTotal(total.intValue());
        returnModel.setModel(recordList);
        return returnModel;
    }

    /**
     * 新增操作记录
     *
     * @param busId         业务id
     * @param operationType 操作类型
     * @param operationDesc 操作desc
     * @param remark        备注
     */
    public void addOperationRecord(String busId,String operationType,String operationDesc,String remark){
        LsdsOperationRecord operationRecord = new LsdsOperationRecord();
        operationRecord.setOperationRecordId(UtilityClass.uuid());
        operationRecord.setBusId(busId);
        operationRecord.setOperationType(operationType);
        operationRecord.setOperationDesc(operationDesc);
        operationRecord.setRemark(remark);
        operationRecord.setCreateDate(new Date());
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        if(tokenInfo!=null){
            operationRecord.setCompanyId(tokenInfo.getCompanyId());
            operationRecord.setLoginName(tokenInfo.getLoginName());
            operationRecord.setCreateBy(tokenInfo.getUserBaseId());
            operationRecord.setCreateName(tokenInfo.getUsername());
        }
        operationRecordMapper.insertOperationRecord(operationRecord);
    }
}
