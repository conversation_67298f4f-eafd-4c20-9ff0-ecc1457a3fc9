package com.wanlianyida.lsds.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.isoftstone.hig.annotations.LogPrintPoint;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.DateUtils;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.common.utils.inter.DistributedLocker;
import com.isoftstone.hig.lsds.api.entity.*;
import com.isoftstone.hig.lsds.api.enums.BidOpeningStatusEnum;
import com.isoftstone.hig.lsds.api.enums.GoodsSourceTypeEnum;
import com.isoftstone.hig.lsds.api.enums.OfferStatusEnum;
import com.isoftstone.hig.platform.api.entity.PlatformUmCompany;
import com.wanlianyida.lsds.domain.assembler.GoodsSourceOperatorAssembler;
import com.wanlianyida.lsds.domain.model.bo.GoodsNextOfferParamBO;
import com.wanlianyida.lsds.domain.model.bo.GoodsNextOffersSaveBO;
import com.wanlianyida.lsds.domain.model.entity.GoodsSourceEntity;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.infrastructure.exchange.AmountRoundingModeExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import com.wanlianyida.lsds.infrastructure.util.MessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 企业货源报价操作
 */
@Slf4j
@Service
public class GoodsNextOfferDomainService extends BizDomainService<GoodsNextOfferParamBO, GoodsNextOffersSaveBO, String> {


    @Resource
    private GoodsOperatorRepository goodsOperatorRepository;

    @Resource
    private DistributedLocker distributedLocker;

    @Resource
    private AmountRoundingModeExchangeService amountRoundingModeService;

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private MessageUtils messageUtils;

    @Resource
    private TaskExecutor asynTaskExecutor;

    /**
     * 货源报价锁
     */
    private static final String GOOD_NET_ROUND_LOCK = "lsds:goods:net:round";

    @Override
    public ResultMode<String> checkParam(GoodsNextOfferParamBO goodsNextOfferParamBO) {
        LsdsGoods paramLsdsGoods = goodsNextOfferParamBO.getLsdsGoods();
        if(ObjUtil.isEmpty(paramLsdsGoods)){
            return ResultMode.fail("参数为空");
        }
        LsdsGoods lsdsGoods = goodsOperatorRepository.selectLsdsGoodsByGoodsId(paramLsdsGoods.getGoodsId());
        if(ObjUtil.isNull(lsdsGoods)){
            return ResultMode.fail("货源不存在");
        }
        if (StrUtil.isEmpty(lsdsGoods.getGoodsId())) {
            return ResultMode.fail("goodsId:不能为空字符");
        }
        if (ObjUtil.isEmpty(lsdsGoods.getOfferCurrentRounds())) {
            return ResultMode.fail("当前报价轮次:不能为空字符");
        }
        if (CollUtil.isEmpty(goodsNextOfferParamBO.getAssignSupplierList()) && CollUtil.isEmpty(goodsNextOfferParamBO.getGoodsNextRoundOfferList())) {
            return ResultMode.fail("当前轮次没有承运商报价不允许进入下一轮报价");
        }
        if(!ObjUtil.isEmpty(lsdsGoods.getOfferRound()) && !ObjUtil.isEmpty(lsdsGoods.getOfferCurrentRounds()) && lsdsGoods.getOfferCurrentRounds() >= lsdsGoods.getOfferRound()){
            return ResultMode.fail("报价伦次已达上限");
        }

        if(ObjUtil.isEmpty(lsdsGoods.getEnquiryOfferTime()) || lsdsGoods.getEnquiryOfferTime() <= 0){
            return ResultMode.fail("当前货源未填写报价有效期，只允许一轮报价！");
        }
        return ResultMode.success();
    }

    @LogPrintPoint
    @Override
    public GoodsNextOffersSaveBO dataAssembly(GoodsNextOfferParamBO goodsNextOfferParamBO) {

        LsdsGoods lsdsGoods = goodsNextOfferParamBO.getLsdsGoods();
        List<LsdsGoodsAssignSupplier> assignSupplierList = goodsNextOfferParamBO.getAssignSupplierList();

        //加锁
        distributedLocker.lock(GOOD_NET_ROUND_LOCK, 30);

        try {
            lsdsGoods.setCurrentOfferStartDate(DateUtils.getDateTime());
            //金额取整
            amountRoundingModeService.setLsdsGoodsScale(lsdsGoods);

            List<LsdsGoodsAssignSupplier> supplierListNew = null;
            //新增货源当前轮次承运商信息（页面勾选了报价的承运商信息）到下一轮
            if (IterUtil.isNotEmpty(assignSupplierList)) {
                supplierListNew = this.processTargetOffer(assignSupplierList,lsdsGoods);
            } else {
                supplierListNew = this.processNextOffer(goodsNextOfferParamBO);
            }
            this.setDefaultData(lsdsGoods,supplierListNew,goodsNextOfferParamBO);
            //查询当前报价信息
            List<Map<String,String>> offerList = this.getOfferData(lsdsGoods);
            //同步货源操作记录
            LsdsGoodsRecord lgr = GoodsSourceOperatorAssembler.buildOfferRecord(lsdsGoods,goodsNextOfferParamBO);
            //指派承运商，生成报价单信息
            List<LsdsGoodsOffer> lsdsGoodsOffers = buildSupplierOffer(lsdsGoods, supplierListNew, lsdsGoods.getOfferCurrentRounds() + 1);
            GoodsSourceEntity updateGoodsSource = GoodsSourceOperatorAssembler.buildUpdateGoodSource(lsdsGoods);

            return GoodsSourceOperatorAssembler.buildGoodsNextOffersSaveBO(lsdsGoods,updateGoodsSource,lsdsGoodsOffers,offerList,supplierListNew,lgr);
        }catch (Exception e){
            log.error("进入下一轮报价异常"+e.getMessage());
            return new GoodsNextOffersSaveBO();
        }finally {
            distributedLocker.unlock(GOOD_NET_ROUND_LOCK);
            log.info("执行完成释放锁");
        }
    }

    /**
     * 查询 当前报价信息
     * @param lsdsGoods
     * @return
     */
    private List<Map<String, String>> getOfferData(LsdsGoods lsdsGoods) {
        if(StringUtils.isEmpty(lsdsGoods.getOfferCurrentRounds()) || lsdsGoods.getOfferCurrentRounds() < 1){
            return null;
        }
        LsdsGoodsOffer offer = new LsdsGoodsOffer();
        offer.setGoodsId(lsdsGoods.getGoodsId());
        offer.setOfferRound(lsdsGoods.getOfferCurrentRounds());
        return goodsOperatorRepository.getCurrentOfferdMap(offer);
    }

    /**
     * 复制默认信息
     * @param lsdsGoods
     * @param supplierListNew
     * @param goodsNextOfferParamBO
     */
    private void setDefaultData(LsdsGoods lsdsGoods, List<LsdsGoodsAssignSupplier> supplierListNew, GoodsNextOfferParamBO goodsNextOfferParamBO) {
        if(CollUtil.isEmpty(supplierListNew)){
            return;
        }
        for (LsdsGoodsAssignSupplier supplier : supplierListNew) {
            supplier.setGoodsAssignSupplierId(String.valueOf(IdUtil.generateId()));
            supplier.setGoodsId(lsdsGoods.getGoodsId());
            supplier.setOfferRound(lsdsGoods.getOfferCurrentRounds() + 1);
            supplier.setCreateDate(DateUtils.getDateTime());
            supplier.setModifyDate(DateUtils.getDateTime());
            supplier.setCreateBy(goodsNextOfferParamBO.getUserId());
            supplier.setModifyBy(goodsNextOfferParamBO.getUserId());
        }
    }

    /**
     * 处理指定承运商报价
     * @param assignSupplierList
     * @param lsdsGoods
     * @return
     */
    private List<LsdsGoodsAssignSupplier> processTargetOffer(List<LsdsGoodsAssignSupplier> assignSupplierList, LsdsGoods lsdsGoods) {
        List<LsdsGoodsAssignSupplier> supplierListNew = new ArrayList<>();
        List<String> companyIdList = assignSupplierList.stream().map(LsdsGoodsAssignSupplier::getCompanyId).collect(Collectors.toList());
        LsdsGoodsAssignSupplier condition = new LsdsGoodsAssignSupplier();
        condition.setCompanyIdList(companyIdList);
        condition.setGoodsId(lsdsGoods.getGoodsId());
        condition.setOfferRound(lsdsGoods.getOfferCurrentRounds());
        List<LsdsGoodsAssignSupplier> querySupplierList = goodsOperatorRepository.querySupplierList(condition);
        if (IterUtil.isEmpty(querySupplierList)) {
            assembleSupplierList(companyIdList, supplierListNew);
        } else {
            supplierListNew = BeanUtil.copyToList(querySupplierList, LsdsGoodsAssignSupplier.class);
        }
        return supplierListNew;
    }

    /**
     * 处理所有承运商报价
     * @param goodsNextOfferParamBO
     * @return
     */
    private List<LsdsGoodsAssignSupplier> processNextOffer(GoodsNextOfferParamBO goodsNextOfferParamBO) {
        LsdsGoods lsdsGoods = goodsNextOfferParamBO.getLsdsGoods();
        List<Map<String, Object>> goodsNextRoundOfferList = goodsNextOfferParamBO.getGoodsNextRoundOfferList();
        //没有勾选当前轮次报价承运商信息，将当前报价的所有承运商转入下一轮报价
        if (ObjUtil.isEmpty(goodsNextRoundOfferList)) {
            return null;
        }
        List<LsdsGoodsAssignSupplier> supplierListNew = new ArrayList<>();
        List<String> companyIdList = goodsNextRoundOfferList.stream().map(info -> info.get("companyId").toString())
            .collect(Collectors.toList());
        LsdsGoodsAssignSupplier condition = new LsdsGoodsAssignSupplier();
        condition.setCompanyIdList(companyIdList);
        condition.setGoodsId(lsdsGoods.getGoodsId());
        condition.setOfferRound(lsdsGoods.getOfferCurrentRounds());
        List<LsdsGoodsAssignSupplier> querySupplierList = goodsOperatorRepository.querySupplierList(condition);
        if (IterUtil.isEmpty(querySupplierList)) {
            assembleSupplierList(companyIdList, supplierListNew);
        } else {
            supplierListNew = BeanUtil.copyToList(querySupplierList, LsdsGoodsAssignSupplier.class);
        }
        return supplierListNew;
    }

    @Override
    public void saveRep(GoodsNextOffersSaveBO goodsNextOffersSaveBO) {
        goodsOperatorRepository.handleNexOffer(goodsNextOffersSaveBO);
    }

    @Override
    public void asyOper(GoodsNextOfferParamBO goodsNextOfferParamBO, GoodsNextOffersSaveBO goodsNextOffersSaveBO) {
        asynTaskExecutor.execute(()->{
           this.sendMsg(goodsNextOffersSaveBO);
        });
    }

    /**
     * 发送信息
     * @param goodsNextOffersSaveBO
     */
    private void sendMsg(GoodsNextOffersSaveBO goodsNextOffersSaveBO) {
        List<LsdsGoodsAssignSupplier> saveSupplierListNew = goodsNextOffersSaveBO.getSaveSupplierListNew();
        if(CollUtil.isEmpty(saveSupplierListNew)){
            log.info("sendMsg#承运商消息为空，不发消息");
            return;
        }
        LsdsGoods updateLsdsGoods = goodsNextOffersSaveBO.getUpdateLsdsGoods();
        List<Map<String, String>> offerList = goodsNextOffersSaveBO.getOfferList();
        for (LsdsGoodsAssignSupplier supplier : saveSupplierListNew) {
            if(null != offerList && offerList.size() > 0){
                List<Map<String,String>> tempOfferList = offerList.stream().filter(p -> (p.get("companyId")).equals(supplier.getCompanyId())).collect(Collectors.toList());
                //发送站内信，通知承运商
                Map<String, String> param = new HashMap();
                String phoneNumber = supplier.getCompanyPhoneNumber();
                String userId = tempOfferList.get(0).get("createBy");
                String companyId = supplier.getCompanyId();
                String templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_SUPPLIER_OFFER.getCode();
                param.put("goodsNo",updateLsdsGoods.getGoodsId());
                param.put("offerRound",Integer.toString(updateLsdsGoods.getOfferCurrentRounds() + 1));
                //发送站内信
                log.info("sendMsg#承运商消息发送：{}",JSONUtil.toJsonStr(param));
                messageUtils.sendMsg(phoneNumber,userId,companyId,templateId,param);
            }
        }
    }


    /**
     *
     * @param companyIdList
     * @param supplierListNew
     */
    private void assembleSupplierList(List<String> companyIdList, List<LsdsGoodsAssignSupplier> supplierListNew) {
        List<PlatformUmCompany> companyList = platformExchangeService.getCompanyByIds(companyIdList);
        if (IterUtil.isEmpty(companyList)) {
            log.error("assembleSupplierList#未查询到企业信息:{}", JSONUtil.toJsonStr(companyIdList));
            return;
        }

        companyList.stream().forEach(company -> {
            LsdsGoodsAssignSupplier supplier = new LsdsGoodsAssignSupplier();
            supplier.setCompanyId(company.getCompanyId());
            supplier.setCompanyName(company.getCompanyName());
            supplier.setCompanyShortName(company.getCompanyShortName());
            supplier.setCompanyCode(company.getSocialCreditCode());
            supplierListNew.add(supplier);
        });

    }

    /**
     * 构建 LsdsGoodsOffer
     * @param lsdsGoods
     * @param supplierList
     * @param offerCurrentRounds
     * @return
     */
    public List<LsdsGoodsOffer> buildSupplierOffer(LsdsGoods lsdsGoods, List<LsdsGoodsAssignSupplier> supplierList, Integer offerCurrentRounds) {
        if (ObjUtil.hasNull(lsdsGoods, offerCurrentRounds) || IterUtil.isEmpty(supplierList)) {
            return null;
        }

        //第一轮、指定物流公司
        if (offerCurrentRounds == 1 && !StrUtil.equals(lsdsGoods.getEnquiryRange(), LsdsEnum.LsdsgoodsEnquiryRangeEnum.enquiryrange1.getCode())) {
            return null;
        }

        //报价单数据构造
        return assembleOfferAdd(lsdsGoods, supplierList, offerCurrentRounds);

    }

    /**
     * 报价信息
     * @param lsdsGoods
     * @param supplierList
     * @param offerCurrentRounds
     * @return
     */
    private List<LsdsGoodsOffer> assembleOfferAdd(LsdsGoods lsdsGoods, List<LsdsGoodsAssignSupplier> supplierList, Integer offerCurrentRounds) {
        List<LsdsGoodsOffer> offerList = new ArrayList<>();
        //查询报价人是否是交易签约主体
        List<String> companyIdList = supplierList.stream().map(LsdsGoodsAssignSupplier::getCompanyId).collect(Collectors.toList());
        Map<String, Boolean> map = platformExchangeService.getTransactionBody(companyIdList);

        String now = DateUtils.getDateTime();
        String userBaseId = JwtUtil.getInstance().getUserBaseIdByToken();
        for (LsdsGoodsAssignSupplier supplier : supplierList) {
            LsdsGoodsOffer offer = new LsdsGoodsOffer();
            String offerId = String.valueOf(IdUtil.generateId());
            offer.setOfferId(offerId);
            offer.setGoodsAddressId(lsdsGoods.getGoodsAddressId());
            offer.setOfferRound(offerCurrentRounds);
            offer.setGoodsId(lsdsGoods.getGoodsId());
            offer.setCompanyId(supplier.getCompanyId());
            offer.setCompanyShortName(supplier.getCompanyShortName());
            offer.setTransportationType(lsdsGoods.getTransportationType());
            offer.setEnquiryTypeBasePrice(lsdsGoods.getEnquiryTypeBasePrice());
            offer.setEnquiryTypeBaseTaxRate(lsdsGoods.getEnquiryTypeBaseTaxRate());
            offer.setEnquiryTypeBaseOpenTicket(lsdsGoods.getEnquiryTypeBaseOpenTicket());
            offer.setHasPlan("21");
            offer.setStatus(1);
            offer.setReleaseDate(lsdsGoods.getReleaseDate());
            offer.setArriveDate(lsdsGoods.getArriveDate());
            offer.setSortNode(1);

            offer.setCreateBy(userBaseId);
            offer.setCreateDate(now);
            offer.setModifyBy(userBaseId);
            offer.setModifyDate(now);

            offer.setOfferStatus(OfferStatusEnum.NONE.getCode());
            offer.setBidOpeningStatus(BidOpeningStatusEnum.TO_BE.getCode());
            if (MapUtil.isNotEmpty(map) && ObjUtil.isNotNull(map.get(supplier.getCompanyId()))
                && map.get(supplier.getCompanyId())) {
                offer.setContractFlag(1);
                //判断是向下游询价货源
                if (lsdsGoods.getReleaseType() != 2 && !StrUtil.equals(lsdsGoods.getGoodsSourceType(), GoodsSourceTypeEnum.BID_OPENING.getCode())) {
                    offer.setDownOfferFlag(1);
                } else {
                    offer.setDownOfferFlag(0);
                }
            } else {
                offer.setContractFlag(0);
                offer.setDownOfferFlag(0);
            }

            offerList.add(offer);
        }

        return offerList;

    }
}
