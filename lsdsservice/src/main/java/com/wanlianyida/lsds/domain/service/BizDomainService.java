package com.wanlianyida.lsds.domain.service;

import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;

/**
 * 业务 模板 domain service
 * @param <P> 参数
 * @param <S> 保存save
 * @param <R> 返回值
 */
public abstract class BizDomainService<P,S,R> {

    /**
     * 处理业务层
     * @param p
     */
    public ResultMode<R> handleBiz(P p){
        if(ObjUtil.isNull(p)){
            return ResultMode.fail("参数为空");
        }
        //参数校验
        ResultMode checkParamResult = this.checkParam(p);
        if(!checkParamResult.isSucceed()){
            return checkParamResult;
        }
        //数据组装
        S s = this.dataAssembly(p);
        //保存入库
        this.saveRep(s);
        //异步操作
        this.asyOper(p,s);

        return ResultMode.success();
    }

    /**
     * 参数校验
     * @return
     */
    public abstract  ResultMode<R> checkParam(P p);


    /**
     * 数据组装
     * @return
     */
    public abstract S dataAssembly(P p);


    /**
     * 保存
     * @return
     */
    public abstract void saveRep(S s);


    /**
     * 异步操作
     * @return
     */
    public abstract void asyOper(P p,S s);
}
