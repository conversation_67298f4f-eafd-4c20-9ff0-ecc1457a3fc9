package com.wanlianyida.lsds.domain.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.isoftstone.hig.annotations.LogPrintPoint;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsIdentifyCodeRel;
import com.isoftstone.hig.lsds.api.filter.IdentifyCodeRelFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsIdentifyCodeRelVO;
import com.isoftstone.hig.qrs.api.constants.QrsEnum;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.repository.mapper.IdentifyCodeRelMapper;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * 司机货源识别码关系 ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-02-07
 */
@Slf4j
@Service
public class LsdsIdentifyCodeRelDomainService {

   @Resource
   private IdentifyCodeRelMapper lsdsidentifycoderelMapper;

	/**
	 * 分页查询
	 * @param pagingInfo 分页参数
	 * @return {@link PageInfo}<{@link LsdsIdentifyCodeRel}>
	 */
	@LogPrintPoint

	public PageInfo<LsdsIdentifyCodeRel> queryPage(PagingInfo<LsdsIdentifyCodeRel> pagingInfo){
		if (null == pagingInfo.filterModel || ObjectUtil.isAllEmpty(ReflectUtil.getFieldsValue(pagingInfo.filterModel))) {
			throw new LsdsWlydException(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK);
		}
		PageHelper.startPage(pagingInfo.getCurrentPage(), pagingInfo.getPageLength(), pagingInfo.getCountTotal());
		PageHelper.orderBy("created_date desc,id");
		pagingInfo.filterModel.setDelFlag(QrsEnum.DelFlagEnum.NORMAL.getCode());
		List<LsdsIdentifyCodeRel> list = lsdsidentifycoderelMapper.queryByCondition(pagingInfo.filterModel);
		if (IterUtil.isEmpty(list)) {
			return new PageInfo<>(new ArrayList<>());
		}
		return new PageInfo<>(list);
	}

	/**
	 * 列表查询
	 *
	 * @param filter
	 * @return {@link List}<{@link LsdsIdentifyCodeRel}>
	 */
	@LogPrintPoint

	public List<LsdsIdentifyCodeRel> queryList(LsdsIdentifyCodeRel filter) {
		if (ObjectUtil.isAllEmpty(ReflectUtil.getFieldsValue(filter))) {
            throw new LsdsWlydException(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK);
		}
        filter.setDelFlag(LsdsEnum.DelFlagEnum.NORMAL.getCode());
		return lsdsidentifycoderelMapper.queryByCondition(filter);
	}

	/**
	 * 新增
	 *
	 * @param record
	 * @return int
	 */
	@LogPrintPoint(ret = true)

	public int insertSelective(LsdsIdentifyCodeRel record) {
		if (ObjectUtil.isAllEmpty(ReflectUtil.getFieldsValue(record))) {
            throw new LsdsWlydException(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK);
		}
		setInsertDefaultValue(record);
		return lsdsidentifycoderelMapper.insertSelective(record);
	}

	/**
	* 根据主键修改
	*
	* @param record
	* @return int
	*/
	@LogPrintPoint(ret = true)

	public int updateByPrimaryKeySelective(LsdsIdentifyCodeRel record) {
		checkId(String.valueOf(record.getId()));
		LsdsIdentifyCodeRel recordCheck = new LsdsIdentifyCodeRel();
		BeanUtil.copyProperties(record, recordCheck, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
		recordCheck.setId(null);
		if (ObjectUtil.isAllEmpty(ReflectUtil.getFieldsValue(recordCheck))) {
            throw new LsdsWlydException(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK);
		}
		setUpdateDefaultValue(record);
		return lsdsidentifycoderelMapper.updateByPrimaryKeySelective(record);
	}

	/**
	* 逻辑删除：走修改
	*
	* @param id
	* @return int
	*/
	@LogPrintPoint(ret = true)

	public int deleteByPrimaryKey(Long id){
		checkId(String.valueOf(id));
		LsdsIdentifyCodeRel record=new LsdsIdentifyCodeRel();
		record.setId(id);
		record.setDelFlag(QrsEnum.DelFlagEnum.DELETED.getCode());
		return updateByPrimaryKeySelective(record);
	}

	/**
	 * 根据主键查询
	 *
	 * @param id
	 * @return {@link LsdsIdentifyCodeRel}
	 */
	@LogPrintPoint

	public LsdsIdentifyCodeRel selectByPrimaryKey(Long id) {
		checkId(String.valueOf(id));
		return lsdsidentifycoderelMapper.selectByPrimaryKey(id);
	}


    public List<LsdsIdentifyCodeRelVO> queryBindIdentifyCodeRel(IdentifyCodeRelFilter identifyCodeRelFilter) {
        LsdsIdentifyCodeRel filter = new LsdsIdentifyCodeRel();
        filter.setGoodsId(identifyCodeRelFilter.getGoodsId());
        filter.setCompanyId(identifyCodeRelFilter.getCompanyId());
        Opt.ofBlankAble(identifyCodeRelFilter.getIdentifyCodeType()).ifPresent(filter::setIdentifyCodeType);
        List<LsdsIdentifyCodeRel> lsdsIdentifyCodeRels = queryList(filter);
        return BeanUtil.copyToList(lsdsIdentifyCodeRels, LsdsIdentifyCodeRelVO.class);
    }


    private void setInsertDefaultValue(LsdsIdentifyCodeRel record) {
		record.setId(IdUtil.generateId());
		record.setDelFlag(QrsEnum.DelFlagEnum.NORMAL.getCode());
		record.setCreatedDate(Opt.ofNullable(record.getCreatedDate()).orElseGet(DateUtil::date));
		record.setCreatorId(Opt.ofBlankAble(record.getCreatorId()).orElseGet(() -> JwtUtil.getInstance().getUserBaseIdByToken()));
		record.setUpdatedDate(Opt.ofNullable(record.getUpdatedDate()).orElseGet(DateUtil::date));
		record.setUpdaterId(Opt.ofBlankAble(record.getUpdaterId()).orElseGet(() -> JwtUtil.getInstance().getUserBaseIdByToken()));
	}

	private void setUpdateDefaultValue(LsdsIdentifyCodeRel record) {
		record.setUpdatedDate(Opt.ofNullable(record.getUpdatedDate()).orElseGet(DateUtil::date));
		record.setUpdaterId(Opt.ofBlankAble(record.getUpdaterId()).orElseGet(() -> JwtUtil.getInstance().getUserBaseIdByToken()));
	}

	private void checkId(String id) {
		Opt.ofNullable(id).orElseThrow(() -> new LsdsWlydException(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK));
	}

    /**
     * 根据identifyCode修改
     *
     * @param record
     * @return int
     */
    @LogPrintPoint(ret = true)
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)

    public int updateByIdengtifyCode(LsdsIdentifyCodeRel record) {
        checkId(record.getGoodsId());
        checkId(record.getIdentifyCode());
        LsdsIdentifyCodeRel recordCheck = new LsdsIdentifyCodeRel();
        BeanUtil.copyProperties(record, recordCheck, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        recordCheck.setId(null);
        if (ObjectUtil.isAllEmpty(ReflectUtil.getFieldsValue(recordCheck))) {
            throw new LsdsWlydException(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK);
        }
        setUpdateDefaultValue(record);
        lsdsidentifycoderelMapper.delByIdengtifyCode(record);
        setInsertDefaultValue(record);
        return lsdsidentifycoderelMapper.insertSelective(record);
    }

    /**
     * 逻辑删除
     *
     * @param record
     * @return int
     */
    @LogPrintPoint(ret = true)

    public int deleteSelective(LsdsIdentifyCodeRel record) {
        if (ObjectUtil.isAllEmpty(ReflectUtil.getFieldsValue(record))) {
            throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_BCOM0500.getCode(), "删除参数为空");
        }
        return lsdsidentifycoderelMapper.deleteSelective(record);
    }

    /**
     * 批量查询
     *
     * @param filter
     * @return
     */
    @LogPrintPoint

    public List<LsdsIdentifyCodeRelVO> queryListByGoodsIds(IdentifyCodeRelFilter filter) {
        if (IterUtil.isEmpty(filter.getGoodsIds())) {
            return new ArrayList<>();
        }
        List<LsdsIdentifyCodeRel> list = lsdsidentifycoderelMapper.queryListByGoodsIds(filter);
        if (IterUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(list, LsdsIdentifyCodeRelVO.class);
    }


    public void insertOrUpdate(LsdsIdentifyCodeRel record) {
        LsdsIdentifyCodeRel filter = new LsdsIdentifyCodeRel();
        filter.setGoodsId(record.getGoodsId());
        filter.setCompanyId(record.getCompanyId());
        filter.setIdentifyCodeType(record.getIdentifyCodeType());
        List<LsdsIdentifyCodeRel> codeRelList = queryList(filter);
        if (IterUtil.isNotEmpty(codeRelList)) {
            LsdsIdentifyCodeRel codeRel = IterUtil.getFirst(codeRelList);
            codeRel.setIdentifyCode(record.getIdentifyCode());
            codeRel.setIdentifyCodeName(record.getIdentifyCodeName());
            codeRel.setPayeePhone(record.getPayeePhone());
            codeRel.setUserBaseId(record.getUserBaseId());
            updateByIdengtifyCode(codeRel);
        } else {
            filter.setIdentifyCode(record.getIdentifyCode());
            filter.setIdentifyCodeName(record.getIdentifyCodeName());
            filter.setPayeePhone(record.getPayeePhone());
            filter.setUserBaseId(record.getUserBaseId());
            insertSelective(filter);
        }
    }


    /**
     * 批量查询
     *
     * @param filter
     * @return
     */
    @LogPrintPoint

    public List<LsdsIdentifyCodeRel> queryByIdentifyCodeOrName(IdentifyCodeRelFilter filter) {
        List<LsdsIdentifyCodeRel> list = lsdsidentifycoderelMapper.queryByIdentifyCodeOrName(filter);
        if (IterUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }
}
