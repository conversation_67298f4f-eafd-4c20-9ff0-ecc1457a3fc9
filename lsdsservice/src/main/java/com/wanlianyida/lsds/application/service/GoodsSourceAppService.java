package com.wanlianyida.lsds.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.isoftstone.hig.annotations.LogPrintPoint;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.inter.DistributedLocker;
import com.isoftstone.hig.lsds.api.entity.GoodsKafkaNotice;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.wanlianyida.lsds.application.assembler.GoodsSourceAssembler;
import com.wanlianyida.lsds.application.model.command.audit.GoodsSourceAuditCommand;
import com.wanlianyida.lsds.application.model.command.publish.PublishGoodsSourceAgainCommand;
import com.wanlianyida.lsds.application.model.command.publish.PublishGoodsSourceCommand;
import com.wanlianyida.lsds.application.model.command.publish.PublishOneClickCommand;
import com.wanlianyida.lsds.application.model.dto.GoodsSourceDetailDTO;
import com.wanlianyida.lsds.application.model.dto.GoodsSourceListDTO;
import com.wanlianyida.lsds.application.model.dto.PublishGoodsSourceDTO;
import com.wanlianyida.lsds.application.model.query.GoodsSourceListQuery;
import com.wanlianyida.lsds.application.model.query.GoodsSourceQuery;
import com.wanlianyida.lsds.domain.assembler.GoodsSourceOperatorAssembler;
import com.wanlianyida.lsds.domain.assembler.PublishGoodsSourcePersonalizeAssembler;
import com.wanlianyida.lsds.domain.model.bo.GoodsRefundBO;
import com.wanlianyida.lsds.domain.model.bo.GoodsSourceListBO;
import com.wanlianyida.lsds.domain.model.bo.GoodsSourceQuantityBO;
import com.wanlianyida.lsds.domain.model.condition.GoodsSourceListCondition;
import com.wanlianyida.lsds.domain.model.entity.GoodsSourceEntity;
import com.wanlianyida.lsds.domain.service.BizDomainService;
import com.wanlianyida.lsds.domain.service.GoodsSourceDomainService;
import com.wanlianyida.lsds.domain.service.operate.GoodsOperateExecutor;
import com.wanlianyida.lsds.domain.service.publish.GoodsPublishDomainService;
import com.wanlianyida.lsds.infrastructure.constants.DistributedLockConstant;
import com.wanlianyida.lsds.infrastructure.enums.GoodsOperateTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsPublishTypeEnum;
import com.wanlianyida.lsds.infrastructure.event.model.GoodsAuditEvent;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.exchange.AmountRoundingModeExchangeService;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import static com.isoftstone.hig.lsds.api.util.Constants.MATCHMAKING_GOODS_CHANGE_TOPIC;
import static com.wanlianyida.lsds.infrastructure.enums.GoodsPublishTypeEnum.getPublishHandleByType;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月14日 10:48
 */
@Slf4j
@Service
public class GoodsSourceAppService {

    @Resource
    private GoodsSourceDomainService goodsSourceDomainService;
    @Resource
    private Map<String, GoodsPublishDomainService> goodsPublishDomainServiceMap;
    @Resource
    private GoodsOperateExecutor goodsOperateExecutor;
    @Resource
    private TaskExecutor asynTaskExecutor;

    @Resource
    private Map<String, BizDomainService> lsdsGoodsAuditDomainServiceMap;
    @Resource
    private DistributedLocker distributedLocker;
    @Autowired
    private LsdsKafkaSender lsdsKafkaSender;

    @Resource
    private AmountRoundingModeExchangeService amountRoundingModeService;
    /**
     * 发布货源（新增）
     */
    public ResultMode<PublishGoodsSourceDTO> publish(PublishGoodsSourceCommand command) {
        GoodsPublishDomainService goodsPublishDomainService = goodsPublishDomainServiceMap.get(getPublishHandleByType(command.getPublisherType()));
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        return goodsPublishDomainService.publish(command, tokenInfo);
    }

    /**
     * 发布货源（修改）
     */
    public ResultMode<PublishGoodsSourceDTO> publishAgain(PublishGoodsSourceAgainCommand command) {
        GoodsPublishDomainService goodsPublishDomainService = goodsPublishDomainServiceMap.get(getPublishHandleByType(command.getPublisherType()));
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        return goodsPublishDomainService.publishAgain(command, tokenInfo);
    }


    /**
     * 保存草稿箱后列表一键发布
     */
    public ResultMode<String> publishOneClick(PublishOneClickCommand command) {
        //1、发布货源
        goodsSourceDomainService.publishOneClick(command.getGoodsId());
        GoodsSourceEntity goodsSourceEntity = goodsSourceDomainService.queryGoodsSourceByGoodsId(command.getGoodsId());
        //2、货源自动审核
        asynTaskExecutor.execute(() -> {
            audit(GoodsSourceAssembler.buildGoodsSourceAuditCommand(goodsSourceEntity, null));
        });
        //3、广播通知货源状态变更
        asynTaskExecutor.execute(() -> {
            GoodsKafkaNotice goodsKafkaNotice = PublishGoodsSourcePersonalizeAssembler.getGoodsKafkaNotice(goodsSourceEntity);
            lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(goodsKafkaNotice));
            if (GoodsPublishTypeEnum.DRIVER.getType().equals(goodsSourceEntity.getPublisherType())) {
                lsdsKafkaSender.send(MATCHMAKING_GOODS_CHANGE_TOPIC, goodsSourceEntity.getGoodsId());
            }
        });
        return ResultMode.success(goodsSourceEntity.getGoodsId());
    }

    /**
     * 查询货源详情
     */
    public GoodsSourceDetailDTO queryDetail(GoodsSourceQuery query) {
        return GoodsSourceAssembler.assembleGoodsSourceDetailDTO(goodsSourceDomainService.queryDetail(query.getGoodsId()));
    }

    /**
     * 货源数量扣减
     */
    public void goodsAbatement(GoodsSourceQuantityBO goodsQuantityBO) {
        goodsOperateExecutor.operate(GoodsSourceAssembler.assembleGoodsAbatement(goodsQuantityBO), GoodsOperateTypeEnum.ABATEMENT);
    }

    /**
     * 货源数量返还
     */
    public void goodsRefund(GoodsRefundBO goodsRefundBO) {
        goodsOperateExecutor.operate(GoodsSourceAssembler.assembleGoodsRefund(goodsRefundBO), GoodsOperateTypeEnum.REFUND);
    }

    /**
     * 关闭货源
     */
    public ResultMode<?> closeGoods(String goodsId) {
        GoodsSourceEntity goodsSource = goodsSourceDomainService.queryGoodsSourceByGoodsId(goodsId);
        if (ObjUtil.isNull(goodsSource)) {
            throw new LsdsWlydException("货源不存在");
        }
        return goodsOperateExecutor.operate(GoodsSourceAssembler.assembleCloseGoods(goodsSource), GoodsOperateTypeEnum.CLOSE);
    }

    /**
     * 删除货源
     */
    public ResultMode<?> deleteGoods(String goodsId) {
        GoodsSourceEntity goodsSource = goodsSourceDomainService.queryGoodsSourceByGoodsId(goodsId);
        if (ObjUtil.isNull(goodsSource)) {
            throw new LsdsWlydException("货源不存在");
        }
        return goodsOperateExecutor.operate(GoodsSourceAssembler.assembleDeleteGoods(goodsSource), GoodsOperateTypeEnum.DELETE);
    }

    /**
     * 货源过期定时任务
     */
    public void goodsSourceExpired() {
        try {
            boolean lockFlag = distributedLocker.tryLock(DistributedLockConstant.GOOD_EXPIRED_LOCK, TimeUnit.SECONDS, 0, 180);
            if (!lockFlag) {
                return;
            }
            goodsOperateExecutor.operate(null, GoodsOperateTypeEnum.EXPIRE);
        } finally {
            distributedLocker.unlock(DistributedLockConstant.GOOD_EXPIRED_LOCK);
        }
    }

    /**
     * 获取货源列表
     */
    public ResultMode<GoodsSourceListDTO> queryPage(PagingInfo<GoodsSourceListQuery> pageInfo) {
        GoodsSourceListCondition condition = BeanUtil.toBean(pageInfo.getFilterModel(), GoodsSourceListCondition.class);
        String sourceEntryType = condition.getSourceEntryType();
        //逗号分隔赋值给sourceEntryTypes
        if (StrUtil.isNotBlank(sourceEntryType) && sourceEntryType.contains(",")) {
            condition.setSourceEntryTypes(StrUtil.split(sourceEntryType, ","));
            condition.setSourceEntryType(null);
        }
        //逗号分隔赋值给dealStatusList
        if (StrUtil.isNotBlank(condition.getDealStatus()) && condition.getDealStatus().contains(",")) {
            condition.setDealStatusList(StrUtil.split(condition.getDealStatus(), ","));
            condition.setDealStatus(null);
        }
        //3pl从token中获取公司id
        PagingInfo<GoodsSourceListCondition> conditionPagingInfo = new PagingInfo<>();
        conditionPagingInfo.setFilterModel(condition);
        conditionPagingInfo.setCurrentPage(pageInfo.currentPage);
        conditionPagingInfo.setPageLength(pageInfo.pageLength);
        List<GoodsSourceListDTO> goodsSourceListDTOS = new ArrayList<>();
        //获取总数
        int count = goodsSourceDomainService.queryCount(conditionPagingInfo);
        if (count > 0) {
            //获取列表
            List<GoodsSourceListBO> goodsSourceListBOS = goodsSourceDomainService.queryList(conditionPagingInfo);
            //获取goodsId集合
            List<String> goodsIds = goodsSourceListBOS.stream().map(GoodsSourceListBO::getGoodsId).collect(Collectors.toList());
            Map<String, Integer> roundingModeMap = amountRoundingModeService.getRoundingMode(goodsIds);
            goodsSourceListDTOS = GoodsSourceAssembler.assembleGoodsSourceListDTO(goodsSourceListBOS,roundingModeMap);
        }
        return ResultMode.successPageList(goodsSourceListDTOS, count);
    }

    /**
     * 货源审核
     *
     * @param command
     * @return
     */
    @LogPrintPoint
    public ResultMode audit(GoodsSourceAuditCommand command) {
        String publisherType = command.getPublisherType();
        String auditHandleByType = GoodsPublishTypeEnum.getAuditHandleByType(publisherType);
        BizDomainService bizDomainService = lsdsGoodsAuditDomainServiceMap.get(auditHandleByType);
        if (ObjUtil.isNull(bizDomainService)) {
            return ResultMode.fail("货源审核没有找到对应的处理器");
        }
        return bizDomainService.handleBiz(GoodsSourceOperatorAssembler.buildGoodsSourceAuditBO(command));
    }

    /**
     * 处理货源自动审核事件
     */
    @LogPrintPoint(ret = true)
    public ResultMode handleGoodsAutoAuditEvent(GoodsAuditEvent event) {
        GoodsSourceEntity goodsSource = event.getGoodsSourceEntity();
        PublishGoodsSourceCommand goodsSourceCommand = event.getGoodsSourceCommand();
        return audit(GoodsSourceAssembler.buildGoodsSourceAuditCommand(goodsSource,goodsSourceCommand));
    }
}
