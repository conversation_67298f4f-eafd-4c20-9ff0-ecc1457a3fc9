package com.wanlianyida.lsds.application.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.LogHelper;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsInfo;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsInfoFilter;
import com.isoftstone.hig.platform.api.client.PlatformCommonInterClient;
import com.isoftstone.hig.platform.api.entity.PlatformUmUserbaseinfo;
import com.wanlianyida.lsds.infrastructure.config.FormsAuthTrader;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsGoodsInfoMapper;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 货品事务处理类
 * <AUTHOR>
 * 创建时间 2020/6/17
 */
@Service
public class LsdsGoodsInfoAppService {

//#endregion
    //#region

    @Autowired
    PlatformCommonInterClient platformCommonInterClient;

    /**
     * 新增
     * 创建者：cgb
     * 创建时间：2020/6/16
     * @param model
     * @return {@code ResultMode<String>}
     */
    public ResultMode<String> lsdsGoodsInfoAdd(LsdsGoodsInfo model) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {

            LsdsGoodsInfoMapper infoDao = SpringContextUtil.getBeanByClass(LsdsGoodsInfoMapper.class);
            //重复性校验
            List<LsdsGoodsInfo> infoList = infoDao.getByNameType(model);
            if(null != infoList && infoList.size() > 0){
                errMsg = "货品名称及类型已存在！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }
            String infoId = String.valueOf(IdUtil.generateId());
            PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
            model.setGoodsInfoId(infoId);
            model.setCreateBy(userInfoFromCache.getUserBaseId());
            model.setModifyBy(userInfoFromCache.getUserBaseId());
            infoDao.add(model);

            resultmodel.getModel().add("新增货品信息成功，货品ID:" + infoId);
        } catch (Exception ex) {
            errMsg = "新增货品信息异常";
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
            LogHelper.writeError(errMsg, ex);
        }
        return resultmodel;
    }

    /**
     * 删除
     * 创建者：cgb
     * 创建时间：2020/6/16
     * @param model
     * @return {@code ResultMode<String>}
     */
    public ResultMode<String> lsdsGoodsInfoDel(LsdsGoodsInfo model) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
            LsdsGoodsInfoMapper infoDao = SpringContextUtil.getBeanByClass(LsdsGoodsInfoMapper.class);
            model.setModifyBy(userInfoFromCache.getUserBaseId());
            infoDao.remove(model);

            resultmodel.getModel().add("删除货品信息成功！");
        } catch (Exception ex) {
            errMsg = "删除货品信息异常";
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
            LogHelper.writeError(errMsg, ex);
        }
        return resultmodel;
    }

    /**
     * 更新
     * 创建者：cgb
     * 创建时间：2020/6/16
     * @param model
     * @return {@code ResultMode<String>}
     */
    public ResultMode<String> LsdsGoodsInfoUpdate(LsdsGoodsInfo model) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
            LsdsGoodsInfoMapper infoDao = SpringContextUtil.getBeanByClass(LsdsGoodsInfoMapper.class);
            LsdsGoodsInfo oldGoodsInfo = infoDao.getModel(model);
            List<LsdsGoodsInfo> infoList = infoDao.getByNameType(model);
            if(null != infoList && infoList.size() > 0){
                if(!oldGoodsInfo.getGoodsInfoId().equals(infoList.get(0).getGoodsInfoId())){
                    errMsg = "货品名称及类型已存在！";
                    resultmodel.setErrMsg(errMsg);
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }
            }
            model.setModifyBy(userInfoFromCache.getUserBaseId());
            infoDao.modify(model);

            resultmodel.getModel().add("修改货品信息成功！");
        } catch (Exception ex) {
            errMsg = "修改货品信息异常";
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
            LogHelper.writeError(errMsg, ex);
        }
        return resultmodel;
    }

    /**
     * 分页查询
     * 创建者：cgb
     * 创建时间：2020/6/16
     * @param pageInfo
     * @return {@code ResultMode<LsdsGoodsInfo>}
     */
    public ResultMode<LsdsGoodsInfo> getGoodsInfoListPage(PagingInfo<LsdsGoodsInfoFilter> pageInfo) {
        ResultMode<LsdsGoodsInfo> returnModel = new ResultMode<LsdsGoodsInfo>();

        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        LsdsGoodsInfoMapper infoDao = SpringContextUtil.getBeanByClass(LsdsGoodsInfoMapper.class);
        List<LsdsGoodsInfo> goodsInfoList = infoDao.getGoodsInfoList(pageInfo.filterModel);
        PageInfo<LsdsGoodsInfo> pageInfoList = new PageInfo<>(goodsInfoList);
        Long total = pageInfoList.getTotal();
        returnModel.setTotal(total.intValue());
        returnModel.setModel(goodsInfoList);
        return returnModel;
    }

    /**
     * 查询详情
     * 创建者：cgb
     * 创建时间：2020/6/16
     * @param model
     * @return {@code ResultMode<LsdsGoodsInfo>}
     */
    public ResultMode<LsdsGoodsInfo> lsdsGoodsInfoGet(LsdsGoodsInfo model) {
        String errMsg = "";
        ResultMode<LsdsGoodsInfo> resultmodel = new ResultMode<LsdsGoodsInfo>();
        try {
            LsdsGoodsInfoMapper infoDao = SpringContextUtil.getBeanByClass(LsdsGoodsInfoMapper.class);
            LsdsGoodsInfo goodsInfo = infoDao.getModel(model);
            resultmodel.setTotal(1);
            resultmodel.getModel().add(goodsInfo);
        } catch (Exception ex) {
            errMsg = "获取货品详情信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }

}
