package com.wanlianyida.lsds.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.isoftstone.hig.common.constants.PrefixCodeConstants;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.*;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.crm.api.entity.CrmCustomer;
import com.isoftstone.hig.crm.api.filter.CrmCustomerFilter;
import com.isoftstone.hig.crm.api.inter.CrmCustomerInter;
import com.isoftstone.hig.framework.component.file.utils.ExcelUtil;
import com.isoftstone.hig.lsds.api.command.DriverGoodsExtendCommand;
import com.isoftstone.hig.lsds.api.command.LsdsMatchmakingConfigUpdateCommand;
import com.isoftstone.hig.lsds.api.dto.GoodsStatisticsDTO;
import com.isoftstone.hig.lsds.api.dto.LsdsMatchmakingConfigDTO;
import com.isoftstone.hig.lsds.api.dto.StatisticsDTO;
import com.isoftstone.hig.lsds.api.entity.*;
import com.isoftstone.hig.lsds.api.enums.GoodsKindEnum;
import com.isoftstone.hig.lsds.api.filter.DriverGoodsFilter;
import com.isoftstone.hig.lsds.api.filter.DriverOfferFilter;
import com.isoftstone.hig.lsds.api.filter.IdentifyCodeRelFilter;
import com.isoftstone.hig.lsds.api.mvcvo.DriverOfferGenBusiVO;
import com.isoftstone.hig.lsds.api.mvcvo.GoodsDeductionQuantityVO;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsIdentifyCodeRelVO;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsWaybillVo;
import com.isoftstone.hig.lsds.api.query.LsdsMatchmakingConfigQuery;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.lsds.api.util.Result;
import com.isoftstone.hig.platform.api.entity.PlatformCmOperationMainBody;
import com.isoftstone.hig.platform.api.entity.PlatformCmSensitiveWord;
import com.isoftstone.hig.platform.api.entity.PlatformUmCompanyMain;
import com.isoftstone.hig.platform.api.entity.PlatformUmUserbaseinfo;
import com.isoftstone.hig.platform.api.enums.PlatformEnum;
import com.isoftstone.hig.platform.api.filter.PlatformCmOperationMainBodyFilter;
import com.isoftstone.hig.platform.api.filter.PlatformCmSensitiveWordFilter;
import com.isoftstone.hig.platform.api.filter.PlatformQrCodeFilter;
import com.isoftstone.hig.platform.api.inter.PlatformCmOperationMainBodyInter;
import com.isoftstone.hig.platform.api.inter.PlatformCmSensitiveWordInter;
import com.isoftstone.hig.platform.api.inter.PlatformUmCompanyInter;
import com.isoftstone.hig.platform.api.mvcvo.PlatformPricingTypeConfigVo;
import com.isoftstone.hig.platform.api.mvcvo.PlatformQrCodeRelationVO;
import com.isoftstone.hig.qrs.api.constants.QrsEnum;
import com.isoftstone.hig.tcs.api.mvcvo.TcsDriverVO;
import com.isoftstone.hig.tms.api.command.message.CancelWaybillMsgCommand;
import com.isoftstone.hig.tms.api.entity.TmsWaybill;
import com.isoftstone.hig.tms.api.enums.PricingMethodEnum;
import com.isoftstone.hig.tms.api.inter.TmsWaybillInter;
import com.isoftstone.hig.tms.api.query.TmsWaybillFilter;
import com.wanlianyida.lsds.domain.model.entity.InsuranceEntity;
import com.wanlianyida.lsds.domain.service.DriverGoodsExtendDomainService;
import com.wanlianyida.lsds.domain.service.LsdsIdentifyCodeRelDomainService;
import com.wanlianyida.lsds.domain.service.LsdsMatchmakingDomainService;
import com.wanlianyida.lsds.infrastructure.config.FormsAuthTrader;
import com.wanlianyida.lsds.infrastructure.enums.DriverDealStatusEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsDeductibleEnum;
import com.wanlianyida.lsds.infrastructure.exception.LsdsStatusCodeEnum;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.exchange.*;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.CrmCompanyLineAddressBO;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.PlatformTaxRateFilter;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.QrsInvitationIdentifyCodeBO;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.WoaLogisticsPlanBO;
import com.wanlianyida.lsds.infrastructure.repository.mapper.DriverGoodsMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.GoodsRecordMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsSequenceMapper;
import com.wanlianyida.lsds.infrastructure.util.AopSpringFactory;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import com.wanlianyida.lsds.infrastructure.util.ZeroUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 司机货源Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@Slf4j
@Service
public class DriverGoodsAppService {
    @Resource
    private DriverGoodsMapper driverGoodsMapper;

    @Resource
    private DriverOfferAppService driverOfferService;
    @Resource
    private SearchExchangeService searchExchangeService;
    @Resource
    private TmsWaybillInter tmsWaybillInter;

    @Resource
    private PlatformUmCompanyInter platformUmCompanyInter;

    @Resource
    private CrmCustomerInter crmCustomerInter;

    @Resource
    private PlatformCmOperationMainBodyInter platformCmOperationMainBodyInter;

    @Resource
    private LsdsGoodsAppService lsdsGoodsTran;

    @Resource
    private PlatformCmSensitiveWordInter platformCmSensitiveWordInter;

    @Autowired
    private LsdsKafkaSender lsdsKafkaSender;

    @Resource
    private GoodsDeductibleAppService goodsDeductibleService;

    @Resource
    private DcsAppService dcsBusiness;


    @Resource
    private AmountRoundingModeExchangeService amountRoundingModeService;
    @Resource
    private LsdsGoodsAttentionAppService goodsAttentionServiceTran;

    @Resource
    private Executor asyncContextAwareExecutor;

    @Resource
    private QrsExchangeService qrsExchangeService;

    @Resource
    private LsdsIdentifyCodeRelDomainService lsdsIdentifyCodeRelService;

    @Resource
    private WoaExchangeService woaExchangeService;

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private DcsExchangeService dcsExchangeService;
    @Resource
    private ImsExchangeService imsExchangeService;
    @Resource
    private LsdsMatchmakingDomainService lsdsMatchmakingService;
    @Resource
    private DriverGoodsExtendDomainService driverGoodsExtendBusiness;
    @Resource
    private TcsExchangeService tcsExchangeService;

    @Resource
    private LsdsSequenceMapper lsdsSequenceMapper;

    @Resource
    private DriverGoodsRuleAppService driverGoodsRuleAppService;

    /**
     * 查询司机货源
     *
     * @param id 司机货源ID
     * @return 司机货源
     */
    public DriverGoods getById(String id) {
        DriverGoods driverGoods = driverGoodsMapper.getById(id);
        return driverGoods;
    }

    /**
     * 查询司机货源列表
     *
     * @param driverGoodsFilter 司机货源
     * @return 司机货源
     */
    public List<DriverGoods> listByEntity(DriverGoodsFilter driverGoodsFilter) {
        if (ObjectUtil.isAllEmpty(ReflectUtil.getFieldsValue(driverGoodsFilter))) {
            return new ArrayList<>();
        }
        List<DriverGoods> driverGoodss = driverGoodsMapper.listByEntity(driverGoodsFilter);
        return driverGoodss;
    }

    /**
     * 新增司机货源
     *
     * @param driverGoods 司机货源
     * @return 结果
     */
    @Transactional
    public ResultMode<DriverGoods> save(DriverGoods driverGoods) throws Exception {

        if (StringUtils.isBlank(driverGoods.getDealStatus()) || (!driverGoods.getDealStatus().equals(Constants.GOODS_STATUS_NOT_RELEASED) && !driverGoods.getDealStatus().equals(Constants.GOODS_STATUS_AUDIT))) {
            return new ResultMode(LsdsStatusCodeEnum.BUSS_ERROR_BLSD0102.getCode(), LsdsStatusCodeEnum.BUSS_ERROR_BLSD0102.getMsg());
        }


        //设置创建和修改用户信息
        PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
        if (userInfoFromCache == null) {
            return new ResultMode(CommonStatusCodeEnum.USER_ERROR_PARAM_NOTNULL.getCode(), "获取当前登录用户数据为空，请重新登录后再试！");
        }

        PlatformCmOperationMainBody mainBody = null;
        //发货方主体信息
        String companyId = StrUtil.isNotBlank(driverGoods.getCompanyId()) ? driverGoods.getCompanyId() : userInfoFromCache.getCompanyId();
        ResultMode<PlatformCmOperationMainBody> bodyMode = platformCmOperationMainBodyInter.getMainBodyInfo(companyId);
        if (bodyMode.getSucceed() && !CollectionUtils.isEmpty(bodyMode.getModel())) {
            mainBody = bodyMode.getModel().get(0);
        }
        //判断所选的客户对应的企业ID为空(发货方是网络货运主体时)，目前发现部分客户数据企业ID为空
        if (mainBody != null && !StringUtils.equals("1", mainBody.getType())) {
            if (StringUtils.isNotBlank(driverGoods.getCustomerId())) {
                PagingInfo<CrmCustomerFilter> pageInfo = new PagingInfo<CrmCustomerFilter>();
                CrmCustomerFilter filter = new CrmCustomerFilter();
                filter.setCustomerId(driverGoods.getCustomerId());
                pageInfo.setFilterModel(filter);
                String chooseCompanyId = "";
                //发布货源时选中的客户
                ResultMode<CrmCustomer> customerResultMode = crmCustomerInter.crmCustomerPaging(pageInfo);
                if (customerResultMode.getSucceed() && !CollectionUtils.isEmpty(customerResultMode.getModel())) {
                    chooseCompanyId = customerResultMode.getModel().get(0).getCustomerCompanyId();
                }
                if (StringUtils.isBlank(chooseCompanyId)) {
                    return new ResultMode(CommonStatusCodeEnum.USER_ERROR_PARAM_NOTNULL.getCode(), "所选客户对应的企业ID为空，请重新维护客户信息后再试！");
                }
            }
        }
        //判断所选的客户对应的企业ID为空(发货方是网络货运主体时)，目前发现部分客户数据企业ID为空
        ResultMode x = checkCustomCompanyId(driverGoods, mainBody);
        if (x != null) return x;
        driverGoods.setCompanyId(companyId);

        //设置剩余数量
        driverGoods.setRemainingQuantity(driverGoods.getTotalQuantity());
        //设置提交审核时间
        if (Constants.GOODS_STATUS_AUDIT.equals(driverGoods.getDealStatus())) {
            driverGoods.setSubmitAuditDate(new Date());
        }
        //如果是公开询价，就把价格置为空
        if (driverGoods.getEnquiryType().equals(Constants.ENQUIRY_TYPE_OPEN)) {
            driverGoods.setEnquiryTypeBasePrice(null);
            driverGoods.setEnquiryTypeBaseOpenTicket(null);
        }

        //同步货源操作记录
        GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
        LsdsGoodsRecord lgr = LsdsGoodsRecord.of().setRecordId(String.valueOf(IdUtil.generateId())).setCompanyId(userInfoFromCache.getCompanyId());
        if (Constants.GOODS_STATUS_NOT_RELEASED.equals(driverGoods.getDealStatus())) {
            lgr.setOperateStatus("1");
            lgr.setOperateContent("货源保存");
        } else {
            lgr.setOperateStatus("2");
            lgr.setOperateContent("货源发布");
        }
        lgr.setUserId(userInfoFromCache.getUserBaseId());
        lgr.setUserLoginName(userInfoFromCache.getLoginName());
        lgr.setUserName(userInfoFromCache.getUsername());

        //状态为待审核时，需要确定是否进行自动审核
        List<String> errors = null;
        if (Constants.GOODS_STATUS_AUDIT.equals(driverGoods.getDealStatus())) {
            errors = autoGoodsAudit(driverGoods, lgr, errors);
        }

        String manualConfirmFlag = driverGoods.getManualConfirmFlag();
        if (!org.springframework.util.StringUtils.hasText(manualConfirmFlag)) {
            //人工确认标志【10-自动确认；20-人工确认】
            driverGoods.setManualConfirmFlag("20");
        }

        //已通过创建线路时赋值
//        setTransportMileageByGoodsId(driverGoods);
        if (StrUtil.equals(driverGoods.getPremiumServStatus(), "10")) {
            //查询履约保证金额
            String paramValue = platformExchangeService.getParamValue("2074");
            driverGoods.setGuarAmount(StrUtil.isBlank(paramValue) ? BigDecimal.ZERO : new BigDecimal(paramValue));
        }
        log.info(">>>>>>>driverGoodsId:{}----driverGoods:{}", driverGoods.getId(), JSONUtil.toJsonStr(driverGoods));
        driverGoods.setFreightSurvivalRate(Optional.ofNullable(driverGoods.getFreightSurvivalRate()).orElse(BigDecimal.ZERO));
        //根据ID是否为空判断是新增还是修改
        if (StringUtils.isNotEmpty(driverGoods.getId())) {

            log.info(">>>>>2324sdf>>driverGoods:{}", JSONUtil.toJsonStr(driverGoods));
            //创建线路0424
            // addCrmLine(driverGoods);

            // ES中更新货源对应亏涨吨
            Boolean saveEsSuccess = dcsBusiness.saveLsdsGoodsDeductible(driverGoods.getLsdsGoodsDeductible());
            if (!saveEsSuccess) {
                throw new LsdsWlydException(LsdsStatusCodeEnum.BUSS_ERROR_BLSDS0300);
            }

            this.updateById(driverGoods);
            if (ObjUtil.isNotNull(driverGoods.getMatchmakingConfigAddCommand())) {
                //更新撮合配置表
                lsdsMatchmakingService.updateConfig(BeanUtil.toBean(driverGoods.getMatchmakingConfigAddCommand(), LsdsMatchmakingConfigUpdateCommand.class));
            }
        } else {
//            String goodsId = genericGoodsId();
            String goodsId = lsdsSequenceMapper.generateGoodsPrimaryNum(PrefixCodeConstants.GOODS_D, DateUtil.formatDate(DateUtil.date()));
            driverGoods.setId(goodsId);
            driverGoods.setGoodsId(goodsId);
            driverGoods.setCreateBy(userInfoFromCache.getUserBaseId());
            driverGoods.setCreateName(userInfoFromCache.getUsername());
            driverGoods.setCreateDate(new Date());
            driverGoods.setOrderAlias(StrUtil.nullToDefault(driverGoods.getOrderAlias(), ""));
            //添加司机货源 0424
            log.info("....driverGoodsMapper.save:{}", JSON.toJSONString(driverGoods));
            driverGoodsMapper.save(driverGoods);
            if (ObjUtil.isNotNull(driverGoods.getMatchmakingConfigAddCommand())) {
                driverGoods.getMatchmakingConfigAddCommand().setBusId(driverGoods.getGoodsId());
                // 新增更新撮合配置表
                lsdsMatchmakingService.addConfig(driverGoods.getMatchmakingConfigAddCommand());
            }

            // 保存金额取整方式
            amountRoundingModeService.saveRoundingMode(driverGoods.getGoodsId(), driverGoods.getRoundingMode());

            //保存抹零方式
            dcsExchangeService.saveZeroRoundingMethod(driverGoods.getGoodsId(), driverGoods.getZeroRoundingMethod());

            // ES中增加货源对应亏涨吨 调用公共方法
            LsdsGoodsDeductible lsdsGoodsDeductible = driverGoods.getLsdsGoodsDeductible();
            lsdsGoodsDeductible.setBizId(driverGoods.getGoodsId());
            Boolean saveEsSuccess = dcsBusiness.saveLsdsGoodsDeductible(lsdsGoodsDeductible);
            if (!saveEsSuccess) {
                throw new LsdsWlydException(LsdsStatusCodeEnum.BUSS_ERROR_BLSDS0300);
            }

            //创建线路0424
            //    addCrmLine(driverGoods);
            //    driverGoodsMapper.updateByGoodsId(driverGoods);
        }

        // 网络货运 保险登记
        InsuranceEntity insuranceEntity = BeanUtil.toBean(driverGoods, InsuranceEntity.class);
        imsExchangeService.guaranteeRegistration(insuranceEntity);
        //记录货源扩展信息
        DriverGoodsExtendCommand driverGoodsExtendCommand = BeanUtil.toBean(driverGoods, DriverGoodsExtendCommand.class);
        driverGoodsExtendBusiness.saveDriverGoodsExtendData(driverGoodsExtendCommand);
        //保存分次支付金额到es
        searchExchangeService.saveDcsPartPay(driverGoods.getGoodsId(), driverGoods.getAdvancePayment(), driverGoods.getReceiptAmount());
        //保存业务扩展信息
        saveTmsBizExtend(driverGoods);
        //同步货源操作记录保存
        lgr.setGoodsId(driverGoods.getGoodsId());
        recDao.add(lgr);

        //自动审核通过，执行审核操作
        if (errors != null
            && errors.size() == 0
            && driverGoods.getFreightType().equals(Constants.FREIGHT_TYPE_NETWORK)) {
            this.audit(driverGoods);
        }

        if (StringUtils.equals(driverGoods.getDealStatus(), Constants.GOODS_STATUS_AUDIT)) {
            transactionBodyAutoAudit(driverGoods, userInfoFromCache, errors);
        }
//       增加货源绑定关系操作
        saveRelationList(driverGoods.getQrsCodeRelations(), driverGoods.getGoodsId(), driverGoods.getCompanyId());
        // 广播通知货源状态变更
        GoodsKafkaNotice goodsKafkaNotice = BeanUtil.copyProperties(driverGoods, GoodsKafkaNotice.class);
        if (StrUtil.isNotBlank(driverGoods.getDispatcherPhone()) && StrUtil.equals(driverGoods.getFreightType(), Constants.FREIGHT_TYPE_NETWORK)) {
            // 自动审核通过的woa保存并推送渠道货源线索，否则只保存不推送
            if (StrUtil.equals(driverGoods.getDealStatus(), Constants.GOODS_STATUS_RELEASED)) {
                goodsKafkaNotice.setSaveWoaGoodsClue(true);
                goodsKafkaNotice.setPushWoaGoodsClue(true);
                log.info("save#发布后自动审核通过，推送到woa->{}", JSONUtil.toJsonStr(goodsKafkaNotice));
            } else {
                goodsKafkaNotice.setSaveWoaGoodsClue(true);
                log.info("save#发布后自动审核不通过，推送到woa->{}", JSONUtil.toJsonStr(goodsKafkaNotice));
            }
        }
        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(goodsKafkaNotice));

        return ResultMode.success(driverGoods);
    }

    /**
     * 保存业务扩展信息
     */
    private void saveTmsBizExtend(DriverGoods driverGoods) {
        //查询业务类型、差价率和成本价
        if (StrUtil.isBlank(driverGoods.getTrafficClassification())) {
            PlatformTaxRateFilter filter = new PlatformTaxRateFilter()
                .setFreightType(driverGoods.getFreightType())
                .setTransportMileage(driverGoods.getTransportMileage())
                .setGoodsType(driverGoods.getGoodsType())
                .setMainBodyId(driverGoods.getNetworkMainBodyId())
                .setCompanyId(driverGoods.getCompanyId())
                .setDirectPayment(true);
            com.isoftstone.hig.lsds.api.entity.BizExtendVO bizExtendVO = platformExchangeService.getTaxRate(filter);
            if (ObjUtil.isNull(bizExtendVO)) {
                return;
            }
            driverGoods.setTrafficClassification(bizExtendVO.getTrafficClassification());
            driverGoods.setFreightSurvivalRate(bizExtendVO.getFreightDiffRate());
            driverGoods.setCostPrice(bizExtendVO.getCostPrice());
        }
        driverGoods.setFreightDiffRate(driverGoods.getFreightSurvivalRate());
        searchExchangeService.saveTmsBizExtend(new BizExtendVO()
            .setBizId(driverGoods.getGoodsId())
            .setTrafficClassification(driverGoods.getTrafficClassification()));
//            .setFreightDiffRate(driverGoods.getFreightDiffRate())
//            .setCostPrice(driverGoods.getCostPrice()));
    }

    /**
     * 保存关系操作
     *
     * @param list
     * @param goodsId
     * @param companyId
     */
    private void saveRelationList(List<LsdsIdentifyCodeRelVO> list, String goodsId, String companyId) {
        log.info("增加货源绑定货源码和收款人:{}", JSONUtil.toJsonStr(list));
        //先清除草稿关系
        LsdsIdentifyCodeRel codeRel = new LsdsIdentifyCodeRel();
        codeRel.setGoodsId(goodsId);
        codeRel.setCompanyId(companyId);
        codeRel.setIdentifyCodeType(QrsEnum.IdentifyCodeTypeEnum.COMPANY_GOODS_CODE.getCode());
        lsdsIdentifyCodeRelService.deleteSelective(codeRel);

        if (IterUtil.isEmpty(list)) {
            return;
        }

        list.forEach(item -> {
            if (StrUtil.isEmpty(item.getIdentifyCode())) {
                //过滤无效数据
                return;
            }
            if (item.getIdentifyCodeType().equals(QrsEnum.IdentifyCodeTypeEnum.COMPANY_GOODS_CODE.getCode())) {
                //更新货源码关系表
                LsdsIdentifyCodeRel goodsCode = new LsdsIdentifyCodeRel();
                goodsCode.setGoodsId(goodsId);
                goodsCode.setCompanyId(companyId);
                goodsCode.setIdentifyCodeType(QrsEnum.IdentifyCodeTypeEnum.COMPANY_GOODS_CODE.getCode());
                goodsCode.setIdentifyCode(item.getIdentifyCode());
                goodsCode.setIdentifyCodeName(item.getIdentifyCodeName());
                lsdsIdentifyCodeRelService.insertOrUpdate(goodsCode);
            } else {
                //更新收款码
                LsdsIdentifyCodeRel code = new LsdsIdentifyCodeRel();
                code.setGoodsId(goodsId);
                code.setCompanyId(companyId);
                code.setIdentifyCodeType(QrsEnum.IdentifyCodeTypeEnum.COMPANY_RESERVE_CODE.getCode());
                code.setIdentifyCode(item.getIdentifyCode());
                code.setIdentifyCodeName(item.getPayeeName());
                code.setUserBaseId(item.getUserBaseId());
                code.setPayeePhone(item.getPayeePhone());
                lsdsIdentifyCodeRelService.insertOrUpdate(code);
            }
        });
    }

    private void transactionBodyAutoAudit(DriverGoods driverGoods, PlatformUmUserbaseinfo userInfoFromCache, List<String> errors) throws Exception {
        //查询当前登录用户是否为交易签约主体
        Boolean transactionBody = platformCmOperationMainBodyInter.isTransactionBody(userInfoFromCache.getCompanyId());
        //如果是交易签约主体，则将数据进行自动审核，改为已审核状态
        if (transactionBody) {
            //获取企业维护的默认交易签约主体
            ResultMode<PlatformUmCompanyMain> platformUmCompanyMainResultMode = platformUmCompanyInter.selectPlatformUmCompanyMainsEnableByCompanyIdOrSocialCreditCode(userInfoFromCache.getCompanyId(), "", "1");
            if (platformUmCompanyMainResultMode.getSucceed()) {
                List<PlatformUmCompanyMain> list = platformUmCompanyMainResultMode.getModel().stream()
                    .filter(obj -> StringUtils.isNotBlank(obj.getDefaultMain()) && obj.getDefaultMain().equals("1"))
                    .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(list)) {
                    //设置货源的默认交易签约主体
                    driverGoods.setTransactionContractingBodyId(list.get(0).getMainId());
                } else {
                    throw new Exception("未设置默认交易签约主体");
                }
            } else {
                throw new Exception("查询默认交易签约主体失败");
            }
            //设置状态为已审核
            driverGoods.setDealStatus(Constants.GOODS_STATUS_RELEASED);
            //设置上浮类型为按单价上浮
            driverGoods.setFeeClearType(Constants.FEE_CLEAR_TYPE_UN_PRICE);
            //设置上浮单价为0
            driverGoods.setFeeClearValue(new BigDecimal("0"));
            //如果自动审核不通，网络货运主体和交易签约主体的货源状态改为审核不通过 40
            if (!CollectionUtils.isEmpty(errors)) {
                driverGoods.setDealStatus(Constants.GOODS_STATUS_AUDIT);
            }
            //执行审核操作
            this.audit(driverGoods);
        }
    }

    private List<String> autoGoodsAudit(DriverGoods driverGoods, LsdsGoodsRecord lgr, List<String> errors) throws Exception {
        //自动审核逻辑,调用接口，入参（companyId,maniBodyId）
        Map<String, String> model = new HashMap<>();
        model.put("companyId", driverGoods.getCompanyId());
        model.put("mainBodyId", driverGoods.getNetworkMainBodyId());
        model.put("bizType", Constants.DRIVER_GOODS_AUTO_CHECK);

        ResultMode<Boolean> booleanResultMode = platformUmCompanyInter.judgeAutoAudit(model);
        log.info("司机货源自动审核请求出参：{}", booleanResultMode.toString());
        List<Boolean> booleans = booleanResultMode.getModel();
        boolean check = (booleans != null && booleans.size() > 0) ? booleans.get(0) : false;
        log.info("是否司机货源自动审核：{},货运类型：{}", check, driverGoods.getFreightType());
        StringBuffer remark = new StringBuffer();
        String content = "";
        if (check && driverGoods.getFreightType().equals(Constants.FREIGHT_TYPE_NETWORK)) {
            errors = driverGoodsRuleAppService.check(driverGoods);
            if (CollectionUtils.isEmpty(errors)) {
                //审核通过
                driverGoods.setDealStatus(Constants.GOODS_STATUS_RELEASED);
                content = "货源自动审核成功";

                // 获取取上浮金额
                setGoodsFloatFeeClearValue(driverGoods);

                PagingInfo pagingInfo = new PagingInfo();
                PlatformCmOperationMainBodyFilter filter = new PlatformCmOperationMainBodyFilter();
                filter.setType("1"); //交易签约主体类型 1
                pagingInfo.setFilterModel(filter);
                ResultMode<PlatformCmOperationMainBody> resultMode = platformCmOperationMainBodyInter.listMainBodyPage(pagingInfo);
                if (resultMode.getSucceed()) {
                    List<PlatformCmOperationMainBody> mainBodies = resultMode.getModel();
                    if (!CollectionUtils.isEmpty(mainBodies)) {
                        PlatformCmOperationMainBody platformCmOperationMainBody = mainBodies.get(0);
                        driverGoods.setTransactionContractingBodyId(platformCmOperationMainBody.getOperationMainBodyId());

                    } else {
                        throw new Exception("查询默认交易签约主体失败");
                    }
                } else {
                    throw new Exception("查询默认交易签约主体失败");
                }
            } else {
                //审核不通过
                driverGoods.setDealStatus(Constants.OFFER_STATUS_WAIT_CONFIRM);
                for (String error : errors) {
                    remark.append(error).append(",");
                }
                content = "货源自动审核失败";
            }


            log.info(content);
            lgr.setOperateStatus("1");
            lgr.setOperateContent(content);
            lgr.setUserId("autoCheckAdminId");
            lgr.setUserLoginName("autoCheckAdmin");
            lgr.setUserName("自动审核管理员");
            lgr.setRemark(remark.toString());
        }
        return errors;
    }


    private ResultMode checkCustomCompanyId(DriverGoods driverGoods, PlatformCmOperationMainBody mainBody) {
        if (mainBody != null && !StringUtils.equals("1", mainBody.getType())) {
            if (StringUtils.isNotBlank(driverGoods.getCustomerId())) {
                PagingInfo<CrmCustomerFilter> pageInfo = new PagingInfo<CrmCustomerFilter>();
                CrmCustomerFilter filter = new CrmCustomerFilter();
                filter.setCustomerId(driverGoods.getCustomerId());
                pageInfo.setFilterModel(filter);
                String chooseCompanyId = "";
                //发布货源时选中的客户
                ResultMode<CrmCustomer> customerResultMode = crmCustomerInter.crmCustomerPaging(pageInfo);
                if (customerResultMode.getSucceed() && !CollectionUtils.isEmpty(customerResultMode.getModel())) {
                    chooseCompanyId = customerResultMode.getModel().get(0).getCustomerCompanyId();
                }
                if (StringUtils.isBlank(chooseCompanyId)) {
                    return new ResultMode(CommonStatusCodeEnum.BUSS_ERROR_BCOM0500.getCode(), "所选客户对应的企业ID为空，请重新维护客户信息后再试！");
                }
            }
        }
        return null;
    }

    private void setGoodsFloatFeeClearValue(DriverGoods driverGoods) {
        PlatformUmCompanyMain companyMainFilter = new PlatformUmCompanyMain();
        companyMainFilter.setCompanyId(driverGoods.getCompanyId());
        companyMainFilter.setMainId(driverGoods.getNetworkMainBodyId());
        companyMainFilter.setMainType(Constants.FREIGHT_TYPE_NETWORK);
        PlatformUmCompanyMain companyMainModel = platformExchangeService.getCompanyMainModel(companyMainFilter);
        driverGoods.setFeeClearType(companyMainModel.getFeeClearType());
        driverGoods.setFeeClearValue(companyMainModel.getFeeClearValue());
    }


    /**
     * @return com.wanlianyida.framework.lgicommon.entity.ResultMode
     * <AUTHOR>
     * @Description 司机货源审核
     * @Date 2021/6/2
     * @Param [driverGoods]
     **/
    @Transactional
    public ResultMode audit(DriverGoods driverGoods) throws Exception {
        if (StringUtils.isBlank(driverGoods.getDealStatus()) || (!StringUtils.equals(driverGoods.getDealStatus(), Constants.GOODS_STATUS_RELEASED) && !StringUtils.equals(driverGoods.getDealStatus(), Constants.GOODS_STATUS_REFUSE))) {
            return new ResultMode(CommonStatusCodeEnum.BUSS_ERROR_BCOM0500.getCode(), "审核状态传输错误，请确认后再试！");
        }

        //设置创建和修改用户信息
        PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();

        //设置剩余数量
        driverGoods.setRemainingQuantity(driverGoods.getTotalQuantity());
        driverGoods.setAuditDate(new Date());

        if (userInfoFromCache != null) {
            driverGoods.setModifyBy(userInfoFromCache.getModifyBy());
            driverGoods.setModifyName(userInfoFromCache.getUsername());
        }


        //单独拿出来执行是因为绑定货源码需要货源状态为已审核才可以
        this.doAudit(driverGoods);


        //同步货源操作记录
        GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
        LsdsGoodsRecord lgr = LsdsGoodsRecord.of().setRecordId(String.valueOf(IdUtil.generateId())).setGoodsId(driverGoods.getGoodsId()).setCompanyId(driverGoods.getCompanyId());
        if (Constants.GOODS_STATUS_RELEASED.equals(driverGoods.getDealStatus())) {
            lgr.setOperateStatus("3");
            lgr.setOperateContent("货源审核通过");
        } else {
            lgr.setOperateStatus("4");
            lgr.setOperateContent("货源审核不通过");
            lgr.setRemark(driverGoods.getAuditRecordStatusMark());
        }
        lgr.setUserId(userInfoFromCache.getUserBaseId());
        lgr.setUserLoginName(userInfoFromCache.getLoginName());
        lgr.setUserName(userInfoFromCache.getUsername());
        recDao.add(lgr);

        //发送站内信给发货方
        Map<String, String> param = new HashMap<String, String>();
        String phoneNumber = "";
        String userId = driverGoods.getCreateBy();
        String companyId = driverGoods.getCompanyId();
        String templateId = "";
        if (Constants.GOODS_STATUS_RELEASED.equals(driverGoods.getDealStatus())) {
            templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_AUDIT_PASS.getCode();
        } else {
            templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_AUDIT_NO_PASS.getCode();
        }
        param.put("goodsNo", driverGoods.getGoodsId());
        param.put("sendShortName", driverGoods.getStartSiteCityName());
        param.put("receiveShortName", driverGoods.getEndSiteCityName());
        lsdsGoodsTran.sendMsg(phoneNumber, userId, companyId, templateId, param);

        // 广播通知货源状态变更
        GoodsKafkaNotice goodsKafkaNotice = BeanUtil.copyProperties(driverGoods, GoodsKafkaNotice.class);
        // 审核通过的woa可能要推送渠道货源线索：发布货源时自动审核通过的也走这里
        if (StrUtil.isBlank(driverGoods.getDispatcherPhone()) &&
            StrUtil.equals(driverGoods.getDealStatus(), Constants.GOODS_STATUS_RELEASED) &&
            StrUtil.equals(driverGoods.getFreightType(), Constants.FREIGHT_TYPE_NETWORK)) {
            goodsKafkaNotice.setPushWoaGoodsClue(true);
            log.info("audit#人工审核通过，推送到woa->{}", JSONUtil.toJsonStr(goodsKafkaNotice));
        }
        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(goodsKafkaNotice));
        return new ResultMode(driverGoods);

    }

    @Transactional
    public void doAudit(DriverGoods driverGoods) throws Exception {
        if (driverGoods.getDealStatus().equals(Constants.GOODS_STATUS_RELEASED)) {
            //如果货源为指定单价，则需根据公式设置开票价，上浮后的价格
            if (driverGoods.getEnquiryType().equals(Constants.OFFER_STATUS_WAIT_CONFIRM)) {
                driverOfferService.calculatePrice(driverGoods, true);
            }
            //如果是指定司机的货源，则需要创建对应的待报价和待确定数据给司机
            if (driverGoods.getEnquiryRange().equals(Constants.ENQUIRY_RANGE_DRIVERS) && StringUtils.isNotBlank(driverGoods.getDriverIds())) {
                driverOfferService.generateDriverOffer(driverGoods);
            }
            //设置货源发布时间
            driverGoods.setReleasedDate(new Date());
        }
        //设置审核时间
        driverGoods.setAuditDate(new Date());
        this.updateById(driverGoods);
    }


    /**
     * 修改司机货源
     *
     * @param driverGoods 司机货源
     * @return 结果
     */
    public int updateById(DriverGoods driverGoods) {
        if (!StringUtils.equals(driverGoods.getTransportationType(), UtilityEnum.TransportationAllTypeEnum.INVERT_SHORT.getLsdsCode())) {
            //设置创建和修改用户信息
            PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
            if (userInfoFromCache != null) {
                driverGoods.setModifyBy(userInfoFromCache.getUserBaseId());
                driverGoods.setModifyName(userInfoFromCache.getUsername());
            }
        } else {
            driverGoods.setModifyBy("sysadmin");
            driverGoods.setModifyName("短倒管理员");
        }
        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(BeanUtil.copyProperties(driverGoods, GoodsKafkaNotice.class)));
        return driverGoodsMapper.modifyById(driverGoods);
    }

    public int updateById2(DriverGoods driverGoods) {
        return driverGoodsMapper.modifyById(driverGoods);
    }


    /**
     * 删除司机货源对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int removeByIds(String[] ids) {
        if (ArrayUtil.isNotEmpty(ids)) {
            Arrays.stream(ids).forEach(id -> sendNoticeMessage(id, Constants.GOODS_STATUS_DELETED));
            return driverGoodsMapper.removeByIds(ids);
        }
        return 0;
    }

    /**
     * 删除司机货源信息
     *
     * @param id 司机货源ID
     * @return 结果
     */
    public int removeById(String id) {
        int num = driverGoodsMapper.removeById(id);
        sendNoticeMessage(id, Constants.GOODS_STATUS_DELETED);
        return num;
    }

    private void sendNoticeMessage(String id, String dealStatus) {
        // 广播通知货源状态变更
        GoodsKafkaNotice goodsKafkaNotice = new GoodsKafkaNotice();
        goodsKafkaNotice.setGoodsId(id);
        goodsKafkaNotice.setDealStatus(dealStatus);
        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(goodsKafkaNotice));
    }

    /**
     * 分页查询司机货源列表
     *
     * @param pageInfo 司机货源
     * @return 司机货源
     */
    public List<DriverGoods> page(PagingInfo<DriverGoodsFilter> pageInfo) {
        DriverGoodsFilter filterModel = pageInfo.getFilterModel();
        // 支持4pl调用
        if (StrUtil.isBlank(filterModel.getCompanyId()) || !LsdsEnum.LsdsgoodsAdminCompanyId.companyId1.getCode().equals(filterModel.getCompanyId())) {
            filterModel.setCompanyId(JwtUtil.getInstance().getCompanyIdByToken());
        } else {
            filterModel.setQueryType(20);
            filterModel.setCompanyId("");
        }
        //设置分页参数
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        List<DriverGoods> list = driverGoodsMapper.listByEntity(pageInfo.getFilterModel());

        //对查询出的结果，如果是公开询价类型，则需查询对应货源的报价数据
        for (DriverGoods driverGoods : list) {
            if ((driverGoods.getEnquiryType().equals(Constants.ENQUIRY_TYPE_OPEN) && Constants.NEED_QUERY_OFFER_STATUS.contains(driverGoods.getDealStatus()))
                || (GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(driverGoods.getGoodsKind()) && Constants.NEED_QUERY_OFFER_STATUS.contains(driverGoods.getDealStatus()))) {
                appendOfferInfo(driverGoods);

            }

            //20240204 货源码优化 获取企业邀请码
            QrsInvitationIdentifyCodeBO bo = qrsExchangeService.getByCompanyId(driverGoods.getCompanyId());
            log.info("获取企业邀请码:{}", JSON.toJSONString(bo));
            if (null != bo) {
                driverGoods.setInvitationCode(bo.getIdentifyCode());
                log.info("获取里设置企业邀请码:{}", driverGoods.getInvitationCode());
            }
            // 设置司机数量
            String driverIds = driverGoods.getDriverIds();
            if (StrUtil.isNotBlank(driverIds)) {
                driverGoods.setDriverCount(driverIds.split(",").length);
            }
        }
        wrapLogisticsPlan(list);

        return list;
    }


    private void wrapLogisticsPlan(List<DriverGoods> goodsList) {
        List<String> goodsIds = goodsList.stream().map(DriverGoods::getGoodsId).collect(Collectors.toList());
        List<WoaLogisticsPlanBO> logisticsPlanBOS = woaExchangeService.selectByGoodsIds(goodsIds);
        goodsList.forEach(goods -> {
            WoaLogisticsPlanBO woaLogisticsPlanBO = logisticsPlanBOS.stream()
                .filter(plan -> StrUtil.equals(goods.getGoodsId(), plan.getGoodsId()))
                .findFirst().orElseGet(WoaLogisticsPlanBO::new);
            goods.setPlanId(woaLogisticsPlanBO.getPlanId());
        });
    }


    /**
     * @return java.util.List<com.isoftstone.hig.lsds.api.entity.DriverGoods>
     * <AUTHOR>
     * @Description 货源审核分页列表
     * @Date 2021/6/24
     * @Param [pageInfo]
     **/
    public List<DriverGoods> auditPage(PagingInfo<DriverGoodsFilter> pageInfo) {
        //除过未发布的状态之外，查询其他状态的数据
        List<String> statusList = Arrays.asList(Constants.GOODS_STATUS_AUDIT, Constants.GOODS_STATUS_RELEASED, Constants.GOODS_STATUS_REFUSE, Constants.GOODS_STATUS_DEAL, Constants.GOODS_STATUS_PART_DEAL, Constants.GOODS_STATUS_EXPIRE, Constants.GOODS_STATUS_CLOSED);
        pageInfo.getFilterModel().setStatusList(statusList);
        return driverGoodsMapper.listByEntity(pageInfo.getFilterModel());
    }

    /**
     * @return com.wanlianyida.framework.lgicommon.entity.ResultMode<com.isoftstone.hig.lsds.api.entity.DriverGoods>
     * <AUTHOR>
     * @Description 待审核司机货源
     * @Date 2022/1/14
     **/
    public int auditAuditData(DriverGoodsFilter filterModel) {
        int total = driverGoodsMapper.listByEntityAuditData(filterModel);
        return total;
    }

    /**
     * @return java.util.List<com.isoftstone.hig.lsds.api.entity.DriverGoods>
     * <AUTHOR>
     * @Description app找货分页列表
     * @Date 2021/6/24
     * @Param [pageInfo]
     **/
    public List<DriverGoods> findGoods(PagingInfo<DriverGoodsFilter> pageInfo) {
        return driverGoodsMapper.findGoods(pageInfo.getFilterModel());
    }


    /**
     * 司机端找货：地址不匹配
     *
     * @param pageInfo
     * @return {@link List}<{@link DriverGoods}>
     */
    public List<DriverGoods> findGoodsForDriverAppMismatchLocation(PagingInfo<DriverGoodsFilter> pageInfo) {
        return driverGoodsMapper.findGoodsForDriverAppMismatchLocation(pageInfo.getFilterModel());
    }

    /**
     * 货源大厅 常跑路线货源
     *
     * @param pageInfo
     * @return
     */
    public List<DriverGoods> findGoodsForLine(PagingInfo<DriverGoodsFilter> pageInfo) {
        return driverGoodsMapper.findGoodsForLine(pageInfo.getFilterModel());
    }

    public DriverGoodsVo viewGoods(String id) {
        DriverGoodsVo driverGoodsVo = new DriverGoodsVo();
        ResultMode<JSONObject> resultMode = platformExchangeService.getPlatformUmDriverBindBankAndCar(JwtUtil.getInstance().getAppDriverIdByToken());
        if (resultMode.getSucceed() && resultMode.getModel() != null && resultMode.getModel().size() > 0) {
            JSONObject object = resultMode.getModel().get(0);
            boolean bindBank = object.getBoolean("bindBank");
            boolean bindCar = object.getBoolean("bindCard");
            driverGoodsVo.setBindBank(bindBank);
            driverGoodsVo.setBindCar(bindCar);
        }

        Map<String, List<TcsCar>> carMap = tcsExchangeService.queryCurrentDriverCar();
//        拖车类型【select:1-整车,2-车头,3-挂车】
        driverGoodsVo.setTrailerCarList(carMap.get("1"));
        driverGoodsVo.setPlatformUmCars(carMap.get("3"));

        DriverGoods driverGoods = driverGoodsMapper.getById(id);
        driverGoodsVo.setDriverGoods(driverGoods);
        return driverGoodsVo;
    }


    public List<DriverGoods> homeData(DriverGoodsFilter driverGoodsFilter) {
        List<DriverGoods> list = driverGoodsMapper.homeData(driverGoodsFilter);
        return list;
    }

    public void appendFilter(DriverGoodsFilter filterModel) {
        if (StrUtil.isNotEmpty(filterModel.getGoodsCodeNumber())) {
            IdentifyCodeRelFilter filter = new IdentifyCodeRelFilter();
            filter.setIdentifyCode(filterModel.getGoodsCodeNumber());
            // 支持4pl调用
            if (StrUtil.isBlank(filterModel.getCompanyId()) || !LsdsEnum.LsdsgoodsAdminCompanyId.companyId1.getCode().equals(filterModel.getCompanyId())) {
                filter.setCompanyId(JwtUtil.getInstance().getCompanyIdByToken());
            }
            filter.setIdentifyCodeType(QrsEnum.IdentifyCodeTypeEnum.COMPANY_GOODS_CODE.getCode());
            List<LsdsIdentifyCodeRel> codeRelList = lsdsIdentifyCodeRelService.queryByIdentifyCodeOrName(filter);
            if (IterUtil.isNotEmpty(codeRelList)) {
                Set<String> goodsIds = codeRelList.stream().map(LsdsIdentifyCodeRel::getGoodsId).collect(Collectors.toSet());
                filterModel.setGoodsIds(goodsIds);
            }

            //查询3PL数据
            filterModel.setQueryType(10);
        }
    }

    public void exportDriverGoods(DriverGoodsFilter driverGoodsFilter) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        //设置请求参数
        appendFilter(driverGoodsFilter);
        LogHelper.writeDebug("司机货源导出参数：{}", JSON.toJSONString(driverGoodsFilter));
        PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
        driverGoodsFilter.setCompanyId(userInfoFromCache.getCompanyId());
        List<DriverGoods> resultList = listByEntity(driverGoodsFilter);
        //取配置数据
        Map<String, String> unitsMap = platformExchangeService.getPricingUnitsMap(PlatformEnum.EnableStatusEnum.ENABLE.getCode());

        for (DriverGoods driverGoods : resultList) {
            if (driverGoods.getEnquiryType().equals(Constants.ENQUIRY_TYPE_OPEN)) {
                driverGoods.setOfferInfo("公开询价 " + (driverGoods.getFloatEnquiryTypeBasePrice() == null ? "0.00" : driverGoods.getFloatEnquiryTypeBasePrice()) + "元/" + unitsMap.get(driverGoods.getTotalQuantityUnits()));
                if (Constants.NEED_QUERY_OFFER_STATUS.contains(driverGoods.getDealStatus())) {
                    DriverOfferFilter driverOfferFilter = new DriverOfferFilter();
                    driverOfferFilter.setGoodsId(driverGoods.getGoodsId());
                    List<DriverOffer> driverOffers = driverOfferService.listByEntity(driverOfferFilter);
                    //比对并设置金额最大的报价价格
                    if (!driverOffers.isEmpty()) {
                        if (driverOffers.stream().filter(e -> e.getFloatEnquiryTypeBasePrice() != null).findAny().isPresent()) {
                            DriverOffer driverOffer = driverOffers.stream().filter(e -> e.getFloatEnquiryTypeBasePrice() != null).max(Comparator.comparing(DriverOffer::getEnquiryTypeBasePrice)).get();
                            driverGoods.setMaxOfferPrice(driverOffer.getEnquiryTypeBasePrice());
                        }
                        driverGoods.setOfferInfo("(" + driverOffers.size() + ") " + (driverGoods.getFloatEnquiryTypeBasePrice() == null ? "0.00" : driverGoods.getFloatEnquiryTypeBasePrice()) + "元/" + unitsMap.get(driverGoods.getTotalQuantityUnits()));
                    }
                }
            } else {
                driverGoods.setOfferInfo("指定含税单价 " + driverGoods.getFloatEnquiryTypeBasePrice() + "元/" + unitsMap.get(driverGoods.getTotalQuantityUnits()));
            }
            // 询价范围处理
            String driverIds = driverGoods.getDriverIds();
            if (StrUtil.isNotBlank(driverIds)) {
                String[] driverIdList = driverIds.split(",");
                List<TcsDriverVO> platformUmDrivers = tcsExchangeService.queryDriverByDriverIds(Arrays.asList(driverIdList));
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < platformUmDrivers.size(); i++) {
                    TcsDriverVO platformUmDriver = platformUmDrivers.get(i);
                    stringBuilder.append(platformUmDriver.getDriverName());
                    stringBuilder.append(" ");
                    stringBuilder.append(platformUmDriver.getDriverPhoneNumber());
                    if (i < platformUmDrivers.size() - 1) {
                        stringBuilder.append(",");
                    }
                }
                driverGoods.setDriverInfoStr(stringBuilder.toString());
            }

            if (GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(driverGoodsFilter.getGoodsKind())) {
                LsdsMatchmakingConfigQuery configQuery = new LsdsMatchmakingConfigQuery();
                configQuery.setBusId(driverGoods.getGoodsId());
                LsdsMatchmakingConfigDTO config = lsdsMatchmakingService.queryConfig(configQuery);
                if (ObjUtil.isNotNull(config)) {
                    driverGoods.setBargainConfigStr(config.getBargainConfig().equals(10) ? "可议价" : "不可议价");
                    String totalQuantityUnits = driverGoods.getTotalQuantityUnits();
                    Map<String, String> unitsMaps = platformExchangeService.getPricingUnitsMap(PlatformEnum.EnableStatusEnum.ENABLE.getCode());
                    driverGoods.setTotalQuantityUnitsStr("元/" + unitsMaps.get(totalQuantityUnits));
                }
            }
        }
        LogHelper.writeDebug("司机货源导出数据resultList：{}", JSON.toJSONString(resultList));
        //导出excel文件
        try {
            ClassPathResource resource = null;
            if (GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(driverGoodsFilter.getGoodsKind())) {
                resource = new ClassPathResource("verifyxml/ExportMatchmakingGoodsMode.xml");
            } else {
                resource = new ClassPathResource("verifyxml/ExportDriverGoodsMode.xml");
            }
            InputStream is = resource.getInputStream();
            ExcelUtil.exportDataToExcel(request, response, "司机货源信息", is, resultList, "司机货源信息", JwtUtil.getInstance().getTokenInfo().getUsername() + "(" + JwtUtil.getInstance().getTokenInfo().getLoginName() + ")");
            is.close();
        } catch (IOException e) {
            LogHelper.writeError("司机货源导出异常e:{}", e);
            throw new LsdsWlydException("导出失败!");
        }
    }

    public void exportWayBill(DriverGoodsFilter driverGoodsFilter) {

        //取配置数据
        Map<String, String> unitMap = platformExchangeService.getPricingUnitsMap(PlatformEnum.EnableStatusEnum.ENABLE.getCode());
        String blank = "  ";
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        //设置请求参数
        LogHelper.writeDebug("司机货源运单导出参数：{}", JSON.toJSONString(driverGoodsFilter));
        TmsWaybillFilter filter = new TmsWaybillFilter();
        filter.setGoodsId(driverGoodsFilter.getId());
        filter.setWaybillId(driverGoodsFilter.getWaybillId());
        filter.setWabillStatus(driverGoodsFilter.getWaybillStatus());
        ResultMode<TmsWaybill> waybillResultMode = tmsWaybillInter.listDataByGoodsId(filter);
        List<LsdsWaybillVo> lsdsWaybillVos = new ArrayList<>();
        StringBuffer sb = new StringBuffer();
        DriverGoods driverGoods = getById(driverGoodsFilter.getId());
        sb.append("单据号：").append(driverGoods.getGoodsId()).append(blank)
            .append("出发地：").append(driverGoods.getSendAddrProvinceName()).append(driverGoods.getSendAddrCityName()).append(driverGoods.getSendAddrAreaName()).append(blank)
            .append("目的地：").append(driverGoods.getReceiveAddrProvinceName()).append(driverGoods.getReceiveAddrCityName()).append(driverGoods.getReceiveAddrAreaName()).append(blank);
        if (Constants.FREIGHT_TYPE_TRADITION.equals(driverGoods.getFreightType())) {
            sb.append("传统货运-").append(blank);
        } else {
            sb.append("网络货运-").append(driverGoods.getNetworkMainBodyName()).append(blank);
        }
        if (Constants.ENQUIRY_TYPE_OPEN.equals(driverGoods.getEnquiryType())) {
            sb.append("公开询价").append(blank);
        } else {
            /*sb.append("指定单价").append(blank);*/
            sb.append("单价(不含差价)：").append(driverGoods.getEnquiryTypeBasePrice()).append("元/").append(unitMap.get(driverGoods.getTotalQuantityUnits())).append(blank);
            sb.append("单价(含差价)：").append(driverGoods.getEnquiryTypeBaseOpenTicket()).append("元/").append(unitMap.get(driverGoods.getTotalQuantityUnits())).append(blank);
        }
        sb.append("货物名称：").append(driverGoods.getGoodsName()).append(blank)
            .append("总重量（吨）：").append(driverGoods.getWeightSum()).append(blank)
            .append("总数量（").append(unitMap.get(driverGoods.getTotalQuantityUnits())).append("）：").append(driverGoods.getTotalQuantity()).append(blank);

        // 获取新亏涨吨对象
        LsdsGoodsDeductible lsdsGoodsDeductible = goodsDeductibleService.wrapGoodsDeductible(driverGoods.getOtherKuiTonsRatio(), driverGoods.getGoodsId());
        if (ObjectUtil.isNotEmpty(lsdsGoodsDeductible) && lsdsGoodsDeductible.getEnable() == GoodsDeductibleEnum.SwitchStatus.OPEN.getCode()) {
            if (lsdsGoodsDeductible.getDeductibleType() == GoodsDeductibleEnum.DeductibleType.RATIO.getCode()) {
                sb.append("亏吨免赔系数（‰）：").append(lsdsGoodsDeductible.getDeductibleValue()).append(blank);
            } else if (lsdsGoodsDeductible.getDeductibleType() == GoodsDeductibleEnum.DeductibleType.WEIGHT.getCode()) {
                sb.append("亏吨免赔重量值（千克/车）：").append(lsdsGoodsDeductible.getDeductibleValue()).append(blank);
            }
        }
        if (waybillResultMode.getSucceed() && waybillResultMode.getModel() != null && waybillResultMode.getModel().size() > 0) {
            List<TmsWaybill> waybillList = waybillResultMode.getModel();
            //字段转换
            lsdsWaybillVos = BeanUtil.copyToList(waybillList, LsdsWaybillVo.class);
            for (LsdsWaybillVo vo : lsdsWaybillVos) {
                vo.setReceivingNumberStr(vo.getReceivingNumber() == null ? "" : ZeroUtil.subZeroAndDot(String.valueOf(vo.getReceivingNumber())));
                vo.setPlaceOrderNumberStr(vo.getPlaceOrderNumber() == null ? "" : ZeroUtil.subZeroAndDot(String.valueOf(vo.getPlaceOrderNumber())));
                vo.setLoadQuantityStr(vo.getLoadQuantity() == null ? "" : ZeroUtil.subZeroAndDot(String.valueOf(vo.getLoadQuantity())));
                vo.setUnloadQuantityStr(vo.getUnloadQuantity() == null ? "" : ZeroUtil.subZeroAndDot(String.valueOf(vo.getUnloadQuantity())));
                vo.setPoundDifferenceStr(vo.getPoundDifference() == null ? "" : ZeroUtil.subZeroAndDot(String.valueOf(vo.getPoundDifference())));
                vo.setSettlementQuantityStr(vo.getSettlementQuantity() == null ? "" : ZeroUtil.subZeroAndDot(String.valueOf(vo.getSettlementQuantity())));
            }
        }
        LogHelper.writeDebug("司机货源运单导出数据resultList：{}", JSON.toJSONString(lsdsWaybillVos));
        //导出excel文件
        try {
            ClassPathResource resource = new ClassPathResource("verifyxml/ExportDriverGoodsWaybillMode.xml");
            InputStream is = resource.getInputStream();
            ExcelUtil.exportDataCustomizedToExcel(request, response, "运单信息", is, lsdsWaybillVos, "运单信息", JwtUtil.getInstance().getTokenInfo().getUsername() + "(" + JwtUtil.getInstance().getTokenInfo().getLoginName() + ")", sb.toString());
            is.close();
        } catch (IOException e) {
            LogHelper.writeError("运单导出异常e:{}", e);
            throw new LsdsWlydException("导出失败!");
        }
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 关闭货源
     * @Date 2021/6/17
     * @Param
     **/
    @Transactional
    public ResultMode closeDriverGoods(DriverGoodsFilter driverGoodsFilter) {
        try {
            DriverGoods driverGoods = driverGoodsMapper.getById(driverGoodsFilter.getId());

            if (Constants.GOODS_STATUS_PART_DEAL.equals(driverGoods.getDealStatus())) {
                driverGoods.setDealStatus(Constants.GOODS_STATUS_DEAL);
            } else {
                driverGoods.setDealStatus(Constants.GOODS_STATUS_CLOSED);
            }

            driverGoodsMapper.modifyById(driverGoods);

            // 广播通知货源状态变更
            GoodsKafkaNotice goodsKafkaNotice = BeanUtil.copyProperties(driverGoods, GoodsKafkaNotice.class);
            lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(goodsKafkaNotice));
            return Result.success();
        } catch (Exception e) {
            LogHelper.writeError("关闭货源异常e:{}", e);
            throw new LsdsWlydException("关闭货源失败!");
        }
    }


    /**
     * 通过订单IDS查询绑定的订单信息
     *
     * @param pageInfo 绑定订单IDS
     * @return ResultMode<TmsOrder>
     */
    public ResultMode<DriverGoods> getBindSupplyListByIds(PlatformQrCodeFilter pageInfo) {
        ResultMode<DriverGoods> resultMode = new ResultMode<>();
        List<PlatformQrCodeRelationVO> platformQrCodeRelationVOList = pageInfo.getPlatformQrCodeRelationVOList();
        if (CollectionUtils.isEmpty(platformQrCodeRelationVOList)) {
            resultMode.setSucceed(false);
            resultMode.setErrMsg("绑定货源列表为空");
            return resultMode;
        }
        String driverId = null;
        List<DriverGoods> orderListByIds = null;
        try {
            driverId = JwtUtil.getInstance().getTokenInfo().getDriverId();
            log.info("driverId:{}", driverId);
        } catch (Exception e) {
            log.info("error!!!");
            e.printStackTrace();
        }

        if (StringUtils.isNotBlank(driverId)) {
            log.info(">>>>>>>>>>getBindSupplyListByIds>>>>>11111>>>>>>>>>>>>>>>>>");
            orderListByIds = driverGoodsMapper.getBindSupplyListByDriverIdAndIds(driverId, pageInfo);
        } else {
            log.info(">>>>>>>>getBindSupplyListByIds>>>>>>>>3333>>>>>>>>>>>>>>>>");
            orderListByIds = driverGoodsMapper.getBindSupplyListByIds(pageInfo);
        }

//        if (CollectionUtils.isEmpty(orderListByIds)) {
//            resultMode.setSucceed(false);
//            resultMode.setErrMsg("货源详情获取失败");
//            return resultMode;
//        }
        resultMode.setModel(orderListByIds);
        return resultMode;
    }


    /**
     * 过滤敏感词
     *
     * @param driverGoods
     * @return
     */
    public ResultMode<String> filterSensitiveWord(DriverGoods driverGoods) {
        ResultMode<String> resultModel = new ResultMode<String>();
        try {

            resultModel.setSucceed(true);
            Map<String, String> map = new HashMap<String, String>();
            map.put("出发地简称", driverGoods.getStartSiteCityName());
            map.put("出发地联系人", driverGoods.getStartSendLinker());
            map.put("出发地详细地址 ", driverGoods.getStartSiteAddress());
            map.put("目的地简称", driverGoods.getEndSiteCityName());
            map.put("目的地联系人", driverGoods.getEndReceiveLinker());
            map.put("目的详细地址 ", driverGoods.getEndSiteAddress());
            map.put("销售合同号", driverGoods.getSalesContractNumber());
            map.put("货物名称", driverGoods.getGoodsName());
            map.put("货物描述", driverGoods.getGoodsDesc());
            map.put("运输要求", driverGoods.getOtherRemark());
            Map<String, String> returnMap = sensitiveWordValidMatch(map);
            if (!MapUtils.isEmpty(returnMap)) {
                resultModel.setErrMsg("敏感词校验不通过:" + returnMap);
                resultModel.getModel().add("敏感词校验不通过:" + returnMap);
                resultModel.setErrCode(CommonStatusCodeEnum.BUSS_ERROR_BCOM0500.getCode());
                resultModel.setSucceed(false);
            }
            return resultModel;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultModel;
    }


    public Map<String, String> sensitiveWordValidMatch(Map<String, String> words) {
        log.info(">>>>>>>>>>>>>司机货源敏感词>>>>>>>>>>>>>>>>>>");
        Map<String, String> resultMap = new HashMap<>();
        PagingInfo<PlatformCmSensitiveWordFilter> pageInfo = new PagingInfo<>();
        pageInfo.currentPage = 1;
        pageInfo.pageLength = 999999999;
        pageInfo.filterModel = new PlatformCmSensitiveWordFilter();
        log.info(">>>>>>>查询敏感词开始>>>>>>>>>>>>wordsSize:{}", words.size());
        ResultMode<PlatformCmSensitiveWord> resultMode = platformCmSensitiveWordInter.platformCmSensitiveWordPaging(pageInfo);
        List<PlatformCmSensitiveWord> res = resultMode.getModel();
        if (!CollectionUtils.isEmpty(words) && !CollectionUtils.isEmpty(res)) {
            log.info(">>>>333>>>res size:{},id:{}", res.size(), res.get(0).getId());
            for (Map.Entry<String, String> entry : words.entrySet()) {
                String sensitiveWord = entry.getValue();
                if (!org.springframework.util.StringUtils.isEmpty(sensitiveWord)) {
                    res.forEach(item -> {
                        if (sensitiveWord.equals(item.getSenWord())) {
                            resultMap.put(entry.getKey(), entry.getValue());
                            return;
                        }
                    });
                }
            }
        }
        return resultMap;
    }

    public DriverGoods getByGoodsId(String id) {
        if (StrUtil.isEmpty(id)) {
            return null;
        }
        return driverGoodsMapper.getByGoodsId(id);
    }

    /**
     * 查询货源城市
     *
     * @param goodsId 商品id
     * @return {@link DriverGoods}
     */
    public DriverGoods findGoodsCityById(String goodsId) {
        if (StrUtil.isEmpty(goodsId)) {
            return null;
        }
        return driverGoodsMapper.findGoodsCityById(goodsId);
    }

    public List<DriverGoods> getDriverGoodsListByGoodsIdList(List<String> goodsIdList) {
        if (IterUtil.isEmpty(goodsIdList)) {
            return new ArrayList<>();
        }
        return driverGoodsMapper.getDriverGoodsListByGoodsIdList(goodsIdList);
    }

    public List<DriverGoods> getGoodsBasePriceById(String goodsId) {
        return driverGoodsMapper.getGoodsBasePriceById(goodsId);
    }


    public List<DriverGoods> getTransportmileageOrTakeTimeNullData() {
        return driverGoodsMapper.getTransportmileageOrTakeTimeNullData();
    }


    /**
     * 根据Key删除货源编号  司机货源信息表信息
     * 创建者: 柳鹏
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param goodsId 司机货源编号  司机货源信息表
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【删除成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【删除成功】编码,ResultMode.errMsg为相应【删除成功】描述；
     * 否则ResultMode.succeed=false【删除失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【删除失败】编码,ResultMode.errMsg为相应【删除失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> driverGoodsDel(String goodsId) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {

            DriverGoods driverGoods = driverGoodsMapper.getByGoodsId(goodsId);
            boolean tempTag = null != driverGoods && (driverGoods.getDealStatus().equals(Constants.GOODS_STATUS_NOT_RELEASED) || driverGoods.getDealStatus().equals(Constants.GOODS_STATUS_REFUSE) || driverGoods.getDealStatus().equals(Constants.GOODS_STATUS_EXPIRE) || driverGoods.getDealStatus().equals(Constants.GOODS_STATUS_CLOSED));
            if (tempTag) {
                boolean flag = driverGoodsMapper.remove(goodsId);
                sendNoticeMessage(goodsId, Constants.GOODS_STATUS_DELETED);
                resultmodel.setSucceed(flag);
            } else {
                errMsg = "货源当前状态不允许删除！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
            }
            if (!resultmodel.getSucceed()) {
                return resultmodel;
            }
        } catch (Exception ex) {
            errMsg = "根据goodsId删除司机货源信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }

    /**
     * 更新货源里程和耗时
     *
     * @param driverGoods
     * <AUTHOR>
     * @Date 2023/10/10 13:46
     */
    public int updateTransportmileage(DriverGoods driverGoods) {
        return driverGoodsMapper.updateTransportmileage(driverGoods);
    }

    public String initCustomerShortName(String customerId) {
        String customerShortName = "";
        if (StrUtil.isNotBlank(customerId)) {
            ResultMode<CrmCustomer> crmCustomerMode = crmCustomerInter.getBeanByKey(customerId);
            log.info("crmCustomerMode{}", JSON.toJSONString(crmCustomerMode));
            if (crmCustomerMode.getSucceed() && IterUtil.isNotEmpty(crmCustomerMode.getModel())) {
                CrmCustomer crmCustomer = IterUtil.getFirst(crmCustomerMode.getModel());
                customerShortName = crmCustomer.getShortName();
            }
        }
        return customerShortName;
    }

    public void initAddressInfo(DriverGoods driverGoods, CrmCompanyLineAddressBO startAddress, CrmCompanyLineAddressBO endAddress,
                                CrmCompanyLineAddressBO crmCompanyLineAddressBO) {
        driverGoods.setStartLineId(startAddress.getLineId());
        driverGoods.setSendAddrProvince(startAddress.getSendAddrProvince());
        driverGoods.setSendAddrProvinceName(startAddress.getSendAddrProvinceName());
        driverGoods.setSendAddrCity(startAddress.getSendAddrCity());
        driverGoods.setSendAddrCityName(startAddress.getSendAddrCityName());
        driverGoods.setSendAddrArea(startAddress.getSendAddrArea());
        driverGoods.setSendAddrAreaName(startAddress.getSendAddrAreaName());
        driverGoods.setSendAddrDetail(startAddress.getSendAddrDetail());
        driverGoods.setStartSiteCityName(startAddress.getLineShortName());
        driverGoods.setStartSendLinker(startAddress.getStartSendLinker());
        driverGoods.setStartSendPhoneNumber(startAddress.getStartSendPhoneNumber());
        driverGoods.setStartSiteAddress(startAddress.getSendAddrDetail());

        driverGoods.setEndLineId(endAddress.getLineId());
        driverGoods.setReceiveAddrProvince(endAddress.getSendAddrProvince());
        driverGoods.setReceiveAddrProvinceName(endAddress.getSendAddrProvinceName());
        driverGoods.setReceiveAddrCity(endAddress.getSendAddrCity());
        driverGoods.setReceiveAddrCityName(endAddress.getSendAddrCityName());
        driverGoods.setReceiveAddrArea(endAddress.getSendAddrArea());
        driverGoods.setReceiveAddrAreaName(endAddress.getSendAddrAreaName());
        driverGoods.setReceiveAddrDetail(endAddress.getSendAddrDetail());
        driverGoods.setEndSiteCityName(endAddress.getLineShortName());
        driverGoods.setEndReceiveLinker(endAddress.getStartSendLinker());
        driverGoods.setEndReceivePhoneNumber(endAddress.getStartSendPhoneNumber());
        driverGoods.setEndSiteAddress(endAddress.getSendAddrDetail());

        driverGoods.setTransportMileage(crmCompanyLineAddressBO.getTransportMileage());
        driverGoods.setTakeUpTime(crmCompanyLineAddressBO.getTakeUpTime());
    }

    /**
     * 司机接单处理货源和报价
     *
     * @param driverOfferGenBusiVO
     */
    public void handleGoodsOfOrderReceiving(DriverOfferGenBusiVO driverOfferGenBusiVO) {
        try {
            long start = System.currentTimeMillis();
            DriverOffer offer = driverOfferGenBusiVO.getDriverOffer();

            DriverGoods goods = driverGoodsMapper.getById(offer.getGoodsId());
            driverOfferGenBusiVO.setGoods(goods);

            //3pl-司机货源-报价下单逻辑，代码注释
//            DriverGoods goodsVo = driverOfferGenBusiVO.getGoods();
            /*if (goodsVo != null) {
                //更新预付费
                goods.setAdvancePayment(goodsVo.getAdvancePayment());
                goods.setAdvancePaymentFlag(goodsVo.getAdvancePaymentFlag());
                //尾货处理价格
                goods.setEnquiryTypeBasePrice(goodsVo.getEnquiryTypeBasePrice());
                goods.setEnquiryTypeBaseOpenTicket(goodsVo.getEnquiryTypeBaseOpenTicket());
                goods.setFloatEnquiryTypeBasePrice(goodsVo.getFloatEnquiryTypeBasePrice());
                goods.setFloatEnquiryTypeBaseOpenTicket(goodsVo.getFloatEnquiryTypeBaseOpenTicket());
            }*/

            //1、处理货源单和报价单
            AopSpringFactory.getAopProxy(this).dealGoodsAndDriverOffer(driverOfferGenBusiVO);
            //3、其他异步处理
            asyncContextAwareExecutor.execute(() -> handleAsyncMessageOfOrderReceiving(driverOfferGenBusiVO));
            long end = System.currentTimeMillis();
            log.info("司机接单处理货源和报价耗时：{}", (end - start));
        } catch (Exception e) {
            log.error("handleGoodsOfOrderReceiving#异常:", e);
        }
    }

    private void handleAsyncMessageOfOrderReceiving(DriverOfferGenBusiVO driverOfferGenBusiVO) {
        DriverOffer offer = driverOfferGenBusiVO.getDriverOffer();
        DriverGoods goods = driverOfferGenBusiVO.getGoods();

        //1、货源状态变更的异步处理
        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(BeanUtil.copyProperties(goods, GoodsKafkaNotice.class)));

        //2、司机接单时,取消上次货源关注信息
        goodsAttentionServiceTran.sendCancelGoodsAttentionMsg(offer.getDriverId(), offer.getWaybillId(), offer.getGoodsId());

        //3、发送站内信给发布货源操作人
        driverOfferService.sendGoodsMsgToShipper(goods, offer.getWaybillId());
        driverOfferService.sendOrderMsgToShipper(goods, offer.getOrderId(), offer.getWaybillId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void dealGoodsAndDriverOffer(DriverOfferGenBusiVO driverOfferGenBusiVO) {
        log.info("订单下单成功后，处理货源单和报价单异常..driverOfferGenBusiVO->[" + JSON.toJSONString(driverOfferGenBusiVO) + "]");

        DriverOffer offer = driverOfferGenBusiVO.getDriverOffer();
        DriverGoods goods = driverOfferGenBusiVO.getGoods();

        //1、处理货源单剩余数量和状态
        handleGoods(offer, goods, driverOfferGenBusiVO.getTokenInfo());

        //2、处理报价单
        driverOfferService.handleDriverOffer(offer, goods);

        //3、同步货源操作记录
        driverOfferService.syncDriverGoodsOpertorRecord(driverOfferGenBusiVO.getTokenInfo(), offer, goods);
    }

    public void handleGoods(DriverOffer offer, DriverGoods goods, TokenInfo tokenInfo) {
        tokenInfo = getTokenInfoIfNull(tokenInfo);
        BigDecimal remainQuantity = goods.getRemainingQuantity();
        if (offer.getSubQuantity() != null) {
            remainQuantity = remainQuantity.subtract(offer.getSubQuantity());
        } else {
            //按照配置减（报价下单使用这个逻辑）（报价下单新接口废弃）
            remainQuantity = getRemainQuantity(offer, goods);
        }

        if (remainQuantity.compareTo(BigDecimal.ZERO) < 0) {
            remainQuantity = BigDecimal.ZERO;
        }

        //如果货源剩余数量小于等于报价接单数量，则表示所有货物已处理完成，将剩余数量改为0，货源状态改为已成交
        if (remainQuantity.compareTo(BigDecimal.ZERO) == 0) {
            goods.setDealStatus(Constants.GOODS_STATUS_DEAL);
        }
        //如果货源剩余数量大于报价接单数量，则需扣减剩余数量，货源状态改为部分成交
        else {
            goods.setDealStatus(Constants.GOODS_STATUS_PART_DEAL);
        }
        goods.setRemainingQuantity(remainQuantity);
        goods.setModifyBy(tokenInfo.getUserBaseId());
        goods.setModifyName(tokenInfo.getUsername());
        goods.setModifyDate(new Date());
        driverGoodsMapper.modifyById(goods);
    }

    /**
     * 扣减数据计算
     *
     * @param offer
     * @param goods
     * @return
     */
    private BigDecimal getRemainQuantity(DriverOffer offer, DriverGoods goods) {
        BigDecimal remainQuantity = BigDecimal.ZERO;
        if (StrUtil.equals(offer.getTotalQuantityUnits(), goods.getTotalQuantityUnits())) {
            //单位一直直接扣
            remainQuantity = goods.getRemainingQuantity().subtract(offer.getReceivingOrdersQuantity());
        } else {
            //单位不一直取配置扣
            List<PlatformPricingTypeConfigVo> platformPricingTypeConfigVos = platformExchangeService.queryPricingTypeConfigEnum(PlatformEnum.EnableStatusEnum.ENABLE.getCode());
            Map<String, List<PlatformPricingTypeConfigVo>> groupByPricingType = platformPricingTypeConfigVos
                .stream().collect(Collectors.groupingBy(PlatformPricingTypeConfigVo::getPricingType));
            PlatformPricingTypeConfigVo platformPricingTypeConfigVo = IterUtil.getFirst(groupByPricingType.get(goods.getTotalQuantityUnits()));
            if (StrUtil.equals(platformPricingTypeConfigVo.getQuantityValueMethod(), PricingMethodEnum.TEN.getCode())) {
                remainQuantity = goods.getRemainingQuantity().subtract(offer.getReceivingOrdersWeight());
            } else if (StrUtil.equals(platformPricingTypeConfigVo.getQuantityValueMethod(), PricingMethodEnum.TWENTY.getCode())) {
                remainQuantity = goods.getRemainingQuantity().subtract(new BigDecimal(platformPricingTypeConfigVo.getFixedValue()));
            } else {
                remainQuantity = goods.getRemainingQuantity().subtract(BigDecimal.ONE);
            }
        }
        return remainQuantity;
    }

    /**
     * 如何token为空构建token
     *
     * @param tokenInfo
     * @return
     */
    private TokenInfo getTokenInfoIfNull(TokenInfo tokenInfo) {
        if (!Objects.isNull(tokenInfo)) {
            return tokenInfo;
        }
        TokenInfo newTokenInfo = new TokenInfo();
        PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
        if (ObjectUtil.isNotEmpty(userInfoFromCache)) {
            newTokenInfo.setUserBaseId(userInfoFromCache.getUserBaseId());
            newTokenInfo.setUsername(userInfoFromCache.getUsername());
        } else {
            newTokenInfo.setUserBaseId("sysadmin");
            newTokenInfo.setUsername("短倒管理员");
        }
        return newTokenInfo;
    }

    /**
     * 分页查询司机货源列表
     *
     * @param pageInfo 司机货源
     * @return 司机货源
     */
    public List<DriverGoods> getUnBindList(PagingInfo<DriverGoodsFilter> pageInfo) {
        //设置创建和修改用户信息
        PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
        if (pageInfo != null && userInfoFromCache != null) {
            pageInfo.getFilterModel().setCompanyId(userInfoFromCache.getCompanyId());
            List<DriverGoods> list = driverGoodsMapper.getUnBindList(pageInfo.getFilterModel());

            //对查询出的结果，如果是公开询价类型，则需查询对应货源的报价数据
            for (DriverGoods driverGoods : list) {
                if (driverGoods.getEnquiryType().equals(Constants.ENQUIRY_TYPE_OPEN) && Constants.NEED_QUERY_OFFER_STATUS.contains(driverGoods.getDealStatus())) {
                    DriverOfferFilter driverOfferFilter = new DriverOfferFilter();
                    driverOfferFilter.setGoodsId(driverGoods.getId());
                    driverOfferFilter.setOfferStatus(Constants.OFFER_STATUS_WAIT_CONFIRM);
                    List<DriverOffer> driverOffers = driverOfferService.listByEntity(driverOfferFilter);
                    //比对并设置金额最大的报价价格
                    try {
                        if (!driverOffers.isEmpty()) {
                            DriverOffer driverOffer = driverOffers.stream().min(Comparator.comparing(DriverOffer::getEnquiryTypeBaseOpenTicket)).get();
                            driverGoods.setMaxOfferPrice(driverOffer.getEnquiryTypeBaseOpenTicket());
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //设置报价单列表
                    driverGoods.setDriverOfferList(driverOffers);

                }

                //20240204 货源码优化 获取企业邀请码
                QrsInvitationIdentifyCodeBO bo = qrsExchangeService.getByCompanyId(driverGoods.getCompanyId());
                log.info("获取企业邀请码:{}", JSON.toJSONString(bo));
                if (null != bo) {
                    driverGoods.setInvitationCode(bo.getIdentifyCode());
                    log.info("获取里设置企业邀请码:{}", driverGoods.getInvitationCode());
                }

            }
            return list;

        }
        return new ArrayList<DriverGoods>();
    }

    /**
     * 更新货源剩余数据
     *
     * @param goodsDeductionQuantityVO
     */
    public void doHandleGoodsRemainingQuantity(GoodsDeductionQuantityVO goodsDeductionQuantityVO) {
        try {
            log.info("doHandleGoodsRemainingQuantity->更新货源剩余数据参数：{}", JSONUtil.toJsonStr(goodsDeductionQuantityVO));
            String message = ValidatorUtils.validate(goodsDeductionQuantityVO);
            if (StrUtil.isNotBlank(message)) {
                log.info("doHandleGoodsRemainingQuantity->更新货源剩余数量货源号为空！");
                return;
            }
            goodsDeductionQuantityVO.setUpdateSubQuantity(goodsDeductionQuantityVO.getUpdateSubQuantity());
            driverGoodsMapper.updateRemainQuantityByGoodsId(goodsDeductionQuantityVO);
            driverGoodsMapper.updateRemainQuantityForComplete(goodsDeductionQuantityVO.getGoodsId());
        } catch (Exception e) {
            log.error("doHandleGoodsRemainingQuantity->货源数量扣减更新剩余总量异常：", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelWaybillBackQuantity(DriverOfferGenBusiVO driverOfferGenBusiVO) {
        DriverOffer offer = driverOfferGenBusiVO.getDriverOffer();
        DriverGoods goods = driverGoodsMapper.getById(offer.getGoodsId());

        // 处理货源单剩余数量和状态
        handleBackGoods(offer, goods, driverOfferGenBusiVO.getTokenInfo());
    }

    private void handleBackGoods(DriverOffer offer, DriverGoods goods, TokenInfo tokenInfo) {
        // 校验货源单状态
        if (Constants.GOODS_STATUS_RELEASED.equals(goods.getDealStatus()) || Constants.GOODS_STATUS_PART_DEAL.equals(goods.getDealStatus())) {
            BigDecimal remainQuantity = BigDecimal.ZERO;
            if (StrUtil.equals(goods.getTotalQuantityUnits(), Constants.QUANTITY_UNITS_TON)) {
                remainQuantity = goods.getRemainingQuantity().add(offer.getReceivingOrdersWeight());
            } else if (StrUtil.equalsAny(goods.getTotalQuantityUnits(), Constants.QUANTITY_UNITS_CAR, Constants.QUANTITY_UNITS_CABINET)) {
                remainQuantity = goods.getRemainingQuantity().add(BigDecimal.ONE);
            }

            // 返还后数量若超过总数量，取总数量
            remainQuantity = remainQuantity.compareTo(goods.getTotalQuantity()) >= 0 ? goods.getTotalQuantity() : remainQuantity;
            goods.setRemainingQuantity(remainQuantity);
            if (tokenInfo != null) {
                goods.setModifyBy(tokenInfo.getUserBaseId());
                goods.setModifyName(tokenInfo.getUsername());
                goods.setModifyDate(new Date());
            }
            driverGoodsMapper.modifyById(goods);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelWaybill(CancelWaybillMsgCommand command) {
        DriverGoods goods = driverGoodsMapper.getById(command.getGoodsId());
        // 校验货源单状态
        if (Constants.GOODS_STATUS_RELEASED.equals(goods.getDealStatus()) || Constants.GOODS_STATUS_PART_DEAL.equals(goods.getDealStatus())) {
            DriverGoods update = new DriverGoods();
            update.setId(goods.getId());
            update.setGoodsId(goods.getGoodsId());
            BigDecimal backQuantity = goods.getRemainingQuantity().add(command.getTotalQuantity());
            update.setRemainingQuantity(backQuantity.compareTo(goods.getTotalQuantity()) > 0 ? goods.getTotalQuantity() : backQuantity);
            update.setModifyBy(command.getModifyBy());
            update.setModifyName(command.getModifyName());
            update.setModifyDate(new Date());
            driverGoodsMapper.modifyById(update);
        }
    }

    /**
     * 司机端意向货源
     *
     * @param filterModel
     * @return {@link List}<{@link DriverGoods}>
     */
    public List<DriverGoods> findChannelGoodsForDriverApp(DriverGoodsFilter filterModel) {
        return driverGoodsMapper.findChannelGoodsForDriverApp(filterModel);
    }

    public GoodsStatisticsDTO platformStatistics(DriverGoodsFilter filter) {
        Map<String, StatisticsDTO> statistics = driverGoodsMapper.platformStatistics(filter);
        GoodsStatisticsDTO result = new GoodsStatisticsDTO();
        if (CollectionUtil.isNotEmpty(statistics)) {
            StatisticsDTO deal = statistics.get(DriverDealStatusEnum.DEAL.getCode());
            StatisticsDTO release = statistics.get(DriverDealStatusEnum.RELEASE.getCode());
            StatisticsDTO partDeal = statistics.get(DriverDealStatusEnum.PART_DEAL.getCode());
            StatisticsDTO expire = statistics.get(DriverDealStatusEnum.EXPIRED.getCode());
            int dealCount = deal != null ? deal.getNum() : 0;
            int releaseCount = release != null ? release.getNum() : 0;
            int partDealCount = partDeal != null ? partDeal.getNum() : 0;
            int expireCount = expire != null ? expire.getNum() : 0;
            // 已成交
            result.setTraded(dealCount);
            // 发布中 + 已过期
            result.setUnsettled(releaseCount + partDealCount + expireCount);
            // 已成交 + 发布中 + 已过期
            result.setValid(result.getTraded() + result.getUnsettled());
        }
        return result;
    }

    /**
     * 填充报价信息
     *
     * @param driverGoods
     */
    private void appendOfferInfo(DriverGoods driverGoods) {
        DriverOfferFilter driverOfferFilter = new DriverOfferFilter();
        driverOfferFilter.setGoodsId(driverGoods.getId());
        driverOfferFilter.setOfferStatus(Constants.OFFER_STATUS_WAIT_CONFIRM);
        List<DriverOffer> driverOffers = driverOfferService.listByEntity(driverOfferFilter);
        //比对并设置金额最大的报价价格
        try {
            if (!driverOffers.isEmpty()) {
                DriverOffer driverOffer = driverOffers.stream().min(Comparator.comparing(DriverOffer::getEnquiryTypeBaseOpenTicket)).get();
                driverGoods.setMaxOfferPrice(driverOffer.getEnquiryTypeBaseOpenTicket());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //设置报价单列表
        driverGoods.setDriverOfferList(driverOffers);
    }
}

