package com.wanlianyida.lsds.application.service;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.lsds.api.dto.LsdsMatchmakingConfigDTO;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.DriverOffer;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.enums.GoodsKindEnum;
import com.isoftstone.hig.lsds.api.filter.DriverOfferFilter;
import com.isoftstone.hig.lsds.api.query.LsdsMatchmakingConfigQuery;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.platform.api.inter.PlatformCmDictionaryInter;
import com.isoftstone.hig.tms.api.common.Result;
import com.isoftstone.hig.tms.api.entity.TmsEnum;
import com.wanlianyida.lsds.application.model.command.DriverGoodsQuoteCommand;
import com.wanlianyida.lsds.domain.service.LsdsMatchmakingDomainService;
import com.wanlianyida.lsds.infrastructure.config.FormsAuthTrader;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.exchange.EvalExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.TcsExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.TmsExchangeService;
import com.wanlianyida.lsds.infrastructure.repository.mapper.DriverOfferMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import javax.annotation.Resource;

/**
 * 司机货源校验类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DriverGoodsValidatorAppService {

    @Resource
    private PlatformCmDictionaryInter platformCmDictionaryInter;

    @Resource
    private TcsExchangeService tcsExchangeService;

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private RmsAppService rmsBusiness;

    @Resource
    private EvalExchangeService evalExchangeService;

    @Resource
    private TmsExchangeService tmsExchangeService;

    @Resource
    private DriverOfferMapper lsdsDriverOfferMapper;

    @Resource
    private Executor asyncServiceExecutor;

    @Resource
    private LsdsMatchmakingDomainService lsdsMatchmakingService;

    /**
     * 司机货源报价校验
     */
    public ResultMode goodsQuoteValidator(DriverGoodsQuoteCommand driverGoodsQuoteVo) {
        DriverOfferFilter offerFilter = driverGoodsQuoteVo.getOfferFilter();
        DriverGoods driverGoods = driverGoodsQuoteVo.getDriverGoods();
        TokenInfo tokenInfo = driverGoodsQuoteVo.getTokenInfo();

        if (ObjUtil.hasNull(offerFilter, driverGoods, tokenInfo)) {
            throw new LsdsWlydException("报价参数错误");
        }
        //货源有效性校验
        if (checkGoodsStatus(driverGoods)) {
            return new ResultMode("500", "无效货源！");
        }
        //撮合货源议价校验
        ResultMode resultMode = checkMatchmaking(driverGoods);
        if (!resultMode.getSucceed()) {
            return resultMode;
        }
        //车辆信息非空校验
        resultMode = checkCarInfo(offerFilter);
        if (!resultMode.getSucceed()) {
            return resultMode;
        }

        //司机信息校验
        CompletableFuture<ResultMode> checkDriverInfoFuture = CompletableFuture.supplyAsync(() -> {
            return this.checkDriverInfo(offerFilter, LsdsEnum.OrderOpTypeEnum.APPQUOTATION30.getCode());
        }, asyncServiceExecutor);

        //挂车校验
        CompletableFuture<ResultMode> checkTrailerInfoFuture = CompletableFuture.supplyAsync(() -> {
            return this.checkTrailerInfo(offerFilter);
        }, asyncServiceExecutor);

        //风控接单测算
        CompletableFuture<ResultMode> checkOrderReceivingFuture = CompletableFuture.supplyAsync(() -> {
            return rmsBusiness.checkOrderReceiving(driverGoods, offerFilter);
        }, asyncServiceExecutor);

        //司机评级校验
        CompletableFuture<ResultMode> checkDriverGradeFuture = CompletableFuture.supplyAsync(() -> {
            return this.checkDriverGrade(offerFilter, driverGoods, tokenInfo);
        }, asyncServiceExecutor);

        //校验报价单
        CompletableFuture<ResultMode> checkDriverOfferFuture = CompletableFuture.supplyAsync(() -> {
            return this.checkDriverOffer(offerFilter, driverGoods, tokenInfo, driverGoodsQuoteVo);
        }, asyncServiceExecutor);

        //多次接单/报价校验
        CompletableFuture<ResultMode> checkOfferNumberFuture = CompletableFuture.supplyAsync(() -> {
            return this.checkOfferNumber(offerFilter, driverGoods, tokenInfo);
        }, asyncServiceExecutor);

        try {
            List<CompletableFuture<ResultMode>> allFutures = Arrays.asList(
                checkDriverInfoFuture, checkTrailerInfoFuture,
                checkOrderReceivingFuture, checkDriverGradeFuture,
                checkDriverOfferFuture, checkOfferNumberFuture);

            for (CompletableFuture<ResultMode> future : allFutures) {
                resultMode = future.get();
                if (!resultMode.isSucceed()) {
                    return resultMode;
                }
            }
        } catch (Exception e) {
            log.error("goodsQuoteValidator->参数校验超时：", e);
            return ResultMode.fail("司机货源报价校验失败：" + e.getMessage());
        }

        return ResultMode.success();
    }

    /**
     * 撮合货源议价校验
     */
    private ResultMode checkMatchmaking(DriverGoods driverGoods) {
        //非撮合货源不校验
        if (!GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(driverGoods.getGoodsKind())) {
            return ResultMode.success();
        }

        LsdsMatchmakingConfigQuery query = new LsdsMatchmakingConfigQuery();
        query.setBusId(driverGoods.getGoodsId());
        LsdsMatchmakingConfigDTO lsdsMatchmakingConfigDTO = lsdsMatchmakingService.queryConfig(query);
        if (lsdsMatchmakingConfigDTO.getBargainConfig() != 10) {
            return ResultMode.fail("该货源不可议价");
        }

        return ResultMode.success();
    }

    /**
     * 检查货源是否有效
     */
    private boolean checkGoodsStatus(DriverGoods driverGoods) {
        //货源交易状态：30-发布中(即审核通过),60-部分成交
        return !StrUtil.equalsAny(driverGoods.getDealStatus(), Constants.GOODS_STATUS_RELEASED, Constants.GOODS_STATUS_PART_DEAL)
            || new Date().after(driverGoods.getValidityDate());
    }

    /**
     * 车辆信息校验
     */
    public ResultMode checkCarInfo(DriverOfferFilter offerFilter) {
        if (StrUtil.isBlank(offerFilter.getTractorPlateCarId())) {
            return ResultMode.fail("车辆id为空");
        }
        if (StrUtil.isBlank(offerFilter.getTractorPlateNumber())) {
            return ResultMode.fail("车牌号为空");
        }
        if (StrUtil.isBlank(offerFilter.getTractorPlateColor())) {
            return ResultMode.fail("车牌颜色为空");
        }
        //车牌颜色枚举
        String parentDicId = "267";
        List<String> carColorCodes = FormsAuthTrader.getDictionaryEnumCodeList(platformCmDictionaryInter, parentDicId);
        if (IterUtil.isEmpty(carColorCodes)) {
            return ResultMode.fail("车牌颜色字典不存在");
        }
        if (!carColorCodes.contains(offerFilter.getTractorPlateColor())) {
            return ResultMode.fail("车牌颜色编码不存在");
        }

        //选择挂车校验
        if (StrUtil.isNotBlank(offerFilter.getTrailerPlateNumber())) {
            if (StrUtil.isBlank(offerFilter.getTrailerPlateCarId())) {
                return ResultMode.fail("挂车id为空");
            }
            if (StrUtil.isBlank(offerFilter.getTrailerPlateColor())) {
                return ResultMode.fail("挂车车牌颜色为空");
            }
            if (!carColorCodes.contains(offerFilter.getTrailerPlateColor())) {
                return ResultMode.fail("挂车车牌颜色编码不存在");
            }
        }

        return ResultMode.success();
    }

    /**
     * 司机信息校验
     * HWJC-413
     * 运单表结算id校验逻辑优化
     * 代收校验
     */
    public ResultMode checkDriverInfo(DriverOfferFilter offerFilter, String opType) {
        if (StrUtil.equals(LsdsEnum.FreightTypeEnum.FREIGHT_TYPE_1.getCode(), offerFilter.getFreightType())) {
            return ResultMode.success();
        }
        String message1 = String.format("用户【%s】您好，因承运司机信息异常，当前操作失败，请与万联易达客服取得联系处理。客服电话：400-015-8686", offerFilter.getDriverName());
        String message2 = String.format("用户【%s】您好，因收款信息异常，当前操作失败，请与万联易达客服取得联系处理。客服电话：400-015-8686", offerFilter.getDriverName());

        //1-司机id不能为空
        if (StrUtil.isEmpty(offerFilter.getDriverId())) {
            return ResultMode.fail(message1);
        }
        //2-收款人身份证号不能为空
        if (StrUtil.equals(opType, LsdsEnum.OrderOpTypeEnum.PCORDER20.getCode())) {
            //3PL报价下单
            if (StrUtil.isEmpty(offerFilter.getIdCard())) {
                return ResultMode.fail(message2);
            }
            offerFilter.setReceiptorIdCardNo(offerFilter.getIdCard());
        } else {
            //APP大厅直接或扫码下单或报价
            if (StrUtil.isEmpty(offerFilter.getReceiptorIdCardNo())) {
                return ResultMode.fail(message2);
            }
        }

        //3-如果收款人司机id为空时，通过收款人身份证号查询对应的司机id
        if (StrUtil.isEmpty(offerFilter.getReceiptorDriverId())) {
            //根据身份证号反查driverId
            String driverId = tcsExchangeService.getDriverIdByIdCardNo(offerFilter.getReceiptorIdCardNo());
            if (StrUtil.isEmpty(driverId)) {
                return ResultMode.fail(message2);
            }
            offerFilter.setReceiptorDriverId(driverId);
        }
        //4-receiptorDriverld，receiptAccountName校验不能为空
        if (StrUtil.isEmpty(offerFilter.getReceiptorDriverId()) || StrUtil.isEmpty(offerFilter.getReceiptAccountName())) {
            return ResultMode.fail(message2);
        }

        return ResultMode.success();
    }

    /**
     * 挂车校验
     */
    public ResultMode checkTrailerInfo(DriverOfferFilter offerFilter) {
        //牵引车
        if (!StrUtil.startWith(offerFilter.getAssignCarType(), "Q")) {
            return ResultMode.success();
        }

        //挂车必填校验
        String paramValue = platformExchangeService.getParamValue("052");
        log.info("checkTrailerInfo#平台挂车必填响应:{}", paramValue);
        ResultMode fail = Result.fail();
        fail.setErrCode("301");
        if (StrUtil.equals("1", paramValue) && StrUtil.isBlank(offerFilter.getTrailerPlateNumber())) {
            String colorName = platformExchangeService.getCarColorNameByColorCode(offerFilter.getTractorPlateColor());
            fail.setErrMsg(String.format("【%s %s】的挂车车牌需必填!", offerFilter.getTractorPlateNumber(), colorName));
            fail.setModel(Arrays.asList(offerFilter.getTractorPlateCarId()));
            return fail;
        }
        //挂车有效状态校验
        if (StrUtil.isNotBlank(offerFilter.getTrailerPlateNumber()) && StrUtil.isNotBlank(offerFilter.getTrailerPlateColor())) {
            String carStatus = tcsExchangeService.getCarStatus(offerFilter.getTrailerPlateNumber(),offerFilter.getTrailerPlateColor());
            if (!StrUtil.equals(carStatus, "10")) {
                fail.setErrMsg(String.format("无效的挂车【%s %s】", offerFilter.getTrailerPlateNumber(), TmsEnum.CarColorTypeEnum.getValueBykey(String.valueOf(offerFilter.getTrailerPlateColor()))));
                fail.setModel(Arrays.asList(offerFilter.getTractorPlateCarId()));
                return fail;
            }
        }

        return ResultMode.success();
    }

    /**
     * 司机评级校验
     */
    private ResultMode checkDriverGrade(DriverOfferFilter offerFilter, DriverGoods goods, TokenInfo tokenInfo) {
        if (StrUtil.isBlank(goods.getDriverGrade())) {
            return ResultMode.success();
        }

        List<Double> doubleResultMode = evalExchangeService.selectByDriverId(tokenInfo.getDriverId());
        log.info("司机评级校验:checkDriverGrade:{}", JSON.toJSONString(doubleResultMode));
        if (ObjUtil.isNull(doubleResultMode)) {
            return new ResultMode(UtilityEnum.AppErrorCode.CODE_1014.getCode(), UtilityEnum.AppErrorCode.CODE_1014.getDesc());
        }

        Double grade = doubleResultMode.get(0);
        offerFilter.setDriverGrade(doubleResultMode.get(0) + "");
        Double grade2 = null;
        switch (goods.getDriverGrade()) {
            case "10":
                grade2 = new Double(5);
                break;
            case "20":
                grade2 = new Double(4);
                break;
            case "30":
                grade2 = new Double(3);
                break;
            case "40":
                grade2 = new Double(2);
                break;
            case "50":
                grade2 = new Double(1);
                break;
            case "60":
                grade2 = new Double(0);
                break;
        }
        //司机评级不满足
        if (grade < grade2) {
            return new ResultMode(UtilityEnum.AppErrorCode.CODE_1015.getCode(), UtilityEnum.AppErrorCode.CODE_1015.getDesc());
        }

        return ResultMode.success();
    }

    /**
     * 检查报价单
     */
    private ResultMode checkDriverOffer(DriverOfferFilter offerFilter, DriverGoods driverGoods, TokenInfo tokenInfo, DriverGoodsQuoteCommand driverGoodsQuoteVo) {
        //指定司机 校验报价单是否存在
        if (StrUtil.equals(driverGoods.getEnquiryRange(), Constants.ENQUIRY_RANGE_DRIVERS)) {
            List<DriverOffer> driverOfferList = lsdsDriverOfferMapper.listByEntity(DriverOfferFilter.of()
                .setDriverId(tokenInfo.getDriverId()).setGoodsId(driverGoods.getGoodsId()));
            if (IterUtil.isEmpty(driverOfferList)) {
                return new ResultMode("500", "报价单不存在！");
            }
            //当前报价单赋值
            driverGoodsQuoteVo.setOffer(IterUtil.getFirst(driverOfferList));
        }
        //公开询价、撮合报价 需要检验报价次数
        if (StrUtil.equals(driverGoods.getEnquiryType(), Constants.ENQUIRY_TYPE_OPEN) || GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(driverGoods.getGoodsKind())) {
            List<DriverOffer> driverOfferList = lsdsDriverOfferMapper.listByEntity(DriverOfferFilter.of()
                .setDriverId(tokenInfo.getDriverId()).setGoodsId(driverGoods.getGoodsId()).setOfferStatus(Constants.OFFER_STATUS_WAIT_CONFIRM));
            if (IterUtil.isNotEmpty(driverOfferList)) {
                return new ResultMode(UtilityEnum.AppErrorCode.CODE_1017.getCode(), UtilityEnum.AppErrorCode.CODE_1017.getDesc().replace("XXX", offerFilter.getDriverName()));
            }
        }
        return ResultMode.success();
    }

    /**
     * 多次接单/报价限制
     */
    private ResultMode checkOfferNumber(DriverOfferFilter offerFilter, DriverGoods goods, TokenInfo tokenInfo) {
        String paraValue = platformExchangeService.getParamValue(LsdsEnum.WeightLimitParams.ORDER_LIMIT.getCode());
        if (StrUtil.isBlank(paraValue)) {
            return ResultMode.success();
        }

        Integer orderLimit = Integer.valueOf(paraValue);
        Integer waybillCount = tmsExchangeService.getDriverWaybillCount(tokenInfo.getDriverId(), goods.getGoodsId());
        log.info("offerNumberCheck#多次接单/报价限制:resultMode:{}", waybillCount);
        if (ObjUtil.isNull(waybillCount)) {
            throw new LsdsWlydException(UtilityEnum.AppErrorCode.CODE_1016.getDesc(), UtilityEnum.AppErrorCode.CODE_1016.getCode());
        }

        if (waybillCount < orderLimit) {
            return ResultMode.success();
        }

        if (StrUtil.equals(goods.getEnquiryType(), Constants.ENQUIRY_TYPE_OPEN) || GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(goods.getGoodsKind())) {
            return new ResultMode(UtilityEnum.AppErrorCode.CODE_1017.getCode(), UtilityEnum.AppErrorCode.CODE_1017.getDesc().replace("XXX", offerFilter.getDriverName()));
        } else {
            return new ResultMode(UtilityEnum.AppErrorCode.CODE_1018.getCode(), UtilityEnum.AppErrorCode.CODE_1018.getDesc().replace("XXX", offerFilter.getDriverName()));
        }
    }

}
