package com.wanlianyida.lsds.application.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.enums.GoodsRecommendSceneTypeEnum;
import com.isoftstone.hig.lsds.api.filter.DriverGoodsFilter;
import com.isoftstone.hig.lsds.api.mvcvo.GoodsRecommendReqVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsAttentionVO;
import com.isoftstone.hig.oms.api.entity.OmsOrderAddress;
import com.isoftstone.hig.oms.api.inter.OmsOrderAddressInter;
import com.isoftstone.hig.tms.api.entity.TmsOrder;
import com.isoftstone.hig.tms.api.entity.TmsWaybill;
import com.isoftstone.hig.tms.api.inter.TmsWaybillInter;
import com.isoftstone.hig.tms.api.mvcvo.TmsWaybillAddressRespVO;
import com.isoftstone.hig.tms.api.query.TmsOrderFilter;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.repository.mapper.DriverGoodsMapper;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 货源推荐
 * @Date 2024年06月12 15:32
 */
@Service
public class GoodsRecommendAppService {

    @Lazy
    @Resource
    private LsdsGoodsAttentionAppService goodsAttentionServiceTran;
    @Resource
    private TmsWaybillInter tmsWaybillInter;
    @Resource
    private DriverGoodsMapper driverGoodsMapper;
    @Resource
    private OmsOrderAddressInter omsOrderAddressInter;

    /**
     * 每页数据量
     */
    private static final int PAGE_SIZE = 10;

    /**
     * 默认场景降级顺序，10：回程货（目的地-出发地），20：去程货（出发地-目的地），30：出发地（出发地一致），40：目的地（目的地一致）
     */
    private static final int[] SCENE_ORDER = new int[]{10, 20, 30, 40};

    /**
     * 获取司机货源
     */
    public List<LsdsGoodsAttentionVO> getGoodsRecommendInfo(GoodsRecommendReqVo recommendReq, AddressInfo queryAddress) {
        int surplus = PAGE_SIZE;
        List<DriverGoods> result = new ArrayList<>(PAGE_SIZE);
        // 获取场景降级顺序，由前端传参确定起始场景
        int[] sceneOrder = getSceneOrder(recommendReq.getSceneType());
        for (int sceneType : sceneOrder) {
            AddressInfo addressInfo = getAddress(queryAddress, sceneType);
            if (addressInfo == null) {
                return Collections.EMPTY_LIST;
            }
            List<DriverGoods> data = findGoodsData(surplus, addressInfo);
            result.addAll(data);
            result = new ArrayList<>(result.stream().collect(Collectors.toMap(DriverGoods::getGoodsId, e -> e, (existing, replacement) -> existing)).values());
            if (result.size() >= PAGE_SIZE) {
                break;
            }
            surplus = PAGE_SIZE - result.size();
        }
        return goodsAttentionServiceTran.transferDriverGoodsRecommend(result, recommendReq);
    }

    /**
     * 获取绑码订单
     */
    public List<LsdsGoodsAttentionVO> getBindOrderRecommend(GoodsRecommendReqVo recommendReq, AddressInfo queryAddress) {
        int surplus = PAGE_SIZE;
        List<TmsOrder> result = new ArrayList<>(PAGE_SIZE);
        // 获取场景降级顺序，由前端传参确定起始场景
        int[] sceneOrder = getSceneOrder(recommendReq.getSceneType());
        for (int sceneType : sceneOrder) {
            AddressInfo addressInfo = getAddress(queryAddress, sceneType);
            if (addressInfo == null) {
                return Collections.EMPTY_LIST;
            }
            List<TmsOrder> data = findOrderData(surplus, addressInfo, recommendReq.getGoodsTypeList());
            result.addAll(data);
            result = new ArrayList<>(result.stream().collect(Collectors.toMap(TmsOrder::getGoodsId, e -> e, (existing, replacement) -> existing)).values());
            if (result.size() >= PAGE_SIZE) {
                break;
            }
            surplus = PAGE_SIZE - result.size();
        }
        return goodsAttentionServiceTran.transferBindOrderRecommend(result, recommendReq);
    }

    /**
     * 查询货源数据
     */
    private List<DriverGoods> findGoodsData(int surplus, AddressInfo addressInfo) {
        DriverGoodsFilter filter = new DriverGoodsFilter();
        filter.setPageLimit(surplus);
        filter.setSendAddrCity(addressInfo.getSendAddrCity());
        filter.setReceiveAddrCity(addressInfo.getReceiveAddrCity());
        //获取司机货源
        return goodsAttentionServiceTran.getDriverGoodsRecommend(filter);
    }

    /**
     * 查询订单数据
     */
    private List<TmsOrder> findOrderData(int surplus, AddressInfo addressInfo, List<String> goodsTypeList) {
        TmsOrderFilter filter = new TmsOrderFilter();
        filter.setPageLimit(surplus);
        filter.setSendAddrCity(addressInfo.getSendAddrCity());
        filter.setReceiveAddrCity(addressInfo.getReceiveAddrCity());
        if (IterUtil.isNotEmpty(goodsTypeList)) {
            filter.setGoodsTypeList(goodsTypeList);
        }
        //获取司机货源
        return goodsAttentionServiceTran.getBindOrderRecommend(filter);
    }

    /**
     * 获取地址信息
     */
    public AddressInfo getAddress(AddressInfo queryAddress, int sceneType) {
        AddressInfo addressInfo = new AddressInfo();
        if (GoodsRecommendSceneTypeEnum.SEND_BACK.getType() == sceneType) {
            addressInfo.setSendAddrCity(queryAddress.getSendAddrCity());
            addressInfo.setReceiveAddrCity(queryAddress.getReceiveAddrCity());
        } else if (GoodsRecommendSceneTypeEnum.BACK_SEND.getType() == sceneType) {
            addressInfo.setSendAddrCity(queryAddress.getReceiveAddrCity());
            addressInfo.setReceiveAddrCity(queryAddress.getSendAddrCity());
        } else if (GoodsRecommendSceneTypeEnum.SEND.getType() == sceneType) {
            addressInfo.setSendAddrCity(queryAddress.getSendAddrCity());
        } else if (GoodsRecommendSceneTypeEnum.BACK.getType() == sceneType) {
            addressInfo.setReceiveAddrCity(queryAddress.getReceiveAddrCity());
        } else {
            return null;
        }
        return addressInfo;
    }

    public AddressInfo findAddress(GoodsRecommendReqVo recommendReq) {
        AddressInfo addressInfo = new AddressInfo();
        if (StrUtil.isNotBlank(recommendReq.getWaybillId())) {
            TmsWaybill waybill = new TmsWaybill();
            waybill.setWaybillId(recommendReq.getWaybillId());
            ResultMode<TmsWaybillAddressRespVO> receiveResult = tmsWaybillInter.getWaybillEndAddress(waybill);
            if (receiveResult == null) {
                return null;
            }
            if (!receiveResult.isSucceed() || CollectionUtil.isEmpty(receiveResult.getModel())) {
                return null;
            }
            ResultMode<TmsWaybillAddressRespVO> sendResult = tmsWaybillInter.getWaybillSendAddress(waybill);
            TmsWaybillAddressRespVO receiveAddress = receiveResult.getModel().get(0);
            TmsWaybillAddressRespVO sendAddress = sendResult.getModel().get(0);
            if (StrUtil.isEmpty(receiveAddress.getProvince()) && StrUtil.isEmpty(receiveAddress.getCity()) && StrUtil.isEmpty(receiveAddress.getArea())) {
                //地址都为空时，不推荐
                return null;
            }
            addressInfo.setSendAddrProvince(sendAddress.getProvince());
            addressInfo.setSendAddrCity(sendAddress.getCity());
            addressInfo.setSendAddrArea(sendAddress.getArea());
            addressInfo.setReceiveAddrProvince(receiveAddress.getProvince());
            addressInfo.setReceiveAddrCity(receiveAddress.getCity());
            addressInfo.setReceiveAddrArea(receiveAddress.getArea());
            return addressInfo;
        } else if (StrUtil.isNotBlank(recommendReq.getGoodsId()) && recommendReq.getGoodsId().startsWith("DFQ")){
            DriverGoods driverGoods = driverGoodsMapper.getByGoodsId(recommendReq.getGoodsId());
            addressInfo.setSendAddrCity(driverGoods.getSendAddrCity());
            addressInfo.setReceiveAddrCity(driverGoods.getReceiveAddrCity());
            return addressInfo;
        } else if (StrUtil.isNotBlank(recommendReq.getOrderId())) {
            OmsOrderAddress omsOrderAddress = omsOrderAddressInter.getByOrderId(recommendReq.getOrderId()).getModel().get(0);
            addressInfo.setSendAddrCity(omsOrderAddress.getSendAddrCity());
            addressInfo.setReceiveAddrCity(omsOrderAddress.getReceiveAddrCity());
            return addressInfo;
        } else {
            throw new LsdsWlydException("必要参数为空");
        }
    }

    @Data
    public static class AddressInfo {

        @ApiModelProperty(value = " 出发地省code")
        private String sendAddrProvince;

        @ApiModelProperty(value = "出发地市code ")
        private String sendAddrCity;

        @ApiModelProperty(value = "出发地区县code")
        private String sendAddrArea;

        @ApiModelProperty(value = "目的地省code")
        private String receiveAddrProvince;

        @ApiModelProperty(value = "目的地市code")
        private String receiveAddrCity;

        @ApiModelProperty(value = "目的地省区县code")
        private String receiveAddrArea;
    }

    private int[] getSceneOrder(Integer sceneType) {
        if (sceneType == null || sceneType == 0) {
            // 默认起始场景为回程货
            sceneType = 10;
        }
        int[] sub = ArrayUtil.sub(SCENE_ORDER, ArrayUtil.indexOf(SCENE_ORDER, sceneType), SCENE_ORDER.length);
        return sub.length > 0 ? sub : SCENE_ORDER;
    }
}
