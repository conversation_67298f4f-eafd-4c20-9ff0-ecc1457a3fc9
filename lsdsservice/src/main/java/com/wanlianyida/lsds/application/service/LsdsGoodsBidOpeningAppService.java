package com.wanlianyida.lsds.application.service;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.lsds.api.command.BidOpeningCommand;
import com.isoftstone.hig.lsds.api.entity.LsdsGoods;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsBidOpening;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsOffer;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsRecord;
import com.isoftstone.hig.lsds.api.enums.BidOpeningStatusEnum;
import com.isoftstone.hig.lsds.api.enums.GoodsSourceTypeEnum;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.repository.mapper.GoodsMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.GoodsRecordMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsGoodsBidOpeningMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsGoodsOfferMapper;
import com.wanlianyida.lsds.infrastructure.util.AopSpringFactory;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

/**
 * 货源开标记录
 *
 * <AUTHOR>
 */
@Service
public class LsdsGoodsBidOpeningAppService {

    @Resource
    private LsdsGoodsBidOpeningMapper lsdsGoodsBidOpeningMapper;

    @Resource
    private LsdsGoodsOfferMapper lsdsGoodsOfferRepository;

    @Resource
    private GoodsRecordMapper lsdsGoodsRecordRepository;

    @Resource
    private GoodsMapper goodsMapper;



    public ResultMode bidOpening(BidOpeningCommand command) {
        //校验
        LsdsGoods lsdsGoodsInfo = goodsMapper.getLsdsGoodsInfo(command.getGoodsId());
        if (ObjUtil.isNull(lsdsGoodsInfo)) {
            throw new LsdsWlydException("货源不存在");
        }

        if (!StrUtil.equals(lsdsGoodsInfo.getGoodsSourceType(), GoodsSourceTypeEnum.BID_OPENING.getCode())) {
            throw new LsdsWlydException("不支持当前操作");
        }

        if (!command.getOfferRound().equals(lsdsGoodsInfo.getOfferCurrentRounds())) {
            throw new LsdsWlydException("货源当前为第" + lsdsGoodsInfo.getOfferCurrentRounds() + "轮报价");
        }

        //查询本轮是否已报价
        LsdsGoodsOffer query = new LsdsGoodsOffer();
        query.setGoodsId(command.getGoodsId());
        query.setOfferRound(command.getOfferRound());
        int quoteOffer = lsdsGoodsOfferRepository.existsQuoteOffer(query);
        if (quoteOffer == 0) {
            throw new LsdsWlydException("货源当前轮次未报价");
        }

        LsdsGoodsBidOpening condition = new LsdsGoodsBidOpening();
        condition.setGoodsId(command.getGoodsId());
        condition.setOfferRound(command.getOfferRound());
        List<LsdsGoodsBidOpening> list = queryCondition(condition);
        if (IterUtil.isNotEmpty(list)) {
            LsdsGoodsBidOpening opening = IterUtil.getFirst(list);
            if (StrUtil.equals(opening.getBidOpeningStatus(), BidOpeningStatusEnum.OPENED.getCode())) {
                throw new LsdsWlydException("货源" + command.getGoodsId()
                    + "第" + command.getOfferRound() + "轮报价已开标");
            }

            LsdsGoodsBidOpening entity = assembleBidOpeningUpd(opening);
            AopSpringFactory.getAopProxy(this).updateBidOpening(entity, lsdsGoodsInfo);
            return ResultMode.success();
        }

        //新增
        LsdsGoodsBidOpening entity = assembleBidOpeningAdd(command);
        AopSpringFactory.getAopProxy(this).addBidOpening(entity, lsdsGoodsInfo);
        return ResultMode.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public void addBidOpening(LsdsGoodsBidOpening entity, LsdsGoods lsdsGoodsInfo) {
        //新增开标记录
        lsdsGoodsBidOpeningMapper.insertSelective(entity);
        //开标操作记录
        LsdsGoodsRecord lsdsGoodsRecord = assembleBidOpeningRecord(entity, lsdsGoodsInfo);
        lsdsGoodsRecordRepository.add(lsdsGoodsRecord);
        //更新报价单开标状态
        LsdsGoodsOffer lsdsGoodsOffer = assembleOfferUpd(entity);
        lsdsGoodsOfferRepository.updateBidOpening(lsdsGoodsOffer);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBidOpening(LsdsGoodsBidOpening entity, LsdsGoods lsdsGoodsInfo) {
        //更新开标记录
        lsdsGoodsBidOpeningMapper.updateByPrimaryKeySelective(entity);
        //开标操作记录
        LsdsGoodsRecord lsdsGoodsRecord = assembleBidOpeningRecord(entity, lsdsGoodsInfo);
        lsdsGoodsRecordRepository.add(lsdsGoodsRecord);
        //更新报价单开标状态
        LsdsGoodsOffer lsdsGoodsOffer = assembleOfferUpd(entity);
        lsdsGoodsOfferRepository.updateBidOpening(lsdsGoodsOffer);
    }

    /**
     * 开标操作记录
     */
    private LsdsGoodsRecord assembleBidOpeningRecord(LsdsGoodsBidOpening entity, LsdsGoods lsdsGoodsInfo) {
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        //同步货源操作记录
        LsdsGoodsRecord lgr = LsdsGoodsRecord.of();
        lgr.setRecordId(String.valueOf(IdUtil.generateId()));
        lgr.setGoodsId(entity.getGoodsId());
        lgr.setCompanyId(tokenInfo.getCompanyId());
        lgr.setOperateStatus("20");
        lgr.setOperateContent("货源进入第" + entity.getOfferRound() + "轮开标");
        lgr.setUserId(entity.getUpdaterId());
        lgr.setUserLoginName(tokenInfo.getLoginName());
        lgr.setUserName(tokenInfo.getUsername());

        String currentOfferStartDate = lsdsGoodsInfo.getCurrentOfferStartDate();
        Integer enquiryOfferTime = lsdsGoodsInfo.getEnquiryOfferTime();
        String validityDate = lsdsGoodsInfo.getValidityDate();
        long offerLeftTime = 0;
        Date updatedDate = entity.getUpdatedDate();
        if (StrUtil.isNotBlank(currentOfferStartDate) && ObjUtil.isNotNull(enquiryOfferTime) && enquiryOfferTime > 0) {
            offerLeftTime = enquiryOfferTime * 60 * 60 * 1000 - (DateUtil.betweenMs(updatedDate, DateUtil.parseDateTime(currentOfferStartDate)));
        } else if (StrUtil.isNotBlank(currentOfferStartDate) && (ObjUtil.isNull(enquiryOfferTime) || enquiryOfferTime == 0)) {
            offerLeftTime = DateUtil.betweenMs(DateUtil.parseDateTime(validityDate), updatedDate);
        }

        String formatBetween = DateUtil.formatBetween(offerLeftTime, BetweenFormatter.Level.SECOND);

        lgr.setRemark("当前轮次剩余时间" + formatBetween);
        return lgr;
    }

    private LsdsGoodsOffer assembleOfferUpd(LsdsGoodsBidOpening entity) {
        LsdsGoodsOffer offer = new LsdsGoodsOffer();
        offer.setGoodsId(entity.getGoodsId());
        offer.setOfferRound(entity.getOfferRound());
        offer.setBidOpeningStatus(entity.getBidOpeningStatus());
        offer.setBidOpeningDate(entity.getUpdatedDate());
        offer.setModifyDate(DateUtil.formatDateTime(entity.getUpdatedDate()));
        offer.setModifyBy(entity.getUpdaterId());
        return offer;
    }

    private LsdsGoodsBidOpening assembleBidOpeningUpd(LsdsGoodsBidOpening opening) {
        LsdsGoodsBidOpening entity = new LsdsGoodsBidOpening();
        entity.setId(opening.getId());
        entity.setBidOpeningStatus(BidOpeningStatusEnum.OPENED.getCode());

        String userBaseId = JwtUtil.getInstance().getUserBaseIdByToken();
        DateTime now = DateUtil.date();
        entity.setUpdaterId(userBaseId);
        entity.setUpdatedDate(now);

        return entity;
    }

    private LsdsGoodsBidOpening assembleBidOpeningAdd(BidOpeningCommand command) {
        LsdsGoodsBidOpening entity = new LsdsGoodsBidOpening();
        entity.setGoodsId(command.getGoodsId());
        entity.setOfferRound(command.getOfferRound());
        entity.setBidOpeningStatus(BidOpeningStatusEnum.OPENED.getCode());

        String userBaseId = JwtUtil.getInstance().getUserBaseIdByToken();
        DateTime now = DateUtil.date();
        entity.setCreatorId(userBaseId);
        entity.setUpdaterId(userBaseId);
        entity.setCreatedDate(now);
        entity.setUpdatedDate(now);

        return entity;
    }


    public List<LsdsGoodsBidOpening> queryCondition(LsdsGoodsBidOpening condition) {
        if (ObjUtil.isNull(condition) || ObjUtil.isAllEmpty(ReflectUtil.getFieldsValue(condition))) {
            return null;
        }

        List<LsdsGoodsBidOpening> list = lsdsGoodsBidOpeningMapper.queryCondition(condition);
        return list;
    }

}
