package com.wanlianyida.lsds.application.service;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.filter.DriverOfferFilter;
import com.isoftstone.hig.platform.api.entity.PlatformCmDictionary;
import com.isoftstone.hig.platform.api.inter.PlatformCmDictionaryInter;
import com.wanlianyida.hig.rms.api.common.IdUtils;
import com.wanlianyida.hig.rms.api.enums.RmsEnum;
import com.wanlianyida.hig.rms.api.inter.WaybillGenerateBussInter;
import com.wanlianyida.hig.rms.api.po.RiskBody;
import com.wanlianyida.hig.rms.api.po.RiskExecInfo;
import com.wanlianyida.hig.rms.api.po.WaybillRiskRuleParam;
import com.wanlianyida.hig.rms.api.po.platform.*;
import com.wanlianyida.hig.rms.api.po.tms.TmsWaybillOwnGoods;
import com.wanlianyida.hig.rms.api.po.tms.WaybillLicenseScopeVo;
import com.wanlianyida.hig.rms.api.po.tms.WaybillNotFinishVo;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.exchange.TcsExchangeService;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 风控相关业务处理
 *
 * <AUTHOR>
 * @since 2023/6/14 18:05
 */
@Log4j2
@Service
public class RmsAppService {

    @Resource
    private WaybillGenerateBussInter waybillGenerateBussInter;

    @Resource
    private PlatformCmDictionaryInter platformCmDictionaryInter;

    @Resource
    private TcsExchangeService tcsExchangeService;
    /**
     * 风控接单测算
     *
     * @param waybillRiskRuleParam
     * <AUTHOR>
     * @Date 2023/6/15 9:26
     */
    public ResultMode<RiskBody> receiveOrderCalculate(WaybillRiskRuleParam waybillRiskRuleParam) {
        try {
            log.info("风控接单测算入参：{}", JSONUtil.toJsonStr(waybillRiskRuleParam));
            ResultMode<RiskBody> resultMode = waybillGenerateBussInter.receiveOrderCalculate(waybillRiskRuleParam);
            log.info("waybillGenerateBussInter#receiveOrderCalculate结果：{}", JSONUtil.toJsonStr(resultMode));
            return resultMode;
        } catch (Exception e) {
            log.error("调用风控waybillGenerateBussInter#receiveOrderCalculate异常:{}", e);
            throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_BCOM0500, "接单风控测算失败");
        }
    }

    /**
     * 接单风控校验
     *
     * @param driverGoods
     * @param offerFilter
     * @return void
     * <AUTHOR>
     * @Date 2023/6/15 9:26
     */
    public ResultMode<RiskBody> checkOrderReceiving(DriverGoods driverGoods, DriverOfferFilter offerFilter) {
        // 组装参数
        WaybillRiskRuleParam waybillRiskRuleParam = assemblyRiskRuleParam(driverGoods, offerFilter);
        log.info("RmsBusiness#checkOrderReceiving 接单风控校验入参：{}", JSONUtil.toJsonStr(waybillRiskRuleParam));
        // 风控测算
        ResultMode<RiskBody> resultMode = receiveOrderCalculate(waybillRiskRuleParam);

        if (ObjectUtil.isNotNull(resultMode) && IterUtil.isNotEmpty(resultMode.getModel())) {
            RiskBody riskBody = IterUtil.getFirst(resultMode.getModel());
            // 结果构造
            return assemblyRiskRuleRes(riskBody);
        }
        return ResultMode.success();
    }

    /**
     * 接单风控校验响应组装
     *
     * @param riskBody
     * @return void
     * <AUTHOR>
     * @Date 2023/6/15 9:26
     */
    private ResultMode<RiskBody> assemblyRiskRuleRes(RiskBody riskBody) {
        List<RiskExecInfo> riskList = riskBody.getRiskList();
        if (IterUtil.isNotEmpty(riskList)) {
            // 高风险
            List<RiskExecInfo> highRiskInfo = riskList.stream().filter(risk -> risk.getSuccess()
                && StrUtil.equals(RmsEnum.RiskLevelEnum.HIGH.getCode(), risk.getRiskLevel())).collect(Collectors.toList());
            if (IterUtil.isNotEmpty(highRiskInfo)) {
                // 拼接到errMsg返回
                String errMsg = highRiskInfo.stream().map(RiskExecInfo::getRiskContent).collect(Collectors.joining("；\n "));
                return ResultMode.fail(errMsg, riskBody);
//                return ResultMode.fail("", riskBody);
            } else {
                return ResultMode.success(riskBody);
            }
        }

        return ResultMode.success();
    }

    /**
     * 接单风控校验参数组装
     *
     * @param driverGoods
     * @param offerFilter
     * @return void
     * <AUTHOR>
     * @Date 2023/6/15 9:26
     */
    private WaybillRiskRuleParam assemblyRiskRuleParam(DriverGoods driverGoods, DriverOfferFilter offerFilter) {
        WaybillRiskRuleParam waybillRiskRuleParam = new WaybillRiskRuleParam();
        waybillRiskRuleParam.setBussSceneType(RmsEnum.BussItemEnum.TMS_WAYBILL_RECEIVIE_ORDER.getCode());
        waybillRiskRuleParam.setFreightType(offerFilter.getFreightType());
        StringBuilder scene = new StringBuilder(RmsEnum.RiskItemEnum.TMS_SELF_DELIVERY.getCode()).append("|")
            .append(RmsEnum.RiskItemEnum.TMS_CAN_NOE_DRIVE.getCode()).append("|")
            .append(RmsEnum.RiskItemEnum.TMS_WAYBILL_NOT_FINISHED.getCode()).append("|")
            .append(RmsEnum.RiskItemEnum.PLATFORM_PAYEE_BUSS_APTITUDE_VERIFY.getCode()).append("|")
            .append(RmsEnum.RiskItemEnum.PLATFORM_PAYEE_ACCOUNT_APTITUDE_VERIFY.getCode()).append("|")
            .append(RmsEnum.RiskItemEnum.PLATFORM_DRIVER_APTITUDE_VERIFY.getCode()).append("|")
            .append(RmsEnum.RiskItemEnum.PLATFORM_CAR_APTITUDE_VERIFY.getCode()).append("|")
            .append(RmsEnum.RiskItemEnum.PLATFORM_CARRIER_APTITUDE_VERIFY.getCode()).append("|")
            .append(RmsEnum.RiskItemEnum.TMS_WAYBILL_DRIVER_REGISTRATION_TIME.getCode()).append("|")
            .append(RmsEnum.RiskItemEnum.TMS_WAYBILL_DRIVING_LICENSE_INITIAL_ISSUE_TIME.getCode()).append("|")
            .append(RmsEnum.RiskItemEnum.TMS_WAYBILL_CAR_REGISTRATION_TIME.getCode()).append("|")
            .append(RmsEnum.RiskItemEnum.TMS_WAYBILL_CAR_DRIVING_LICENSE_REGISTER_TIME.getCode()).append("|")
            .append(RmsEnum.RiskItemEnum.LSDS_NETWORK_MAIN_BODY_UNAVAILABLE.getCode()).append("|")
            ;
//        if (StrUtil.isNotBlank(offerFilter.getTrailerPlateNumber())) {
//            scene.append(RmsEnum.RiskItemEnum.TMS_SELF_DELIVERY_TRAILER.getCode()).append("|");
//        }
        //waybillRiskRuleParam.setBizId(driverGoods.getGoodsId());
        //业务id
        waybillRiskRuleParam.setBizId(IdUtils.generateShardingId() + String.valueOf(IdUtil.generateId()));
        waybillRiskRuleParam.setBussId(offerFilter.getDriverId());
        //网络货运主体
        waybillRiskRuleParam.setNetworkMainBodyId(offerFilter.getNetworkMainBodyId());
        //车型匹配
        PlatformCarrierBusiness platformCarrierBusiness = new PlatformCarrierBusiness();
        platformCarrierBusiness.setDriverId(offerFilter.getDriverId());
        platformCarrierBusiness.setDriverName(offerFilter.getDriverName());
        platformCarrierBusiness.setCarPlateNo(offerFilter.getTractorPlateNumber());
        platformCarrierBusiness.setFreightType(offerFilter.getFreightType());

        //车牌颜色
        Integer carColor = null;
        if (StrUtil.isNotEmpty(offerFilter.getTractorPlateColor())) {
            try {
                carColor = Integer.parseInt(offerFilter.getTractorPlateColor());
            } catch (Exception e) {
                log.error("assemblyRiskRuleParam#转换异常:{}",e);
            }
            platformCarrierBusiness.setCarColor(carColor);
        } else {
            Integer color = tcsExchangeService.getCarColor(offerFilter.getTractorPlateNumber());
            if(ObjUtil.isNotNull(color)){
                platformCarrierBusiness.setCarColor(color);
            }
        }
        carColor = platformCarrierBusiness.getCarColor();
        waybillRiskRuleParam.setPlatformCarrierBusiness(platformCarrierBusiness);

        //自货自运
        TmsWaybillOwnGoods tmsWaybillOwnGoods = new TmsWaybillOwnGoods();
        tmsWaybillOwnGoods.setGoodsId(driverGoods.getGoodsId());
        tmsWaybillOwnGoods.setPlateNumber(offerFilter.getTractorPlateNumber());

        String colorName = getCarColorNameByColorCode(offerFilter.getTractorPlateColor());
        tmsWaybillOwnGoods.setPlateColor(colorName);
        tmsWaybillOwnGoods.setCarColor(carColor);
        waybillRiskRuleParam.setTmsWaybillOwnGoods(tmsWaybillOwnGoods);

        //运单存在历史未完成运单-------------------------------------------------------------------
        WaybillNotFinishVo waybillNotFinishVo = new WaybillNotFinishVo();
        waybillNotFinishVo.setCardIdNo(offerFilter.getIdCardNo());
        waybillNotFinishVo.setDriverName(offerFilter.getDriverName());
        waybillNotFinishVo.setPlateNumber(offerFilter.getTractorPlateNumber());
        Date date = new Date();
        waybillNotFinishVo.setOperatorTime(date);
        waybillNotFinishVo.setFreightType(offerFilter.getFreightType());

        waybillRiskRuleParam.setWaybillNotFinishVo(waybillNotFinishVo);

        // 收款人是否具备参与业务资格
        PayeeBussInfoVO payeeBussInfoVO = new PayeeBussInfoVO();
        payeeBussInfoVO.setFreightType(offerFilter.getFreightType());
        payeeBussInfoVO.setPayeeIdCard(offerFilter.getReceiptorIdCardNo());
        waybillRiskRuleParam.setPayeeBussInfoVO(payeeBussInfoVO);

        // 收款人账户是否具备参与业务资格
        PayeeAccountInfoVO payeeAccountInfoVO = new PayeeAccountInfoVO();
        payeeAccountInfoVO.setFreightType(offerFilter.getFreightType());
        payeeAccountInfoVO.setPayeeName(offerFilter.getReceiptAccountName());
        payeeAccountInfoVO.setPayeeIdCard(offerFilter.getReceiptorIdCardNo());
        payeeAccountInfoVO.setDriverName(offerFilter.getDriverName());
        payeeAccountInfoVO.setDriverIdCardNo(offerFilter.getIdCardNo());
        payeeAccountInfoVO.setAccountNo(offerFilter.getReceiptAccount());
        waybillRiskRuleParam.setPayeeAccountInfoVO(payeeAccountInfoVO);

        // R00012 驾驶员是否具备参与业务资格
        DriverBussInfoVO driverBussInfoVO = new DriverBussInfoVO();
        driverBussInfoVO.setDriverId(offerFilter.getDriverId());
        driverBussInfoVO.setFreightType(offerFilter.getFreightType());
        waybillRiskRuleParam.setDriverBussInfoVO(driverBussInfoVO);

        // R00013 车辆是否具备参与业务资格
        CarBussInfoVO carBussInfoVO = new CarBussInfoVO();
        carBussInfoVO.setCarPlateNo(offerFilter.getTractorPlateNumber());
        carBussInfoVO.setCarColor(offerFilter.getTractorPlateColor());
        carBussInfoVO.setFreightType(offerFilter.getFreightType());
        waybillRiskRuleParam.setCarBussInfoVO(carBussInfoVO);

        // R00014 挂车是否具备参与业务资格
        if (!StrUtil.isBlank(offerFilter.getTrailerPlateNumber())) {
            scene.append(RmsEnum.RiskItemEnum.PLATFORM_TRAILER_APTITUDE_VERIFY.getCode()).append("|");
            CarBussInfoVO trailerBussInfoVO = new CarBussInfoVO();
            trailerBussInfoVO.setCarPlateNo(offerFilter.getTrailerPlateNumber());
            trailerBussInfoVO.setCarColor(offerFilter.getTrailerPlateColor());
            trailerBussInfoVO.setFreightType(offerFilter.getFreightType());
            waybillRiskRuleParam.setTrailerBussInfoVO(trailerBussInfoVO);
        }

        // R00015 承运人是否具备参与业务资格
        CarrierBussInfoVO carrierBussInfoVO = new CarrierBussInfoVO();
        carrierBussInfoVO.setDriverId(offerFilter.getDriverId());
        waybillRiskRuleParam.setCarrierBussInfoVO(carrierBussInfoVO);

        //黑名单校验
        //组装黑名单风控条件-----------------------------------begin--------------------------------------
        log.info("组装黑名单风控条件入参getFreightType:{},getIdCard:{}，getIdCardNo:{},getNetworkMainBodyId:{}",
             JSON.toJSONString(offerFilter.getFreightType()),
             JSON.toJSONString(offerFilter.getReceiptorIdCardNo())
            ,JSON.toJSONString(offerFilter.getIdCardNo())
            ,JSON.toJSONString(offerFilter.getNetworkMainBodyId())
        );

        PlatformUmCollectorBlacklistVo platformUmCollectorBlacklistVo = new PlatformUmCollectorBlacklistVo();

        scene.append(RmsEnum.RiskItemEnum.PLATFORM_COLLECTOR_BLACKLIST_VERIFY.getCode()).append("|");
        platformUmCollectorBlacklistVo.setRealityShipperId(driverGoods.getCompanyId());
        platformUmCollectorBlacklistVo.setNetworkMainBodyId(offerFilter.getNetworkMainBodyId());
        platformUmCollectorBlacklistVo.setPlaceOrderType(offerFilter.getPlaceOrderType());

        platformUmCollectorBlacklistVo.setPayeeIdCardNo(offerFilter.getReceiptorIdCardNo());
        platformUmCollectorBlacklistVo.setDirverIdCardNo(offerFilter.getIdCardNo());
        platformUmCollectorBlacklistVo.setFreightType(offerFilter.getFreightType());
        platformUmCollectorBlacklistVo.setDriverName(offerFilter.getDriverName());

        waybillRiskRuleParam.setPlatformUmCollectorBlacklistVo(platformUmCollectorBlacklistVo);

        LocalDateTime waybillCreateTime = LocalDateTimeUtil.of(date);
        // R10023 驾驶员入驻平台时间是否合理
        DriverRegistrationTimeVO driverRegistrationTimeVO = new DriverRegistrationTimeVO();
        driverRegistrationTimeVO.setDriverId(offerFilter.getDriverId());
        driverRegistrationTimeVO.setDriverName(offerFilter.getDriverName());
        driverRegistrationTimeVO.setPhoneNumber(offerFilter.getContactPhoneNumber());
        driverRegistrationTimeVO.setFreightType(offerFilter.getFreightType());
        driverRegistrationTimeVO.setWaybillCreateTime(waybillCreateTime);
        waybillRiskRuleParam.setDeviceRegistrationTimeVO(driverRegistrationTimeVO);

        // R10024 驾驶证初次领证日期是否合理
        DriverLicenseIssueTimeVO driverLicenseIssueTimeVO = new DriverLicenseIssueTimeVO();
        driverLicenseIssueTimeVO.setFreightType(offerFilter.getFreightType());
        driverLicenseIssueTimeVO.setDriverId(offerFilter.getDriverId());
        driverLicenseIssueTimeVO.setDriverName(offerFilter.getDriverName());
        driverLicenseIssueTimeVO.setPhoneNumber(offerFilter.getContactPhoneNumber());
        driverLicenseIssueTimeVO.setWaybillCreateTime(waybillCreateTime);
        waybillRiskRuleParam.setDevLicenseIssueTimeVO(driverLicenseIssueTimeVO);

        // R10025 车辆入驻平台时间是否合理
        CarRegistrationTimeVO carRegistrationTimeVO = new CarRegistrationTimeVO();
        carRegistrationTimeVO.setFreightType(offerFilter.getFreightType());
        carRegistrationTimeVO.setDriverId(offerFilter.getDriverId());
        carRegistrationTimeVO.setDriverName(offerFilter.getDriverName());
        carRegistrationTimeVO.setPhoneNumber(offerFilter.getContactPhoneNumber());
        carRegistrationTimeVO.setCarPlateNo(offerFilter.getTractorPlateNumber());
        carRegistrationTimeVO.setCarColor(offerFilter.getTractorPlateColor());
        carRegistrationTimeVO.setWaybillCreateTime(waybillCreateTime);
        waybillRiskRuleParam.setCarRegistrationTimeVO(carRegistrationTimeVO);
        // R10026 挂车入驻平台时间是否合理
        if (StrUtil.isNotBlank(offerFilter.getTrailerPlateNumber())) {
            scene.append(RmsEnum.RiskItemEnum.TMS_WAYBILL_TRAILER_CAR_REGISTRATION_TIME.getCode()).append("|");
            TrailerCarRegistrationTimeVO trailerCarRegistrationTimeVO = new TrailerCarRegistrationTimeVO();
            trailerCarRegistrationTimeVO.setFreightType(offerFilter.getFreightType());
            trailerCarRegistrationTimeVO.setDriverId(offerFilter.getDriverId());
            trailerCarRegistrationTimeVO.setDriverName(offerFilter.getDriverName());
            trailerCarRegistrationTimeVO.setWaybillCreateTime(waybillCreateTime);
            trailerCarRegistrationTimeVO.setPhoneNumber(offerFilter.getContactPhoneNumber());
            trailerCarRegistrationTimeVO.setTrailerCarPlateNo(offerFilter.getTrailerPlateNumber());
            trailerCarRegistrationTimeVO.setTrailerCarColor(offerFilter.getTrailerPlateColor());
            waybillRiskRuleParam.setTrailerCarRegistrationTimeVO(trailerCarRegistrationTimeVO);
        }

        // R10027 车辆行驶证注册日期是否合理
        CarDrivingLicenseRegisterTimeVO carDrivingLicenseRegisterTimeVO = new CarDrivingLicenseRegisterTimeVO();
        carDrivingLicenseRegisterTimeVO.setFreightType(offerFilter.getFreightType());
        carDrivingLicenseRegisterTimeVO.setDriverId(offerFilter.getDriverId());
        carDrivingLicenseRegisterTimeVO.setDriverName(offerFilter.getDriverName());
        carDrivingLicenseRegisterTimeVO.setPhoneNumber(offerFilter.getContactPhoneNumber());
        carDrivingLicenseRegisterTimeVO.setCarPlateNo(offerFilter.getTractorPlateNumber());
        carDrivingLicenseRegisterTimeVO.setCarColor(offerFilter.getTractorPlateColor());
        carDrivingLicenseRegisterTimeVO.setWaybillCreateTime(waybillCreateTime);
        waybillRiskRuleParam.setCarDrivingLicenseRegisterTimeVO(carDrivingLicenseRegisterTimeVO);

        // R10028 挂车行驶证注册日期是否合理
        if (StrUtil.isNotBlank(offerFilter.getTrailerPlateNumber())) {
            scene.append(RmsEnum.RiskItemEnum.TMS_WAYBILL_TRAILER_DRIVING_LICENSE_REGISTER_TIME.getCode()).append("|");
            CarDrivingLicenseRegisterTimeVO trailerDrivingLicenseRegisterTimeVO = new CarDrivingLicenseRegisterTimeVO();
            trailerDrivingLicenseRegisterTimeVO.setFreightType(offerFilter.getFreightType());
            trailerDrivingLicenseRegisterTimeVO.setDriverId(offerFilter.getDriverId());
            trailerDrivingLicenseRegisterTimeVO.setDriverName(offerFilter.getDriverName());
            trailerDrivingLicenseRegisterTimeVO.setPhoneNumber(offerFilter.getContactPhoneNumber());
            trailerDrivingLicenseRegisterTimeVO.setCarPlateNo(offerFilter.getTrailerPlateNumber());
            trailerDrivingLicenseRegisterTimeVO.setCarColor(offerFilter.getTrailerPlateColor());
            trailerDrivingLicenseRegisterTimeVO.setWaybillCreateTime(waybillCreateTime);
            waybillRiskRuleParam.setTrailerDrivingLicenseRegisterTimeVO(trailerDrivingLicenseRegisterTimeVO);
        }

        // R10058 车辆经营范围是否合理
        scene.append(RmsEnum.RiskItemEnum.TMS_WAYBILL_LICENSE_SCOPE.getCode()).append("|");
        WaybillLicenseScopeVo waybillLicenseScopeVo = new WaybillLicenseScopeVo();
        waybillLicenseScopeVo.setFreightType(offerFilter.getFreightType());
        waybillLicenseScopeVo.setPlateNumber(offerFilter.getTrailerPlateNumber());
        waybillLicenseScopeVo.setPlateColor(offerFilter.getTrailerPlateColor());
        waybillRiskRuleParam.setWaybillLicenseScopeVo(waybillLicenseScopeVo);
        waybillRiskRuleParam.setRiskSceneType(scene.toString());
        log.info("黑名单风控入参组装................:{}",JSON.toJSONString(waybillRiskRuleParam));


        return waybillRiskRuleParam;
    }


    /**
     * 根据颜色编号查询颜色名称
     * @param carColor
     * @return {@link String}
     */
    private String getCarColorNameByColorCode(String carColor){
        String parentDicId = "267";
        ResultMode<PlatformCmDictionary> dictionaryResultMode = platformCmDictionaryInter.getDictByParentIdAndEnumCode(parentDicId,carColor);
        if(dictionaryResultMode!=null && !IterUtil.isEmpty(dictionaryResultMode.getModel())){
            PlatformCmDictionary dictionary = IterUtil.getFirst(dictionaryResultMode.getModel());
            if(dictionary!=null){
                return dictionary.getName();
            }
        }
        return null;
    }


}
