package com.wanlianyida.lsds.application.service;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.command.ReceiptPayCheckCommand;
import com.isoftstone.hig.lsds.api.enums.DriverGoodsEnquiryTypeEnum;
import com.isoftstone.hig.lsds.api.mvcvo.PrepayReqVO;
import com.isoftstone.hig.lsds.api.mvcvo.PrepayResVO;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.platform.api.entity.PlatformCmPlatformParameter;
import com.isoftstone.hig.platform.api.inter.PlatformCmPlatformParameterInter;
import com.isoftstone.hig.tms.api.enums.PricingMethodEnum;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * <功能描述/>
 * 预付费
 *
 * <AUTHOR> href="mailto:<EMAIL>">ronglijun</A>
 * @version 1.0
 * @since 2022/5/7 16:16
 */

@Transactional
@Service
@Slf4j
public class PrepayAppService {


    @Resource
    private com.isoftstone.hig.platform.api.inter.PlatformCmPlatformParameterInter PlatformCmPlatformParameterInter;

    @Resource
    private  PlatformCmPlatformParameterInter platformCmPlatformParameterInter;

    @Resource
    private PlatformExchangeService platformExchangeService;

    /***
      * 判断设置的预付费是否超限额接口
     * 30吨/默认 指定单价
     * 1车  默认
     * 1柜 默认
      * @param
      * @Description
      * @return {@link }
      * <AUTHOR>
      * @date 2022/5/7 16:24
      ***/
    public ResultMode<PrepayResVO> prepayCheck(PrepayReqVO prepayReqVO){
        List<String> switchFlag= new ArrayList();
        switchFlag.add("1052");
        ResultMode<PlatformCmPlatformParameter> switchFlagResult = platformCmPlatformParameterInter.getByParaCodes(switchFlag);
        List<PlatformCmPlatformParameter> switchFlagResultRarameter = switchFlagResult.getModel();
        if (!CollectionUtils.isEmpty(switchFlagResultRarameter)){
            if("2".equalsIgnoreCase(switchFlagResultRarameter.get(0).getParaValue())){
                return ResultMode.success(PrepayResVO.of().setOverLimit(false));
            }
        }
        //预付费限额比例
        List<String> paraCodes= new ArrayList();
        paraCodes.add("1046");
        ResultMode<PlatformCmPlatformParameter> resultMode = PlatformCmPlatformParameterInter.getByParaCodes(paraCodes);
        List<PlatformCmPlatformParameter> results = resultMode.getModel();

        if(CollectionUtils.isEmpty(results)){
            return ResultMode.fail("参数设置中没有设置预付费限额比例！");
        }

        List<String> paraCodesDefaultLoad= new ArrayList();
        paraCodesDefaultLoad.add("1050");
        ResultMode<PlatformCmPlatformParameter> resultModeDefaultLoad = PlatformCmPlatformParameterInter.getByParaCodes(paraCodesDefaultLoad);
        List<PlatformCmPlatformParameter> platformCmPlatformParameters = resultModeDefaultLoad.getModel();
        if (CollectionUtils.isEmpty(platformCmPlatformParameters)){
            return ResultMode.fail("默认车载重为空,请先在平台设置默认车载重！！");
        }
        String defaultLoadCount =  platformCmPlatformParameters.get(0).getParaValue();

        String prepayRate = results.get(0).getParaValue();
        log.info("预付费限额比例:{}",prepayRate);
        BigDecimal prepayRatePercent = new BigDecimal(prepayRate).divide(new BigDecimal("100"), 7, BigDecimal.ROUND_DOWN);
        log.info("预付费限额比例百分比:{}",prepayRatePercent);
        String chargeType = prepayReqVO.getChargeType();//结算类型
        String enquiryType = prepayReqVO.getEnquiryType();//询价方式
        String batchAddFlag = prepayReqVO.getBatchAddFlag();//派车
        log.info("结算类型type:{},询价方式：{}",chargeType,enquiryType);

        String quantityValueMethod = platformExchangeService.getQuantityValueMethod(chargeType);
        if(StrUtil.isBlank(quantityValueMethod)){
            return ResultMode.fail("没有找到对应的计价类型！");
        }

        //派车
        if("true".equalsIgnoreCase(batchAddFlag)){
            //柜或车
            if (StrUtil.equals(quantityValueMethod, PricingMethodEnum.TWENTY.getCode())){

                BigDecimal basePrice = prepayReqVO.getEnquiryTypeBasePrice();
                BigDecimal preMoney = basePrice.multiply(prepayRatePercent);
                log.info("预付费派车(柜车)检验公式：{}",basePrice+"*"+prepayRatePercent);
                log.info("预付费限额标准preMoney:{}", preMoney);
                log.info("preMoney:{}", preMoney.setScale(0, BigDecimal.ROUND_DOWN));
                return comparePrePay(prepayReqVO, preMoney.setScale(0, BigDecimal.ROUND_DOWN));
            }


            // 如果存在预付费同时来源是接单
            if (StrUtil.equalsAny(quantityValueMethod, PricingMethodEnum.TEN.getCode(), PricingMethodEnum.THIRTY.getCode())) {
                log.info("...... source: {}", prepayReqVO.getSource());
                log.info("...... prepayMoney: {}", prepayReqVO.getPrePayMoney());
                log.info("...... defaultLoadWeight: {}", prepayReqVO.getDefaultLoadWeight());
                if ( StringUtils.hasText(prepayReqVO.getPrePayMoney()) && prepayReqVO.getDefaultLoadWeight() !=null && "JD".equals(prepayReqVO.getSource())){

                    int yufufeiValue =  new BigDecimal(prepayReqVO.getPrePayMoney()).intValue();//预付费
                    int yfValue = prepayReqVO.getEnquiryTypeBasePrice().multiply(prepayReqVO.getDefaultLoadWeight()).intValue();//运费
                    log.info("...... yufufeiValue: {}", yufufeiValue);
                    log.info("...... yfValue: {}", yfValue);
                    if (yfValue >= yufufeiValue){
                        log.info("@@@@@@@@ yfValue >= yufufeiValue ----->{}", yfValue >= yufufeiValue);
                        PrepayResVO prepayResVO = PrepayResVO.of().setPrepayMax(new BigDecimal(yufufeiValue)).setOverLimit(false).setEnquiryType(prepayReqVO.getEnquiryType());
                        return ResultMode.success(prepayResVO);
                    }
                    log.info("########## yfValue >= yufufeiValue ----->{}", yfValue >= yufufeiValue);
                    PrepayResVO prepayResVO = PrepayResVO.of().setPrepayMax(new BigDecimal(yufufeiValue)).setOverLimit(true).setEnquiryType(prepayReqVO.getEnquiryType());
                    log.info("prepayResVO：{}", JSON.toJSONString(prepayResVO));
                    return ResultMode.success(prepayResVO);
                }
                /**
                * X=基价 *默认车的载重*预付费上限比例，展示格式为千分位，小数四舍五入，小数位为0，例如基价 *默认车的载重*预付费上限比例=1234.56 则X=1,234。
                */
                BigDecimal basePrice = prepayReqVO.getEnquiryTypeBasePrice();
                BigDecimal preMoneyMax = basePrice.multiply(prepayReqVO.getDefaultLoadWeight()).multiply(prepayRatePercent);
                log.info("预付费派车(重量)检验公式"+basePrice+"*"+prepayReqVO.getDefaultLoadWeight()+"*"+prepayRatePercent);
                log.info("预付费限额标准preMoney:{}",preMoneyMax);
                log.info("preMoney:{}", preMoneyMax.setScale(0,BigDecimal.ROUND_DOWN ));

                return comparePrePay(prepayReqVO, preMoneyMax.setScale(0,BigDecimal.ROUND_DOWN));
            }
        }
        //10-公开询价,20-指定单价
        if(StringUtils.hasText(quantityValueMethod)){
            //指定单价
            if(org.apache.commons.lang3.StringUtils.equals(enquiryType,"20")&&!"true".equalsIgnoreCase(batchAddFlag)){
                if (ObjUtil.isNull(prepayReqVO.getEnquiryTypeBasePrice())) {
                    return ResultMode.fail("不含税价单价不允许为空");
                }
                //10-按柜,20-按重量,30-按车

                //柜或车
                if (StrUtil.equals(quantityValueMethod, PricingMethodEnum.TWENTY.getCode())){
                    /**
                     * 预付费大于基价 *预付费上限比例时，提示“您输入的预付费超过最高限额X元，请重新输入”，不能提价或保存草稿。X=基价 *预付费上限比例，展示格式为千分位，小数四舍五入，小数位为0，例如基价 *预付费上限比例=1234.56 则X=1,234。
                     * **/
                    // String defaultLoadCount = environment.getProperty("prepayment.defaultLoadNum");
                    BigDecimal basePrice = prepayReqVO.getEnquiryTypeBasePrice();
                    BigDecimal preMoney = basePrice.multiply(prepayRatePercent);
                    if("true".equals(prepayReqVO.getSupplement())) { // 订单补录逻辑补充
                        BigDecimal defaultLoadValue = new BigDecimal(defaultLoadCount);
                        preMoney = preMoney.multiply(defaultLoadValue);
                    }
                    log.info("指定单价(柜车)检验公式"+basePrice+"*"+prepayRatePercent);
                    log.info("预付费限额标准preMoney:{}",preMoney);
                    log.info("preMoney:{}",  preMoney.setScale(0, BigDecimal.ROUND_DOWN));
                    return comparePrePay(prepayReqVO,preMoney.setScale(0,BigDecimal.ROUND_DOWN));
                }

                //重量
                /**
                 * 预付费大于基价 *默认车的载重*预付费上限比例时，提示“您输入的预付费超过最高限额X元，请重新输入”
                 * */
                if (StrUtil.equalsAny(quantityValueMethod, PricingMethodEnum.TEN.getCode(), PricingMethodEnum.THIRTY.getCode())) {
                    prepayReqVO.setDefaultLoadWeight(new BigDecimal(defaultLoadCount));//数量默认为三十吨
                    /**
                     * X=基价 *默认车的载重*预付费上限比例，展示格式为千分位，小数四舍五入，小数位为0，例如基价 *默认车的载重*预付费上限比例=1234.56 则X=1,234。
                     * */
                    BigDecimal basePrice = prepayReqVO.getEnquiryTypeBasePrice();
                    BigDecimal preMoneyMax = basePrice.multiply(new BigDecimal(defaultLoadCount)).multiply(prepayRatePercent);
                    log.info("指定单价(重量)检验公式"+basePrice+"*"+defaultLoadCount+"*"+prepayRatePercent);
                    log.info("预付费限额标准preMoney:{}",preMoneyMax);
                    log.info("preMoney:{}", preMoneyMax.setScale(0, BigDecimal.ROUND_DOWN));
                    return comparePrePay(prepayReqVO, preMoneyMax.setScale(0,BigDecimal.ROUND_DOWN));
                }

            }
            //公开询价
            if(org.apache.commons.lang3.StringUtils.equals(enquiryType,"10")&&!"true".equalsIgnoreCase(batchAddFlag)){
                //查询报价表的数据
                String placeOrderOptFlag = prepayReqVO.getPlaceOrderOptFlag();

                //下单操作
                if (org.apache.commons.lang3.StringUtils.equals("10",placeOrderOptFlag)){
                    //重量
                    /**
                     * 预付费大于基价 *默认车的载重*预付费上限比例时，提示“您输入的预付费超过最高限额X元，请重新输入”
                     * */
                    if (StrUtil.equalsAny(quantityValueMethod, PricingMethodEnum.TEN.getCode(), PricingMethodEnum.THIRTY.getCode())) {
                        BigDecimal defaultLoadWeight = prepayReqVO.getReceivingOrdersQuantity();
                        /**
                         * X=基价 *默认车的载重*预付费上限比例，展示格式为千分位，小数四舍五入，小数位为0，例如基价 *默认车的载重*预付费上限比例=1234.56 则X=1,234。
                         * */

                        BigDecimal basePrice = prepayReqVO.getReceivingOrdersUnitPrice();
                        BigDecimal preMoneyMax = basePrice.multiply(defaultLoadWeight).multiply(prepayRatePercent);
                        log.info("公开询价(下单重量)检验公式"+basePrice+"*"+defaultLoadWeight+"*"+prepayRatePercent);
                        log.info("预付费限额标准preMoney:{}",preMoneyMax);
                        log.info("preMoney:{}",preMoneyMax.setScale(0,BigDecimal.ROUND_DOWN));
                        return comparePrePay(prepayReqVO, preMoneyMax.setScale(0, BigDecimal.ROUND_DOWN));
                    }

                    //柜或车
                    if (StrUtil.equals(quantityValueMethod, PricingMethodEnum.TWENTY.getCode())) {
                        /**
                         * 预付费大于基价 *预付费上限比例时，提示“您输入的预付费超过最高限额X元，请重新输入”，不能提价或保存草稿。X=基价 *预付费上限比例，展示格式为千分位，小数四舍五入，小数位为0，例如基价 *预付费上限比例=1234.56 则X=1,234。
                         * **/
                        BigDecimal defaultLoadNum = prepayReqVO.getReceivingOrdersQuantity();
                        BigDecimal basePrice = prepayReqVO.getReceivingOrdersUnitPrice();
                        BigDecimal preMoney = basePrice.multiply(defaultLoadNum).multiply(prepayRatePercent);
                        log.info("公开询价(下单柜车)检验公式"+basePrice+"*"+defaultLoadNum+"*"+prepayRatePercent);
                        log.info("预付费限额标准preMoney:{}",preMoney);
                        log.info("preMoney:{}", preMoney.setScale(0, BigDecimal.ROUND_DOWN));
                        return comparePrePay(prepayReqVO, preMoney.setScale(0, BigDecimal.ROUND_DOWN));

                    }

                }
                //非下单操作
                if (org.apache.commons.lang3.StringUtils.equals("20",placeOrderOptFlag)){
                    log.info("公开询价(不是下单)不校验");
                    //返回未超限额
                    return  ResultMode.success( PrepayResVO.of().setOverLimit(false).setEnquiryType("10"));
                }

            }

        }

        return  ResultMode.success( PrepayResVO.of().setOverLimit(false).setEnquiryType(prepayReqVO.getEnquiryType()));
    }


    private ResultMode<PrepayResVO> comparePrePay(PrepayReqVO prepayReqVO, BigDecimal preMoneyMax) {
        if (prepayReqVO != null && preMoneyMax !=null){
            BigDecimal compare = preMoneyMax.subtract(new BigDecimal(prepayReqVO.getPrePayMoney()));
            log.info("预付费标准-设置的预付费：{}", compare.floatValue());

            if (compare.floatValue() < 0) {//超额

                return ResultMode.success(PrepayResVO.of().setPrepayMax(preMoneyMax.setScale(0, BigDecimal.ROUND_DOWN)).setOverLimit(true).setEnquiryType(prepayReqVO.getEnquiryType()));
            }
            PrepayResVO prepayResVO = PrepayResVO.of().setPrepayMax(preMoneyMax.setScale(0, BigDecimal.ROUND_DOWN)).setOverLimit(false).setEnquiryType(prepayReqVO.getEnquiryType());
            return ResultMode.success(prepayResVO);
        }
        return ResultMode.fail();
    }

    /**
     * 回单付金额上限校验
     */
    public ResultMode<PrepayResVO> receiptPayCheck(ReceiptPayCheckCommand command) {
        PrepayResVO prepayResVO = PrepayResVO.of().setPrepayMax(BigDecimal.ZERO).setOverLimit(false).setEnquiryType(command.getEnquiryType());
        //公开询价
        if (StrUtil.equals(command.getEnquiryType(), DriverGoodsEnquiryTypeEnum.TYPE10.getType())) {
            return ResultMode.success(prepayResVO);
        }

        if (ObjUtil.isNull(command.getEnquiryTypeBasePrice())) {
            return ResultMode.fail("不含税价单价不允许为空");
        }
        //回单付限额比例
        String receiptPayRateStr = platformExchangeService.getParamValue(Constants.RECEIPT_PAY_PARAM_CODE);
        if (StrUtil.isBlank(receiptPayRateStr)) {
            return ResultMode.fail("参数设置中没有设置预付费限额比例！");
        }
        BigDecimal receiptPayRate = new BigDecimal(receiptPayRateStr).divide(new BigDecimal("100"), 7, BigDecimal.ROUND_DOWN);
        if (BigDecimal.ZERO.compareTo(receiptPayRate) == 0) {
            return ResultMode.success(prepayResVO);
        }

        //计价单位数量取值方式
        String quantityValueMethod = platformExchangeService.getQuantityValueMethod(command.getQuantityUnits());
        if(StrUtil.isBlank(quantityValueMethod)){
            return ResultMode.fail("没有找到对应的计价类型！");
        }

        BigDecimal defaultLoadValue;
        if (StrUtil.equals(quantityValueMethod, PricingMethodEnum.TWENTY.getCode())){
            //货源不含税单价 * 平台参数【回单付限额比例】
            defaultLoadValue = BigDecimal.ONE;
        } else {
            //货源不含税单价 * 平台参数【预付费默认车载重】 *平台参数【回单付限额比例】
            //预付费默认车载重
            String defaultLoadCountStr = platformExchangeService.getParamValue(Constants.DEFAULT_LOAD_PARAM_CODE);
            if (StrUtil.isBlank(defaultLoadCountStr)) {
                return ResultMode.fail("预付费默认车载重为空，请先在平台设置默认车载重！");
            }
            defaultLoadValue = new BigDecimal(defaultLoadCountStr);
        }
        //回单付上限
        BigDecimal receiptAmountMax = command.getEnquiryTypeBasePrice().multiply(receiptPayRate).multiply(defaultLoadValue);
        prepayResVO.setPrepayMax(receiptAmountMax.setScale(0, BigDecimal.ROUND_DOWN));
        if (receiptAmountMax.compareTo(command.getReceiptAmount()) < 0) {
            prepayResVO.setOverLimit(true);
            return ResultMode.success(prepayResVO);
        }

        return ResultMode.success(prepayResVO);
    }

}
