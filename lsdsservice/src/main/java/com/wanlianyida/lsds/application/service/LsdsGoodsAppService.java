package com.wanlianyida.lsds.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.*;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.isoftstone.hig.common.constants.PrefixCodeConstants;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.*;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.common.utils.inter.DistributedLocker;
import com.isoftstone.hig.contract.api.constants.ContractEnum;
import com.isoftstone.hig.contract.api.filter.ContractCreateNoticeFilter;
import com.isoftstone.hig.lsds.api.dto.GoodsStatisticsDTO;
import com.isoftstone.hig.lsds.api.dto.StatisticsDTO;
import com.isoftstone.hig.lsds.api.entity.*;
import com.isoftstone.hig.lsds.api.enums.GoodsSourceTypeEnum;
import com.isoftstone.hig.lsds.api.enums.LsdsAttachmentTypeEnum;
import com.isoftstone.hig.lsds.api.enums.OfferStatusEnum;
import com.isoftstone.hig.lsds.api.filter.BidsFilesFilter;
import com.isoftstone.hig.lsds.api.filter.CreateCallBidFileFilter;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsFilter;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsRecordFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsVo;
import com.isoftstone.hig.lsds.api.mvcvo.WebsiteGoods;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.msg.api.entity.MsgInfo;
import com.isoftstone.hig.msg.api.inter.SystemMsgInter;
import com.isoftstone.hig.oms.api.entity.OmsOrder;
import com.isoftstone.hig.oms.api.entity.OmsOrderAddress;
import com.isoftstone.hig.oms.api.inter.OmsOrderInter;
import com.isoftstone.hig.oms.api.mvcvo.OmsOrderVo;
import com.isoftstone.hig.platform.api.client.PlatformCommonInterClient;
import com.isoftstone.hig.platform.api.entity.*;
import com.isoftstone.hig.platform.api.enums.PlatformEnum;
import com.isoftstone.hig.platform.api.filter.PlatformCmSensitiveWordFilter;
import com.isoftstone.hig.platform.api.filter.PlatformQrCodeFilter;
import com.isoftstone.hig.platform.api.filter.PlatformUmCompanyFilter;
import com.isoftstone.hig.platform.api.filter.PlatformUmUserbaseinfoFilter;
import com.isoftstone.hig.platform.api.inter.PlatformCmOperationMainBodyInter;
import com.isoftstone.hig.platform.api.inter.PlatformCmSensitiveWordInter;
import com.isoftstone.hig.platform.api.inter.PlatformUmCompanyInter;
import com.wanlianyida.lsds.domain.repository.GoodsOperatorRepository;
import com.wanlianyida.lsds.domain.service.LsdsGoodsDomainService;
import com.wanlianyida.lsds.domain.service.LsdsGoodsRuleDomainService;
import com.wanlianyida.lsds.infrastructure.config.FormsAuthTrader;
import com.wanlianyida.lsds.infrastructure.enums.DealStatusEnum;
import com.wanlianyida.lsds.infrastructure.enums.FreightTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.LineSourceEnum;
import com.wanlianyida.lsds.infrastructure.enums.StatusCodeEnum;
import com.wanlianyida.lsds.infrastructure.exception.LsdsStatusCodeEnum;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.exchange.*;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.CrmCompanyLineAddressBO;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.WoaLogisticsPlanBO;
import com.wanlianyida.lsds.infrastructure.repository.mapper.*;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

/**
 * 货源事务处理类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LsdsGoodsAppService {
    @Resource
    private LsdsGoodsDomainService lsdsGoodsDomainService;

    @Autowired
    private LsdsKafkaSender lsdsKafkaSender;

    @Autowired
    private OmsOrderInter omsOrderInter;

    @Autowired
    private SystemMsgInter systemMsgInter;

    @Autowired
    private PlatformCommonInterClient platformCommonInterClient;

    @Autowired
    @Lazy
    private PlatformCmOperationMainBodyInter platformCmOperationMainBodyInter;

    @Resource
    private DistributedLocker distributedLocker;

    @Resource
    private Executor asyncServiceExecutor;

    @Resource
    private DriverGoodsMapper driverGoodsMapper;

    @Resource
    private PlatformUmCompanyInter platformUmCompanyInter;

    @Resource
    private PlatformCmSensitiveWordInter platformCmSensitiveWordInter;
    @Resource
    private GoodsMapper goodsMapper;
    @Resource
    private LsdsGoodsAttentionAppService goodsAttentionServiceTran;

    @Resource
    private GoodsDeductibleAppService goodsDeductibleService;

    @Resource
    private DcsAppService dcsBusiness;

    @Resource
    private AmountRoundingModeExchangeService amountRoundingModeService;

    @Resource
    private DriverGoodsAppService driverGoodsService;

    @Resource
    private CrmExchangeService lineAddressExchangeService;

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private WoaExchangeService woaExchangeService;

    @Resource
    private LsdsGoodsOfferAppService lsdsGoodsOfferService;

    @Resource
    private LsdsGoodsOfferAppService lsdsGoodsOfferTran;

    @Resource
    private LsdsGoodsBidFileAppService lsdsGoodsBidFileService;

    @Resource
    private LsdsSequenceMapper lsdsSequenceMapper;

    @Resource
    private LsdsGoodsRuleDomainService lsdsGoodsRuleDomainService;

    /**
     * 货源过期修改锁
     */
    private static final String GOOD_EXPIRED_LOCK = "lsds:goods:expired";

    /**
     * 货源更新锁
     */
    private static final String GOOD_UPDATE_LOCK = "lsds:goods:update";

    /**
     * 货源ID生成锁
     */
    private static final String GOODS_ADD_LOCK = "lsds:goods:add";

    /**
     * 货源ID生成锁
     */
    private static final String GOODS_DOWN_ADD_LOCK = "lsds:goods:down:add";

    /**
     * 货源ID生成锁
     */
    private static final String GOODS_ID_ADD_LOCK = "lsds:goods:id:add";

    /**
     * 货源ID生成锁
     */
    private static final String GOOD_NET_ROUND_LOCK = "lsds:goods:net:round";


    /**
     * 根据货源编号  货源信息表主键精确查询货源编号  货源信息表
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param goodsId 为0时忽略
     * @param token   预留要传递的token，服务端进行判断权限功能
     * @return LsdsGoods
     */
    public ResultMode<LsdsGoods> lsdsGoodsGet1(String goodsId, String token) {
        String errMsg = "";
        ResultMode<LsdsGoods> resultmodel = new ResultMode<LsdsGoods>();
        try {
            if (StringUtils.hasText(goodsId)){
                GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
                LsdsGoods lsdsGoods = dao.getModel(goodsId);
                //金额取整
                amountRoundingModeService.setLsdsGoodsScale(lsdsGoods);
                if (lsdsGoods != null){
                    lsdsGoods.setAdvancePayment(new BigDecimal(0));
                    lsdsGoods.setAdvancePaymentFlag("1");

                    //根据goodsId
                    OmsOrderVo omsOrder = new OmsOrderVo();
                    omsOrder.setGoodsId(goodsId);
                    ResultMode<OmsOrder> omsOrderRes = omsOrderInter.omsOrderGet(omsOrder);
                    if (!CollectionUtils.isEmpty(omsOrderRes.getModel()) && lsdsGoods != null){
                        lsdsGoods.setAdvancePaymentFlag(omsOrderRes.getModel().get(0).getAdvancePaymentFlag());
                        lsdsGoods.setAdvancePayment(omsOrderRes.getModel().get(0).getAdvancePayment());
                        resultmodel.setTotal(1);
                        resultmodel.getModel().add(lsdsGoods);
                    }

                }

            }
        } catch (Exception ex) {
            errMsg = "获取货源编号  货源信息表信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setSucceed(false);
        }
        log.info("lsdsGoodsGet返回结果集:{}",JSON.toJSONString(resultmodel));
        return resultmodel;
    }

    /**
     * 根据货源编号  货源信息表主键精确查询货源编号  货源信息表
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param goodsId 为0时忽略
     * @param token   预留要传递的token，服务端进行判断权限功能
     * @return LsdsGoods
     */
    public LsdsGoods lsdsGoodsGet(String goodsId, String token) {
        String errMsg = "";
        LsdsGoods model = null;
        try {
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            model = dao.getModel(goodsId);

            //金额取整
            SpringContextUtil.getBeanByClass(AmountRoundingModeExchangeService.class).setLsdsGoodsScale(model);

        } catch (Exception ex) {
            errMsg = "获取货源编号  货源信息表信息异常";
            LogHelper.writeError(errMsg, ex);
            model = null;
        }
        return model;
    }

    /**
     * 获取平台公司id，sql封装
     * @return  String
     */
    public String  getPlatformCompanyId() {
        List<PlatformCmOperationMainBody> listBody= platformCmOperationMainBodyInter.getAllTransaction().getModel();
        String platformCompanyId="";
        for (int i=0;i<listBody.size();i++){
            platformCompanyId=platformCompanyId+listBody.get(i).getCompanyId()+",";
        }
        platformCompanyId=platformCompanyId.substring(0,platformCompanyId.length()-1);
        platformCompanyId= "(lgo.company_id = '2' or find_in_set(lgo.company_id,'"+platformCompanyId+"'))";
        return platformCompanyId;
    }

    /**
     * 获取平台公司id值封装
     * @return  String
     */
    public String  getPlatformCompanyIdStr() {
        ResultMode<PlatformCmOperationMainBody> model=platformCmOperationMainBodyInter.getAllTransaction();
        List<PlatformCmOperationMainBody> listBody= model.getModel();
        String platformCompanyId="";
        for (int i=0;i<listBody.size();i++){
            platformCompanyId=platformCompanyId+listBody.get(i).getCompanyId()+",";
        }
        platformCompanyId=platformCompanyId.substring(0,platformCompanyId.length()-1);
        return platformCompanyId;
    }


    /**
     * 根据货源编号查询货源详细信息
     * 创建者: cgb
     * 创建时间: 2019/11/20
     *
     * @param goodsId 货源编号
     * @param token   预留要传递的token，服务端进行判断权限功能
     * @return {@code  ResultMode<LsdsGoodsVo>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoodsVo列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsGoodsVo> lsdsGoodsGetDetails(String goodsId, String token) {
        String errMsg = "";
        ResultMode<LsdsGoodsVo> resultmodel = new ResultMode<LsdsGoodsVo>();
        StringBuffer sb = new StringBuffer();
        LsdsGoodsVo vo =  new LsdsGoodsVo();
        LsdsGoodsOffer lgf = new LsdsGoodsOffer();
        String  platformCompanyIdStr=getPlatformCompanyIdStr();
        try {
            //获取货源信息
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoods lsdsGoods = dao.getModel(goodsId);
            //交易签约主体名称
            if(StrUtil.isNotBlank(lsdsGoods.getTransactionContractingBodyId())){
                ResultMode<PlatformCmOperationMainBody> bodyMode = platformCmOperationMainBodyInter.getMainBodyInfo(lsdsGoods.getTransactionContractingBodyId());
                if (bodyMode.getSucceed() && !CollectionUtils.isEmpty(bodyMode.getModel())) {
                    PlatformCmOperationMainBody mainBody = bodyMode.getModel().get(0);
                    lsdsGoods.setTransactionContractingBodyname(mainBody.getBodyName());
                }
            }
            //金额取整
            amountRoundingModeService.setLsdsGoodsScale(lsdsGoods);
            if (null != lsdsGoods) {
                String customerShortName = driverGoodsService.initCustomerShortName(lsdsGoods.getCustomerId());
                lsdsGoods.setCustomerShortName(customerShortName);
                PlatformUmCompany companyByModel = platformExchangeService.getCompanyByModel(lsdsGoods.getCompanyId());
                lsdsGoods.setCompanyName(companyByModel.getCompanyName());
                int roundingMode = amountRoundingModeService.getRoundingMode(lsdsGoods.getGoodsId());

                vo.setLsdsGoods(lsdsGoods);
                log.info("货源信息VO：{}",JSONUtil.toJsonStr(vo));
                //返回分段信息
                GoodsSplitMapper splitDao = SpringContextUtil.getBeanByClass(GoodsSplitMapper.class);
                List<LsdsGoodsSplit> splitList = new ArrayList<>();
//                if (null != lsdsGoods && !StringUtils.isEmpty(lsdsGoods.getChildGoodsId())) {
//                    //存在子货源号，则该货源进行了下游询价，分段信息则取自子货源的分段信息
//                    splitList = splitDao.getSplitList(lsdsGoods.getChildGoodsId());
//                    lgf.setGoodsId(lsdsGoods.getChildGoodsId());
//                } else {
//                    //不存在子货源号，则为非下游询价货源
//                    splitList = splitDao.getSplitList(goodsId);
//                    lgf.setGoodsId(goodsId);
//                }

                splitList = splitDao.getSplitList(goodsId);
                //填充数量和数量单位
                if(ObjectUtil.isNotEmpty(splitList)){
                    splitList.forEach(sitem->{
                        sitem.setTotalQuantity(lsdsGoods.getTotalQuantity());
                        sitem.setTotalQuantityUnits(lsdsGoods.getTotalQuantityUnits());
                    });
                }
                lgf.setGoodsId(goodsId);

                LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
                Map<String, List<Map<String, Object>>> map = new HashMap<String, List<Map<String, Object>>>();
                if (null != splitList && splitList.size() > 0) {
                    //装载分段信息
                    vo.setGoodsSplitList(splitList);

                    String  platformCompanyId=getPlatformCompanyId();

                    //splitList.size() > 0 则为分段报价
                    if (null != lsdsGoods.getOfferCurrentRounds() && lsdsGoods.getOfferCurrentRounds() > 0) {
                        if (splitList.size() == 1) {
                            //sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND lgo.company_id = '2'" +
                            sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND " +   platformCompanyId+
                                    "THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                   /*"  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                    "  WHEN lgo.sort_node = 1 THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  ELSE 0 END END) sumNodePrice");
                           // sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND lgo.company_id = '2' " +
                            sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND "  +   platformCompanyId+
                                    "THEN CONVERT(lgo.enquiry_type_base_open_ticket * ls.total_quantity, DECIMAL(18,5)) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * ls.total_quantity, DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100) * ls.total_quantity, DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value) * ls.total_quantity, DECIMAL(18,5))" +*/
                                    "  WHEN lgo.sort_node = 1 THEN CONVERT(lgo.enquiry_type_base_open_ticket * ls.total_quantity, DECIMAL(18,5))" +
                                    "  ELSE 0 END END) freightPrice");
                        } else {
                            //计算拆段后的总单价
                            for (int i = 1; i <= splitList.size(); i++) {
                                if (i == 1) {
                                   // sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND lgo.company_id = '2'" +
                                    sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND " +   platformCompanyId+
                                            " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                            /*"  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                            "  WHEN lgo.sort_node = 1 THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  ELSE 0 END END)");
                                } else if (i == splitList.size()) {
                                    //sb.append("+MAX(CASE WHEN lgo.sort_node = " + i + " AND lgo.company_id = '2' " +
                                    sb.append("+MAX(CASE WHEN lgo.sort_node = " + i + " AND " +   platformCompanyId+
                                            "THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                            /*"  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                            "  WHEN lgo.sort_node =  " + i + " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  ELSE 0 END END) sumNodePrice");
                                } else {
                                    //sb.append("+MAX(CASE WHEN lgo.sort_node = " + i + " AND lgo.company_id = '2' " +
                                    sb.append("+MAX(CASE WHEN lgo.sort_node = " + i + " AND " +   platformCompanyId+
                                            "THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                            /*"  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                            "  WHEN lgo.sort_node =  " + i + " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  ELSE 0 END END)");
                                }
                            }
                            //计算拆段后的总运费
                            for (int i = 1; i <= splitList.size(); i++) {
                                if (i == 1) {
                                    //sb.append(",CONVERT((MAX(CASE WHEN lgo.sort_node = 1 AND lgo.company_id = '2' " +
                                    sb.append(",CONVERT((MAX(CASE WHEN lgo.sort_node = 1 AND " +   platformCompanyId+
                                            "THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                            /*"  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                            "  WHEN lgo.sort_node = 1 THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  ELSE 0 END END)");
                                } else if (i == splitList.size()) {
                                   //sb.append("+MAX(CASE WHEN lgo.sort_node =  " + i + "  AND lgo.company_id = '2' " +
                                     sb.append("+MAX(CASE WHEN lgo.sort_node =  " + i + "  AND " +   platformCompanyId+
                                            "THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                            /*"  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                            "  WHEN lgo.sort_node =  " + i + " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  ELSE 0 END END)) * ls.total_quantity,DECIMAL(18,5)) freightPrice");
                                } else {
                                   // sb.append("+MAX(CASE WHEN lgo.sort_node =  " + i + "  AND lgo.company_id = '2'" +
                                    sb.append("+MAX(CASE WHEN lgo.sort_node =  " + i + "  AND " +   platformCompanyId+
                                            " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                            /*"  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '0' THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '1' THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                            "  WHEN lgo.sort_node =  " + i + " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  ELSE 0 END END)");
                                }
                            }
                        }
                        for (int i = 1; i <= splitList.size(); i++) {
                            if(roundingMode==1){
                                //向下取整
                                sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND " +   platformCompanyId+
                                    "THEN CONCAT(lgo.offer_id, '-', TRUNCATE(lgo.enquiry_type_base_open_ticket,2) ) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '0'  THEN CONCAT(lgo.offer_id, '-', TRUNCATE(lgo.enquiry_type_base_open_ticket,2) )" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '1'  THEN CONCAT(lgo.offer_id, '-', TRUNCATE(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),2) )" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '2' THEN CONCAT(lgo.offer_id, '-', TRUNCATE((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),2) )" +*/
                                    "  WHEN lgo.sort_node = " + i + " THEN CONCAT(lgo.offer_id, '-', TRUNCATE(lgo.enquiry_type_base_open_ticket,2) )" +
                                    "  ELSE null END END) as node" + i);
                            }else if(roundingMode==4){
                                //四舍五入
                                sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND " +   platformCompanyId+
                                    "THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,2))) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '0'  THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,2)))" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '1'  THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,2)))" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '2' THEN CONCAT(lgo.offer_id, '-', CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,2)))" +*/
                                    "  WHEN lgo.sort_node = " + i + " THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,2)))" +
                                    "  ELSE null END END) as node" + i);
                            }else{
                                //向上取整
                                sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND " +   platformCompanyId+
                                    "THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil(lgo.enquiry_type_base_open_ticket*100)/100,2)) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '0'  THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil(lgo.enquiry_type_base_open_ticket*100)/100,2) )" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '1'  THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100)*100)/100,2) )" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '2' THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value)*100)/100,2) )" +*/
                                    "  WHEN lgo.sort_node = " + i + " THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil(lgo.enquiry_type_base_open_ticket*100)/100,2) )" +
                                    "  ELSE null END END) as node" + i);
                            }
                            //sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND lgo.company_id = '2' " +
                        }

                        log.info(">>>>SQL:{}",sb.toString());
                        lgf.setSqlStr(sb.toString());
                        for (int i = 1; i <= lsdsGoods.getOfferCurrentRounds(); i++) {
                            lgf.setOfferRound(i);
                            List<Map<String, Object>> offerListMap = offerDao.getSplitOfferList(lgf);

                            //按金额取整方式设置基价
                            setBasePriceScaleOfSplitOffer(offerListMap);

                            if (null != offerListMap && offerListMap.size() > 0) {
                                map.put(String.valueOf(i), offerListMap);
                            }
                            //装载报价信息
                            vo.setGoodsOfferMap(map);
                        }
                    }
                } else {
                    //为整单报价
                    List<Map<String, Object>> currentOffer = null;
                    for (int i = 1; i <= lsdsGoods.getOfferCurrentRounds(); i++) {
                        lgf.setOfferRound(i);
                        lgf.setPlatformCompanyId(platformCompanyIdStr);
                        List<Map<String, Object>> offerList = offerDao.getNoSplitOfferList(lgf);
                        //按金额取整方式设置基价
                        setBasePriceScaleOfSplitOffer(offerList);

                        if (null != offerList && offerList.size() > 0) {
                            map.put(String.valueOf(i), offerList);
                        }

                        if("5".equals(lsdsGoods.getDealStatus()) && i == lsdsGoods.getOfferCurrentRounds()){
                            currentOffer = offerList.stream().filter(p -> 2 == ((Long) p.get("status")).intValue()).collect(Collectors.toList());
                        }
                        //装载报价信息
                        vo.setGoodsOfferMap(map);
                    }
                    if(!CollectionUtils.isEmpty(currentOffer)){
                        lsdsGoods.setUnitPrice(new BigDecimal((currentOffer.get(0).get("enquiryTypeBaseOpenTicket")).toString()));
                        lsdsGoods.setSumPrice(new BigDecimal((currentOffer.get(0).get("freightPrice")).toString()));
                        vo.setLsdsGoods(lsdsGoods);
                    }
                }

                //返回货源当前承运商信息
                GoodsAssignSupplierMapper supplierDao = SpringContextUtil.getBeanByClass(GoodsAssignSupplierMapper.class);
                LsdsGoodsAssignSupplier supplier = new LsdsGoodsAssignSupplier();
                supplier.setGoodsId(goodsId);
                supplier.setOfferRound(lsdsGoods.getOfferCurrentRounds());
                List<LsdsGoodsAssignSupplier> supplierList = supplierDao.getPreviousSupplierList(supplier);
                if (!CollectionUtils.isEmpty(supplierList)) {
                    //装载承运商信息
                    vo.setAssignSupplierList(supplierList);
                }

                //当货源询价范围为指定物流公司时，返回指定的所有物流公司
                if (org.apache.commons.lang3.StringUtils.equals("1",lsdsGoods.getEnquiryRange())){
                    LsdsGoodsAssignSupplier allSupplier = new LsdsGoodsAssignSupplier();
                    allSupplier.setGoodsId(goodsId);
                    allSupplier.setOfferRound(1);
                    List<LsdsGoodsAssignSupplier> allSupplierList = supplierDao.getPreviousSupplierList(allSupplier);
                    if (null != allSupplierList && allSupplierList.size() > 0) {
                        //装载承运商信息
                        vo.setAllAssignSupplierList(allSupplierList);
                    }
                }

                //封装货源操作记录
//                LsdsGoodsRecordRepository recDao = SpringContextUtil.getBeanByClass(LsdsGoodsRecordRepository.class);
//                LsdsGoodsRecord lgr = new LsdsGoodsRecord();
//                lgr.setGoodsId(goodsId);
//                List<LsdsGoodsRecord> recordList = recDao.getRecordList(lgr);
//                vo.setGoodsRecordList(recordList);
                //设置预付费
                vo.setAdvancePayment(new BigDecimal(0));
                vo.setAdvancePaymentFlag("1");

                //根据goodsId
                OmsOrderVo omsOrder = new OmsOrderVo();
                omsOrder.setGoodsId(vo.getLsdsGoods().getGoodsId());
                ResultMode<OmsOrder> omsOrderRes = omsOrderInter.omsOrderGet(omsOrder);
                if (!CollectionUtils.isEmpty(omsOrderRes.getModel())){
                     vo.setAdvancePaymentFlag(omsOrderRes.getModel().get(0).getAdvancePaymentFlag());
                     vo.setAdvancePayment(omsOrderRes.getModel().get(0).getAdvancePayment());
                }

                // 展示亏涨吨参数配置
                LsdsGoodsDeductible lsdsGoodsDeductible =
                    goodsDeductibleService.wrapGoodsDeductible(vo.getLsdsGoods().getOtherKuiTonsRatio(), vo.getLsdsGoods().getGoodsId());
                vo.getLsdsGoods().setLsdsGoodsDeductible(lsdsGoodsDeductible);

                //填充招标信息
                List<BidsFilesFilter> bidsFilesFilter = lsdsGoodsBidFileService.getBidsFilesFilter(vo.getLsdsGoods().getGoodsId(), LsdsAttachmentTypeEnum.CALL_BIDS.getType());
                if(CollUtil.isNotEmpty(bidsFilesFilter)){
                    vo.getLsdsGoods().setBidsFiles(bidsFilesFilter);
                }
                //填充经纬度
                CrmCompanyLineAddressBO startLine = lineAddressExchangeService.getCrmCompanyLine(vo.getLsdsGoods().getStartLineId());
                vo.getLsdsGoods().setStartLngLat(startLine.getItem1()+","+startLine.getItem2());
                CrmCompanyLineAddressBO endLine = lineAddressExchangeService.getCrmCompanyLine(vo.getLsdsGoods().getEndLineId());
                vo.getLsdsGoods().setEndLngLat(endLine.getItem1()+","+endLine.getItem2());
                resultmodel.getModel().add(vo);
                resultmodel.setTotal(1);
            }

        } catch (Exception ex) {
            errMsg = "获取货源编号  货源信息表信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setSucceed(false);
            resultmodel = null;
        }

        return resultmodel;
    }

    /**
     * 根据货源编号查询货源详细信息
     * 创建者: cgb
     * 创建时间: 2019/11/20
     * @param goods 货源实体
     * @return {@code  ResultMode<LsdsGoodsVo>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoodsVo列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsGoodsVo> lsdsGoodsGetDetailsForDown(LsdsGoods goods) {
        String errMsg = "";
        ResultMode<LsdsGoodsVo> resultmodel = new ResultMode<LsdsGoodsVo>();
        StringBuffer sb = new StringBuffer();
        LsdsGoodsVo vo =  new LsdsGoodsVo();
        LsdsGoodsOffer lgf = new LsdsGoodsOffer();
        String  platformCompanyIdStr=getPlatformCompanyIdStr();
        try {
            //获取货源信息
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoods lsdsGoods = dao.getModel(goods.getGoodsId());
            //金额取整
            amountRoundingModeService.setLsdsGoodsScale(lsdsGoods);

            if (null != lsdsGoods) {
                vo.setLsdsGoods(lsdsGoods);

                int roundingMode = amountRoundingModeService.getRoundingMode(lsdsGoods.getGoodsId());

                //返回分段信息
                GoodsSplitMapper splitDao = SpringContextUtil.getBeanByClass(GoodsSplitMapper.class);
                List<LsdsGoodsSplit> splitList = new ArrayList<>();
//                if (null != lsdsGoods && !StringUtils.isEmpty(lsdsGoods.getChildGoodsId())) {
//                    //存在子货源号，则该货源进行了下游询价，分段信息则取自子货源的分段信息
//                    splitList = splitDao.getSplitList(lsdsGoods.getChildGoodsId());
//                    lgf.setGoodsId(lsdsGoods.getChildGoodsId());
//                } else {
//                    //不存在子货源号，则为非下游询价货源
//                    splitList = splitDao.getSplitList(goods.getGoodsId());
//                    lgf.setGoodsId(goods.getGoodsId());
//                }

                splitList = splitDao.getSplitList(goods.getGoodsId());
                lgf.setGoodsId(goods.getGoodsId());

                LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
                Map<String, List<Map<String, Object>>> map = new HashMap<String, List<Map<String, Object>>>();
                if (null != splitList && splitList.size() > 0) {
                    //装载分段信息
                    vo.setGoodsSplitList(splitList);

                    String  platformCompanyId=getPlatformCompanyId();

                    //splitList.size() > 0 则为分段报价
                    if (null != lsdsGoods.getOfferCurrentRounds() && lsdsGoods.getOfferCurrentRounds() > 0) {
                        if (splitList.size() == 1) {
                            //sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND lgo.company_id = '2'" +
                             sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND " + platformCompanyId+
                                    " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                    "  WHEN lgo.sort_node = 1 THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  ELSE 0 END END) sumNodePrice");
                          //  sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND lgo.company_id = '2' " +
                              sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND " + platformCompanyId+
                                    "THEN CONVERT(lgo.enquiry_type_base_open_ticket * ls.weight_sum, DECIMAL(18,5)) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * ls.weight_sum, DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100) * ls.weight_sum, DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value) * ls.weight_sum, DECIMAL(18,5))" +*/
                                    "  WHEN lgo.sort_node = 1 THEN CONVERT(lgo.enquiry_type_base_open_ticket * ls.weight_sum, DECIMAL(18,5))" +
                                    "  ELSE 0 END END) freightPrice");
                        } else {
                            //计算拆段后的总单价
                            for (int i = 1; i <= splitList.size(); i++) {
                                if (i == 1) {
                                   // sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND lgo.company_id = '2' " +
                                      sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND  "  + platformCompanyId+
                                            "THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                            /*"  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                            "  WHEN lgo.sort_node = 1 THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  ELSE 0 END END)");
                                } else if (i == splitList.size()) {
                                   // sb.append("+MAX(CASE WHEN lgo.sort_node = " + i + " AND lgo.company_id = '2'" +
                                    sb.append("+MAX(CASE WHEN lgo.sort_node = " + i + " AND" +platformCompanyId+
                                            " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                            /*"  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                            "  WHEN lgo.sort_node =  " + i + " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  ELSE 0 END END) sumNodePrice");
                                } else {
                                   // sb.append("+MAX(CASE WHEN lgo.sort_node = " + i + " AND lgo.company_id = '2' " +
                                    sb.append("+MAX(CASE WHEN lgo.sort_node = " + i + " AND  " +platformCompanyId+
                                            "THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                            /*"  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                            "  WHEN lgo.sort_node =  " + i + " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  ELSE 0 END END)");
                                }
                            }
                            //计算拆段后的总运费
                            for (int i = 1; i <= splitList.size(); i++) {
                                if (i == 1) {
                                   // sb.append(",CONVERT((MAX(CASE WHEN lgo.sort_node = 1 AND lgo.company_id = '2' " +
                                    sb.append(",CONVERT((MAX(CASE WHEN lgo.sort_node = 1 AND   " +platformCompanyId+
                                            "THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                            /*"  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                            "  WHEN lgo.sort_node = 1 THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  ELSE 0 END END)");
                                } else if (i == splitList.size()) {
                                    //sb.append("+MAX(CASE WHEN lgo.sort_node =  " + i + "  AND lgo.company_id = '2' " +
                                    sb.append("+MAX(CASE WHEN lgo.sort_node =  " + i + "  AND   " +platformCompanyId+
                                            "THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                            /*"  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                            "  WHEN lgo.sort_node =  " + i + " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  ELSE 0 END END)) * ls.weight_sum,DECIMAL(18,5)) freightPrice");
                                } else {
                                   // sb.append("+MAX(CASE WHEN lgo.sort_node =  " + i + "  AND lgo.company_id = '2'" +
                                    sb.append("+MAX(CASE WHEN lgo.sort_node =  " + i + "  AND   " +platformCompanyId+
                                            " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                            /*"  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '0' THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '1' THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                            "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                            "  WHEN lgo.sort_node =  " + i + " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                            "  ELSE 0 END END)");
                                }
                            }
                        }
                        for (int i = 1; i <= splitList.size(); i++) {
                            //sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND lgo.company_id = '2' " +
                            /*sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND  " +platformCompanyId+
                                    "THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))) ELSE CASE" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '0'  THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)))" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '1'  THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5)))" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '2' THEN CONCAT(lgo.offer_id, '-', CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5)))" +
                                    "  ELSE null END END) as node" + i);*/
                            if(roundingMode==1){
                                //向下取整
                                sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND " +   platformCompanyId+
                                    "THEN CONCAT(lgo.offer_id, '-', TRUNCATE(lgo.enquiry_type_base_open_ticket,2) ) ELSE CASE" +
                                   /* "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '0'  THEN CONCAT(lgo.offer_id, '-', TRUNCATE(lgo.enquiry_type_base_open_ticket,2) )" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '1'  THEN CONCAT(lgo.offer_id, '-', TRUNCATE(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),2) )" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '2' THEN CONCAT(lgo.offer_id, '-', TRUNCATE((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),2) )" +*/
                                    "  WHEN lgo.sort_node = " + i + " THEN CONCAT(lgo.offer_id, '-', TRUNCATE(lgo.enquiry_type_base_open_ticket,2) )" +
                                    "  ELSE null END END) as node" + i);
                            }else if(roundingMode==4){
                                //四舍五入
                                sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND " +   platformCompanyId+
                                    "THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,2))) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '0'  THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,2)))" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '1'  THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,2)))" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '2' THEN CONCAT(lgo.offer_id, '-', CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,2)))" +*/
                                    "  WHEN lgo.sort_node = " + i + " THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,2)))" +
                                    "  ELSE null END END) as node" + i);
                            }else{
                                //向上取整  FORMAT(ceil(12.56512*100)/100,2)
                                sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND " +   platformCompanyId+
                                    "THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil(lgo.enquiry_type_base_open_ticket*100)/100,2)) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '0'  THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil(lgo.enquiry_type_base_open_ticket*100)/100,2) )" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '1'  THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100)*100)/100,2) )" +
                                    "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '2' THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value)*100)/100,2) )" +*/
                                    "  WHEN lgo.sort_node = " + i + " THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil(lgo.enquiry_type_base_open_ticket*100)/100,2) )" +
                                    "  ELSE null END END) as node" + i);
                            }
                        }

                        lgf.setSqlStr(sb.toString());
                        for (int i = 1; i <= lsdsGoods.getOfferCurrentRounds(); i++) {
                            lgf.setOfferRound(i);
                            List<Map<String, Object>> offerListMap = offerDao.getSplitOfferList(lgf);

                            //按金额取整方式设置
                            setBasePriceScaleOfSplitOffer(offerListMap);

                            if (null != offerListMap && offerListMap.size() > 0) {
                                map.put(String.valueOf(i), offerListMap);
                            }
                            //装载报价信息
                            vo.setGoodsOfferMap(map);
                        }
                    }
                } else {
                    //为整单报价
                    for (int i = 1; i <= lsdsGoods.getOfferCurrentRounds(); i++) {
                        lgf.setOfferRound(i);
                        lgf.setPlatformCompanyId(platformCompanyIdStr);
                        List<Map<String, Object>> offerList = offerDao.getNoSplitOfferList(lgf);

                        //按金额取整方式设置基价
                        setBasePriceScaleOfSplitOffer(offerList);

                        if (null != offerList && offerList.size() > 0) {
                            map.put(String.valueOf(i), offerList);
                        }
                        //装载报价信息
                        vo.setGoodsOfferMap(map);
                    }
                }

                //返回承运商信息
                GoodsAssignSupplierMapper supplierDao = SpringContextUtil.getBeanByClass(GoodsAssignSupplierMapper.class);
                LsdsGoodsAssignSupplier supplier = new LsdsGoodsAssignSupplier();
                supplier.setGoodsId(goods.getGoodsId());
                supplier.setOfferRound(lsdsGoods.getOfferCurrentRounds());
                List<LsdsGoodsAssignSupplier> supplierList = supplierDao.getPreviousSupplierList(supplier);
                if (null != supplierList && supplierList.size() > 0) {
                    //装载承运商信息
                    vo.setAssignSupplierList(supplierList);
                }

                //当货源询价范围为指定物流公司时，返回指定的所有物流公司
                if(null != lsdsGoods && "1".equals(lsdsGoods.getEnquiryRange())){
                    LsdsGoodsAssignSupplier allSupplier = new LsdsGoodsAssignSupplier();
                    allSupplier.setGoodsId(goods.getGoodsId());
                    allSupplier.setOfferRound(1);
                    List<LsdsGoodsAssignSupplier> allSupplierList = supplierDao.getPreviousSupplierList(allSupplier);
                    if (null != allSupplierList && allSupplierList.size() > 0) {
                        //装载承运商信息
                        vo.setAllAssignSupplierList(allSupplierList);
                    }
                }

                //返回历史使用报价信息
                LsdsGoodsOffer offer = new LsdsGoodsOffer();
                offer.setGoodsId(lsdsGoods.getGoodsId());
                offer.setCompanyId(lsdsGoods.getCompanyId());
                List<LsdsGoodsOffer> offerList = offerDao.getOfferList(offer);
                if (null != offerList && offerList.size() > 0 && null != lsdsGoods.getOfferCurrentRounds() && lsdsGoods.getOfferCurrentRounds() > 0) {
                    Map<String, List<LsdsGoodsOffer>> offerMap = new HashMap<>();
//                    Map<String, List<LsdsGoodsPlan>> planMap = new HashMap<>();
                    for (int i = 1; i <= lsdsGoods.getOfferCurrentRounds(); i++) {
                        String temp = String.valueOf(i);
                        List<LsdsGoodsOffer> tempOfferList = offerList.stream().filter(s -> s.getOfferRound().toString().equals(temp)).collect(Collectors.toList());
                        if (null != tempOfferList && tempOfferList.size() > 0) {
                            offerMap.put(temp, tempOfferList);
//                            LsdsGoodsPlanRepository planDao = SpringContextUtil.getBeanByClass(LsdsGoodsPlanRepository.class);
//                            List<LsdsGoodsPlan> tempPlanList = planDao.getPlanListByOfferId(tempOfferList.get(0).getOfferId());
//                            if(null != tempPlanList && tempPlanList.size() > 0){
//                                planMap.put(temp, tempPlanList);
//                            }
                        }
                    }
                    vo.setGoodsOfferListMap(offerMap);
//                    vo.setGoodsOfferPlanListMap(planMap);
                }

                //封装货源操作记录
//                LsdsGoodsRecordRepository recDao = SpringContextUtil.getBeanByClass(LsdsGoodsRecordRepository.class);
//                LsdsGoodsRecord lgr = new LsdsGoodsRecord();
//                lgr.setGoodsId(goods.getGoodsId());
//                List<LsdsGoodsRecord> recordList = recDao.getRecordList(lgr);
//                vo.setGoodsRecordList(recordList);

                resultmodel.getModel().add(vo);
                resultmodel.setTotal(1);
            }

        } catch (Exception ex) {
            errMsg = "获取货源详细信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setSucceed(false);
            resultmodel = null;
        }
        return resultmodel;
    }


    /**
     * 货源详情页面报价列表
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<Map<String, Object>>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     * <AUTHOR>
     * 创建时间 2019/12/2
     */
    public ResultMode<Map<String, Object>> getOfferMapListPage(PagingInfo<LsdsGoodsFilter> pageInfo) {
        ResultMode<Map<String, Object>> returnModel = new ResultMode<Map<String, Object>>();

        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);

        StringBuffer sb = new StringBuffer();
        LsdsGoodsOffer lgf = new LsdsGoodsOffer();
        lgf.setGoodsId(pageInfo.filterModel.getGoodsId());
        lgf.setOfferRound(pageInfo.filterModel.getOfferRound());
        String  platformCompanyIdStr=getPlatformCompanyIdStr();
        //返回货源分段数
        GoodsSplitMapper splitDao = SpringContextUtil.getBeanByClass(GoodsSplitMapper.class);
        int splitCount = splitDao.existCounts(pageInfo.filterModel.getGoodsId());

        List<Map<String, Object>> offerListMap = new ArrayList<>(16);
        LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
        if (splitCount > 0) {
            //splitList.size() > 0 则为分段报价
            if (null != pageInfo.filterModel.getOfferRound() && pageInfo.filterModel.getOfferRound() > 0) {

                int roundingMode = amountRoundingModeService.getRoundingMode(lgf.getGoodsId());

                String  platformCompanyId=getPlatformCompanyId();

                if (splitCount == 1) {
                   // sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND lgo.company_id = '2'" +
                    sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND   " +platformCompanyId+
                            " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                            /*"  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                            "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                            "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                            "  WHEN lgo.sort_node = 1 THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                            "  ELSE 0 END END) sumNodePrice");
                   // sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND lgo.company_id = '2' " +
                    sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND   " +platformCompanyId+
                            "THEN CONVERT(lgo.enquiry_type_base_open_ticket * ls.total_quantity, DECIMAL(18,5)) ELSE CASE" +
                            /*"  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * ls.total_quantity, DECIMAL(18,5))" +
                            "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100) * ls.total_quantity, DECIMAL(18,5))" +
                            "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value) * ls.total_quantity, DECIMAL(18,5))" +*/
                            "  WHEN lgo.sort_node = 1 THEN CONVERT(lgo.enquiry_type_base_open_ticket * ls.total_quantity, DECIMAL(18,5))" +
                            "  ELSE 0 END END) freightPrice");
                } else {
                    //计算拆段后的总单价
                    for (int i = 1; i <= splitCount; i++) {
                        if (i == 1) {
                           // sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND lgo.company_id = '2'" +
                            sb.append(",MAX(CASE WHEN lgo.sort_node = 1 AND  " +platformCompanyId+
                                    " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                    "  WHEN lgo.sort_node = 1 THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  ELSE 0 END END)");
                        } else if (i == splitCount) {
                           // sb.append("+MAX(CASE WHEN lgo.sort_node = " + i + " AND lgo.company_id = '2' " +
                            sb.append("+MAX(CASE WHEN lgo.sort_node = " + i + " AND  " +platformCompanyId+
                                    "THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                    "  WHEN lgo.sort_node =  " + i + " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  ELSE 0 END END) sumNodePrice");
                        } else {
                           // sb.append("+MAX(CASE WHEN lgo.sort_node = " + i + " AND lgo.company_id = '2' " +
                            sb.append("+MAX(CASE WHEN lgo.sort_node = " + i + " AND   " +platformCompanyId+
                                    "THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                    "  WHEN lgo.sort_node =  " + i + " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  ELSE 0 END END)");
                        }
                    }
                    //计算拆段后的总运费
                    for (int i = 1; i <= splitCount; i++) {
                        if (i == 1) {
                           // sb.append(",CONVERT((MAX(CASE WHEN lgo.sort_node = 1 AND lgo.company_id = '2'" +
                            sb.append(",CONVERT((MAX(CASE WHEN lgo.sort_node = 1 AND   " +platformCompanyId+
                                    " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node = 1 AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                    "  WHEN lgo.sort_node = 1 THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  ELSE 0 END END)");
                        } else if (i == splitCount) {
                           // sb.append("+MAX(CASE WHEN lgo.sort_node =  " + i + "  AND lgo.company_id = '2' " +
                            sb.append("+MAX(CASE WHEN lgo.sort_node =  " + i + "  AND   " +platformCompanyId+
                                    "THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '0'  THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '1'  THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                    "  WHEN lgo.sort_node =  " + i + " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  ELSE 0 END END)) * ls.weight_sum,DECIMAL(18,5)) freightPrice");
                        } else {
                            //sb.append("+MAX(CASE WHEN lgo.sort_node =  " + i + "  AND lgo.company_id = '2'" +
                            sb.append("+MAX(CASE WHEN lgo.sort_node =  " + i + "  AND   " +platformCompanyId+
                                    " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)) ELSE CASE" +
                                    /*"  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '0' THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '1' THEN CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5))" +
                                    "  WHEN lgo.sort_node =  " + i + "  AND ls.fee_clear_type = '2' THEN CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5))" +*/
                                    "  WHEN lgo.sort_node =  " + i + " THEN CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))" +
                                    "  ELSE 0 END END)");
                        }
                    }
                }
                for (int i = 1; i <= splitCount; i++) {
                   // sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND lgo.company_id = '2' " +
                    /*sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND  " +platformCompanyId+
                            "THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5))) ELSE CASE" +
                            "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '0'  THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,5)))" +
                            "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '1'  THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,5)))" +
                            "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '2' THEN CONCAT(lgo.offer_id, '-', CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,5)))" +
                            "  ELSE null END END) as node" + i);*/
                    if(roundingMode==1){
                        //向下取整
                        sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND " +   platformCompanyId+
                            "THEN CONCAT(lgo.offer_id, '-', TRUNCATE(lgo.enquiry_type_base_open_ticket,2) ) ELSE CASE" +
                            /*"  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '0'  THEN CONCAT(lgo.offer_id, '-', TRUNCATE(lgo.enquiry_type_base_open_ticket,2) )" +
                            "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '1'  THEN CONCAT(lgo.offer_id, '-', TRUNCATE(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),2) )" +
                            "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '2' THEN CONCAT(lgo.offer_id, '-', TRUNCATE((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),2) )" +*/
                            "  WHEN lgo.sort_node = " + i + " THEN CONCAT(lgo.offer_id, '-', TRUNCATE(lgo.enquiry_type_base_open_ticket,2) )" +
                            "  ELSE null END END) as node" + i);
                    }else if(roundingMode==4){
                        //四舍五入
                        sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND " +   platformCompanyId+
                            "THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,2))) ELSE CASE" +
                            /*"  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '0'  THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,2)))" +
                            "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '1'  THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100),DECIMAL(18,2)))" +
                            "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '2' THEN CONCAT(lgo.offer_id, '-', CONVERT((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value),DECIMAL(18,2)))" +*/
                            "  WHEN lgo.sort_node = " + i + " THEN CONCAT(lgo.offer_id, '-', CONVERT(lgo.enquiry_type_base_open_ticket,DECIMAL(18,2)))" +
                            "  ELSE null END END) as node" + i);
                    }else{
                        //向上取整
                        sb.append(",MAX(CASE WHEN lgo.sort_node =  " + i + "  AND " +   platformCompanyId+
                            "THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil(lgo.enquiry_type_base_open_ticket*100)/100,2)) ELSE CASE" +
                            /*"  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '0'  THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil(lgo.enquiry_type_base_open_ticket*100)/100,2) )" +
                            "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '1'  THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil(lgo.enquiry_type_base_open_ticket * (1 + ls.fee_clear_value / 100)*100)/100,2) )" +
                            "  WHEN lgo.sort_node = " + i + " AND ls.fee_clear_type = '2' THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil((lgo.enquiry_type_base_open_ticket + ls.fee_clear_value)*100)/100,2) )" +*/
                            "  WHEN lgo.sort_node = " + i + " THEN CONCAT(lgo.offer_id, '-', FORMAT(ceil(lgo.enquiry_type_base_open_ticket*100)/100,2) )" +
                            "  ELSE null END END) as node" + i);
                    }
                }

                lgf.setSqlStr(sb.toString());
                offerListMap = offerDao.getSplitOfferList(lgf);

                //按金额取整方式设置基价
                setBasePriceScaleOfSplitOffer(offerListMap);
            }
        } else {
            //为整单报价
            lgf.setPlatformCompanyId(platformCompanyIdStr);
            offerListMap = offerDao.getNoSplitOfferList(lgf);

            //按金额取整方式设置基价
            setBasePriceScaleOfSplitOffer(offerListMap);
        }
        //填充中标文件
        this.appendBidFile(pageInfo.filterModel.getGoodsId(),offerListMap);
        PageInfo<Map<String, Object>> pageInfoList = new PageInfo<Map<String, Object>>(offerListMap);
        Long total = pageInfoList.getTotal();
        returnModel.setTotal(total.intValue());
        returnModel.setModel(offerListMap);
        return returnModel;
    }


    /**
     * 新增货源编号  货源信息表信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param vo    货源VO
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【增加成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【增加成功】编码,ResultMode.errMsg为相应【增加成功】描述；
     * 否则ResultMode.succeed=false【增加失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【增加失败】编码,ResultMode.errMsg为相应【增加失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsGoodsAdd(LsdsGoodsVo vo, String token) {
        String errMsg = "";
        String newGoodsId = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            //加锁
            distributedLocker.lock(GOODS_ADD_LOCK, 3);

            PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
            //#region 封装货源信息
            GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);

//            String goodsId = genericGoodsId();
            String goodsId = lsdsSequenceMapper.generateGoodsPrimaryNum(PrefixCodeConstants.GOODS_C, DateUtil.formatDate(DateUtil.date()));

            vo.getLsdsGoods().setGoodsId(goodsId);
            //首次发布货源，报价轮次为 1
            vo.getLsdsGoods().setOfferCurrentRounds(1);
            //#endregion
            String addressId = String.valueOf(IdUtil.generateId());
            //#region 封装货源地址信息
            LsdsGoodsAddress goodsAddress = LsdsGoodsAddress.of().setSourceId(goodsId).setSendAddrShortName(vo.getLsdsGoods().getSendAddrShortName())
                .setGoodsAddressId(addressId).setSendAddrProvince(vo.getLsdsGoods().getSendAddrProvince()).setSendAddrCity(vo.getLsdsGoods().getSendAddrCity())
                .setSendAddrArea(vo.getLsdsGoods().getSendAddrArea()).setSendAddrStreet(vo.getLsdsGoods().getSendAddrStreet()).setSendAddrProvinceName(vo.getLsdsGoods().getSendAddrProvinceName())
                .setSendAddrCityName(vo.getLsdsGoods().getSendAddrCityName()).setSendAddrAreaName(vo.getLsdsGoods().getSendAddrAreaName()).setSendAddrStreetName(vo.getLsdsGoods().getSendAddrStreetName())
                .setSendAddrDetail(vo.getLsdsGoods().getSendAddrDetail()).setSendLinker(vo.getLsdsGoods().getSendLinker()).setSendPhoneNumber(vo.getLsdsGoods().getSendPhoneNumber())
                .setReceiveAddrShortName(vo.getLsdsGoods().getReceiveAddrShortName()).setReceiveAddrProvince(vo.getLsdsGoods().getReceiveAddrProvince())
                .setReceiveAddrCity(vo.getLsdsGoods().getReceiveAddrCity()).setReceiveAddrArea(vo.getLsdsGoods().getReceiveAddrArea())
                .setReceiveAddrStreet(vo.getLsdsGoods().getReceiveAddrStreet()).setReceiveAddrProvinceName(vo.getLsdsGoods().getReceiveAddrProvinceName())
                .setReceiveAddrCityName(vo.getLsdsGoods().getReceiveAddrCityName()).setReceiveAddrAreaName(vo.getLsdsGoods().getReceiveAddrAreaName())
                .setReceiveAddrStreetName(vo.getLsdsGoods().getReceiveAddrStreetName()).setReceiveAddrDetail(vo.getLsdsGoods().getReceiveAddrDetail())
                .setReceiveLinker(vo.getLsdsGoods().getReceiveLinker()).setReceivePhoneNumber(vo.getLsdsGoods().getReceivePhoneNumber())
                .setCreateBy(userInfoFromCache.getUserBaseId()).setCreateDate(DateUtils.getDateTime()).setModifyBy(userInfoFromCache.getUserBaseId())
                .setModifyDate(DateUtils.getDateTime()).setAddressType("1")
                ;

            //#end region

            //写入货源地址信息
            GoodsAddressMapper addressDao = SpringContextUtil.getBeanByClass(GoodsAddressMapper.class);
//            addressDao.insert(goodsAddress);
            //写入货源主信息
            vo.getLsdsGoods().setGoodsAddressId(addressId);
            vo.getLsdsGoods().setCreateBy(userInfoFromCache.getUserBaseId());
            vo.getLsdsGoods().setCreateDate(DateUtils.getDateTime());
            vo.getLsdsGoods().setModifyBy(userInfoFromCache.getUserBaseId());
            vo.getLsdsGoods().setModifyDate(DateUtils.getDateTime());
            if(!StringUtils.isEmpty(vo.getLsdsGoods().getDealStatus()) && LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus2.getCode().equals(vo.getLsdsGoods().getDealStatus())){
                vo.getLsdsGoods().setSubmitBy(userInfoFromCache.getUserBaseId());
            }


            log.info("企业货源入库:{}",JSONObject.toJSONString(vo.getLsdsGoods()));
            LsdsGoods lsdsGoods  = vo.getLsdsGoods();
            goodsDao.add(lsdsGoods);

            // 广播通知货源状态变更
            GoodsKafkaNotice goodsKafkaNotice = BeanUtil.copyProperties(lsdsGoods, GoodsKafkaNotice.class);
            lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD,JSONUtil.toJsonStr(goodsKafkaNotice));

            // 保存金额取整方式
            amountRoundingModeService.saveRoundingMode(lsdsGoods.getGoodsId(), lsdsGoods.getRoundingMode());
            // ES中增加货源对应亏涨吨 调用公共方法
            LsdsGoodsDeductible lsdsGoodsDeductible = lsdsGoods.getLsdsGoodsDeductible();
            lsdsGoodsDeductible.setBizId(lsdsGoods.getGoodsId());
            Boolean saveEsSuccess = dcsBusiness.saveLsdsGoodsDeductible(lsdsGoodsDeductible);
            if (!saveEsSuccess) {
                throw new LsdsWlydException(LsdsStatusCodeEnum.BUSS_ERROR_BLSDS0300);
            }
            newGoodsId = vo.getLsdsGoods().getGoodsId();
            goodsAddress.setSourceId(vo.getLsdsGoods().getGoodsId());
            addressDao.add(goodsAddress);

            //货源分段与提供运输方案互斥（拆段后就不能勾选运输方案）
            if (null != vo.getGoodsSplitList() && vo.getGoodsSplitList().size() > 0) {
                boolean tranTemp = null == vo.getLsdsGoods().getTransportIsOptions() || (null != vo.getLsdsGoods().getTransportIsOptions() && !vo.getLsdsGoods().getTransportIsOptions().equals("11"));
                List<LsdsGoodsAddress> splitAddressList = new ArrayList<>();
                if (tranTemp) {
                    for (LsdsGoodsSplit goodsSplit : vo.getGoodsSplitList()) {
                        String splitGoodsAddressId = String.valueOf(IdUtil.generateId());
                        String splitId = String.valueOf(IdUtil.generateId());
                        //#region 封装分段信息
                        // goodsSplit.setGoodsId(goodsId);
                        goodsSplit.setGoodsId(newGoodsId);
                        goodsSplit.setGoodsSplitId(splitId);
                        goodsSplit.setGoodsAddressId(splitGoodsAddressId);
                        goodsSplit.setCreateBy(userInfoFromCache.getUserBaseId());
                        goodsSplit.setCreateDate(DateUtils.getDateTime());
                        goodsSplit.setModifyBy(userInfoFromCache.getUserBaseId());
                        goodsSplit.setModifyDate(DateUtils.getDateTime());
                        //#endregion

                        //#region 封装分段地址信息
                        LsdsGoodsAddress splitAddress =  LsdsGoodsAddress.of();
                        splitAddress.setGoodsAddressId(splitGoodsAddressId);
                        //splitAddress.setSourceId(goodsId);
                        splitAddress.setSourceId(newGoodsId);
                        splitAddress.setSendAddrShortName(goodsSplit.getSendAddrShortName());
                        splitAddress.setSendAddrProvince(goodsSplit.getSendAddrProvince());
                        splitAddress.setSendAddrCity(goodsSplit.getSendAddrCity());
                        splitAddress.setSendAddrArea(goodsSplit.getSendAddrArea());
                        splitAddress.setSendAddrStreet(goodsSplit.getSendAddrStreet());
                        splitAddress.setSendAddrProvinceName(goodsSplit.getSendAddrProvinceName());
                        splitAddress.setSendAddrCityName(goodsSplit.getSendAddrCityName());
                        splitAddress.setSendAddrAreaName(goodsSplit.getSendAddrAreaName());
                        splitAddress.setSendAddrStreetName(goodsSplit.getSendAddrStreetName());
                        splitAddress.setSendAddrDetail(goodsSplit.getSendAddrDetail());
                        splitAddress.setSendLinker(goodsSplit.getSendLinker());
                        splitAddress.setSendPhoneNumber(goodsSplit.getSendPhoneNumber());
                        splitAddress.setReceiveAddrShortName(goodsSplit.getReceiveAddrShortName());
                        splitAddress.setReceiveAddrProvince(goodsSplit.getReceiveAddrProvince());
                        splitAddress.setReceiveAddrCity(goodsSplit.getReceiveAddrCity());
                        splitAddress.setReceiveAddrArea(goodsSplit.getReceiveAddrArea());
                        splitAddress.setReceiveAddrStreet(goodsSplit.getReceiveAddrStreet());
                        splitAddress.setReceiveAddrProvinceName(goodsSplit.getReceiveAddrProvinceName());
                        splitAddress.setReceiveAddrCityName(goodsSplit.getReceiveAddrCityName());
                        splitAddress.setReceiveAddrAreaName(goodsSplit.getReceiveAddrAreaName());
                        splitAddress.setReceiveAddrStreetName(goodsSplit.getReceiveAddrStreetName());
                        splitAddress.setReceiveAddrDetail(goodsSplit.getReceiveAddrDetail());
                        splitAddress.setReceiveLinker(goodsSplit.getReceiveLinker());
                        splitAddress.setReceivePhoneNumber(goodsSplit.getReceivePhoneNumber());
                        splitAddress.setAddressType("2");
                        splitAddress.setCreateBy(userInfoFromCache.getUserBaseId());
                        splitAddress.setCreateDate(DateUtils.getDateTime());
                        splitAddress.setModifyBy(userInfoFromCache.getUserBaseId());
                        splitAddress.setModifyDate(DateUtils.getDateTime());
                        splitAddressList.add(splitAddress);
                        //#endregion
                    }
                    //写入货源分段信息
                    GoodsSplitMapper splitDao = SpringContextUtil.getBeanByClass(GoodsSplitMapper.class);
                    splitDao.batchAdd(vo.getGoodsSplitList());

                    //写入货源分段地址信息
                    addressDao.insertBatch(splitAddressList);
                } else {
                    errMsg = "参数有误：分段后不能再选择提供运输方案！";
                    resultmodel.setErrMsg(errMsg);
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }
            }

            if (null != vo.getAssignSupplierList() && vo.getAssignSupplierList().size() > 0) {
                for (LsdsGoodsAssignSupplier assSupplier : vo.getAssignSupplierList()) {
                    //assSupplier.setGoodsId(goodsId);
                    assSupplier.setGoodsId(newGoodsId);
                    assSupplier.setGoodsAssignSupplierId(String.valueOf(IdUtil.generateId()));
                    //设置承运商当前报价轮次
                    assSupplier.setOfferRound(vo.getLsdsGoods().getOfferCurrentRounds());
                    assSupplier.setCreateBy(userInfoFromCache.getUserBaseId());
                    assSupplier.setCreateDate(DateUtils.getDateTime());
                    assSupplier.setModifyBy(userInfoFromCache.getUserBaseId());
                    assSupplier.setModifyDate(DateUtils.getDateTime());
                }
                //写入货源指定承运商信息
                GoodsAssignSupplierMapper supplierDao = SpringContextUtil.getBeanByClass(GoodsAssignSupplierMapper.class);
                supplierDao.batchAdd(vo.getAssignSupplierList());
            }
            //关联货源号到上游货源
//            if(!StringUtils.isEmpty(vo.getLsdsGoods().getParentGoodsId())){
//                LsdsGoods lg = new LsdsGoods();
//                lg.setGoodsId(vo.getLsdsGoods().getParentGoodsId());
//                lg.setChildGoodsId(goodsId);
//                goodsDao.update(lg);
//            }
//            redisLock.unlock(tempGoodsId,String.valueOf(time));
            //添加招标文件
            CreateCallBidFileFilter filter = new CreateCallBidFileFilter();
            filter.setLsdsGoods(vo.getLsdsGoods());
            lsdsGoodsBidFileService.createCallFile(filter);
            //同步货源操作记录
            GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
            LsdsGoodsRecord lgr =  LsdsGoodsRecord.of()
                .setRecordId(String.valueOf(IdUtil.generateId()))
                //.setGoodsId(goodsId)
                .setGoodsId(newGoodsId)
                .setCompanyId(userInfoFromCache.getCompanyId());

            if("1".equals(vo.getLsdsGoods().getDealStatus())){
                lgr.setOperateStatus("1");
                lgr.setOperateContent("货源保存");
            }else{
                lgr.setOperateStatus("2");
                lgr.setOperateContent("货源发布");
            }
            lgr.setUserId(userInfoFromCache.getUserBaseId());
            lgr.setUserLoginName(userInfoFromCache.getLoginName());
            lgr.setUserName(userInfoFromCache.getUsername());
            recDao.add(lgr);

            //保存业务扩展信息
//            saveTmsBizExtend(vo.getLsdsGoods());

            resultmodel.getModel().add(newGoodsId);
        } catch (Exception ex) {
            errMsg = "新增货源信息异常";
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
            LogHelper.writeError(errMsg, ex);
        }finally {
            //解锁
            distributedLocker.unlock(GOODS_ADD_LOCK);
        }
        return resultmodel;
    }

    /**
     * 保存业务扩展信息
     */
    /*private void saveTmsBizExtend(LsdsGoods lsdsGoods) {
        //查询业务类型、差价率和成本价
        PlatformTaxRateFilter filter = new PlatformTaxRateFilter()
            .setFreightType(lsdsGoods.getFreightType())
            .setTransportMileage(lsdsGoods.getTransportMileage())
            .setGoodsType(lsdsGoods.getGoodsType())
            .setMainBodyId(lsdsGoods.getNetworkMainBodyId())
            .setCompanyId(lsdsGoods.getCompanyId())
            .setDirectPayment(true);
        BizExtendVO bizExtendVO = platformExchangeService.getTaxRate(filter);
        if (ObjUtil.isNull(bizExtendVO)) {
            return;
        }
        lsdsGoods.setTrafficClassification(bizExtendVO.getTrafficClassification());
        lsdsGoods.setFreightDiffRate(bizExtendVO.getFreightDiffRate());
        lsdsGoods.setCostPrice(bizExtendVO.getCostPrice());
        bizExtendVO.setBizId(lsdsGoods.getGoodsId());
        searchExchangeService.saveTmsBizExtend(bizExtendVO);
    }*/

  /*  private void updateAddressAndLineId(LsdsGoodsVo vo, LsdsGoods lsdsGoods) {
        ResultMode<CrmCompanyLineAddressBO> resultMode = getCrmCompanyLineAddress(lsdsGoods);
        if(!resultMode.getSucceed()){
            return;
        }
        CrmCompanyLineAddressBO lineObj = resultMode.getModel().get(0);
        LsdsGoods lsdsGoodsDto = vo.getLsdsGoods();


        if (lineObj != null && !StringUtils.isEmpty(lineObj.getLineId())){
            lsdsGoodsDto.setLineId(lineObj.getLineId());
            vo.setLineId(lineObj.getLineId());
        }
        vo.setLsdsGoods(lsdsGoodsDto);


        goodsMapper dao = SpringContextUtil.getBeanByClass(goodsMapper.class);
        lsdsGoodsDto.setGoodsId(lsdsGoods.getGoodsId());
        dao.update(lsdsGoodsDto);
    }*/

    /**
     * 下游询价新增货源
     * 创建者: cgb
     * 创建时间: 2020/4/21
     * @param vo
     * @return {@code ResultMode<String>}
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsGoodsDownAdd(LsdsGoodsVo vo, String token) {
        String errMsg = "";
        String newGoodsId = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            //加锁
            distributedLocker.lock(GOODS_DOWN_ADD_LOCK, 3);

            PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();

            //判断货源是否已经进行过下游询价
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoods voLsdsGoods = vo.getLsdsGoods();
            LsdsGoods lsdsGoods = dao.getModel(voLsdsGoods.getParentGoodsId());
            if(null != lsdsGoods && !StringUtils.isEmpty(lsdsGoods.getChildGoodsId())) {
                errMsg = "货源已经执行下游询价，请勿重复提交！";
                throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_DB_CREATE_ERR, errMsg);
            }

            //判断平台3pl是否已经对货源报价，如果已报价则不允许再下游询价
            LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
            LsdsGoodsOffer lgs = new LsdsGoodsOffer();
            lgs.setGoodsId(voLsdsGoods.getParentGoodsId());
            lgs.setOfferRound(1);
            lgs.setCompanyId(userInfoFromCache.getCompanyId());
            lgs.setOfferStatus(OfferStatusEnum.QUOTE.getCode());
            int existOffer = offerDao.existsCurrentOffer(lgs);
            if (existOffer > 0) {
                errMsg = "货源已经报价，不允许再进行下游询价！";
                throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_DB_CREATE_ERR, errMsg);
            }

            //#region 封装货源信息
            GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);

//            String goodsId = genericGoodsId();
            String goodsId = lsdsSequenceMapper.generateGoodsPrimaryNum(PrefixCodeConstants.GOODS_C, DateUtil.formatDate(DateUtil.date()));
            voLsdsGoods.setGoodsId(goodsId);
            //首次发布货源，报价轮次为 1
            voLsdsGoods.setOfferCurrentRounds(1);
            //#endregion

            //#region 封装货源地址信息
            LsdsGoodsAddress goodsAddress = LsdsGoodsAddress.of();
            String addressId = String.valueOf(IdUtil.generateId());
            goodsAddress.setSourceId(goodsId);
            goodsAddress.setGoodsAddressId(addressId);
            goodsAddress.setSendAddrShortName(voLsdsGoods.getSendAddrShortName());
            goodsAddress.setSendAddrProvince(voLsdsGoods.getSendAddrProvince());
            goodsAddress.setSendAddrCity(voLsdsGoods.getSendAddrCity());
            goodsAddress.setSendAddrArea(voLsdsGoods.getSendAddrArea());
            goodsAddress.setSendAddrStreet(voLsdsGoods.getSendAddrStreet());
            goodsAddress.setSendAddrProvinceName(voLsdsGoods.getSendAddrProvinceName());
            goodsAddress.setSendAddrCityName(voLsdsGoods.getSendAddrCityName());
            goodsAddress.setSendAddrAreaName(voLsdsGoods.getSendAddrAreaName());
            goodsAddress.setSendAddrStreetName(voLsdsGoods.getSendAddrStreetName());
            goodsAddress.setSendAddrDetail(voLsdsGoods.getSendAddrDetail());
            goodsAddress.setSendLinker(voLsdsGoods.getSendLinker());
            goodsAddress.setSendPhoneNumber(voLsdsGoods.getSendPhoneNumber());
            goodsAddress.setReceiveAddrShortName(voLsdsGoods.getReceiveAddrShortName());
            goodsAddress.setReceiveAddrProvince(voLsdsGoods.getReceiveAddrProvince());
            goodsAddress.setReceiveAddrCity(voLsdsGoods.getReceiveAddrCity());
            goodsAddress.setReceiveAddrArea(voLsdsGoods.getReceiveAddrArea());
            goodsAddress.setReceiveAddrStreet(voLsdsGoods.getReceiveAddrStreet());
            goodsAddress.setReceiveAddrProvinceName(voLsdsGoods.getReceiveAddrProvinceName());
            goodsAddress.setReceiveAddrCityName(voLsdsGoods.getReceiveAddrCityName());
            goodsAddress.setReceiveAddrAreaName(voLsdsGoods.getReceiveAddrAreaName());
            goodsAddress.setReceiveAddrStreetName(voLsdsGoods.getReceiveAddrStreetName());
            goodsAddress.setReceiveAddrDetail(voLsdsGoods.getReceiveAddrDetail());
            goodsAddress.setReceiveLinker(voLsdsGoods.getReceiveLinker());
            goodsAddress.setReceivePhoneNumber(voLsdsGoods.getReceivePhoneNumber());
            goodsAddress.setCreateBy(userInfoFromCache.getUserBaseId());
            goodsAddress.setCreateDate(DateUtils.getDateTime());
            goodsAddress.setModifyBy(userInfoFromCache.getUserBaseId());
            goodsAddress.setModifyDate(DateUtils.getDateTime());
            goodsAddress.setAddressType("1");
            //#endregion

            //写入货源地址信息
            GoodsAddressMapper addressDao = SpringContextUtil.getBeanByClass(GoodsAddressMapper.class);
            //addressDao.insert(goodsAddress);
            //写入货源主信息
            voLsdsGoods.setGoodsAddressId(addressId);
            voLsdsGoods.setCreateBy(userInfoFromCache.getUserBaseId());
            voLsdsGoods.setCreateDate(DateUtils.getDateTime());
            voLsdsGoods.setModifyBy(userInfoFromCache.getUserBaseId());
            voLsdsGoods.setModifyDate(DateUtils.getDateTime());
            CrmCompanyLineAddressBO crmCompanyLineAddressBO = lineAddressExchangeService.getCrmCompanyLine(voLsdsGoods.getLineId());
            if (ObjectUtil.isNotNull(crmCompanyLineAddressBO)) {
                voLsdsGoods.setTransportMileage(crmCompanyLineAddressBO.getTransportMileage());
                voLsdsGoods.setTakeUpTime(crmCompanyLineAddressBO.getTakeUpTime());
            }
            goodsDao.add(voLsdsGoods);

            // 广播通知货源状态变更
            GoodsKafkaNotice goodsKafkaNotice = BeanUtil.copyProperties(voLsdsGoods, GoodsKafkaNotice.class);
            lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD,JSONUtil.toJsonStr(goodsKafkaNotice));

            newGoodsId = voLsdsGoods.getGoodsId();
            // 保存金额取整方式
            amountRoundingModeService.saveRoundingMode(newGoodsId, voLsdsGoods.getRoundingMode());

            // ES增加货源亏涨吨数据
            LsdsGoodsDeductible lsdsGoodsDeductible = voLsdsGoods.getLsdsGoodsDeductible();
            lsdsGoodsDeductible.setBizId(newGoodsId);
            Boolean saveEsSuccess = dcsBusiness.saveLsdsGoodsDeductible(lsdsGoodsDeductible);
            if (!saveEsSuccess) {
                throw new LsdsWlydException(LsdsStatusCodeEnum.BUSS_ERROR_BLSDS0300);
            }

            goodsAddress.setSourceId(newGoodsId);
            addressDao.add(goodsAddress);

            //更新父货源的子货源号
            LsdsGoods parentGoods = LsdsGoods.of();
            parentGoods.setGoodsId(voLsdsGoods.getParentGoodsId());
            //parentGoods.setChildGoodsId(goodsId);
            parentGoods.setChildGoodsId(newGoodsId);
            goodsDao.modify(parentGoods);

            //货源分段与提供运输方案互斥（拆段后就不能勾选运输方案）
            if (null != vo.getGoodsSplitList() && vo.getGoodsSplitList().size() > 0) {
                boolean tranTemp = null == voLsdsGoods.getTransportIsOptions() || (null != voLsdsGoods.getTransportIsOptions() && !voLsdsGoods.getTransportIsOptions().equals("11"));
                List<LsdsGoodsAddress> splitAddressList = new ArrayList<>();
                if (tranTemp) {
                    for (LsdsGoodsSplit goodsSplit : vo.getGoodsSplitList()) {
                        String splitGoodsAddressId = String.valueOf(IdUtil.generateId());
                        String splitId = String.valueOf(IdUtil.generateId());
                        //#region 封装分段信息
                        //goodsSplit.setGoodsId(goodsId);
                        goodsSplit.setGoodsId(newGoodsId);
                        goodsSplit.setGoodsSplitId(splitId);
                        goodsSplit.setGoodsAddressId(splitGoodsAddressId);
                        goodsSplit.setCreateBy(userInfoFromCache.getUserBaseId());
                        goodsSplit.setCreateDate(DateUtils.getDateTime());
                        goodsSplit.setModifyBy(userInfoFromCache.getUserBaseId());
                        goodsSplit.setModifyDate(DateUtils.getDateTime());
                        //#endregion

                        //#region 封装分段地址信息
                        LsdsGoodsAddress splitAddress =  LsdsGoodsAddress.of();
                        splitAddress.setGoodsAddressId(splitGoodsAddressId);
                        //splitAddress.setSourceId(goodsId);
                        splitAddress.setSourceId(newGoodsId);
                        splitAddress.setSendAddrShortName(goodsSplit.getSendAddrShortName());
                        splitAddress.setSendAddrProvince(goodsSplit.getSendAddrProvince());
                        splitAddress.setSendAddrCity(goodsSplit.getSendAddrCity());
                        splitAddress.setSendAddrArea(goodsSplit.getSendAddrArea());
                        splitAddress.setSendAddrStreet(goodsSplit.getSendAddrStreet());
                        splitAddress.setSendAddrProvinceName(goodsSplit.getSendAddrProvinceName());
                        splitAddress.setSendAddrCityName(goodsSplit.getSendAddrCityName());
                        splitAddress.setSendAddrAreaName(goodsSplit.getSendAddrAreaName());
                        splitAddress.setSendAddrStreetName(goodsSplit.getSendAddrStreetName());
                        splitAddress.setSendAddrDetail(goodsSplit.getSendAddrDetail());
                        splitAddress.setSendLinker(goodsSplit.getSendLinker());
                        splitAddress.setSendPhoneNumber(goodsSplit.getSendPhoneNumber());
                        splitAddress.setReceiveAddrShortName(goodsSplit.getReceiveAddrShortName());
                        splitAddress.setReceiveAddrProvince(goodsSplit.getReceiveAddrProvince());
                        splitAddress.setReceiveAddrCity(goodsSplit.getReceiveAddrCity());
                        splitAddress.setReceiveAddrArea(goodsSplit.getReceiveAddrArea());
                        splitAddress.setReceiveAddrStreet(goodsSplit.getReceiveAddrStreet());
                        splitAddress.setReceiveAddrProvinceName(goodsSplit.getReceiveAddrProvinceName());
                        splitAddress.setReceiveAddrCityName(goodsSplit.getReceiveAddrCityName());
                        splitAddress.setReceiveAddrAreaName(goodsSplit.getReceiveAddrAreaName());
                        splitAddress.setReceiveAddrStreetName(goodsSplit.getReceiveAddrStreetName());
                        splitAddress.setReceiveAddrDetail(goodsSplit.getReceiveAddrDetail());
                        splitAddress.setReceiveLinker(goodsSplit.getReceiveLinker());
                        splitAddress.setReceivePhoneNumber(goodsSplit.getReceivePhoneNumber());
                        splitAddress.setAddressType("2");
                        splitAddress.setCreateBy(userInfoFromCache.getUserBaseId());
                        splitAddress.setCreateDate(DateUtils.getDateTime());
                        splitAddress.setModifyBy(userInfoFromCache.getUserBaseId());
                        splitAddress.setModifyDate(DateUtils.getDateTime());
                        splitAddressList.add(splitAddress);
                        //#endregion
                    }
                    //写入货源分段信息
                    GoodsSplitMapper splitDao = SpringContextUtil.getBeanByClass(GoodsSplitMapper.class);
                    splitDao.batchAdd(vo.getGoodsSplitList());

                    //写入货源分段地址信息
                    addressDao.insertBatch(splitAddressList);
                } else {
                    errMsg = "参数有误：分段后不能再选择提供运输方案！";
                    throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_DB_CREATE_ERR, errMsg);
                }
            }

            if (null != vo.getAssignSupplierList() && vo.getAssignSupplierList().size() > 0) {
                for (LsdsGoodsAssignSupplier assSupplier : vo.getAssignSupplierList()) {
                    //assSupplier.setGoodsId(goodsId);
                    assSupplier.setGoodsId(newGoodsId);
                    assSupplier.setGoodsAssignSupplierId(String.valueOf(IdUtil.generateId()));
                    //设置承运商当前报价轮次
                    assSupplier.setOfferRound(voLsdsGoods.getOfferCurrentRounds());
                    assSupplier.setCreateBy(userInfoFromCache.getUserBaseId());
                    assSupplier.setCreateDate(DateUtils.getDateTime());
                    assSupplier.setModifyBy(userInfoFromCache.getUserBaseId());
                    assSupplier.setModifyDate(DateUtils.getDateTime());
                }
                //写入货源指定承运商信息
                GoodsAssignSupplierMapper supplierDao = SpringContextUtil.getBeanByClass(GoodsAssignSupplierMapper.class);
                supplierDao.batchAdd(vo.getAssignSupplierList());
            }

            //同步货源操作记录
            GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
            LsdsGoodsRecord lgr = LsdsGoodsRecord.of();
            lgr.setRecordId(String.valueOf(IdUtil.generateId()));
            //lgr.setGoodsId(goodsId);
            lgr.setGoodsId(newGoodsId);
            lgr.setCompanyId(userInfoFromCache.getCompanyId());
            if("1".equals(voLsdsGoods.getDealStatus())){
                lgr.setOperateStatus("1");
                lgr.setOperateContent("货源保存");
            }else{
                lgr.setOperateStatus("2");
                lgr.setOperateContent("货源发布");
            }
            lgr.setUserId(userInfoFromCache.getUserBaseId());
            lgr.setUserLoginName(userInfoFromCache.getLoginName());
            lgr.setUserName(userInfoFromCache.getUsername());
            recDao.add(lgr);

            // resultmodel.getModel().add(goodsId);
            resultmodel.getModel().add(newGoodsId);
        }finally {
            //解锁
            distributedLocker.unlock(GOODS_DOWN_ADD_LOCK);
        }
        return resultmodel;
    }




    /**
     * 修改货源编号  货源信息表信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param vo    货源编号  货源信息表信息
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【修改成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【修改成功】编码,ResultMode.errMsg为相应【修改成功】描述；
     * 否则ResultMode.succeed=false【修改失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【修改失败】编码,ResultMode.errMsg为相应【修改失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsGoodsUpdate (LsdsGoodsVo vo, String token) throws Exception {
        log.info(">>>>>>>>>>>进入lsdsGoodsUpdate>>>>>>>>>>>>>>:{}",JSONObject.toJSONString(vo));
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        ResultMode<String> tempResult = new ResultMode<String>();
        boolean flag = false;
        LsdsGoods voLsdsGoods = vo.getLsdsGoods();
        try {
            //加锁
            distributedLocker.lock(GOOD_UPDATE_LOCK + voLsdsGoods.getGoodsId(), 2);

            //获取审核状态
            String dealStatus = voLsdsGoods.getDealStatus();
            log.info(">>>dealStatus:{}",dealStatus);
            PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
            //如果货源状态为发布(提交),则将当前用户设为提交人
            if(!StringUtils.isEmpty(dealStatus) && LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus2.getCode().equals(dealStatus)){
                voLsdsGoods.setSubmitBy(userInfoFromCache.getUserBaseId());
            }
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoods lsdsGoods = dao.getModel(voLsdsGoods.getGoodsId());
            //金额取整
            amountRoundingModeService.setLsdsGoodsScale(lsdsGoods);
            //控制重复提交
//            if(!StringUtils.isEmpty(dealStatus) && !StringUtils.isEmpty(lsdsGoods.getDealStatus()) && !"1".equals(lsdsGoods.getDealStatus()) && (lsdsGoods.getDealStatus()).equals(dealStatus)){
//                resultmodel.setErrMsg("请勿重复提交！");
//                resultmodel.setSucceed(false);
//                return resultmodel;
//            }

            //获取平台3pl信息
            /*PlatformUmCompanyFilter companyFilter = new PlatformUmCompanyFilter();
            companyFilter.setCompanyId("2");
            ResultMode<PlatformUmCompany> companyResultMode = platformCommonInterClient
                    .getCompanyAndAdminInfoByCompanyId(companyFilter);*/

            boolean sign = false;
            PlatformUmCompanyFilter companyFilter = new PlatformUmCompanyFilter();
            companyFilter.setCompanyId(voLsdsGoods.getCompanyId());
            ResultMode<PlatformUmCompany> companyResultMode = platformCommonInterClient.getCompanyAndAdminInfoByCompanyId(companyFilter);
            if(companyResultMode.getSucceed() && "1".equals(companyResultMode.getModel().get(0).getPlatformFlag())){
                sign = true;
            }
            if(null != lsdsGoods) {
                log.info(">>>>>>1111>>>>>");
                //货源审核完成，不允许再进行更新操作
                if(!StringUtils.isEmpty(lsdsGoods.getDealStatus()) && !LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus1.getCode().equals(lsdsGoods.getDealStatus()) && !LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus2.getCode().equals(lsdsGoods.getDealStatus())){
                    resultmodel.setErrMsg("货源已经完成审核，或已过期、已关闭、已删除！");
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }
                log.info(">>>dealStatus:{}",dealStatus);
                //更新货源数据
                boolean dealTemp = null != dealStatus && (LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus) || LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus4.getCode().equals(dealStatus));
                if (dealTemp) {
                    //设置审核时间
                    voLsdsGoods.setAuditDate(DateUtils.getDateTime());
                    //审核记录状态【1-审核通过,2-审核不通过】
                    if(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus)){
                        voLsdsGoods.setRecordStatus(LsdsEnum.LsdsgoodsRecordStatusEnum.recordStatus1.getCode());
                        voLsdsGoods.setCurrentOfferStartDate(DateUtils.getDateTime());
                    }else if(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus4.getCode().equals(dealStatus)){
                        voLsdsGoods.setRecordStatus(LsdsEnum.LsdsgoodsRecordStatusEnum.recordStatus2.getCode());
                    }
                }
                if(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus)){
                    // 兼容V27.4审核隐藏按xx上浮，默认为按单价上浮，值为0
                    voLsdsGoods.setFeeClearType("2");
                    voLsdsGoods.setFeeClearValue(BigDecimal.ZERO);
                }
                voLsdsGoods.setModifyBy(userInfoFromCache.getUserBaseId());
                voLsdsGoods.setModifyDate(DateUtils.getDateTime());
                boolean isUpdate = false;
                if(!StringUtils.isEmpty(voLsdsGoods.getContractType()) && "2".equals(voLsdsGoods.getContractType())){
                    isUpdate = true;
                    if("3".equals(dealStatus)){
                        voLsdsGoods.setDealStatus("5");
                    }
                }else if(StringUtils.isEmpty(voLsdsGoods.getContractType())  || !"2".equals(voLsdsGoods.getContractType())){
                    isUpdate = true;
                }

                if(isUpdate){
                    if(!StringUtils.isEmpty(dealStatus) && ("2".equals(dealStatus) || "1".equals(dealStatus))){
                        //更新货源地址信息
                        //1.先删除旧地址数据
                        GoodsAddressMapper addressDao = SpringContextUtil.getBeanByClass(GoodsAddressMapper.class);
                        if(!StringUtils.isEmpty(lsdsGoods.getGoodsAddressId())){
                            addressDao.deleteLsdsGoodsAddressById(lsdsGoods.getGoodsAddressId());
                        }

                        //#region 封装货源地址信息
                        LsdsGoodsAddress goodsAddress =  LsdsGoodsAddress.of();
                        String addressId = String.valueOf(IdUtil.generateId());
                        goodsAddress.setSourceId(voLsdsGoods.getGoodsId());
                        goodsAddress.setGoodsAddressId(addressId);
                        goodsAddress.setSendAddrShortName(voLsdsGoods.getSendAddrShortName());
                        goodsAddress.setSendAddrProvince(voLsdsGoods.getSendAddrProvince());
                        goodsAddress.setSendAddrCity(voLsdsGoods.getSendAddrCity());
                        goodsAddress.setSendAddrArea(voLsdsGoods.getSendAddrArea());
                        goodsAddress.setSendAddrStreet(voLsdsGoods.getSendAddrStreet());
                        goodsAddress.setSendAddrProvinceName(voLsdsGoods.getSendAddrProvinceName());
                        goodsAddress.setSendAddrCityName(voLsdsGoods.getSendAddrCityName());
                        goodsAddress.setSendAddrAreaName(voLsdsGoods.getSendAddrAreaName());
                        goodsAddress.setSendAddrStreetName(voLsdsGoods.getSendAddrStreetName());
                        goodsAddress.setSendAddrDetail(voLsdsGoods.getSendAddrDetail());
                        goodsAddress.setSendLinker(voLsdsGoods.getSendLinker());
                        goodsAddress.setSendPhoneNumber(voLsdsGoods.getSendPhoneNumber());
                        goodsAddress.setReceiveAddrShortName(voLsdsGoods.getReceiveAddrShortName());
                        goodsAddress.setReceiveAddrProvince(voLsdsGoods.getReceiveAddrProvince());
                        goodsAddress.setReceiveAddrCity(voLsdsGoods.getReceiveAddrCity());
                        goodsAddress.setReceiveAddrArea(voLsdsGoods.getReceiveAddrArea());
                        goodsAddress.setReceiveAddrStreet(voLsdsGoods.getReceiveAddrStreet());
                        goodsAddress.setReceiveAddrProvinceName(voLsdsGoods.getReceiveAddrProvinceName());
                        goodsAddress.setReceiveAddrCityName(voLsdsGoods.getReceiveAddrCityName());
                        goodsAddress.setReceiveAddrAreaName(voLsdsGoods.getReceiveAddrAreaName());
                        goodsAddress.setReceiveAddrStreetName(voLsdsGoods.getReceiveAddrStreetName());
                        goodsAddress.setReceiveAddrDetail(voLsdsGoods.getReceiveAddrDetail());
                        goodsAddress.setReceiveLinker(voLsdsGoods.getReceiveLinker());
                        goodsAddress.setReceivePhoneNumber(voLsdsGoods.getReceivePhoneNumber());
                        goodsAddress.setCreateBy(userInfoFromCache.getUserBaseId());
                        goodsAddress.setCreateDate(DateUtils.getDateTime());
                        goodsAddress.setModifyBy(userInfoFromCache.getUserBaseId());
                        goodsAddress.setModifyDate(DateUtils.getDateTime());
                        goodsAddress.setAddressType("1");
                        //#endregion
                        //2.写入货源地址信息
                        addressDao.add(goodsAddress);
                        voLsdsGoods.setGoodsAddressId(addressId);
                    }

                    //更新货源主信息
                    flag = dao.modify(voLsdsGoods);

                    //保存业务扩展信息
//                    saveTmsBizExtend(voLsdsGoods);

                    if (!StrUtil.equals(lsdsGoods.getDealStatus(), voLsdsGoods.getDealStatus())) {
                        // 广播通知货源状态变更
                        GoodsKafkaNotice goodsKafkaNotice = BeanUtil.copyProperties(voLsdsGoods, GoodsKafkaNotice.class);
                        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD,JSONUtil.toJsonStr(goodsKafkaNotice));
                    }

                    // ES中更新货源对应亏涨吨 货源保存、发布，两个场景才更新
                    if (LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus1.getCode().equals(dealStatus)
                        || LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus2.getCode().equals(dealStatus)) {
                        Boolean saveEsSuccess = dcsBusiness.saveLsdsGoodsDeductible(voLsdsGoods.getLsdsGoodsDeductible());
                        if (!saveEsSuccess) {
                            throw new LsdsWlydException(LsdsStatusCodeEnum.BUSS_ERROR_BLSDS0300);
                        }
                    }
                    // ES中更新货源对应亏涨吨 发布货源自动审核场景
                    if (LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus)
                        && ObjectUtil.isNotEmpty(voLsdsGoods.getLsdsGoodsDeductible())) {
                        Boolean saveEsSuccess = dcsBusiness.saveLsdsGoodsDeductible(voLsdsGoods.getLsdsGoodsDeductible());
                        if (!saveEsSuccess) {
                            throw new LsdsWlydException(LsdsStatusCodeEnum.BUSS_ERROR_BLSDS0300);
                        }
                    }

                    if (null != vo.getGoodsSplitList() && vo.getGoodsSplitList().size() > 0) {
                        //清空当前分段信息
                        GoodsSplitMapper splitDao = SpringContextUtil.getBeanByClass(GoodsSplitMapper.class);
                        splitDao.batchDelete(voLsdsGoods.getGoodsId());

                        //清空当前分段地址信息
                        GoodsAddressMapper addressDao = SpringContextUtil.getBeanByClass(GoodsAddressMapper.class);
                        LsdsGoodsAddress lga =  LsdsGoodsAddress.of();
                        lga.setSourceId(voLsdsGoods.getGoodsId());
                        lga.setAddressType("2");
                        addressDao.deleteGoodsAddressList(lga);

                        //插入分段信息
                        List<LsdsGoodsAddress> splitAddressList = new ArrayList<>();
                        for (LsdsGoodsSplit goodsSplit : vo.getGoodsSplitList()) {
                            String splitGoodsAddressId = String.valueOf(IdUtil.generateId());
                            goodsSplit.setGoodsSplitId(String.valueOf(IdUtil.generateId()));
                            goodsSplit.setGoodsAddressId(splitGoodsAddressId);
                            goodsSplit.setGoodsId(voLsdsGoods.getGoodsId());
                            goodsSplit.setCreateBy(userInfoFromCache.getUserBaseId());
                            goodsSplit.setCreateDate(DateUtils.getDateTime());
                            goodsSplit.setModifyBy(userInfoFromCache.getUserBaseId());
                            goodsSplit.setModifyDate(DateUtils.getDateTime());
                            //#region 封装分段地址信息
                            LsdsGoodsAddress splitAddress =  LsdsGoodsAddress.of();
                            splitAddress.setGoodsAddressId(splitGoodsAddressId);
                            splitAddress.setSourceId(voLsdsGoods.getGoodsId());
                            splitAddress.setSendAddrShortName(goodsSplit.getSendAddrShortName());
                            splitAddress.setSendAddrProvince(goodsSplit.getSendAddrProvince());
                            splitAddress.setSendAddrCity(goodsSplit.getSendAddrCity());
                            splitAddress.setSendAddrArea(goodsSplit.getSendAddrArea());
                            splitAddress.setSendAddrStreet(goodsSplit.getSendAddrStreet());
                            splitAddress.setSendAddrProvinceName(goodsSplit.getSendAddrProvinceName());
                            splitAddress.setSendAddrCityName(goodsSplit.getSendAddrCityName());
                            splitAddress.setSendAddrAreaName(goodsSplit.getSendAddrAreaName());
                            splitAddress.setSendAddrStreetName(goodsSplit.getSendAddrStreetName());
                            splitAddress.setSendAddrDetail(goodsSplit.getSendAddrDetail());
                            splitAddress.setSendLinker(goodsSplit.getSendLinker());
                            splitAddress.setSendPhoneNumber(goodsSplit.getSendPhoneNumber());
                            splitAddress.setReceiveAddrShortName(goodsSplit.getReceiveAddrShortName());
                            splitAddress.setReceiveAddrProvince(goodsSplit.getReceiveAddrProvince());
                            splitAddress.setReceiveAddrCity(goodsSplit.getReceiveAddrCity());
                            splitAddress.setReceiveAddrArea(goodsSplit.getReceiveAddrArea());
                            splitAddress.setReceiveAddrStreet(goodsSplit.getReceiveAddrStreet());
                            splitAddress.setReceiveAddrProvinceName(goodsSplit.getReceiveAddrProvinceName());
                            splitAddress.setReceiveAddrCityName(goodsSplit.getReceiveAddrCityName());
                            splitAddress.setReceiveAddrAreaName(goodsSplit.getReceiveAddrAreaName());
                            splitAddress.setReceiveAddrStreetName(goodsSplit.getReceiveAddrStreetName());
                            splitAddress.setReceiveAddrDetail(goodsSplit.getReceiveAddrDetail());
                            splitAddress.setReceiveLinker(goodsSplit.getReceiveLinker());
                            splitAddress.setReceivePhoneNumber(goodsSplit.getReceivePhoneNumber());
                            splitAddress.setAddressType("2");
                            splitAddress.setCreateBy(userInfoFromCache.getUserBaseId());
                            splitAddress.setCreateDate(DateUtils.getDateTime());
                            splitAddress.setModifyBy(userInfoFromCache.getUserBaseId());
                            splitAddress.setModifyDate(DateUtils.getDateTime());
                            splitAddressList.add(splitAddress);
                            //#endregion
                        }
                        //写入货源分段信息
                        splitDao.batchAdd(vo.getGoodsSplitList());
                        //写入货源分段地址信息
                        addressDao.insertBatch(splitAddressList);
                    }

                    if (null != vo.getAssignSupplierList() && vo.getAssignSupplierList().size() > 0) {
                        //先清空当前承运商信息，再写入
                        GoodsAssignSupplierMapper supplierDao = SpringContextUtil.getBeanByClass(GoodsAssignSupplierMapper.class);
                        supplierDao.batchDelete(voLsdsGoods.getGoodsId());

                        //插入承运商信息
                        for (LsdsGoodsAssignSupplier supplier : vo.getAssignSupplierList()) {
                            supplier.setGoodsAssignSupplierId(String.valueOf(IdUtil.generateId()));
                            supplier.setGoodsId(voLsdsGoods.getGoodsId());
                            supplier.setCreateBy(userInfoFromCache.getUserBaseId());
                            supplier.setCreateDate(DateUtils.getDateTime());
                            supplier.setModifyBy(userInfoFromCache.getUserBaseId());
                            supplier.setModifyDate(DateUtils.getDateTime());
                            supplier.setOfferRound(lsdsGoods.getOfferCurrentRounds());
                        }
                        supplierDao.batchAdd(vo.getAssignSupplierList());
                    }
                }

                //同步货源操作记录
                lsdsOperRecord(vo, dealStatus, userInfoFromCache);


                //审核通过或不通过发送站内信及短信    【不发送 信息】 公司id为 2 sysadmin  发布类型 1、货源审核已通过 3、转交易  "2".equals(vo.getLsdsGoods().getCompanyId())
                if((LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus) && !( sign && ( "3".equals(voLsdsGoods.getReleaseType().toString()) || "1".equals(voLsdsGoods.getReleaseType().toString()) ) )) || LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus4.getCode().equals(dealStatus)){
                    //发送站内信前先校验指定单价货源审核时的上浮值是否大于指定的单价
//                    if (LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus)) {
//                        if(StringUtils.isEmpty(vo.getLsdsGoods().getContractType())  || !"2".equals(vo.getLsdsGoods().getContractType())){
//                            if (!StringUtils.isEmpty(lsdsGoods.getEnquiryType()) && "2".equals(lsdsGoods.getEnquiryType()) && "2".equals(vo.getLsdsGoods().getFeeClearType()) && !StringUtils.isEmpty(lsdsGoods.getEnquiryTypeBaseOpenTicket()) && (lsdsGoods.getEnquiryTypeBaseOpenTicket().subtract(vo.getLsdsGoods().getFeeClearValue())).compareTo(BigDecimal.ZERO) < 0) {
//                                resultmodel.setErrMsg("上浮值:必须小于等于发货方指定的单价[" + lsdsGoods.getEnquiryTypeBaseOpenTicket() +"]！");
//                                resultmodel.setSucceed(false);
//                                return resultmodel;
//                            }
//                        }
//                    }
                    Map<String, String> param = new HashMap<String, String>();
                    String phoneNumber = lsdsGoods.getSendPhoneNumber();
                    String userId = lsdsGoods.getSubmitBy();
                    String companyId = lsdsGoods.getCompanyId();
                    String templateId = "";
                    if(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus)){
                        templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_AUDIT_PASS.getCode();
                    }else{
                        templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_AUDIT_NO_PASS.getCode();
                    }
                    param.put("goodsNo",lsdsGoods.getGoodsId());
                    param.put("sendShortName",lsdsGoods.getSendAddrShortName());
                    param.put("receiveShortName",lsdsGoods.getReceiveAddrShortName());
                    //发送站内信
                    String finalTemplateId = templateId;
                    CompletableFuture.runAsync(() -> sendMsg(phoneNumber, userId, companyId, finalTemplateId, param), asyncServiceExecutor);
                }

                //货源审核通过，指定了承运商的发送站内信给相关承运商
                if(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus) && (StringUtils.isEmpty(voLsdsGoods.getContractType()) || !"2".equals(voLsdsGoods.getContractType()))){
                    GoodsAssignSupplierMapper supplierDao = SpringContextUtil.getBeanByClass(GoodsAssignSupplierMapper.class);
                    LsdsGoodsAssignSupplier supplier = new LsdsGoodsAssignSupplier();
                    supplier.setGoodsId(lsdsGoods.getGoodsId());
                    supplier.setOfferRound(lsdsGoods.getOfferCurrentRounds());
                    List<LsdsGoodsAssignSupplier> supplierList = supplierDao.getPreviousSupplierList(supplier);
                    if (null != supplierList && supplierList.size() > 0) {
                        for(LsdsGoodsAssignSupplier las: supplierList){
                            Map<String, String> param = new HashMap<String, String>();
                            String phoneNumber = las.getCompanyPhoneNumber();
                            String companyId = las.getCompanyId();
                            String templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_ASSIGN_SUPPLIER.getCode();
                            param.put("goodsNo",lsdsGoods.getGoodsId());
                            //发送站内信
                            CompletableFuture.runAsync(() -> sendMsg(phoneNumber,"",companyId,templateId,param), asyncServiceExecutor);
                        }

                        //指派承运商，生成报价单信息
                        lsdsGoodsOfferService.generateSupplierOffer(lsdsGoods, supplierList, lsdsGoods.getOfferCurrentRounds());

                    }
                }

                //closebook模式下单
                if (LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(dealStatus) && !StringUtil.isEmpty(voLsdsGoods.getContractType()) && "2".equals(voLsdsGoods.getContractType())) {
                    OmsOrderVo omsOrderVo = new OmsOrderVo();
                    //封装货源主信息
                    omsOrderVo.setGoodsId(lsdsGoods.getGoodsId());
                    omsOrderVo.setContractId(voLsdsGoods.getContractId());
                    omsOrderVo.setContractType(voLsdsGoods.getContractType());
                    omsOrderVo.setContractName(voLsdsGoods.getContractName());
                    omsOrderVo.setOtherClearType(voLsdsGoods.getOtherClearType());
                    omsOrderVo.setFeeClearType(voLsdsGoods.getFeeClearType());
                    omsOrderVo.setFeeClearValue(voLsdsGoods.getFeeClearValue());
                    int roundingMode = amountRoundingModeService.getRoundingMode(lsdsGoods.getGoodsId());
                    if(!StringUtils.isEmpty(voLsdsGoods.getChargeType()) && "1".equals(voLsdsGoods.getChargeType())){
                        omsOrderVo.setOrderAmount(new BigDecimal(lsdsGoods.getContainerAmount()).multiply(voLsdsGoods.getUnitPrice()).setScale(2, roundingMode));
                    }else{
                        omsOrderVo.setOrderAmount(lsdsGoods.getWeightSum().multiply(voLsdsGoods.getUnitPrice()).setScale(2, roundingMode));
                    }
                    if(!StringUtils.isEmpty(lsdsGoods.getSalesContractNumber())){
                        omsOrderVo.setSalesContractNumber(lsdsGoods.getSalesContractNumber());
                    }
                    omsOrderVo.setContractPrice(voLsdsGoods.getUnitPrice());
                    omsOrderVo.setOperationMainBodyId(voLsdsGoods.getOperationMainBodyId());
                    omsOrderVo.setCarrierAppraiseStatus("21");

                    //由前端传值
                    /*omsOrderVo.setCarrierCompanyId("2");
                    omsOrderVo.setCarrierName(companyResultMode.getModel().get(0).getCompanyShortName());
                    omsOrderVo.setRealityCarrierCompanyId("2");
                    omsOrderVo.setRealityCarrierName(companyResultMode.getModel().get(0).getCompanyShortName());*/

                    //change lyh
                    omsOrderVo.setCarrierCompanyId(voLsdsGoods.getCarrierCompanyId());
//                    omsOrderVo.setCarrierName(voLsdsGoods.getCarrierName());
                    omsOrderVo.setRealityCarrierCompanyId(voLsdsGoods.getCarrierCompanyId());
//                    omsOrderVo.setRealityCarrierName(voLsdsGoods.getCarrierName());

                    //查询承运企业简称
                    PlatformUmCompanyFilter filter = new PlatformUmCompanyFilter();
                    filter.setCompanyId(voLsdsGoods.getCarrierCompanyId());
                    ResultMode<PlatformUmCompany> carrierResultMode = platformCommonInterClient.getCompanyInfoByCondition(filter);
                    if (carrierResultMode != null && IterUtil.isNotEmpty(carrierResultMode.getModel())) {
                        PlatformUmCompany carrierCompany = IterUtil.getFirst(carrierResultMode.getModel());
                        if (carrierCompany != null) {
                            omsOrderVo.setCarrierName(carrierCompany.getCompanyShortName());
                            omsOrderVo.setRealityCarrierName(carrierCompany.getCompanyShortName());
                        }
                    }

                    omsOrderVo.setNeedChangeDeal("21");
                    omsOrderVo.setOrderAgreement("1");
                    omsOrderVo.setOrderAppointCarrier("21");
                    omsOrderVo.setOrderPayStatus("1");
                    omsOrderVo.setOrderSource("1");
                    omsOrderVo.setOrderStatus("200");
                    omsOrderVo.setOrderType("1");
                    omsOrderVo.setShipperAppraiseStatus("21");
                    omsOrderVo.setShipperUserBaseId(lsdsGoods.getCreateBy());
                    omsOrderVo.setShipperCompanyId(lsdsGoods.getCompanyId());
                    omsOrderVo.setShipperName(lsdsGoods.getCompanyShortName());
                    omsOrderVo.setGoodsType(lsdsGoods.getGoodsType());
                    omsOrderVo.setGoodsName(lsdsGoods.getGoodsName());
                    omsOrderVo.setGoodsDesc(lsdsGoods.getGoodsDesc());
                    omsOrderVo.setWeightSum(lsdsGoods.getWeightSum());
                    omsOrderVo.setVolumeSum(lsdsGoods.getVolumeSum());
                    omsOrderVo.setTotalGoods(lsdsGoods.getTotalGoods());
                    omsOrderVo.setPackType(lsdsGoods.getPackType());
                    omsOrderVo.setTransportationType(lsdsGoods.getTransportationType());
                    omsOrderVo.setEnquiryType(lsdsGoods.getEnquiryType());
//                    omsOrderVo.setEnquiryTypeBasePrice(lsdsGoods.getEnquiryTypeBasePrice());
                    omsOrderVo.setEnquiryTypeBaseTaxRate(lsdsGoods.getEnquiryTypeBaseTaxRate());
                    omsOrderVo.setEnquiryTypeBaseOpenTicket(voLsdsGoods.getUnitPrice());
                    omsOrderVo.setOtherClearWeight(voLsdsGoods.getOtherClearWeight());
                    omsOrderVo.setClearMonthDay(voLsdsGoods.getClearMonthDay());
                    omsOrderVo.setChargeType(voLsdsGoods.getChargeType());
                    omsOrderVo.setEnquiryRange(Integer.parseInt(lsdsGoods.getEnquiryRange()));
                    omsOrderVo.setAssignCarType(lsdsGoods.getAssignCarType());
                    omsOrderVo.setAssignCarLength(lsdsGoods.getAssignCarLength());
                    omsOrderVo.setAssignCarPlateNumber(lsdsGoods.getAssignCarPlateNumber());
                    omsOrderVo.setReleaseDate(lsdsGoods.getReleaseDate());
                    omsOrderVo.setArriveDate(lsdsGoods.getArriveDate());
                    omsOrderVo.setValidityDate(lsdsGoods.getValidityDate());
                    omsOrderVo.setOtherIsInvoice(lsdsGoods.getOtherIsInvoice());
                    omsOrderVo.setOtherKuiTonsRatio(lsdsGoods.getOtherKuiTonsRatio());
                    omsOrderVo.setOtherReceiptType(lsdsGoods.getOtherReceiptType());
                    omsOrderVo.setOtherRemark(lsdsGoods.getOtherRemark());
                    omsOrderVo.setCustomerId(lsdsGoods.getCustomerId());
                    omsOrderVo.setCustomerType(lsdsGoods.getCustomerType());
                    omsOrderVo.setCustomerName(lsdsGoods.getCustomerName());
                    omsOrderVo.setContainerType(lsdsGoods.getContainerType());
                    omsOrderVo.setContainerNumber(lsdsGoods.getContainerAmount());
                    omsOrderVo.setCreateBy(userInfoFromCache.getUserBaseId());
                    omsOrderVo.setCreateDate(lsdsGoods.getCreateDate());
                    omsOrderVo.setModifyBy(userInfoFromCache.getUserBaseId());
                    omsOrderVo.setModifyDate(DateUtils.getDateTime());
                    omsOrderVo.setFreightType(voLsdsGoods.getFreightType());
                    omsOrderVo.setPrepaidFreight(BigDecimal.ZERO);
                    omsOrderVo.setTransactionContractingBodyId(voLsdsGoods.getTransactionContractingBodyId());
                    omsOrderVo.setNetworkMainBodyId(voLsdsGoods.getNetworkMainBodyId());
                    omsOrderVo.setNetworkMainBodyName(voLsdsGoods.getNetworkMainBodyName());

                    //封装货源地址信息
                    OmsOrderAddress orderAddress = new OmsOrderAddress();
                    orderAddress.setStartSendPhoneNumber(lsdsGoods.getSendPhoneNumber());
                    orderAddress.setSendAddrProvince(lsdsGoods.getSendAddrProvince());
                    orderAddress.setSendAddrCity(lsdsGoods.getSendAddrCity());
                    orderAddress.setSendAddrArea(lsdsGoods.getSendAddrArea());
//                orderAddress.setSendAddrStreet();
                    orderAddress.setSendAddrProvinceName(lsdsGoods.getSendAddrProvinceName());
                    orderAddress.setSendAddrCityName(lsdsGoods.getSendAddrCityName());
                    orderAddress.setSendAddrAreaName(lsdsGoods.getSendAddrAreaName());
//                orderAddress.setSendAddrStreetName();
                    orderAddress.setSendAddrDetail(lsdsGoods.getSendAddrDetail());
                    orderAddress.setSendAddrShorthand(lsdsGoods.getSendAddrShortName());
                    orderAddress.setStartSendLinker(lsdsGoods.getSendLinker());
                    orderAddress.setReceiveAddrShorthand(lsdsGoods.getReceiveAddrShortName());
                    orderAddress.setReceiveAddrProvince(lsdsGoods.getReceiveAddrProvince());
                    orderAddress.setReceiveAddrCity(lsdsGoods.getReceiveAddrCity());
                    orderAddress.setReceiveAddrArea(lsdsGoods.getReceiveAddrArea());
                    orderAddress.setReceiveAddrStreet(lsdsGoods.getReceiveAddrStreet());
                    orderAddress.setReceiveAddrProvinceName(lsdsGoods.getReceiveAddrProvinceName());
                    orderAddress.setReceiveAddrCityName(lsdsGoods.getReceiveAddrCityName());
                    orderAddress.setReceiveAddrAreaName(lsdsGoods.getReceiveAddrAreaName());
                    orderAddress.setReceiveAddrStreetName(lsdsGoods.getReceiveAddrStreetName());
                    orderAddress.setReceiveAddrDetail(lsdsGoods.getReceiveAddrDetail());
                    orderAddress.setEndReceiveLinker(lsdsGoods.getReceiveLinker());
                    orderAddress.setEndReceivePhoneNumber(lsdsGoods.getReceivePhoneNumber());
                    orderAddress.setCreateBy(userInfoFromCache.getUserBaseId());
                    orderAddress.setCreateDate(DateUtils.getDateTime());
                    orderAddress.setModifyBy(userInfoFromCache.getUserBaseId());
                    orderAddress.setModifyDate(DateUtils.getDateTime());
                    omsOrderVo.setOrderAddress(orderAddress);

                    //封装货源分段地址信息
                    //返回分段信息
                    GoodsSplitMapper splitDao = SpringContextUtil.getBeanByClass(GoodsSplitMapper.class);
                    List<LsdsGoodsSplit> splitList = new ArrayList<>();
                    List<OmsOrderVo> omsOrders = new ArrayList<>();
                    if (null != lsdsGoods && !StringUtils.isEmpty(lsdsGoods.getChildGoodsId())) {
                        //存在子货源号，则该货源进行了下游询价，分段信息则取自子货源的分段信息
                        splitList = splitDao.getSplitList(lsdsGoods.getChildGoodsId());
                    } else {
                        //不存在子货源号，则为非下游询价货源
                        splitList = splitDao.getSplitList(lsdsGoods.getGoodsId());
                    }
                    if (null != splitList && splitList.size() > 0) {
                        for (LsdsGoodsSplit split : splitList) {
                            OmsOrderVo omsOrder = new OmsOrderVo();
                            omsOrder.setGoodsId(lsdsGoods.getGoodsId());
                            omsOrder.setGoodsId(lsdsGoods.getGoodsId());
                            omsOrder.setCarrierAppraiseStatus("21");

                           /* omsOrder.setCarrierCompanyId("2");
                            omsOrder.setRealityCarrierCompanyId("2");*/
                            omsOrder.setNeedChangeDeal("21");
                            omsOrder.setOrderAgreement("1");
                            omsOrder.setOrderAppointCarrier("21");
                            omsOrder.setOrderPayStatus("1");
                            omsOrder.setOrderSource("1");
                            omsOrder.setOrderStatus("100");
                            omsOrder.setOrderType("1");
                            omsOrder.setShipperAppraiseStatus("21");
                            omsOrder.setCreateBy(userInfoFromCache.getUserBaseId());
                            omsOrder.setCreateDate(DateUtils.getDateTime());
                            omsOrder.setModifyBy(userInfoFromCache.getUserBaseId());
                            omsOrder.setModifyDate(DateUtils.getDateTime());
                            omsOrder.setShipperUserBaseId(userInfoFromCache.getUserBaseId());
                            omsOrder.setShipperCompanyId(lsdsGoods.getCompanyId());
                            omsOrder.setGoodsType(lsdsGoods.getGoodsType());
                            omsOrder.setGoodsName(lsdsGoods.getGoodsName());
                            omsOrder.setGoodsDesc(lsdsGoods.getGoodsDesc());
                            omsOrder.setWeightSum(lsdsGoods.getWeightSum());
                            omsOrder.setVolumeSum(lsdsGoods.getVolumeSum());
                            omsOrder.setTotalGoods(lsdsGoods.getTotalGoods());
                            omsOrder.setPackType(lsdsGoods.getPackType());
                            omsOrder.setTransportationType(split.getTransportationType());
                            omsOrder.setEnquiryType(lsdsGoods.getEnquiryType());
                            omsOrder.setEnquiryTypeBasePrice(lsdsGoods.getEnquiryTypeBasePrice());
                            omsOrder.setEnquiryTypeBaseTaxRate(lsdsGoods.getEnquiryTypeBaseTaxRate());
                            omsOrder.setEnquiryTypeBaseOpenTicket(lsdsGoods.getEnquiryTypeBaseOpenTicket());
                            omsOrder.setEnquiryRange(Integer.parseInt(lsdsGoods.getEnquiryRange()));
                            omsOrder.setAssignCarType(lsdsGoods.getAssignCarType());
                            omsOrder.setAssignCarLength(lsdsGoods.getAssignCarLength());
                            omsOrder.setAssignCarPlateNumber(lsdsGoods.getAssignCarPlateNumber());
                            omsOrder.setReleaseDate(lsdsGoods.getReleaseDate());
                            omsOrder.setArriveDate(lsdsGoods.getArriveDate());
                            omsOrder.setValidityDate(lsdsGoods.getValidityDate());
                            omsOrder.setOtherIsInvoice(lsdsGoods.getOtherIsInvoice());
                            omsOrder.setOtherKuiTonsRatio(lsdsGoods.getOtherKuiTonsRatio());
                            omsOrder.setOtherReceiptType(lsdsGoods.getOtherReceiptType());
                            omsOrder.setOtherClearType(lsdsGoods.getOtherClearType());
                            omsOrder.setOtherRemark(lsdsGoods.getOtherRemark());
                            omsOrder.setFeeClearType(lsdsGoods.getFeeClearType());
                            omsOrder.setFeeClearValue(lsdsGoods.getFeeClearValue());
                            omsOrder.setCustomerType(lsdsGoods.getCustomerType());
                            omsOrder.setCustomerName(lsdsGoods.getCustomerName());
                            omsOrder.setContainerType(lsdsGoods.getContainerType());
                            omsOrder.setTransactionContractingBodyId(voLsdsGoods.getTransactionContractingBodyId());
                            omsOrder.setNetworkMainBodyId(voLsdsGoods.getNetworkMainBodyId());
                            omsOrder.setNetworkMainBodyName(voLsdsGoods.getNetworkMainBodyName());

                            OmsOrderAddress orderAddr = new OmsOrderAddress();
                            orderAddr.setSendAddrProvince(split.getSendAddrProvince());
                            orderAddr.setSendAddrCity(split.getSendAddrCity());
                            orderAddr.setSendAddrArea(split.getSendAddrArea());
//                      orderAddr.setSendAddrStreet();
                            orderAddr.setSendAddrProvinceName(split.getSendAddrProvinceName());
                            orderAddr.setSendAddrCityName(split.getSendAddrCityName());
                            orderAddr.setSendAddrAreaName(split.getSendAddrAreaName());
//                      orderAddr.setSendAddrStreetName();
                            orderAddr.setSendAddrDetail(split.getSendAddrDetail());
                            orderAddr.setSendAddrShorthand(split.getSendAddrShortName());
                            orderAddr.setStartSendLinker(lsdsGoods.getSendLinker());
                            orderAddr.setStartSendPhoneNumber(lsdsGoods.getSendPhoneNumber());
                            orderAddr.setReceiveAddrShorthand(split.getReceiveAddrShortName());
                            orderAddr.setReceiveAddrProvince(split.getReceiveAddrProvince());
                            orderAddr.setReceiveAddrCity(split.getReceiveAddrCity());
                            orderAddr.setReceiveAddrArea(split.getReceiveAddrArea());
                            orderAddr.setReceiveAddrStreet(split.getReceiveAddrStreet());
                            orderAddr.setReceiveAddrProvinceName(split.getReceiveAddrProvinceName());
                            orderAddr.setReceiveAddrCityName(split.getReceiveAddrCityName());
                            orderAddr.setReceiveAddrAreaName(split.getReceiveAddrAreaName());
                            orderAddr.setReceiveAddrStreetName(split.getReceiveAddrStreetName());
                            orderAddr.setReceiveAddrDetail(split.getReceiveAddrDetail());
                            orderAddr.setEndReceiveLinker(lsdsGoods.getReceiveLinker());
                            orderAddr.setEndReceivePhoneNumber(lsdsGoods.getReceivePhoneNumber());
                            orderAddr.setCreateBy(userInfoFromCache.getUserBaseId());
                            orderAddr.setCreateDate(DateUtils.getDateTime());
                            orderAddr.setModifyBy(userInfoFromCache.getUserBaseId());
                            orderAddr.setModifyDate(DateUtils.getDateTime());
                            omsOrder.setOrderAddress(orderAddr);
                            omsOrders.add(omsOrder);
                        }
                    }
                    omsOrderVo.setOmsOrder(omsOrders);
                    String string = JSONObject.toJSONString(omsOrderVo);
                    log.info("closebook下单实体参数：" + string);
                    //执行closebook模式下单
                    if(flag){
                        log.info("closebook下单开始！" );
                        try {
                            tempResult = omsOrderInter.addCloseOrder(omsOrderVo);
                            omsOrderVo.setOrderId(omsOrderVo.getOrderId());

                            // 合同创建通知
                            kafkaSendContractCreateNotice(omsOrderVo.getOrderId(),ContractEnum.ConTypeEnum.CON_ORDER.getCode());

                            if(!tempResult.getSucceed()){
                                throw new Exception("closebook下单失败:" + tempResult);
                            }
                            log.info("closebook下单结束!");
                        } catch (Exception e) {
                            log.error("closebook下单异常：", e);
                            throw new Exception("调用oms下单接口异常,closebook下单失败!");
                        }
                    }
                }
                if (LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus4.getCode().equals(dealStatus)) {
                    if (null != lsdsGoods.getOrderId()) {
                        String topic = SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.LSDS_TO_OMS_ORDER_STATUS.getTopicName();
                        List<Map<String, String>> mapList = new ArrayList<>();
                        Map<String, String> map = new HashMap<>();
                        map.put("changeDealGoodsId", lsdsGoods.getGoodsId());
                        map.put("orderId", lsdsGoods.getOrderId());
                        mapList.add(map);
                        JSONObject json = new JSONObject();
                        json.put("exlsdsOrderIds", mapList);
                        lsdsKafkaSender.send(topic, json.toJSONString());
                        log.info("货源审核不通过，通知oms系统成功。内容如下：{}", json);
                    }

                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("修改货源信息异常:" + ex);
        }finally{
            distributedLocker.unlock(GOOD_UPDATE_LOCK  + voLsdsGoods.getGoodsId());
        }
        return resultmodel;
    }
    /**
    * @Description:货源操作动作记录
    * @Param: [vo, dealStatus, userInfoFromCache]
    * @return: void
    * @Author: zmj
    * @Date: 2023/7/12
    */
    private void lsdsOperRecord(LsdsGoodsVo vo, String dealStatus, PlatformUmUserbaseinfo userInfoFromCache) {
        GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
        LsdsGoodsRecord lgr =  LsdsGoodsRecord.of();
        lgr.setRecordId(String.valueOf(IdUtil.generateId()));
        lgr.setGoodsId(vo.getLsdsGoods().getGoodsId());
        lgr.setCompanyId(userInfoFromCache.getCompanyId());
        if("1".equals(dealStatus)){
            lgr.setOperateStatus("1");
            lgr.setOperateContent("货源保存");
        }else if("2".equals(dealStatus)){
            lgr.setOperateStatus("2");
            lgr.setOperateContent("货源发布");
        }else if("3".equals(dealStatus)){
            lgr.setOperateStatus("3");
            lgr.setOperateContent("货源审核通过");
        }else if("4".equals(dealStatus)){
            lgr.setOperateStatus("4");
            lgr.setOperateContent("货源审核不通过");
            lgr.setRemark(vo.getLsdsGoods().getAuditRecordStatusMark());
        }
        lgr.setUserId(userInfoFromCache.getUserBaseId());
        lgr.setUserLoginName(userInfoFromCache.getLoginName());
        lgr.setUserName(userInfoFromCache.getUsername());
        recDao.add(lgr);
    }


    /**
     * kafka 发送合同创建通知
     *
     * @param bizId
     * @param nodeType
     */
    private void kafkaSendContractCreateNotice(String bizId, String nodeType) {
        try {
            if (TransactionSynchronizationManager.isSynchronizationActive()){
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCompletion(int status) {
                        if (status != STATUS_COMMITTED) {
                            return;
                        }
                        // 事务正常提交时发送
                        doKafkaSendContractCreateNotice(bizId, nodeType);
                    }
                });
            } else {
                doKafkaSendContractCreateNotice(bizId, nodeType);
            }
        } catch (Exception e) {
            log.error("kafka 消息发送失败", e);
        }
    }

    private void doKafkaSendContractCreateNotice(String bizId, String nodeType) {
        String sendTopic = "contract_sign_notice";
        ContractCreateNoticeFilter noticeFilter = new ContractCreateNoticeFilter();
        noticeFilter.setBizId(bizId);
        noticeFilter.setType(nodeType);
        noticeFilter.setInitiator(JwtUtil.getInstance().getUserBaseIdByToken());
        SpringContextUtil.getBeanByClass(KafkaUtil.class).kafkaProducerSend(sendTopic, JSONUtil.toJsonStr(noticeFilter));
    }

    /**
     * 根据Key删除货源编号  货源信息表信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param goodsId 货源编号  货源信息表
     * @param token   预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【删除成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【删除成功】编码,ResultMode.errMsg为相应【删除成功】描述；
     * 否则ResultMode.succeed=false【删除失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【删除失败】编码,ResultMode.errMsg为相应【删除失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsGoodsDel(String goodsId, String token) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoods lsdsGoods = dao.getModel(goodsId);
            boolean tempTag = null != lsdsGoods && (lsdsGoods.getDealStatus().equals(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus1.getCode()) || lsdsGoods.getDealStatus().equals(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus4.getCode()) || lsdsGoods.getDealStatus().equals(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus6.getCode()) || lsdsGoods.getDealStatus().equals(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus7.getCode()));
            if (tempTag) {
                boolean flag = dao.remove(goodsId);
                //删除货源：推送货源合同归档失效消息
                if (flag) {
                    sendNoticeMessage(goodsId, LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus8.getCode());
                }
                if (flag && null != lsdsGoods && null != lsdsGoods.getOrderId()) {
                    String topic = SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.LSDS_TO_OMS_ORDER_STATUS.getTopicName();
                    List<Map<String, String>> mapList = new ArrayList<>();
                    Map<String, String> map = new HashMap<>();
                    map.put("changeDealGoodsId", lsdsGoods.getGoodsId());
                    map.put("orderId", lsdsGoods.getOrderId());
                    //补传状态：推送订单归档失效消息
                    map.put("dealStatus", LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus8.getCode());
                    mapList.add(map);
                    JSONObject json = new JSONObject();
                    json.put("exlsdsOrderIds", mapList);
                    lsdsKafkaSender.send(topic, json.toJSONString());
                    log.info("货源删除，通知oms系统成功 。内容如下：{}", json);
                }
                resultmodel.setSucceed(flag);
            } else {
                errMsg = "货源当前状态不允许删除！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
            }
            if (!resultmodel.getSucceed()) {
                return resultmodel;
            }
        } catch (Exception ex) {
            errMsg = "根据goodsId删除货源信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }

    /**
     * 分页获取货源编号  货源信息表信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @param token    预留要传递的token，服务端进行判断权限功能
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsGoods> lsdsGoodsPaging(PagingInfo<LsdsGoodsFilter> pageInfo, String token) {

        ResultMode<LsdsGoods> returnmodel = new ResultMode<LsdsGoods>();
        try {
            returnmodel = lsdsGoodsDomainService.pagingLsdsGoods(pageInfo, token);
            return returnmodel;
        } catch (Exception ex) {
            String errMsg = "分页获取货源编号  货源信息表信息异常:" + ex.getMessage();
            returnmodel.setErrMsg(errMsg);
            LogHelper.writeError(errMsg, ex);
            returnmodel.setSucceed(false);
        }
        return returnmodel;
    }


    /**
     * 分页查询货源列表
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     * <AUTHOR>
     * 创建时间 2019/12/2
     */
    public ResultMode<LsdsGoods> getGoodsListPage(PagingInfo<LsdsGoodsFilter> pageInfo) {
        ResultMode<LsdsGoods> returnModel = new ResultMode<LsdsGoods>();

        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<LsdsGoods> goodsList = new ArrayList<>();
        if (null != pageInfo.filterModel && null != pageInfo.filterModel.getCompanyId()) {
            if (LsdsEnum.LsdsgoodsAdminCompanyId.companyId1.getCode().equals(pageInfo.filterModel.getCompanyId())) {
                goodsList = goodsDao.getGoodsListNoAuthority(pageInfo.filterModel);
            } else {
                if(LsdsEnum.LsdsgoodsAdminCompanyId.companyId2.getCode().equals(pageInfo.filterModel.getCompanyId())){
                    goodsList = goodsDao.getGoodsListNoUpAuthority(pageInfo.filterModel);
                }else{
                    String  platformCompanyIdStr=getPlatformCompanyIdStr();
                    pageInfo.filterModel.setPlatformCompanyId(platformCompanyIdStr);
                    goodsList = goodsDao.getGoodsListAuthority(pageInfo.filterModel);

                    //金额取整
                    amountRoundingModeService.setLsdsGoodsScale(goodsList);
                }

            }
        }

        wrapLogisticsPlan(goodsList);

        PageInfo<LsdsGoods> pageInfoList = new PageInfo<>(goodsList);
        Long total = pageInfoList.getTotal();
        returnModel.setTotal(total.intValue());
        returnModel.setModel(goodsList);
        return returnModel;
    }

    private void wrapLogisticsPlan(List<LsdsGoods> goodsList) {
        List<String> goodsIds = goodsList.stream().map(LsdsGoods::getGoodsId).collect(Collectors.toList());
        List<WoaLogisticsPlanBO> logisticsPlanBOS = woaExchangeService.selectByGoodsIds(goodsIds);
        goodsList.forEach(goods -> {
            WoaLogisticsPlanBO woaLogisticsPlanBO = logisticsPlanBOS.stream()
                .filter(plan -> StrUtil.equals(goods.getGoodsId(), plan.getGoodsId()))
                .findFirst().orElseGet(WoaLogisticsPlanBO::new);
            goods.setPlanId(woaLogisticsPlanBO.getPlanId());
        });
    }

    /**
     * 分页查询货源列表
     *
     * @param  filterModel 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     * <AUTHOR>
     * 创建时间 2019/12/2
     */
    public ResultMode<LsdsGoods> getGoodsListAuditData(LsdsGoodsFilter filterModel) {
        ResultMode<LsdsGoods> returnModel = new ResultMode<LsdsGoods>();
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<LsdsGoods> goodsList = new ArrayList<>();
        int count  = goodsDao.getGoodsListNoAuthorityAuditData(filterModel);
        returnModel.setTotal(count);
        return returnModel;
    }
    /**
     * 分页查询货源操作记录列表
     * 创建者：cgb
     * 创建时间：2020/6/9
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoods>}
     */
    public ResultMode<LsdsGoodsRecord> getGoodsRecordListPage(PagingInfo<LsdsGoodsRecordFilter> pageInfo) {
        ResultMode<LsdsGoodsRecord> returnModel = new ResultMode<LsdsGoodsRecord>();
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
        List<LsdsGoodsRecord> goodsRecordList = recDao.getRecordList(pageInfo.filterModel);
        PageInfo<LsdsGoodsRecord> pageInfoList = new PageInfo<>(goodsRecordList);
        Long total = pageInfoList.getTotal();
        returnModel.setTotal(total.intValue());
        returnModel.setModel(goodsRecordList);
        return returnModel;
    }


    /**
     * 查询货源概况信息
     * 创建者: cgb
     * 创建时间: 2019/11/27
     *
     * @param companyId 公司ID
     * @return {@code  ResultMode<Map<String,Object>>} 货源概况信息
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     */
    public ResultMode<Map<String, Object>> lsdsGoodsOverview(String companyId) {
        ResultMode<Map<String, Object>> returnmodel = new ResultMode<Map<String, Object>>();
        try {
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            if (LsdsEnum.LsdsgoodsAdminCompanyId.companyId1.getCode().equals(companyId)) {
                returnmodel.setModel(dao.getGoodsOverviewNoAuthority(companyId));
            } else {
                returnmodel.setModel(dao.getGoodsOverviewAuthority(companyId));
            }

        } catch (Exception ex) {
            String errMsg = "获取货源概况信息异常:" + ex.getMessage();
            returnmodel.setErrMsg(errMsg);
            LogHelper.writeError(errMsg, ex);
            returnmodel.setSucceed(false);
        }
        return returnmodel;
    }


    /**
     * 当货源超过货源有效时间，自动更新货源状态为 已过期
     * 创建者: cgb
     * 创建时间: 2019/11/29
     */

    @XxlJob("lsdsGoodsExpired")
    public void lsdsGoodsExpired() {
//        log.info("start lsdsGoodsExpired...");
        String errMsg = "";
        boolean lockFlag = distributedLocker.tryLock(GOOD_EXPIRED_LOCK, TimeUnit.SECONDS,0,180);
        if(!lockFlag){
            return;
        }
        try {
            //如果货源有效时间小于当前时间，则更新货源状态为已过期
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            List<Map<String,String>> mapList = dao.getExpireGoodsMap();
            List<String> childGoodsIdList = null;
            if(null != mapList && mapList.size() > 0){
                childGoodsIdList = mapList.stream().filter(p -> !StringUtils.isEmpty(p.get("childGoodsId"))).map(p -> p.get("childGoodsId")).collect(Collectors.toList());
            }

            //发送站内信，通知发货方货源过期
            for(Map<String,String> map: mapList){
                Map<String, String> param = new HashMap<String, String>();
                String phoneNumber = map.get("sendPhoneNumber");
                String userId = map.get("submitBy");
                String companyId = map.get("companyId");
                String templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_EXPIRED.getCode();
                param.put("goodsNo",map.get("changeDealGoodsId"));
                param.put("sendShortName",map.get("sendAddrShortName"));
                param.put("receiveShortName",map.get("receiveAddrShortName"));
                //发送站内信
                sendMsg(phoneNumber,userId,companyId,templateId,param);
            }
            if(null != mapList && mapList.size() > 0){
                List<String> goodsIdList = mapList.stream().map(p -> p.get("changeDealGoodsId")).collect(Collectors.toList());
                boolean flag = dao.batchUpdateExpiredGoods(goodsIdList);
                goodsIdList.forEach(goodsId -> sendNoticeMessage(goodsId, DealStatusEnum.EXPIRED.getCode()));
                List<Map<String,String>> forOmsMapList = mapList.stream().filter(p -> !StringUtils.isEmpty(p.get("orderId"))).collect(Collectors.toList());
                if(flag && !CollectionUtils.isEmpty(forOmsMapList)){
                    String topic = SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.LSDS_TO_OMS_ORDER_STATUS.getTopicName();
                    JSONObject json = new JSONObject();
                    json.put("exlsdsOrderIds", mapList);
                    lsdsKafkaSender.send(topic, json.toJSONString());
                    log.info("货源过期，通知oms系统成功 。内容如下：{}", json);
                }
            }

            //同步货源操作记录
            GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
            for(Map<String,String> map: mapList){
                LsdsGoodsRecord lgr =  LsdsGoodsRecord.of();
                lgr.setRecordId(String.valueOf(IdUtil.generateId()));
                lgr.setGoodsId(map.get("changeDealGoodsId"));
                lgr.setCompanyId("system");
                lgr.setOperateStatus("6");
                lgr.setOperateContent("货源过期");
                lgr.setUserId("system");
                lgr.setUserLoginName("system");
                lgr.setUserName("系统");
                recDao.add(lgr);
            }

            //当前更新的货源列表中有下游询价的货源,则同步与母货源相同操作
            if(null != childGoodsIdList && childGoodsIdList.size() > 0){
                List<Map<String,String>> childMapList = dao.getExpireChildGoodsMap(childGoodsIdList);
                for(Map<String,String> map: childMapList){
                    Map<String, String> param = new HashMap<String, String>();
                    String phoneNumber = map.get("sendPhoneNumber");
                    String userId = map.get("submitBy");
                    String companyId = map.get("companyId");
                    String templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_EXPIRED.getCode();
                    param.put("goodsNo",map.get("changeDealGoodsId"));
                    param.put("sendShortName",map.get("sendAddrShortName"));
                    param.put("receiveShortName",map.get("sendPhoneNumber"));
                    //发送站内信
                    sendMsg(phoneNumber,userId,companyId,templateId,param);

                    //同步子货源操作记录
                    LsdsGoodsRecord lgr = LsdsGoodsRecord.of();
                    lgr.setRecordId(String.valueOf(IdUtil.generateId()));
                    lgr.setGoodsId(map.get("changeDealGoodsId"));
                    lgr.setCompanyId("system");
                    lgr.setOperateStatus("6");
                    lgr.setOperateContent("货源过期");
                    lgr.setUserId("system");
                    lgr.setUserLoginName("system");
                    lgr.setUserName("系统");
                    recDao.add(lgr);
                }
                if(null != childMapList && childMapList.size() > 0){
                    boolean flag = dao.batchUpdateExpiredGoods(childGoodsIdList);
                    childGoodsIdList.forEach(goodsId -> sendNoticeMessage(goodsId, DealStatusEnum.EXPIRED.getCode()));
                    List<Map<String,String>> forOmsMapList = childMapList.stream().filter(p -> !StringUtils.isEmpty(p.get("orderId"))).collect(Collectors.toList());
                    if(flag && !CollectionUtils.isEmpty(forOmsMapList)){
                        String topic = SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.LSDS_TO_OMS_ORDER_STATUS.getTopicName();
                        JSONObject json = new JSONObject();
                        json.put("exlsdsOrderIds", childMapList);
                        lsdsKafkaSender.send(topic, json.toJSONString());
                        log.info("货源过期，通知oms系统成功 。内容如下：{}", json);
                    }
                }
            }

            //司机货源处理
            //如果货源有效时间小于当前时间，则更新货源状态为已过期
            List<Map<String, String>> driverGoodsList = driverGoodsMapper.getExpireGoodsMap();

            //发送站内信，通知发货方货源过期
            for (Map<String, String> map : driverGoodsList) {
                String dealStatus = map.get("dealStatus");
                if("20".equals(dealStatus)||"30".equals(dealStatus)){
                    Map<String, String> param = new HashMap<String, String>();
                    String phoneNumber = map.get("sendPhoneNumber");
                    String userId = map.get("submitBy");
                    String companyId = map.get("companyId");
                    String templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_EXPIRED.getCode();
                    param.put("goodsNo", map.get("changeDealGoodsId"));
                    param.put("sendShortName", map.get("sendAddrShortName"));
                    param.put("receiveShortName", map.get("receiveAddrShortName"));
                    //发送站内信
                    sendMsg(phoneNumber, userId, companyId, templateId, param);
                }
            }

            if (null != driverGoodsList && driverGoodsList.size() > 0) {
                List<String> goodsIdList = driverGoodsList.stream().map(p -> p.get("changeDealGoodsId")).collect(Collectors.toList());
                boolean flag2 = driverGoodsMapper.batchUpdateExpiredGoods(goodsIdList);
                goodsIdList.forEach(goodsId -> sendNoticeMessage(goodsId, Constants.GOODS_STATUS_EXPIRE));
            }

            //同步货源操作记录
            for (Map<String, String> map : driverGoodsList) {
                LsdsGoodsRecord lgr = LsdsGoodsRecord.of();
                lgr.setRecordId(String.valueOf(IdUtil.generateId()));
                lgr.setGoodsId(map.get("changeDealGoodsId"));
                lgr.setCompanyId("system");
                lgr.setOperateStatus("70");
                lgr.setOperateContent("货源过期");
                lgr.setUserId("system");
                lgr.setUserLoginName("system");
                lgr.setUserName("系统");
                recDao.add(lgr);
            }
            //List<DriverGoods> driverGoods = driverGoodsMapper.getZeroWeightGoods();
            //司机货源过期时,需要取消货源关注信息
            if(!IterUtil.isEmpty(driverGoodsList)){
                for (Map<String, String> map : driverGoodsList) {
                    goodsAttentionServiceTran.sendCancelGoodsAttentionMsg(map.get("changeDealGoodsId"));
                }
            }

//            List<DriverGoods> driverGoodsStatusDeal =driverGoodsMapper.getGoodsStatusDeal();
//            for (DriverGoods driverGood:driverGoodsStatusDeal
//                 ) {
//                driverGood.setDealStatus("50");
//                driverGoodsMapper.updateByGoodsId(driverGood);
//            }
        } catch (Exception ex) {
            errMsg = "定时更新货源状态异常!";
            LogHelper.writeError(errMsg, ex);
        }finally{
            distributedLocker.unlock(GOOD_EXPIRED_LOCK);
        }
    }


    /**
     * 关闭货源
     * 创建者: cgb
     * 创建时间: 2019/12/3
     *
     * @param goodsId 货源编号  货源信息表
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【关闭成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【关闭成功】编码,ResultMode.errMsg为相应【关闭成功】描述；
     * 否则ResultMode.succeed=false【关闭失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【关闭失败】编码,ResultMode.errMsg为相应【关闭失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsGoodsClose(String goodsId) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
        try {
            GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoods lsdsGoods = dao.getModel(goodsId);
            //当前货源为下游询价货源时,不允许关闭
            if (null != lsdsGoods && !StringUtils.isEmpty(lsdsGoods.getParentGoodsId())) {
                errMsg = "下游询价货源不允许直接关闭！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }
            //关闭货源
            boolean flag = dao.goodsClose(goodsId);
            //关闭货源：推送货源合同归档失效消息
            if (flag) {
                sendNoticeMessage(goodsId, LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus7.getCode());
            }

            //更新报价状态为已撤销
            UpdateGoodsOfferStatusEntity updateGoodsOfferStatus = UpdateGoodsOfferStatusEntity.buildUpdateGoodsOfferStatus(lsdsGoods.getGoodsId(), null,
                userInfoFromCache.getUserBaseId(), OfferStatusEnum.REVOKED.getCode());
            lsdsGoodsOfferTran.updateOfferStatus(updateGoodsOfferStatus);

            //同步货源操作记录
            LsdsGoodsRecord lgr = LsdsGoodsRecord.of();
            lgr.setRecordId(String.valueOf(IdUtil.generateId()));
            lgr.setGoodsId(lsdsGoods.getGoodsId());
            lgr.setCompanyId(userInfoFromCache.getCompanyId());
            lgr.setOperateStatus("7");
            lgr.setOperateContent("货源撤销");
            lgr.setUserId(userInfoFromCache.getUserBaseId());
            lgr.setUserLoginName(userInfoFromCache.getLoginName());
            lgr.setUserName(userInfoFromCache.getUsername());
            recDao.add(lgr);

            boolean sendFlg = true;
            if(flag && null != lsdsGoods && !StringUtils.isEmpty(lsdsGoods.getOrderId())) {
                String topic = SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.LSDS_TO_OMS_ORDER_STATUS.getTopicName();
                List<Map<String, String>> mapList = new ArrayList<>();
                Map<String, String> map = new HashMap<>();
                map.put("changeDealGoodsId", lsdsGoods.getGoodsId());
                map.put("orderId", lsdsGoods.getOrderId());
                //补传状态：推送订单归档失效消息
                map.put("dealStatus", LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus7.getCode());
                mapList.add(map);
                JSONObject json = new JSONObject();
                json.put("exlsdsOrderIds", mapList);
                sendFlg = lsdsKafkaSender.send(topic, json.toJSONString());
                log.info("货源关闭，通知oms系统成功。内容如下：{}", json);
            }

            //同步下游询价货源信息
            if(flag && sendFlg && null != lsdsGoods && !StringUtils.isEmpty(lsdsGoods.getChildGoodsId())) {
                LsdsGoods tempGoods = dao.getModel(lsdsGoods.getChildGoodsId());
                boolean childFlag = dao.goodsClose(lsdsGoods.getChildGoodsId());
                //关闭货源：推送货源合同归档失效消息
                if (childFlag) {
                    sendNoticeMessage(lsdsGoods.getChildGoodsId(), LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus7.getCode());
                }

                //更新报价状态为已撤销
                updateGoodsOfferStatus = UpdateGoodsOfferStatusEntity.buildUpdateGoodsOfferStatus(tempGoods.getGoodsId(), null,
                    userInfoFromCache.getUserBaseId(), OfferStatusEnum.REVOKED.getCode());
                lsdsGoodsOfferTran.updateOfferStatus(updateGoodsOfferStatus);

                //同步子货源操作记录
                LsdsGoodsRecord tempRec = LsdsGoodsRecord.of();
                tempRec.setRecordId(String.valueOf(IdUtil.generateId()));
                tempRec.setGoodsId(lsdsGoods.getChildGoodsId());
                tempRec.setCompanyId(userInfoFromCache.getCompanyId());
                tempRec.setOperateStatus("7");
                tempRec.setOperateContent("货源撤销");
                tempRec.setUserId(userInfoFromCache.getUserBaseId());
                tempRec.setUserLoginName(userInfoFromCache.getLoginName());
                tempRec.setUserName(userInfoFromCache.getUsername());
                recDao.add(tempRec);
                if(childFlag && null != tempGoods && !StringUtils.isEmpty(tempGoods.getOrderId())) {
                    String topic = SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.LSDS_TO_OMS_ORDER_STATUS.getTopicName();
                    List<Map<String, String>> mapList = new ArrayList<>();
                    Map<String, String> map = new HashMap<>();
                    map.put("changeDealGoodsId", tempGoods.getGoodsId());
                    map.put("orderId", tempGoods.getOrderId());
                    //补传状态：推送订单归档失效消息
                    map.put("dealStatus", LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus7.getCode());
                    mapList.add(map);
                    JSONObject json = new JSONObject();
                    json.put("exlsdsOrderIds", mapList);
                    sendFlg = lsdsKafkaSender.send(topic, json.toJSONString());
                    log.info("子货源关闭，通知oms系统成功。内容如下：{}", json);
                }
            }
        } catch (Exception ex) {
            errMsg = "关闭货源异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }



    /**
     * 合同归档失效消息推送
     *
     * @param id
     * @param dealStatus
     */
    private void sendNoticeMessage(String id, String dealStatus) {
        GoodsKafkaNotice goodsKafkaNotice = new GoodsKafkaNotice();
        goodsKafkaNotice.setGoodsId(id);
        goodsKafkaNotice.setDealStatus(dealStatus);
        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(goodsKafkaNotice));
    }

    /**
     * 进入下一轮报价
     * 创建者: cgb
     * 创建时间: 2019/11/30
     *
     * @param model 货源编号  货源信息表
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【报价成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【报价成功】编码,ResultMode.errMsg为相应【报价成功】描述；
     * 否则ResultMode.succeed=false【报价失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【报价失败】编码,ResultMode.errMsg为相应【报价失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> offerToNetRound(LsdsGoodsVo model, String token) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {

            //加锁
            distributedLocker.lock(GOOD_NET_ROUND_LOCK, 30);

            GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
            LsdsGoods lsdsGoods = goodsDao.getModel(model.getLsdsGoods().getGoodsId());
            //金额取整
            amountRoundingModeService.setLsdsGoodsScale(lsdsGoods);

            //配合锁机制防止重复提交
            if(lsdsGoods.getOfferCurrentRounds() == (model.getLsdsGoods().getOfferCurrentRounds() + 1)){
                errMsg = "请勿重复提交！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }

            if(!StringUtils.isEmpty(lsdsGoods.getOfferRound()) && !StringUtils.isEmpty(lsdsGoods.getOfferCurrentRounds()) && lsdsGoods.getOfferCurrentRounds() >= lsdsGoods.getOfferRound()){
                errMsg = "报价伦次已达上限！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }

            if(StringUtils.isEmpty(lsdsGoods.getEnquiryOfferTime()) || lsdsGoods.getEnquiryOfferTime() <= 0){
                errMsg = "当前货源未填写报价有效期，只允许一轮报价！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }

            //更新货源当前报价轮次
            log.info("进入下一轮报价更新的报价开始时间：" + DateUtils.getDateTime());
            model.getLsdsGoods().setCurrentOfferStartDate(DateUtils.getDateTime());
            goodsDao.updateRound(model.getLsdsGoods());
            GoodsSourceMapper goodsSourceMapper = SpringContextUtil.getBeanByClass(GoodsSourceMapper.class);
            goodsSourceMapper.updateRound(model.getLsdsGoods().getGoodsId(),model.getLsdsGoods().getOfferCurrentRounds());
            goodsSourceMapper.updateRoundExtend(model.getLsdsGoods().getGoodsId());


            List<Map<String,String>> offerList = null;
            if(!StringUtils.isEmpty(lsdsGoods.getOfferCurrentRounds()) && lsdsGoods.getOfferCurrentRounds() > 0){
                LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
                LsdsGoodsOffer offer = new LsdsGoodsOffer();
                offer.setGoodsId(lsdsGoods.getGoodsId());
                offer.setOfferRound(lsdsGoods.getOfferCurrentRounds());
                offerList = offerDao.getCurrentOfferdMap(offer);
            }
            List<LsdsGoodsAssignSupplier> supplierListNew = new ArrayList<>(16);
            //新增货源当前轮次承运商信息（页面勾选了报价的承运商信息）到下一轮
            GoodsAssignSupplierMapper supplierDao = SpringContextUtil.getBeanByClass(GoodsAssignSupplierMapper.class);
            if (IterUtil.isNotEmpty(model.getAssignSupplierList())) {
                List<String> companyIdList = model.getAssignSupplierList().stream().map(LsdsGoodsAssignSupplier::getCompanyId).collect(Collectors.toList());
                LsdsGoodsAssignSupplier condition = new LsdsGoodsAssignSupplier();
                condition.setCompanyIdList(companyIdList);
                condition.setGoodsId(model.getLsdsGoods().getGoodsId());
                condition.setOfferRound(model.getLsdsGoods().getOfferCurrentRounds());
                List<LsdsGoodsAssignSupplier> querySupplierList = supplierDao.querySupplierList(condition);
                if (IterUtil.isEmpty(querySupplierList)) {
                    assembleSupplierList(companyIdList, supplierListNew);
                } else {
                    supplierListNew = BeanUtil.copyToList(querySupplierList, LsdsGoodsAssignSupplier.class);
                }

                for (LsdsGoodsAssignSupplier supplier : supplierListNew) {
                    supplier.setGoodsAssignSupplierId(String.valueOf(IdUtil.generateId()));
                    supplier.setOfferRound(model.getLsdsGoods().getOfferCurrentRounds() + 1);
                    supplier.setGoodsId(lsdsGoods.getGoodsId());
                    supplier.setCreateDate(DateUtils.getDateTime());
                    supplier.setModifyDate(DateUtils.getDateTime());
                    supplier.setCreateBy(userInfoFromCache.getUserBaseId());
                    supplier.setModifyBy(userInfoFromCache.getUserBaseId());

                    if(null != offerList && offerList.size() > 0){
                        List<Map<String,String>> tempOfferList = offerList.stream().filter(p -> (p.get("companyId")).equals(supplier.getCompanyId())).collect(Collectors.toList());
                        //发送站内信，通知承运商
                        Map<String, String> param = new HashMap<String, String>();
                        String phoneNumber = supplier.getCompanyPhoneNumber();
                        String userId = tempOfferList.get(0).get("createBy");
                        String companyId = supplier.getCompanyId();
                        String templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_SUPPLIER_OFFER.getCode();
                        param.put("goodsNo",lsdsGoods.getGoodsId());
                        param.put("offerRound",Integer.toString(model.getLsdsGoods().getOfferCurrentRounds() + 1));
                        //发送站内信
                        sendMsg(phoneNumber,userId,companyId,templateId,param);
                    }
                }
                supplierDao.batchAdd(supplierListNew);
            } else {
                //没有勾选当前轮次报价承运商信息，将当前报价的所有承运商转入下一轮报价
                if (null != model.getGoodsNextRoundOfferList() && model.getGoodsNextRoundOfferList().size() > 0) {
                    List<String> companyIdList = model.getGoodsNextRoundOfferList().stream().map(info -> info.get("companyId").toString())
                        .collect(Collectors.toList());
                    LsdsGoodsAssignSupplier condition = new LsdsGoodsAssignSupplier();
                    condition.setCompanyIdList(companyIdList);
                    condition.setGoodsId(model.getLsdsGoods().getGoodsId());
                    condition.setOfferRound(model.getLsdsGoods().getOfferCurrentRounds());
                    List<LsdsGoodsAssignSupplier> querySupplierList = supplierDao.querySupplierList(condition);
                    if (IterUtil.isEmpty(querySupplierList)) {
                        assembleSupplierList(companyIdList, supplierListNew);
                    } else {
                        supplierListNew = BeanUtil.copyToList(querySupplierList, LsdsGoodsAssignSupplier.class);
                    }

                    for (LsdsGoodsAssignSupplier supplier : supplierListNew) {
                        supplier.setGoodsAssignSupplierId(String.valueOf(IdUtil.generateId()));
                        supplier.setGoodsId(lsdsGoods.getGoodsId());
                        supplier.setOfferRound(model.getLsdsGoods().getOfferCurrentRounds() + 1);
                        supplier.setCreateDate(DateUtils.getDateTime());
                        supplier.setModifyDate(DateUtils.getDateTime());
                        supplier.setCreateBy(userInfoFromCache.getUserBaseId());
                        supplier.setModifyBy(userInfoFromCache.getUserBaseId());

                        if(null != offerList && offerList.size() > 0){
                            List<Map<String,String>> tempOfferList = offerList.stream().filter(p -> (p.get("companyId")).equals(supplier.getCompanyId())).collect(Collectors.toList());
                            //发送站内信，通知承运商
                            Map<String, String> param = new HashMap<String, String>();
                            String phoneNumber = supplier.getCompanyPhoneNumber();
                            String userId = tempOfferList.get(0).get("createBy");
                            String companyId = supplier.getCompanyId();
                            String templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_SUPPLIER_OFFER.getCode();
                            param.put("goodsNo",lsdsGoods.getGoodsId());
                            param.put("offerRound",Integer.toString(model.getLsdsGoods().getOfferCurrentRounds() + 1));
                            //发送站内信
                            sendMsg(phoneNumber,userId,companyId,templateId,param);
                        }
                    }
                    supplierDao.batchAdd(supplierListNew);
                }
            }

            //同步货源操作记录
            GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
            LsdsGoodsRecord lgr =  LsdsGoodsRecord.of();
            lgr.setRecordId(String.valueOf(IdUtil.generateId()));
            lgr.setGoodsId(lsdsGoods.getGoodsId());
            lgr.setCompanyId(userInfoFromCache.getCompanyId());
            lgr.setOperateStatus("9");
            lgr.setOperateContent("货源进入第" + (model.getLsdsGoods().getOfferCurrentRounds() + 1) +"轮报价");
            lgr.setUserId(userInfoFromCache.getUserBaseId());
            lgr.setUserLoginName(userInfoFromCache.getLoginName());
            lgr.setUserName(userInfoFromCache.getUsername());
            recDao.add(lgr);

            //指派承运商，生成报价单信息
            lsdsGoodsOfferService.generateSupplierOffer(lsdsGoods, supplierListNew, lsdsGoods.getOfferCurrentRounds() + 1);

        } catch (Exception ex) {
            errMsg = "进入下一轮报价异常！";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }finally{
            distributedLocker.unlock(GOOD_NET_ROUND_LOCK);
        }
        return resultmodel;
    }


    private void assembleSupplierList(List<String> companyIdList, List<LsdsGoodsAssignSupplier> supplierListNew) {
        List<PlatformUmCompany> companyList = platformExchangeService.getCompanyByIds(companyIdList);
        if (IterUtil.isEmpty(companyList)) {
            log.error("assembleSupplierList#未查询到企业信息:{}", JSONUtil.toJsonStr(companyIdList));
            return;
        }

        companyList.stream().forEach(company -> {
            LsdsGoodsAssignSupplier supplier = new LsdsGoodsAssignSupplier();
            supplier.setCompanyId(company.getCompanyId());
            supplier.setCompanyName(company.getCompanyName());
            supplier.setCompanyShortName(company.getCompanyShortName());
            supplier.setCompanyCode(company.getSocialCreditCode());
//            supplier.setCompanyLinker(company.getContacts());
//            supplier.setCompanyPhoneNumber(company.getPhone());
            supplierListNew.add(supplier);
        });

    }

    /**
     * 下单成功后 更新报价状态与货源交易状态
     * 创建者: cgb
     * 创建时间: 2019/11/30
     * @param vo    货源VO
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @returns ResultMode<String>
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsGoodsUpdateAfterOrder(LsdsGoodsVo vo, String token) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            if (null != vo.getOfferOrderList() && vo.getOfferOrderList().size() > 0) {
                GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
                LsdsGoods lsdsGoods = dao.getModel(vo.getLsdsGoods().getGoodsId());
                //更新货源状态为已成交
                vo.getLsdsGoods().setModifyDate(DateUtils.getDateTime());
                vo.getLsdsGoods().setDealStatus(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus5.getCode());
                dao.updateStatus(vo.getLsdsGoods());

                //更新对应下单的报价信息状态
                for (LsdsGoodsOffer offer : vo.getOfferOrderList()) {
                    offer.setModifyDate(DateUtils.getDateTime());
                    offer.setStatus(2);
                }
                LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
                offerDao.batchUpdateStatus(vo.getOfferOrderList());

                //同步货源操作记录
                GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
                LsdsGoodsRecord lgr = LsdsGoodsRecord.of();
                lgr.setRecordId(String.valueOf(IdUtil.generateId()));
                lgr.setGoodsId(lsdsGoods.getGoodsId());
                lgr.setCompanyId("system");
                lgr.setOperateStatus("5");
                lgr.setOperateContent("货源成交");
                lgr.setUserId("system");
                lgr.setUserLoginName("system");
                lgr.setUserName("系统");
                recDao.add(lgr);

                //更新子货源状态为已成交
                if (null != lsdsGoods && !StringUtils.isEmpty(lsdsGoods.getChildGoodsId())) {
                    LsdsGoods tempGoods = LsdsGoods.of();
                    tempGoods.setGoodsId(lsdsGoods.getChildGoodsId());
                    tempGoods.setDealStatus(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus5.getCode());
                    dao.updateStatus(tempGoods);

                    //更新对应子货源的报价信息状态
                    List<LsdsGoodsOffer> childOfferList = new ArrayList<>(16);
                    for (LsdsGoodsOffer childOffer : vo.getOfferOrderList()) {
                        childOffer.setOfferId(childOffer.getChildOfferId());
                        childOffer.setChildOfferId("");
                        childOffer.setModifyDate(DateUtils.getDateTime());
                        childOffer.setStatus(2);
                        childOfferList.add(childOffer);
                    }
                    offerDao.batchUpdateStatus(childOfferList);

                    //同步子货源操作记录
                    LsdsGoodsRecord tempRec =  LsdsGoodsRecord.of();
                    tempRec.setRecordId(String.valueOf(IdUtil.generateId()));
                    tempRec.setGoodsId(lsdsGoods.getChildGoodsId());
                    tempRec.setCompanyId("system");
                    tempRec.setOperateStatus("5");
                    tempRec.setOperateContent("货源成交");
                    tempRec.setUserId("system");
                    tempRec.setUserLoginName("system");
                    tempRec.setUserName("系统");
                    recDao.add(tempRec);
                }
            }
        } catch (Exception ex) {
            errMsg = "回写货源状态信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }


    /**
     * oms下单成功后 返回对应goodsId及offerId数组进行货源及报价单状态更新
     * 创建者: cgb
     * 创建时间: 2019/12/31
     *
     * @param record
     * @returns ResultMode<String>
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsGoodsUpdateAfterOrder(String record) {
        String errMsg = "";
        LsdsGoods lsdsGoods = null;
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            log.info("货源下单成功，接收oms消息，开始状态回写！");
            JSONObject jsonObject = JSONObject.parseObject(record);

                //处理close合同生成订单无订单号问题
                if (!jsonObject.isEmpty() && null != jsonObject.get("goodsId") && jsonObject.get("goodsId") != "" && (null == jsonObject.get("offerId") || jsonObject.get("offerId") == "")) {
                    if (jsonObject.get("parentOrderId").toString().contains("POC")){
                        GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
                        //lsdsGoods = dao.getModel(jsonObject.get("goodsId").toString());
                        LsdsGoods tempLsdsGoods = LsdsGoods.of();
                        //更新货源状态为已成交
                        tempLsdsGoods.setGoodsId(jsonObject.get("goodsId").toString());
                        tempLsdsGoods.setParentOrderId(jsonObject.get("parentOrderId").toString());
                        tempLsdsGoods.setModifyDate(DateUtils.getDateTime());
                        tempLsdsGoods.setDealStatus(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus5.getCode());
                        dao.updateStatus(tempLsdsGoods);
                        lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(BeanUtil.copyProperties(tempLsdsGoods, GoodsKafkaNotice.class)));
                    }
                }

                if (!jsonObject.isEmpty() && null != jsonObject.get("goodsId") && jsonObject.get("goodsId") != "" && (null != jsonObject.get("offerId") && jsonObject.get("offerId") != "")) {

                if (jsonObject.containsKey("goodsType")){

                }else{
                    GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
                    lsdsGoods = dao.getModel(jsonObject.get("goodsId").toString());
                    LsdsGoods tempLsdsGoods = LsdsGoods.of();
                    //更新货源状态为已成交
                    tempLsdsGoods.setGoodsId(jsonObject.get("goodsId").toString());
                    tempLsdsGoods.setParentOrderId(jsonObject.get("parentOrderId").toString());
                    tempLsdsGoods.setModifyDate(DateUtils.getDateTime());
                    tempLsdsGoods.setDealStatus(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus5.getCode());
                    dao.updateStatus(tempLsdsGoods);
                    lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(BeanUtil.copyProperties(tempLsdsGoods, GoodsKafkaNotice.class)));

                    String now = DateUtils.getDateTime();
                    String userBaseId = jsonObject.getString("userBaseId");
                    //更新对应下单的报价信息状态
                    if(null != jsonObject.get("offerId") && jsonObject.get("offerId") != ""){
                        List<LsdsGoodsOffer> offerList = new ArrayList<>(16);
                        JSONArray jsonArray = (JSONArray)jsonObject.get("offerId");
                        for (int i = 0; i < jsonArray.size(); i++) {
                            LsdsGoodsOffer offer = new LsdsGoodsOffer();
                            offer.setOfferId(jsonArray.get(i).toString());
                            offer.setModifyDate(now);
                            offer.setModifyBy(userBaseId);
                            offer.setStatus(2);
                            offer.setOfferStatus(OfferStatusEnum.TRADED.getCode());
                            offer.setShowWinBids(Integer.valueOf(Opt.ofNullable(jsonObject.get("showWinBids")).orElse("20").toString()));
                            offerList.add(offer);
                        }
                        LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
                        offerDao.batchUpdateStatus(offerList);
                        log.info("报价ID："+ jsonObject.get("offerId") +" 状态回写成功！");
                        //更新未中标的报价为未成交
                        UpdateGoodsOfferStatusEntity updateEntity = UpdateGoodsOfferStatusEntity
                            .buildUpdateGoodsOfferStatus(jsonObject.getString("goodsId"), userBaseId, 1, OfferStatusEnum.UNSETTLED.getCode());
                        offerDao.updateOfferStatusByGoodsId(updateEntity);
                    }

                    //通过oms消息传入的userBaseId获取用户信息
                    PlatformUmUserbaseinfo platformUmUserbaseinfo = null;
                    if(!StringUtils.isEmpty(jsonObject.get("userBaseId")) && (StringUtils.isEmpty(lsdsGoods.getContractType()) || !"2".equals(lsdsGoods.getContractType()))){
                        PlatformUmUserbaseinfoFilter baseinfoFilter = new PlatformUmUserbaseinfoFilter();
                        baseinfoFilter.setUserBaseId(jsonObject.get("userBaseId").toString());
                        List<PlatformUmUserbaseinfo> baseinfoList = platformCommonInterClient.getUserInfoByKey(baseinfoFilter).getModel();
                        if(null != baseinfoList && baseinfoList.size() > 0){
                            platformUmUserbaseinfo = baseinfoList.get(0);
                        }
                    }

                    //同步货源操作记录
                    GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
                    LsdsGoodsRecord lgr = LsdsGoodsRecord.of();
                    lgr.setRecordId(String.valueOf(IdUtil.generateId()));
                    lgr.setGoodsId(lsdsGoods.getGoodsId());
                    lgr.setOperateStatus("5");
                    lgr.setOperateContent("货源成交");
                    if(null != platformUmUserbaseinfo){
                        lgr.setCompanyId(platformUmUserbaseinfo.getCompanyId());
                        lgr.setUserId(platformUmUserbaseinfo.getUserBaseId());
                        lgr.setUserLoginName(platformUmUserbaseinfo.getLoginName());
                        lgr.setUserName(platformUmUserbaseinfo.getUsername());
                    }else{
                        lgr.setCompanyId("system");
                        lgr.setUserId("system");
                        lgr.setUserLoginName("system");
                        lgr.setUserName("系统");
                    }
                    recDao.add(lgr);


                    //下游询价，更新子货源及子报价状态为已成交
                    if(null !=lsdsGoods && !StringUtils.isEmpty(lsdsGoods.getChildGoodsId())){
                        LsdsGoods tempGoods = LsdsGoods.of();
                        tempGoods.setGoodsId(lsdsGoods.getChildGoodsId()).setDealStatus(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus5.getCode());


                        //更新对应子货源的报价信息状态
                        if(null != jsonObject.get("offerId") && jsonObject.get("offerId") != ""){
                            LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
                            JSONArray jsonArray = (JSONArray)jsonObject.get("offerId");
                            String [] offerIdArr = new String[jsonArray.size()];
                            for (int i = 0; i < jsonArray.size(); i++) {
                                offerIdArr[i] = jsonArray.get(i).toString();
                            }

                            String  platformCompanyIdStr=getPlatformCompanyIdStr();
                            //List<LsdsGoodsOffer> offerList = offerDao.getOfferListById(offerIdArr);
                            LsdsGoodsOffer lsdsGoodsOffer= new LsdsGoodsOffer();
                            lsdsGoodsOffer.setOfferIdArr(offerIdArr);
                            lsdsGoodsOffer.setPlatformCompanyId(platformCompanyIdStr);
                            List<LsdsGoodsOffer> offerList = offerDao.getOfferListById(lsdsGoodsOffer);

                            //按金额取整方式设置基价
                            setBasePriceScale(offerList);

                            List<String> childOfferIdList = offerList.stream().filter(p -> !StringUtils.isEmpty(p.getChildOfferId())).map(LsdsGoodsOffer::getChildOfferId).collect(Collectors.toList());
                            if(null != childOfferIdList && childOfferIdList.size() > 0){
                                tempGoods.setDealStatus(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus5.getCode());
                            }else{
                                tempGoods.setDealStatus(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus7.getCode());
                            }
                            List<LsdsGoodsOffer> downOfferList = new ArrayList<>(16);
                            for (String childOfferId : childOfferIdList) {
                                LsdsGoodsOffer offer = new LsdsGoodsOffer();
                                offer.setOfferId(childOfferId);
                                offer.setModifyDate(now);
                                offer.setModifyBy(userBaseId);
                                offer.setStatus(2);
                                offer.setOfferStatus(OfferStatusEnum.TRADED.getCode());
                                downOfferList.add(offer);
                            }
                            dao.updateStatus(tempGoods);
                            lsdsKafkaSender.kafkaSendTransaction(Constants.KAFKA_TOPIC_GOODS_STATUS_UPD, JSONUtil.toJsonStr(BeanUtil.copyProperties(tempGoods, GoodsKafkaNotice.class)));

                            log.info("子货源号：" + lsdsGoods.getChildGoodsId() + "状态回写成功！");
                            offerDao.batchUpdateStatus(downOfferList);
                            log.info("下游询价子报价ID：" + childOfferIdList + " 状态回写成功！");
                            //更新未中标的报价为未成交
                            UpdateGoodsOfferStatusEntity updateEntity = UpdateGoodsOfferStatusEntity
                                .buildUpdateGoodsOfferStatus(lsdsGoods.getChildGoodsId(), userBaseId, 1, OfferStatusEnum.UNSETTLED.getCode());
                            offerDao.updateOfferStatusByGoodsId(updateEntity);

                            //同步子货源操作记录
                            LsdsGoodsRecord tempRec = LsdsGoodsRecord.of();
                            tempRec.setRecordId(String.valueOf(IdUtil.generateId()));
                            tempRec.setGoodsId(lsdsGoods.getChildGoodsId());
                            tempRec.setCompanyId("system");
                            tempRec.setOperateStatus("5");
                            tempRec.setOperateContent("货源成交");
                            tempRec.setUserId("system");
                            tempRec.setUserLoginName("system");
                            tempRec.setUserName("系统");
                            recDao.add(tempRec);
                        }
                    }
                    log.info("货源号："+ jsonObject.get("goodsId").toString() +" 状态回写成功！");
                }

            }
        } catch (Exception ex) {
            errMsg = "回写货源状态信息失败！";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }

    /**
     * 报价运输方案
     *
     * @param offerId
     * @return {@code  ResultMode<LsdsGoodsPlan>}
     * <AUTHOR>
     * 创建时间 2019/12/2
     */
    public ResultMode<LsdsGoodsPlan> getPlanListByOfferId(String offerId) {
        ResultMode<LsdsGoodsPlan> returnModel = new ResultMode<LsdsGoodsPlan>();

        LsdsGoodsPlanMapper planDao = SpringContextUtil.getBeanByClass(LsdsGoodsPlanMapper.class);
        List<LsdsGoodsPlan> planList = planDao.getPlanListByOfferId(offerId);
        returnModel.setModel(planList);
        return returnModel;
    }

    /**
     * 根据报价offerId查询报价信息
     *
     * @param offerIdArr
     * @return {@code  ResultMode<LsdsGoodsOffer>}
     * <AUTHOR>
     * 创建时间 2019/12/2
     */
    public ResultMode<LsdsGoodsOffer> getOfferListById(String[] offerIdArr) {
        ResultMode<LsdsGoodsOffer> returnModel = new ResultMode<LsdsGoodsOffer>();

        LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
        String  platformCompanyIdStr=getPlatformCompanyIdStr();
        //List<LsdsGoodsOffer> offerList = offerDao.getOfferListById(offerIdArr);
        LsdsGoodsOffer lsdsGoodsOffer= new LsdsGoodsOffer();
        lsdsGoodsOffer.setOfferIdArr(offerIdArr);
        lsdsGoodsOffer.setPlatformCompanyId(platformCompanyIdStr);
        List<LsdsGoodsOffer> offerList = offerDao.getOfferListById(lsdsGoodsOffer);

        //按金额取整方式设置基价
        setBasePriceScale(offerList);
        //填充全称
        if(CollUtil.isNotEmpty(offerList)){
            offerList.forEach(o ->{
                String companyId = o.getCompanyId();
                PlatformUmCompany companyByModel = platformExchangeService.getCompanyByModel(companyId);
                o.setCompanyName(companyByModel.getCompanyName());
            });
        }

        returnModel.setModel(offerList);
        return returnModel;
    }

    private void setBasePriceScale(List<LsdsGoodsOffer> offerList){
        if(IterUtil.isEmpty(offerList)){
           return;
        }

        Set<String> busiIdSet = new HashSet<>();
        for(LsdsGoodsOffer goodsOffer:offerList){
            if(!StrUtil.isEmpty(goodsOffer.getGoodsId())){
                busiIdSet.add(goodsOffer.getGoodsId());
            }
        }
        Integer roundingMode = null;
        Map<String, Integer> roundingModeMap = amountRoundingModeService.getRoundingMode(new ArrayList<>(busiIdSet));
        for(LsdsGoodsOffer goodsOffer:offerList){
            roundingMode = BigDecimal.ROUND_UP;
            if(!StrUtil.isEmpty(goodsOffer.getGoodsId())){
                roundingMode = roundingModeMap.get(goodsOffer.getGoodsId());
            }
            if(roundingMode==null){
                roundingMode = BigDecimal.ROUND_UP;
            }
            if(goodsOffer.getEnquiryTypeBasePrice()!=null){
                goodsOffer.setEnquiryTypeBasePrice(goodsOffer.getEnquiryTypeBasePrice().setScale(2,roundingMode));
            }
            if(goodsOffer.getEnquiryTypeBaseOpenTicket()!=null){
                goodsOffer.setEnquiryTypeBaseOpenTicket(goodsOffer.getEnquiryTypeBaseOpenTicket().setScale(2,roundingMode));
            }
//            if(goodsOffer.getWeightSum()!=null && goodsOffer.getEnquiryTypeBaseOpenTicket()!=null){
//                goodsOffer.setExFreightPrice(goodsOffer.getWeightSum().multiply(goodsOffer.getEnquiryTypeBaseOpenTicket()).setScale(2,roundingMode ));
//            }
            if(goodsOffer.getTotalQuantity()!=null && goodsOffer.getEnquiryTypeBaseOpenTicket()!=null){
                goodsOffer.setExFreightPrice(goodsOffer.getTotalQuantity().multiply(goodsOffer.getEnquiryTypeBaseOpenTicket()).setScale(2,roundingMode ));
            }
        }
    }

    private void setBasePriceScaleOfSplitOffer(List<Map<String, Object>> offerList){
        if(IterUtil.isEmpty(offerList)){
            return;
        }
        String goodsIdKey = "goodsId";
        String basePriceKey = "enquiryTypeBasePrice";
        String baseOpenTicket = "enquiryTypeBaseOpenTicket";
        String freightPrice = "freightPrice";
        String sumNodePrice = "sumNodePrice";
        String weightSum = "weightSum";
        String totalQuantity = "totalQuantity";
        String createDateKey = "createDate";

        Set<String> busiIdSet = new HashSet<>();
        for(Map<String, Object> data:offerList){
            if(data.containsKey(goodsIdKey)){
                busiIdSet.add(data.get(goodsIdKey).toString());
            }
        }
        Integer roundingMode = null;
        Map<String, Integer> roundingModeMap = amountRoundingModeService.getRoundingMode(new ArrayList<>(busiIdSet));
        for(Map<String, Object> data:offerList){
            roundingMode = BigDecimal.ROUND_UP;
            if(data.containsKey(goodsIdKey)){
                roundingMode = roundingModeMap.get(goodsIdKey);
            }
            if(roundingMode==null){
                roundingMode = BigDecimal.ROUND_UP;
            }
            if(data.containsKey(basePriceKey)){
                data.put(basePriceKey,new BigDecimal(data.get(basePriceKey).toString()).setScale(2,roundingMode));
            }
            if(data.containsKey(baseOpenTicket)){
                data.put(baseOpenTicket,new BigDecimal(data.get(baseOpenTicket).toString()).setScale(2,roundingMode));
            }
            if(data.containsKey(freightPrice)){
                data.put(freightPrice,new BigDecimal(data.get(freightPrice).toString()).setScale(2,roundingMode));
            }
            if(data.containsKey(totalQuantity) && data.containsKey(baseOpenTicket)){
                //重新计算运费=重量*开票价
                data.put(freightPrice,(new BigDecimal(data.get(totalQuantity).toString()).multiply(new BigDecimal(data.get(baseOpenTicket).toString()))).setScale(2,roundingMode));
            }
            if(data.containsKey(sumNodePrice)){
                data.put(sumNodePrice,new BigDecimal(data.get(sumNodePrice).toString()).setScale(2,roundingMode));
            }
            if(data.containsKey(createDateKey) && data.get(createDateKey) != null){
                Date createDate = (Date) data.get(createDateKey);
                //报价时间
                data.put("offerDate", DateUtil.formatDateTime(createDate));
            }
        }
    }

    /**
     * 首页货源大厅（展示5条发布中货源信息：公开询价的货源）
     *
     * @return {@code  ResultMode<LsdsGoods>}
     * <AUTHOR>
     * 创建时间 2019/12/21
     */
    public ResultMode<LsdsGoods> getGoodsListAuditedForHomePage() {
        ResultMode<LsdsGoods> returnModel = new ResultMode<LsdsGoods>();

        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<LsdsGoods> goodsList = goodsDao.getGoodsListAuditedForHomePage();
        returnModel.setModel(goodsList);
        return returnModel;
    }

    /**
     * 首页物流信息分页查询
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * <AUTHOR>
     * 创建时间 2019/12/31
     */
    public ResultMode<WebsiteGoods> getHomeGoodsListPage(PagingInfo<LsdsGoodsFilter> pageInfo) {
        ResultMode<WebsiteGoods> returnModel = new ResultMode<>();

        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength, pageInfo.getCountTotal());
        PageHelper.orderBy("validity_date,goods_id");

        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<LsdsGoods> goodsList = new ArrayList<>();
        if (null != pageInfo.filterModel) {
//            goodsList = goodsDao.getGoodsListForVisitor(pageInfo.filterModel);
            goodsList = goodsDao.getAllGoodsListForVisitor(pageInfo.filterModel);
        }
        List<WebsiteGoods> websiteGoodsList = goodsList.stream().map(goods -> {
            WebsiteGoods websiteGoods = BeanUtil.toBean(goods, WebsiteGoods.class, CopyOptions.create().ignoreError().ignoreNullValue());
            BigDecimal enquiryTypeBaseOpenTicket = goods.getEnquiryTypeBaseOpenTicket();
            if (enquiryTypeBaseOpenTicket!= null) {
                String openTicketFormat = NumberUtil.decimalFormat("#", enquiryTypeBaseOpenTicket);
                openTicketFormat = DesensitizedUtil.firstMask(openTicketFormat);
                websiteGoods.setEnquiryTypeBaseOpenTicket(StrUtil.concat(true,openTicketFormat,".**"));
            }
            return websiteGoods;
        }).collect(Collectors.toList());
        PageInfo<LsdsGoods> pageInfoList = new PageInfo<>(goodsList);
        returnModel.setTotal(Math.toIntExact(pageInfoList.getTotal()));
        returnModel.setModel(websiteGoodsList);
        return returnModel;
    }


    /**
     * 首页货源大厅（展示5条已成交货源信息）
     *
     * @return {@code  ResultMode<LsdsGoods>}
     * <AUTHOR>
     * 创建时间 2019/12/21
     */
    public ResultMode<LsdsGoods> getGoodsListFinishedForHomePage() {
        ResultMode<LsdsGoods> returnModel = new ResultMode<LsdsGoods>();
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<LsdsGoods> goodsList = goodsDao.getGoodsListFinishedForHomePage();
        returnModel.setModel(goodsList);
        return returnModel;
    }


    /**
     * 货源成交资源
     *
     * @return {@code  ResultMode<String>}
     * <AUTHOR>
     * 创建时间 2019/12/27
     */
    @XxlJob("lsdsDriverGoodsFinishedSumWeight")
    public void lsdsDriverGoodsFinishedSumWeight() {
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        String finishedWeight = goodsDao.getGoodsFinishedWeight();
        if(!StringUtils.isEmpty(finishedWeight)){
            RedisUtil.set(UtilityEnum.RedisKeyEnum.LSDS_GOODS_FINISHED_WEIGHT_SUM.getCode(), finishedWeight);
            RedisUtil.setIntoDb(UtilityEnum.RedisKeyEnum.LSDS_GOODS_FINISHED_WEIGHT_SUM.getCode(), finishedWeight, RedisUtil.getDb0());
            log.info("----goodsFinishedWeight----" + RedisUtil.get(UtilityEnum.RedisKeyEnum.LSDS_GOODS_FINISHED_WEIGHT_SUM.getCode()));
        }
    }



    /**
     * 查询工作台发货方货源统计信息
     * 创建者：cgb
     * @param lsdsGoods 货源实体
     * @return {@code ResultMode<Map<String, String>>}
     */
    public ResultMode<Map<String, String>> lsdsGoodsSenderView(LsdsGoods lsdsGoods) {
        ResultMode<Map<String, String>> returnModel = new ResultMode<Map<String, String>>();
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<Map<String,String>> mapList = goodsDao.lsdsGoodsSenderView(lsdsGoods);
        returnModel.setModel(mapList);
        returnModel.setTotal(1);
        return returnModel;
    }


    /**
     * 导出我要发货-货源列表信息
     * <AUTHOR>
     * 创建时间 2020/6/10
     * @param filter 查询条件
     */
    public List<LsdsGoods> getExportGoodsList(LsdsGoodsFilter filter) {
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<LsdsGoods> goodsList = new ArrayList<>();
        if (null != filter && null != filter.getCompanyId()) {
            if (LsdsEnum.LsdsgoodsAdminCompanyId.companyId1.getCode().equals(filter.getCompanyId())) {
                goodsList = goodsDao.getGoodsListNoAuthority(filter);
            } else {
                if(LsdsEnum.LsdsgoodsAdminCompanyId.companyId2.getCode().equals(filter)){
                    goodsList = goodsDao.getGoodsListNoUpAuthority(filter);
                }else{
                    String  platformCompanyId=getPlatformCompanyId();
                    filter.setPlatformCompanyId(platformCompanyId);
                    goodsList = goodsDao.getGoodsListAuthority(filter);

                    //金额取整
                    amountRoundingModeService.setLsdsGoodsScale(goodsList);
                }
            }
        }
        //取配置数据
        Map<String, String> unitsMap = platformExchangeService.getPricingUnitsMap(PlatformEnum.EnableStatusEnum.ENABLE.getCode());

        for(LsdsGoods lsdsGoods: goodsList){
            lsdsGoods.setExSendReceiveShort(lsdsGoods.getSendAddrShortName() + " --- " + lsdsGoods.getReceiveAddrShortName());
            String weightSum = (Objects.isNull(lsdsGoods.getWeightSum()) || (lsdsGoods.getWeightSum()).compareTo(BigDecimal.ZERO) == 0) ? "" : " | " + lsdsGoods.getWeightSum().stripTrailingZeros().toPlainString() +"吨";
            String volumeSum =  (Objects.isNull(lsdsGoods.getVolumeSum()) || (lsdsGoods.getVolumeSum()).compareTo(BigDecimal.ZERO) == 0) ? "" : " | " + lsdsGoods.getVolumeSum().stripTrailingZeros().toPlainString() +"立方";
            String totalGoods = (null == lsdsGoods.getTotalGoods() || lsdsGoods.getTotalGoods() == 0) ? "" : " | " + lsdsGoods.getTotalGoods() +"件";
            String enquiryType = "2".equals(lsdsGoods.getEnquiryType()) ? "指定含税单价" + "(" + lsdsGoods.getEnquiryTypeBaseOpenTicket() + "元/"+unitsMap.get(lsdsGoods.getTotalQuantityUnits())+")" : "公开询价";
            lsdsGoods.setEnquiryType(enquiryType);
            String  enquiryOfferTime = (null == lsdsGoods.getEnquiryOfferTime() || lsdsGoods.getEnquiryOfferTime() == 0) ? "" : lsdsGoods.getEnquiryOfferTime() + "小时";
            lsdsGoods.setExEnquiryOfferTime(enquiryOfferTime);
            lsdsGoods.setExGoodsInfo(lsdsGoods.getGoodsName()+ " | " + lsdsGoods.getTotalQuantity().stripTrailingZeros().toPlainString()+unitsMap.get(lsdsGoods.getTotalQuantityUnits()) +weightSum +volumeSum + totalGoods);
            String supplierSum = lsdsGoods.getSupplierSum() > 0 ? "("+ lsdsGoods.getSupplierSum() +")" : "";
            String offerPrice = StringUtils.isEmpty(lsdsGoods.getOfferPrice()) ? "" : lsdsGoods.getOfferPrice() +"元";
            lsdsGoods.setExOfferInfo("第" + lsdsGoods.getOfferCurrentRounds()+ "轮" + supplierSum + offerPrice );
        }
        return goodsList;
    }

    /**
     * 返回首页货源大厅、最新车源、最新货源信息
     * <AUTHOR>
     * 创建时间 2020/7/8
     */
    public ResultMode<LsdsGoodsVo> getGoodsCarInfoForHomeList() {
        ResultMode<LsdsGoodsVo> returnModel = new ResultMode<LsdsGoodsVo>();
        LsdsGoodsVo vo =  new LsdsGoodsVo();
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<LsdsGoods> finishedGoodsList = goodsDao.getGoodsListFinishedForHomePage();
        if(null != finishedGoodsList && finishedGoodsList.size() > 0){
            vo.setFinishedGoodsHomeList(finishedGoodsList);
        }
        List<LsdsGoods> auditedGoodsList = goodsDao.getGoodsListAuditedForHomePage();
        if(null != auditedGoodsList && auditedGoodsList.size() > 0){
            vo.setAuditedGoodsHomeList(auditedGoodsList);
        }

        LsdsCarSourceMapper dao = SpringContextUtil.getBeanByClass(LsdsCarSourceMapper.class);
        LsdsCarSource carSource = new LsdsCarSource();
        //状态为发布中
        carSource.setCarStatus("0");
        //最多返回5条数据
        carSource.setExLimit(4);
        List<LsdsCarSource> carSourceList = dao.getCarSourceList(carSource);
        if(null != carSourceList && carSourceList.size() > 0){
            vo.setCarSourceHomeList(carSourceList);
        }
        returnModel.getModel().add(vo);
        return returnModel;
    }


    public ResultMode<LsdsGoods> getGoodsListByGoodIds(PlatformQrCodeFilter platformQrCodeFilter) {
        ResultMode<LsdsGoods> resultMode = new ResultMode<>();
        String businessId = platformQrCodeFilter.getBusinessId();
        Set<String> filter = Stream.of(businessId.split(",")).collect(Collectors.toSet());
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<LsdsGoods> goodsListByGoodsIds = goodsDao.getGoodsListByGoodsIds(filter);
        resultMode.setModel(goodsListByGoodsIds);
        return resultMode;
    }

    public ResultMode<String> filterSensitiveWord(LsdsGoodsVo vo) {
        log.info(">>>>>>>>>>>>>>敏感词校验>>>>>55>>>>>>>");
        ResultMode<String> resultModel = new ResultMode<String>();
        if (null != vo && null != vo.getLsdsGoods()) {
            checkParams(vo, resultModel);

            //敏感字校验

            Map<String,String> map = new HashMap<String,String>(16);
            //货源主信息敏感词校验字段
            map.put("出发地简称",vo.getLsdsGoods().getSendAddrShortName());
            map.put("出发地联系人",vo.getLsdsGoods().getSendLinker());
            map.put("出发地详细地址 ",vo.getLsdsGoods().getSendAddrDetail());
            map.put("目的地简称",vo.getLsdsGoods().getReceiveAddrShortName());
            map.put("目的地联系人",vo.getLsdsGoods().getReceiveLinker());
            map.put("目的详细地址 ",vo.getLsdsGoods().getReceiveAddrDetail());
            map.put("客户名称",vo.getLsdsGoods().getCustomerName());
            map.put("合同名称",vo.getLsdsGoods().getContractName());
            map.put("货物名称",vo.getLsdsGoods().getGoodsName());
            map.put("货物描述",vo.getLsdsGoods().getGoodsDesc());
            map.put("指定车牌",vo.getLsdsGoods().getAssignCarPlateNumber());
            map.put("运输要求",vo.getLsdsGoods().getOtherRemark());



            if (!CollectionUtils.isEmpty(vo.getGoodsSplitList())){
                vo.getGoodsSplitList().stream().forEach(e->{
                    map.put(e.getSortNode() + "-运输拆分-出发地简称",e.getSendAddrShortName());
                    map.put(e.getSortNode() + "-运输拆分-出发地详细地址 ",e.getSendAddrDetail());
                    map.put(e.getSortNode() + "-运输拆分-目的地简称",e.getReceiveAddrShortName());
                    map.put(e.getSortNode() + "-运输拆分-目的详细地址 ",e.getReceiveAddrDetail());
                });
            }
            //货源拆段信息敏感词校验字段
            log.info(">>>>>>>>>>>>>>敏感词校验>>>>>11>>>>>>>");
            Map<String,String> returnMap = sensitiveWordValidMatch(map);
            log.info(">>>>>>>>>>>>>>敏感词校验>>>>>22>>>>>>>");
            if(null != returnMap && returnMap.size() > 0){
                log.info(">>>>>>>>>>>>>>敏感词校验>>>>>333>>>>>>>");
                resultModel.setErrMsg("敏感词校验不通过:"+returnMap);
                resultModel.getModel().add("敏感词校验不通过:"+returnMap);
                resultModel.setSucceed(false);
            }

            //#endregion
        } else {
            resultModel.setSucceed(false);
        }

        return resultModel;
    }

    //#endregion




    public Map<String,String> sensitiveWordValidMatch(Map<String, String> words){
        log.info(">>>>>>>>>>>>>承运商货源敏感词>>>>>>>>>>>>>>>>>>");
        Map<String, String> resultMap = new HashMap<>();

        PagingInfo<PlatformCmSensitiveWordFilter> pageInfo = new PagingInfo<>();
        pageInfo.currentPage = 1;
        pageInfo.pageLength = 999999999;
        pageInfo.filterModel = new PlatformCmSensitiveWordFilter();
        log.info(">>>>>>>查询敏感词开始>>>>>>>>>>>>wordsSize:{}",words.size());
        ResultMode<PlatformCmSensitiveWord> resultMode = platformCmSensitiveWordInter.platformCmSensitiveWordPaging(pageInfo);
        List<PlatformCmSensitiveWord> res = resultMode.getModel();
        if (!CollectionUtils.isEmpty(words) && !CollectionUtils.isEmpty(res)){
            log.info(">>>>333>>>res size:{},id:{}",res.size(),res.get(0).getId());
            for(Map.Entry<String, String> entry : words.entrySet()){
                String sensitiveWord = entry.getValue();
                if(!StringUtils.isEmpty(sensitiveWord)){
                    res.forEach(item -> {
                        if (sensitiveWord.equals(item.getSenWord())){
                            resultMap.put(entry.getKey(),entry.getValue());
                            return;
                        }
                    });
                }
            }
        }
        return resultMap;
    }



    @SneakyThrows
    public void checkParams(@RequestBody LsdsGoodsVo vo, ResultMode<String> resultModel) {
        //#region 判断验证数据
        if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrShortName())) {
            resultModel.setErrMsg("出发地简称:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrProvince())) {
            resultModel.setErrMsg("出发地省编码:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrCity())) {
            resultModel.setErrMsg("出发地市编码:" + "不能为空字符");
            resultModel.setSucceed(false);
        }

        if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrProvinceName())) {
            resultModel.setErrMsg("出发地省名称:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrCityName())) {
            resultModel.setErrMsg("出发地市名称:" + "不能为空字符");
            resultModel.setSucceed(false);
        }

        if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrDetail())) {
            resultModel.setErrMsg("出发地详细地址 :" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getSendLinker())) {
            resultModel.setErrMsg("出发地联系人:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getSendPhoneNumber())) {
            resultModel.setErrMsg("出发地联系人联系方式:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrShortName())) {
            resultModel.setErrMsg("目的地简称:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrProvince())) {
            resultModel.setErrMsg("目的地省编码:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrCity())) {
            resultModel.setErrMsg("目的地市编码:" + "不能为空字符");
            resultModel.setSucceed(false);
        }

        if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrProvinceName())) {
            resultModel.setErrMsg("目的地省名称:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrCityName())) {
            resultModel.setErrMsg("目的地市名称:" + "不能为空字符");
            resultModel.setSucceed(false);
        }

        if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrDetail())) {
            resultModel.setErrMsg("目的详细地址 :" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveLinker())) {
            resultModel.setErrMsg("目的地联系人:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getReceivePhoneNumber())) {
            resultModel.setErrMsg("目的地联系人联系方式:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getCompanyId())) {
            resultModel.setErrMsg("公司ID:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getGoodsName())) {
            resultModel.setErrMsg("货物名称:" + "不能为空字符");
            resultModel.setSucceed(false);
        }

        if (StringUtils.isEmpty(vo.getLsdsGoods().getGoodsType())) {
            resultModel.setErrMsg("货物类型:" + "不能为空字符");
            resultModel.setSucceed(false);
        }

        if (vo.getLsdsGoods().getWeightSum() == null || vo.getLsdsGoods().getWeightSum().equals(BigDecimal.ZERO)) {
            resultModel.setErrMsg("总重量:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
//        if (vo.getLsdsGoods().getOtherKuiTonsRatio() == null) {
//            resultModel.setErrMsg("亏吨免赔系数:" + "数据格式不能为空");
//            resultModel.setSucceed(false);
//        }
        goodsDeductibleService.checkParams(vo.getLsdsGoods().getLsdsGoodsDeductible(), resultModel);

        if (StringUtils.isEmpty(vo.getLsdsGoods().getTransportationType())) {
            resultModel.setErrMsg("运输类型:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getReleaseDate())) {
            resultModel.setErrMsg("预计发货时间:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getArriveDate())) {
            resultModel.setErrMsg("要求到货时间:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getDealStatus())) {
            resultModel.setErrMsg("交易状态:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(vo.getLsdsGoods().getValidityDate())) {
            resultModel.setErrMsg("货源有效期:" + "不能为空");
            resultModel.setSucceed(false);
        }
        if (!StringUtils.isEmpty(vo.getLsdsGoods().getValidityDate())) {
            if (vo.getLsdsGoods().getValidityDate().compareTo(DateUtils.getDateTime()) <= 0) {
                resultModel.setErrMsg("货源有效期必须晚于当前时间");
                resultModel.setSucceed(false);
            }
        }
        if (!StringUtils.isEmpty(vo.getLsdsGoods().getEnquiryType()) && vo.getLsdsGoods().getEnquiryType().equals("2") && null != vo.getGoodsSplitList() && vo.getGoodsSplitList().size() > 0) {
            resultModel.setErrMsg("拆分运输段后不允许【指定单价】！");
            resultModel.setSucceed(false);
        }
        if (!StringUtils.isEmpty(vo.getLsdsGoods().getEnquiryRange()) && vo.getLsdsGoods().getEnquiryRange().equals("1") && (null == vo.getAssignSupplierList() || vo.getAssignSupplierList().size() == 0)) {
            resultModel.setErrMsg("询价范围为【指定物流公司】，请至少指定一个物流公司！");
            resultModel.setSucceed(false);
        }
        if (!StringUtils.isEmpty(vo.getLsdsGoods().getEnquiryRange()) && vo.getLsdsGoods().getEnquiryRange().equals("2") && (null != vo.getAssignSupplierList() && vo.getAssignSupplierList().size() > 0)) {
            resultModel.setErrMsg("询价范围为【面向物流公司】，无需指定物流公司！");
            resultModel.setSucceed(false);
        }
    }

    /***
      *
      * @param vo
      * @Description
      * @return boolean
      * <AUTHOR>
      * @date 2022/2/22 15:42
      ***/
    @SneakyThrows
    public boolean checkPlatformIs3PL(LsdsGoodsVo vo) {
        log.info("企业id:{}",vo.getLsdsGoods().getCompanyId());
        PlatformUmCompanyFilter companyFilter = new PlatformUmCompanyFilter();
        companyFilter.setCompanyId(vo.getLsdsGoods().getCompanyId());
        ResultMode<PlatformUmCompany> companyAndAdminInfoByCompanyId = platformCommonInterClient.getCompanyAndAdminInfoByCompanyId(companyFilter);

        if (companyAndAdminInfoByCompanyId != null && !CollectionUtils.isEmpty(companyAndAdminInfoByCompanyId.getModel())){
            log.info("平台3PL标识PlatformFlag:{},调平台是否成功：{}",companyAndAdminInfoByCompanyId.getModel().get(0).getPlatformFlag(),companyAndAdminInfoByCompanyId.getSucceed());
            //平台3PL标识,0不是3PL 1是3PL
            return companyAndAdminInfoByCompanyId.getSucceed() && org.apache.commons.lang3.StringUtils.equals(companyAndAdminInfoByCompanyId.getModel().get(0).getPlatformFlag(),"1");
        }
        return false;

    }
    @SneakyThrows
    public boolean judgeAutoAudit(LsdsGoods lsdsGoods) {
        //企业货源自动审核逻辑,调用接口，入参（companyId,maniBodyId）
        log.info("请求参数：companyId：{},mainBodyId:{}",lsdsGoods.getCompanyId(),lsdsGoods.getNetworkMainBodyId());
        Map<String,String> model = new HashMap<>();
        model.put("companyId",lsdsGoods.getCompanyId());
        model.put("mainBodyId",lsdsGoods.getNetworkMainBodyId());
        model.put("bizType",Constants.GOODS_AUTO_CHECK);
        ResultMode<Boolean> booleanResultMode = platformUmCompanyInter.judgeAutoAudit(model);
        log.info("企业货源自动审核请求出参：{}",booleanResultMode.toString());
        List<Boolean> booleans = booleanResultMode.getModel();
        boolean check = (booleans!=null&&booleans.size()>0)?booleans.get(0):false;
        log.info("是否是企业货源的自动审核：{}",check);//货运类型(1-传统模式(默认)、 2-网络模式)
        return check;
    }

    /***
      * 人工审核
      * @param vo 承运商货源
      * @Description
      * @return ResultMode
      * <AUTHOR>
      * @date 2022/2/22 16:28
      ***/
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> handleManualAudit(LsdsGoodsVo vo)  {
        LsdsGoods lsdsGoods = vo.getLsdsGoods();
        if (StringUtils.isEmpty(lsdsGoods.getGoodsId())) {
            log.info(">>>>>>>>添加人工审核>>>>>>>>>>>>>");
            ResultMode resultModel = lsdsGoodsAdd(vo, "");

            if (resultModel != null && !CollectionUtils.isEmpty(resultModel.getModel())){
                String goodsId = (String)resultModel.getModel().get(0);
                log.info("2-goodsId:{},dealStatus:{}",goodsId,vo.getLsdsGoods().getDealStatus());
                if (StrUtil.equals(vo.getLsdsGoods().getDealStatus(), DealStatusEnum.TODO_AUDIT.getCode()) && resultModel.getSucceed() && !CollectionUtils.isEmpty(resultModel.getModel())) {
                    vo.getLsdsGoods().setGoodsId((String)resultModel.getModel().get(0));
                    vo.getLsdsGoods().setDealStatus(DealStatusEnum.TODO_AUDIT.getCode());//已发布
                    //vo.getLsdsGoods().setTransactionContractingBodyId(vo.getLsdsGoods().getCompanyId());
                    resultModel = lsdsGoodsUpdate(vo, "");
                    resultModel.getModel().add(goodsId);
                }
            }
            return resultModel;
        }
        //如果goodsId不为空，则为更新（即是编辑草稿后保存或提交）,平台3pl发布货源时直接审核
        if (StrUtil.equals(vo.getLsdsGoods().getDealStatus(), DealStatusEnum.TODO_AUDIT.getCode())) {
            vo.getLsdsGoods().setDealStatus(DealStatusEnum.RELEASE.getCode());//已发布
        }
        return lsdsGoodsUpdate(vo, "");
    }

    /***
      *
      * @param vo 承运商货源
      * @Description  企业货源自动审核
      * @return ResultMode
      * <AUTHOR>
      * @date 2022/2/22 16:33
      ***/
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> handleAutoAudit(LsdsGoodsVo vo) {

        log.info("交易状态：{}",vo.getLsdsGoods().getDealStatus());
        //用户信息校验
        PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
        if (userInfoFromCache == null) {
            return new ResultMode("301", "获取当前登录用户数据为空，请重新登录后再试！");
        }

        //待发布和待审核
        if (!StringUtils.hasText(vo.getLsdsGoods().getDealStatus())&&(!org.apache.commons.lang3.StringUtils.equals(vo.getLsdsGoods().getDealStatus(),DealStatusEnum.TODO_AUDIT.getCode()))) {
            return new ResultMode("302", "货源状态传输错误，请确认后再试！");
        }

        //调规则引擎
        List<String> errorsList = lsdsGoodsRuleDomainService.check(vo.getLsdsGoods());
        List<String> remark = new ArrayList<>();
        if (!CollectionUtils.isEmpty(errorsList)){
            log.error("规则引擎校验不通过,错误原因：{}", JSONUtil.toJsonStr(errorsList));

            errorsList.stream().forEach(e->{
                remark.add(e);
            });
            return new ResultMode("308", remark.toString()+"不满足条件,不能自动审核！！");
        }

        //获取企业维护的默认交易签约主体
        String transactionBodyCompanyId = "";
        ResultMode<PlatformUmCompanyMain> platformUmCompanyMainResultMode = platformUmCompanyInter.selectPlatformUmCompanyMainsEnableByCompanyIdOrSocialCreditCode(userInfoFromCache.getCompanyId(), "", "1");//交易签约主体
        if (platformUmCompanyMainResultMode.getSucceed()) {
            List<PlatformUmCompanyMain> list = platformUmCompanyMainResultMode.getModel().stream().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(list)){
                log.info("交易签约主体个数：{}",list.size());
                if (list.size()>1){
                    //String errCode, String errMsg, List<T> model
                    return new ResultMode(StatusCodeEnum.TRANSACTION_SIGN_NOT_ONLY.getCode(),StatusCodeEnum.TRANSACTION_SIGN_NOT_ONLY.getTipMsg());
                }

                //设置货源的默认交易签约主体
                LsdsGoods  lsdsGoods = vo.getLsdsGoods();
                log.info("获取到的交易签约主体id:{}",list.get(0).getMainId());
                transactionBodyCompanyId = list.get(0).getCompanyId();
                lsdsGoods.setTransactionContractingBodyId(list.get(0).getMainId());
                vo.setLsdsGoods(lsdsGoods);
            }else{
                throw new LsdsWlydException("未设置默认交易签约主体!");
            }
        }else{
            throw new LsdsWlydException("查询默认交易签约主体失败!");
        }


        //获取企业维护的默认网络货运主体
        ResultMode<PlatformUmCompanyMain> platformUmCompanyMainResultModeWlhy = platformUmCompanyInter.selectPlatformUmCompanyMainsEnableByCompanyIdOrSocialCreditCode(userInfoFromCache.getCompanyId(), "", "2");//网络货运主体
        if (platformUmCompanyMainResultModeWlhy.getSucceed()) {
            List<PlatformUmCompanyMain> list = platformUmCompanyMainResultModeWlhy.getModel().stream().collect(Collectors.toList());

            log.info("网络货运主体个数：{}",list.size());
            if (CollectionUtils.isEmpty(list)){
                throw new LsdsWlydException("未查找到网络货运主体");
            }
            if (list.size()>1){
                //String errCode, String errMsg, List<T> model
                return new ResultMode(StatusCodeEnum.NETWORK_MAIN_BODY_NOT_ONLY.getCode(),StatusCodeEnum.NETWORK_MAIN_BODY_NOT_ONLY.getTipMsg());
            }

            //设置货源设置网络货运主体
            LsdsGoods  lsdsGoods = vo.getLsdsGoods();
            log.info("获取到的网络货运主体id:{}",list.get(0).getMainId());
            lsdsGoods.setNetworkMainBodyId(list.get(0).getMainId());

            vo.setLsdsGoods(lsdsGoods);

        }else{
            throw new LsdsWlydException("查询默认网络货运主体失败");
        }

        LsdsGoods  lsdsGoods = vo.getLsdsGoods();
        PlatformUmCompanyFilter compFilterModel = new PlatformUmCompanyFilter();
        compFilterModel.setCompanyId(userInfoFromCache.getCompanyId());
        ResultMode<PlatformUmCompany> resMode = platformUmCompanyInter.getCompanyInfoByCondition(compFilterModel);
        List<PlatformUmCompany> companyList = resMode.getModel();
        log.info("企业个数：{}",companyList.size());
        if (!CollectionUtils.isEmpty(companyList)){
            log.info("货运类型：{}",companyList.get(0).getFreightType());
            if (org.apache.commons.lang3.StringUtils.equals("1",companyList.get(0).getFreightType())){
                return new ResultMode(StatusCodeEnum.NET_TRAN_ERROR.getCode(),StatusCodeEnum.NET_TRAN_ERROR.getTipMsg());
            }
        }

        lsdsGoods.setFreightType(FreightTypeEnum.NETWORK_MODE.getCode());//货运类型(1-传统模式(默认)、 2-网络模式

        log.info("甲方id:{},乙方id(交易签约主体id):{}",userInfoFromCache.getCompanyId(),lsdsGoods.getTransactionContractingBodyId());

        // V92删掉查合同逻辑（兼容V27.4审核隐藏按xx上浮，默认为按单价上浮，值为0）
        lsdsGoods.setFeeClearType("2");
        lsdsGoods.setFeeClearValue(BigDecimal.ZERO);

        //设置审核时间
        vo.getLsdsGoods().setAuditDate(DateUtils.getDateTime());
        log.info("1审核状态：{}",vo.getLsdsGoods().getDealStatus());

        vo.setLsdsGoods(lsdsGoods);
        //承运商货源自动审核新增
        log.info("货源id：{}",vo.getLsdsGoods().getGoodsId());
        if (StringUtils.isEmpty(vo.getLsdsGoods().getGoodsId())) {
            ResultMode resultModel = lsdsGoodsAdd(vo, "");
            String goodsId = (String)resultModel.getModel().get(0);
            log.info("goodsId:{},resultModel:{}",goodsId,JSONUtil.toJsonStr(resultModel));
            if ( org.apache.commons.lang3.StringUtils.equals(vo.getLsdsGoods().getDealStatus(), "2") && resultModel.getSucceed() && !CollectionUtils.isEmpty(resultModel.getModel())) {
                lsdsGoods.setGoodsId((String)resultModel.getModel().get(0));
                lsdsGoods.setDealStatus(DealStatusEnum.RELEASE.getCode());
                //审核记录状态【1-审核通过,2-审核不通过】
                vo.getLsdsGoods().setRecordStatus(LsdsEnum.LsdsgoodsRecordStatusEnum.recordStatus1.getCode());
                vo.getLsdsGoods().setCurrentOfferStartDate(DateUtils.getDateTime());
                resultModel = lsdsGoodsUpdate(vo, "");
                resultModel.getModel().add(goodsId);
            }

            return resultModel;
        }
        //如果goodsId不为空，则为更新（即是编辑草稿后保存或提交）,平台3pl发布货源时直接审核
        if (org.apache.commons.lang3.StringUtils.equals(DealStatusEnum.TODO_AUDIT.getCode(),vo.getLsdsGoods().getDealStatus())) {
            vo.getLsdsGoods().setDealStatus(DealStatusEnum.RELEASE.getCode());
        }


        log.info("更新扩展管理属性值,审核状态：{}",lsdsGoods.getRecordStatus());
        return lsdsGoodsUpdate(vo, "");

    }

    /***
      * 操作日志
      * @param
      * @Description
      * @return {@link }
      * <AUTHOR>
      * @date 2022/2/23 10:57
      ***/
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void writeOperateLog(String errMsg,String goodsId) {
        PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
        //同步货源操作记录
        GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
        LsdsGoodsRecord lgr =  LsdsGoodsRecord.of().setRecordId(String.valueOf(IdUtil.generateId())).setCompanyId(userInfoFromCache.getCompanyId());
        lgr.setOperateStatus("10").setOperateContent("承运商货源-自动审核")
            .setUserId("自动审核").setUserLoginName(userInfoFromCache.getLoginName()).setUserName("自动审核管理员")
            .setOperateDate(DateUtils.getDate("yyyy-MM-dd HH:mm:ss"));
        log.info("errMsg:{},goodsId:{}",errMsg,goodsId);
        lgr.setRemark(errMsg);
        log.info("自动审核失败！错误原因：{}",errMsg);
        //同步货源操作记录保存
        lgr.setGoodsId(goodsId);
        recDao.add(lgr);
    }


    @SneakyThrows
    public String checkNetworkMainBody(LsdsGoodsVo vo) {
        //用户信息校验
        PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
        if (userInfoFromCache == null) {
            throw new LsdsWlydException("获取当前登录用户数据为空，请重新登录后再试！");
        }

        //获取企业的网络签约主体
        ResultMode<PlatformUmCompanyMain> platformUmCompanyMainResultModeWlhy = platformUmCompanyInter.selectPlatformUmCompanyMainsEnableByCompanyIdOrSocialCreditCode(userInfoFromCache.getCompanyId(), "", "2");//网络货运主体
        log.info("返回结果：{}",platformUmCompanyMainResultModeWlhy.getSucceed());
        if (platformUmCompanyMainResultModeWlhy.getSucceed()) {
            List<PlatformUmCompanyMain> list = platformUmCompanyMainResultModeWlhy.getModel().stream().filter(obj -> org.apache.commons.lang3.StringUtils.isNotBlank(obj.getDefaultMain()) && obj.getDefaultMain().equals("1")).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(list)) {
                log.info("未查找到网络货运主体,手动审核！！");
                return null;
            }

            if (list.size() > 1) {
                log.info("网络货运主体不唯一,请手动审核！！");
                return null;
            }

            log.info("网络货运主体id:{}",list.get(0).getMainId());
            return list.get(0).getMainId();

        } else {
            throw new LsdsWlydException("未设置默认网络货运主体!");
        }
    }

    /***
      *
      * @param
      * @Description  3PL校验
      * @return
      * <AUTHOR>
      * @date 2022/2/25 14:07
      ***/
    @SneakyThrows
    public ResultMode<String> handle3PLAudit(LsdsGoodsVo vo, boolean flag) {
        ResultMode<String> resultModel = new ResultMode<String>();
        if (StringUtils.isEmpty(vo.getLsdsGoods().getGoodsId())) {
            resultModel = lsdsGoodsAdd(vo, "");
            if (resultModel!=null && !CollectionUtils.isEmpty(resultModel.getModel())){
                String goodsId = resultModel.getModel().get(0);
                if(flag &&org.apache.commons.lang3.StringUtils.equals(DealStatusEnum.TODO_AUDIT.getCode(),vo.getLsdsGoods().getDealStatus()) && resultModel.getSucceed() && !CollectionUtils.isEmpty(resultModel.getModel())){
                    vo.getLsdsGoods().setGoodsId(resultModel.getModel().get(0));
                    vo.getLsdsGoods().setDealStatus(DealStatusEnum.RELEASE.getCode());//交易状态【1-待发布(草稿),2-待审核,3-发布中(即审核通过),4-审核不通过,5-:已成交,6-:已过期,7-:已撤销(货源关闭),8-已删除】
                    //vo.getLsdsGoods().setTransactionContractingBodyId(vo.getLsdsGoods().getCompanyId());
                    resultModel = lsdsGoodsUpdate(vo, "");
                    resultModel.getModel().add(goodsId);
                }
                return resultModel;
            }


        } else {
            //如果goodsId不为空，则为更新（即是编辑草稿后保存或提交）,平台3pl发布货源时直接审核
            if(flag && org.apache.commons.lang3.StringUtils.equals(DealStatusEnum.TODO_AUDIT.getCode(),vo.getLsdsGoods().getDealStatus()) ){
                vo.getLsdsGoods().setDealStatus(DealStatusEnum.RELEASE.getCode());
            }
            return lsdsGoodsUpdate(vo, "");
        }
        return null;

    }


//    @Transactional
//    public void doAudit(LsdsGoods lsdsGoods) throws Exception {
//        if (lsdsGoods.getDealStatus().equals(Constants.GOODS_STATUS_RELEASED)) {
//            //如果货源为指定单价，则需根据公式设置开票价，上浮后的价格
//            if (lsdsGoods.getEnquiryType().equals(Constants.OFFER_STATUS_WAIT_CONFIRM)) {
//                driverOfferService.calculatePrice(lsdsGoods);
//            }
//            //如果是指定司机的货源，则需要创建对应的待报价和待确定数据给司机
//            if (lsdsGoods.getEnquiryRange().equals(Constants.ENQUIRY_RANGE_DRIVERS) && org.apache.commons.lang3.StringUtils.isNotBlank(driverGoods.getDriverIds())) {
//                driverOfferService.generateDriverOffer(lsdsGoods);
//            }
//            //设置货源发布时间
//            lsdsGoods.setReleasedDate(new Date());
//        }
//        //设置审核时间
//        lsdsGoods.setAuditDate(new Date());
//        this.updateById(lsdsGoods);
//    }


    /**
     * 根据货物ID获取基价
     * @param goodsId
     * @return
     */
  public   ResultMode<LsdsGoods> getGoodsBasePriceById(String goodsId){
        List<LsdsGoods> goods = goodsMapper.getGoodsBasePriceById(goodsId);
        log.info("{}",goods.size());

        if(goods.size()>0){
            log.info("返回的货源为{}",goods.get(0));
            if(goods.get(0)!=null){
                return ResultMode.success(goods.get(0));
            }else{
                return ResultMode.fail("没有查到货源记录");
            }

        }
        return ResultMode.fail("没有查到货源记录");
    }

    public ResultMode<LsdsGoods> lsdsGoodsInfo(String goodsId) {
        String errMsg = "";
        try {
            if (StringUtils.hasText(goodsId)){
                GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
                LsdsGoods lsdsGoods = dao.getLsdsGoodsInfo(goodsId);
                if (lsdsGoods != null){
                    return ResultMode.success(lsdsGoods);
                }
            }
        } catch (Exception ex) {
            errMsg = "获取货源编号  货源信息表信息异常 货源编号:"+goodsId;
            LogHelper.writeError(errMsg, ex);

        }
        return ResultMode.fail("获取货源编号  货源信息表信息异常");
    }

    public List<LsdsGoods> getTransportmileageOrTakeTimeNullData() {
        return goodsMapper.getTransportmileageOrTakeTimeNullData();
    }


    /*public CrmCompanyLineAddress createLsdsGoodsLine(LsdsGoods lsdsGoods) {
        CrmCompanyLineAddress startCrmCompanyLineAddress = null;
        CrmCompanyLineAddress endCrmCompanyLineAddress = null;

        String companyId = "";
        if(null != lsdsGoods.getOrderId()
                && lsdsGoods.getOrderId().trim().length()>0
                && !lsdsGoods.getOrderId().startsWith("POW")) {
            OmsOrderVo orderVo = new OmsOrderVo();
            orderVo.setOrderId(lsdsGoods.getOrderId());
            ResultMode<OmsOrder> orderResult = omsOrderInter.omsOrderGet(orderVo);
            if(null != orderResult
                && orderResult.getSucceed()
                && null !=orderResult.getModel()
                && orderResult.getModel().size()>0
                && null != orderResult.getModel().get(0)) {
                OmsOrder order = orderResult.getModel().get(0);
                String goodsId = order.getGoodsId();
                if(null != goodsId && goodsId.trim().length()>0) {
                    LsdsGoods goods = goodsMapper.getModel(goodsId);
                    companyId = goods.getCompanyId();
                } else {
                    throw new WlydException("地址信息异常");
                }
            } else {
                throw new WlydException("订单信息异常");
            }
        } else {
            companyId = lsdsGoods.getCompanyId();
        }

        log.info("      -----> companyId: {}", companyId);
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(lsdsGoods.getSendAddrShortName())){
            //startCrmCompanyLineAddress = crmCompanyLineAddressInter.findLineByLineShortNameAndCompanyId(lsdsGoods.getSendAddrShortName(),lsdsGoods.getCompanyId()).getModel().get(0);
            startCrmCompanyLineAddress = crmCompanyLineAddressInter.findLineByLineShortNameAndCompanyId(lsdsGoods.getSendAddrShortName(),companyId).getModel().get(0);
            lsdsGoods.setStartLineId(startCrmCompanyLineAddress.getLineId());
        }


        log.info(">>>>>>>>>>>>>>>>>>>SendAddrShortName:{},ReceiveAddrShortName:{}",lsdsGoods.getSendAddrShortName(),lsdsGoods.getReceiveAddrShortName());

        if (org.apache.commons.lang3.StringUtils.isNotEmpty(lsdsGoods.getReceiveAddrShortName())){
            //endCrmCompanyLineAddress = crmCompanyLineAddressInter.findLineByLineShortNameAndCompanyId(lsdsGoods.getReceiveAddrShortName(),lsdsGoods.getCompanyId()).getModel().get(0);
            endCrmCompanyLineAddress = crmCompanyLineAddressInter.findLineByLineShortNameAndCompanyId(lsdsGoods.getReceiveAddrShortName(),companyId).getModel().get(0);
            lsdsGoods.setEndLineId(endCrmCompanyLineAddress.getLineId());
        }
        //

        if(startCrmCompanyLineAddress == null){
            log.info(">>>根据出发地ID：{}>>>出发地为空！！",lsdsGoods.getSendAddrShortName());
            return null;
        }

        if(endCrmCompanyLineAddress == null){
            log.info(">>>根据地址ID：{}>>>目的地为空！！",lsdsGoods.getReceiveAddrShortName());
            return null;
        }

        //根据地址判断线路是否存在,存在返回，不存在创建线路
        CheckLineExitsFilter checkLineExitsFilter = new CheckLineExitsFilter();
        checkLineExitsFilter.setEndCrmCompanyLineAddress(endCrmCompanyLineAddress);
        checkLineExitsFilter.setStartCrmCompanyLineAddress(startCrmCompanyLineAddress);
        CrmCompanyLineAddress lineObj = crmCompanyLineAddressInter.checkLineExits(checkLineExitsFilter);
        if (lineObj != null){

            lsdsGoods.setLineId(lineObj.getLineId());
            lsdsGoods.setTransportMileage(lineObj.getTransportMileage());
            lsdsGoods.setTakeUpTime(lineObj.getTakeUpTime());
        }
        return lineObj;
    }*/

    public ResultMode<LsdsGoods> getGoodsInfoByQuery(List<String> goodsIds) {
       log.info("查询物品信息请求参数：goodsIds:{}",JSONUtil.toJsonStr(goodsIds));
        ResultMode<LsdsGoods> resultModel = new ResultMode<LsdsGoods>();
        GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);

        try{
            if (StringUtils.isEmpty(goodsIds)) {
                resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
                resultModel.setErrMsg("goodsIds:不能为空字符");
                resultModel.setSucceed(false);
            } else {
                List<LsdsGoods> list = dao.getGoodsInfoByQuery(goodsIds);
                resultModel.setModel(list);
                resultModel.setTotal(list.size());
                resultModel.setSucceed(true);
            }

        }catch(Exception e){

            resultModel.setErrMsg("操作失败:"+e.getMessage());
            resultModel.setSucceed(false);
            e.printStackTrace();
        }

       return resultModel;
    }


    public ResultMode<LsdsGoods> getGoodsInfoContainAddress(String goodsId) {
       if(!StringUtils.hasText(goodsId)){
           log.info("货源id为空不处理！");
           return ResultMode.success(null);
       }
        GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        LsdsGoods goodsInfoContainAddress = dao.getGoodsInfoContainAddress(goodsId);
        return ResultMode.success(goodsInfoContainAddress);
    }

    /**
     * 统计常用地址已产生的企业货源数
     *
     * @param lineId  地址id
     */
    public ResultMode<Integer> countAddressInUse(String lineId) {
        if(StrUtil.isBlank(lineId)){
            return ResultMode.success(0);
        }

        int lsdsGoodsCount = goodsMapper.countAddressInUse(lineId);
        int driverGoodsCount = driverGoodsMapper.countAddressInUse(lineId);

        return ResultMode.success(lsdsGoodsCount + driverGoodsCount);
    }

    /**
     * 统计常用路线已产生的货源数
     *
     * @param lineIds  路线id
     */
    public ResultMode<Integer> countLineInUse(List<String> lineIds) {
        if(IterUtil.isEmpty(lineIds)){
            return ResultMode.success(0);
        }

        int lsdsGoodsCount = goodsMapper.countLineInUse(lineIds);
        int driverGoodsCount = driverGoodsMapper.countLineInUse(lineIds);

        return ResultMode.success(lsdsGoodsCount + driverGoodsCount);
    }

    private ResultMode<CrmCompanyLineAddressBO> getCrmCompanyLineAddress(LsdsGoods lsdsGoods) {
        ResultMode<CrmCompanyLineAddressBO>  resultModel = new ResultMode<CrmCompanyLineAddressBO>();

        String companyId = JwtUtil.getInstance().getCompanyIdByToken();
        if(StrUtil.isNotBlank(lsdsGoods.getCompanyId())){
            companyId = lsdsGoods.getCompanyId();
        }
        //转交易使用订单上的线路和地址信息，不创建新的线路和地址
        if(StrUtil.isBlank(lsdsGoods.getLineId()) && !lsdsGoods.getIsTransaction()){
            String lineShortName = lsdsGoods.getSendAddrShortName()+"-"+lsdsGoods.getReceiveAddrShortName();
            ResultMode resultMode = lineAddressExchangeService.createCrmCompanyLine(
                lineShortName, lsdsGoods.getStartLineId(), lsdsGoods.getEndLineId(),
                LineSourceEnum.RELEASE_GOODS.getCode(), companyId);
            if(!resultMode.getSucceed()){
                return resultMode;
            }
            String lineId = resultMode.getModel().get(0).toString();
            lsdsGoods.setLineId(lineId);
        }
        CrmCompanyLineAddressBO crmCompanyLineAddressBO = lineAddressExchangeService.getCrmCompanyLine(lsdsGoods.getLineId());
        if(crmCompanyLineAddressBO==null){
            resultModel.setSucceed(false);
            resultModel.setErrMsg("未找到对应的线路");
            return resultModel;
        }
        if(StrUtil.equals(crmCompanyLineAddressBO.getItem3(),"1")){
            resultModel.setSucceed(false);
            resultModel.setErrMsg("该线路已经被删除");
            return resultModel;
        }
        if(!lsdsGoods.getIsTransaction() && !StrUtil.equals(crmCompanyLineAddressBO.getCompanyId(), companyId)){
            resultModel.setSucceed(false);
            resultModel.setErrMsg("线路" + crmCompanyLineAddressBO.getLineShortName() + "在该企业下不存在");
            return resultModel;
        }
        resultModel.setSucceed(true);
        resultModel.getModel().add(crmCompanyLineAddressBO);
        return resultModel;
    }

    private void initAddressInfo(LsdsGoods lsdsGoods, CrmCompanyLineAddressBO startAddress, CrmCompanyLineAddressBO endAddress,
                                 CrmCompanyLineAddressBO crmCompanyLineAddressBO){
        lsdsGoods.setStartLineId(startAddress.getLineId());
        lsdsGoods.setSendAddrProvince(startAddress.getSendAddrProvince());
        lsdsGoods.setSendAddrProvinceName(startAddress.getSendAddrProvinceName());
        lsdsGoods.setSendAddrCity(startAddress.getSendAddrCity());
        lsdsGoods.setSendAddrCityName(startAddress.getSendAddrCityName());
        lsdsGoods.setSendAddrArea(startAddress.getSendAddrArea());
        lsdsGoods.setSendAddrAreaName(startAddress.getSendAddrAreaName());
        lsdsGoods.setSendAddrDetail(startAddress.getSendAddrDetail());

        lsdsGoods.setSendAddrShortName(startAddress.getLineShortName());
        lsdsGoods.setSendLinker(startAddress.getStartSendLinker());
        lsdsGoods.setSendPhoneNumber(startAddress.getStartSendPhoneNumber());


        lsdsGoods.setEndLineId(endAddress.getLineId());
        lsdsGoods.setReceiveAddrProvince(endAddress.getSendAddrProvince());
        lsdsGoods.setReceiveAddrProvinceName(endAddress.getSendAddrProvinceName());
        lsdsGoods.setReceiveAddrCity(endAddress.getSendAddrCity());
        lsdsGoods.setReceiveAddrCityName(endAddress.getSendAddrCityName());
        lsdsGoods.setReceiveAddrArea(endAddress.getSendAddrArea());
        lsdsGoods.setReceiveAddrAreaName(endAddress.getSendAddrAreaName());
        lsdsGoods.setReceiveAddrDetail(endAddress.getSendAddrDetail());

        lsdsGoods.setReceiveAddrShortName(endAddress.getLineShortName());
        lsdsGoods.setReceiveLinker(endAddress.getStartSendLinker());
        lsdsGoods.setReceivePhoneNumber(endAddress.getStartSendPhoneNumber());

        lsdsGoods.setLineId(crmCompanyLineAddressBO.getLineId());
        lsdsGoods.setTransportMileage(crmCompanyLineAddressBO.getTransportMileage());
        lsdsGoods.setTakeUpTime(crmCompanyLineAddressBO.getTakeUpTime());
    }


    public ResultMode<String> createCrmLine(LsdsGoodsVo vo) {
        ResultMode<String> resultModel = new ResultMode<String>();
        LsdsGoods lsdsGoods  = vo.getLsdsGoods();
        ResultMode<CrmCompanyLineAddressBO> crmCompanyLineAddressResultMode =  getCrmCompanyLineAddress(lsdsGoods);
        if(!crmCompanyLineAddressResultMode.getSucceed()){
            resultModel.setSucceed(false);
            resultModel.setErrMsg(crmCompanyLineAddressResultMode.getErrMsg());
            return resultModel;
        }
        CrmCompanyLineAddressBO crmCompanyLineAddressBO = crmCompanyLineAddressResultMode.getModel().get(0);
        CrmCompanyLineAddressBO startAddress = lineAddressExchangeService.getCrmCompanyLine(crmCompanyLineAddressBO.getSendAddrId());
        if(startAddress==null){
            resultModel.setSucceed(false);
            resultModel.setErrMsg("发出地地址为空");
            return resultModel;
        }
        if(!lsdsGoods.getIsTransaction() && !StrUtil.equals(startAddress.getCompanyId(), lsdsGoods.getCompanyId())){
            resultModel.setSucceed(false);
            resultModel.setErrMsg("出发地地址" + startAddress.getLineShortName() + "在该企业下不存在");
            return resultModel;
        }
        CrmCompanyLineAddressBO endAddress = lineAddressExchangeService.getCrmCompanyLine(crmCompanyLineAddressBO.getReceiveAddrId());
        if(endAddress==null){
            resultModel.setSucceed(false);
            resultModel.setErrMsg("目的地地址为空");
            return resultModel;
        }
        if(!lsdsGoods.getIsTransaction() && !StrUtil.equals(endAddress.getCompanyId(), lsdsGoods.getCompanyId())){
            resultModel.setSucceed(false);
            resultModel.setErrMsg("目的地地址" + endAddress.getLineShortName() + "在该企业下不存在");
            return resultModel;
        }

        initAddressInfo(lsdsGoods,startAddress,endAddress, crmCompanyLineAddressBO);
        vo.setLineId(crmCompanyLineAddressBO.getLineId());
        vo.setLsdsGoods(lsdsGoods);
        return resultModel;
    }

    public ResultMode<LsdsGoods> getLsdsGoodsListByGoodsIdList(List<String> goodsIds) {
        log.info("getLsdsGoodsListByGoodsIdList：goodsIds:{}", JSONUtil.toJsonStr(goodsIds));
        ResultMode<LsdsGoods> resultModel = new ResultMode<LsdsGoods>();
        GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        try {
            if (StringUtils.isEmpty(goodsIds)) {
                resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
                resultModel.setErrMsg("货源ID不能为空");
                resultModel.setSucceed(false);
            } else {
                List<LsdsGoods> list = dao.getLsdsGoodsListByGoodsIdList(goodsIds);
                resultModel.setModel(list);
                resultModel.setTotal(list.size());
                resultModel.setSucceed(true);
            }
        } catch (Exception e) {
            resultModel.setErrMsg("查询失败");
            resultModel.setSucceed(false);
        }
        return resultModel;
    }


    /**
     * 填充中标文件
     * @param goodsId
     * @param offerListMap
     */
    private void appendBidFile(String goodsId, List<Map<String, Object>> offerListMap) {
        GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        LsdsGoods lsdsGoods = dao.getLsdsGoodsInfo(goodsId);
        if(ObjUtil.isNull(lsdsGoods)){
            return;
        }
        if(!StrUtil.equals(lsdsGoods.getGoodsSourceType(), GoodsSourceTypeEnum.BID_OPENING.getCode())) {
            return;
        }
        //招标货源填充中标信息
        LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
        List<LsdsGoodsOffer> offer = offerDao.selectGoodsOffer(goodsId, OfferStatusEnum.TRADED.getCode());
        if(CollUtil.isEmpty(offer)) {
            return;
        }
        List<BidsFilesFilter> bidsFilesFilter = lsdsGoodsBidFileService.getBidsFilesFilter(goodsId, LsdsAttachmentTypeEnum.WIN_BIDS.getType());
        if(CollUtil.isEmpty(bidsFilesFilter)) {
            return;
        }
        BidsFilesFilter filesFilter = bidsFilesFilter.get(0);
        offerListMap.get(0).put("winBidFile",filesFilter);
    }

    public GoodsStatisticsDTO platformStatistics(LsdsGoodsFilter filter) {
        GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        Map<String, StatisticsDTO> statistics = dao.platformStatistics(filter);
        GoodsStatisticsDTO result = new GoodsStatisticsDTO();
        if (CollectionUtil.isNotEmpty(statistics)) {
            StatisticsDTO deal = statistics.get(DealStatusEnum.DEAL.getCode());
            StatisticsDTO release = statistics.get(DealStatusEnum.RELEASE.getCode());
            StatisticsDTO expire = statistics.get(DealStatusEnum.EXPIRED.getCode());
            int dealCount = deal != null ? deal.getNum() : 0;
            int releaseCount = release != null ? release.getNum() : 0;
            int expireCount = expire != null ? expire.getNum() : 0;
            // 已成交
            result.setTraded(dealCount);
            // 发布中 + 已过期
            result.setUnsettled(releaseCount + expireCount);
            // 已成交 + 发布中 + 已过期
            result.setValid(result.getTraded() + result.getUnsettled());
        }
        return result;
    }

    public void sendMsg(String phoneNumber,String userId,String companyId,String templateId,Map<String, String> param ) {
        try {
            PlatformUmCompanyFilter companyFilter = new PlatformUmCompanyFilter();
            companyFilter.setCompanyId(companyId);
            ResultMode<PlatformUmCompany> companyResultMode = platformCommonInterClient.getCompanyAndAdminInfoByCompanyId(companyFilter);
            String parentUserId = companyResultMode.getModel().get(0).getExUserBaseId();
            Set<String> receiverList = new HashSet<>(2);
            if(!StringUtils.isEmpty(userId)){
                receiverList.add(userId);
            }
            if(!StringUtils.isEmpty(parentUserId) && !parentUserId.equals(userId)){
                receiverList.add(parentUserId);
            }
            //系统消息埋点,发送审核提交成功消息
            MsgInfo msgInfo = new MsgInfo();
            // 发送人 系统
            msgInfo.setSender("1");
            // 接收人的手机号
            //msgInfo.setPhone(phoneNumber);
            // 设置站内信接收人
            msgInfo.setReceiverList(receiverList);
            msgInfo.setTemplateId(templateId);
            //参数信息
            String paramStr = JSON.toJSONString(param);
            msgInfo.setTemplateParameter(paramStr);
            //将要发送的用户查询出手机号 begin by huchuyin
            Set<String> phoneList = new HashSet<String>();
            if(!CollectionUtils.isEmpty(receiverList)) {
                for(String receiverId:receiverList) {
                    try {
                        log.info("根据用户ID查询用户信息参数：{}",receiverId);
                        ResultMode<PlatformUmUserbaseinfo> resultMode = platformCommonInterClient.getUserBaseInfoByUserBaseId(receiverId);
                        log.info("根据用户ID查询用户信息参数：{}",JSON.toJSONString(resultMode));
                        if(resultMode != null && resultMode.getSucceed()
                            && !CollectionUtils.isEmpty(resultMode.getModel())) {
                            PlatformUmUserbaseinfo userbaseinfo = resultMode.getModel().get(0);
                            if(userbaseinfo != null) {
                                phoneList.add(userbaseinfo.getTelephone());
                            }
                        }
                    } catch (Exception e) {
                        LogHelper.writeError("根据用户ID查询用户信息异常",e);
                    }
                }
            }
            msgInfo.setPhoneList(phoneList);
            //将要发送的用户查询出手机号 end
            systemMsgInter.send(msgInfo);
        } catch (Exception e) {
            LogHelper.writeError("方法【sendMsg】发送消息出现异常：", e);
        }
    }
}
