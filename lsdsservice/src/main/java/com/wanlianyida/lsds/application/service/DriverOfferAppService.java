package com.wanlianyida.lsds.application.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.*;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.eval.api.inter.EvalDriverInfoInter;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.DriverOffer;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsRecord;
import com.isoftstone.hig.lsds.api.enums.GoodsKindEnum;
import com.isoftstone.hig.lsds.api.filter.DriverOfferFilter;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.lsds.api.util.Result;
import com.isoftstone.hig.msg.api.entity.MsgInfo;
import com.isoftstone.hig.msg.api.inter.SystemMsgInter;
import com.isoftstone.hig.platform.api.client.PlatformCommonInterClient;
import com.isoftstone.hig.platform.api.entity.PlatformCmPlatformParameter;
import com.isoftstone.hig.platform.api.entity.PlatformUmUserbaseinfo;
import com.isoftstone.hig.platform.api.inter.PlatformCmPlatformParameterInter;
import com.isoftstone.hig.platform.api.inter.PlatformUmUserbaseinfoInter;
import com.isoftstone.hig.tcs.api.mvcvo.TcsDriverVO;
import com.isoftstone.hig.tms.api.entity.TmsEnum;
import com.isoftstone.hig.tms.api.entity.TmsWaybill;
import com.isoftstone.hig.tms.api.inter.TmsWaybillInter;
import com.isoftstone.hig.tms.api.query.TmsWaybillFilter;
import com.wanlianyida.lsds.infrastructure.exchange.AmountRoundingModeExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.TcsExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.TmsExchangeService;
import com.wanlianyida.lsds.infrastructure.repository.mapper.DriverGoodsMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.DriverOfferMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.GoodsRecordMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.GoodsSourceMapper;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 司机报价Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@Service
@Log4j2
public class DriverOfferAppService {

    @Resource
    private DriverOfferMapper lsdsDriverOfferMapper;

    @Resource
    private DriverGoodsMapper driverGoodsMapper;
    @Resource
    private PlatformCmPlatformParameterInter platformCmPlatformParameterInter;

    @Resource
    private DriverGoodsAppService driverGoodsService;

    @Resource
    private EvalDriverInfoInter evalDriverInfoInter;

    @Resource
    private PlatformCommonInterClient platformCommonInterClient;

    @Resource
    private SystemMsgInter systemMsgInter;

    @Resource
    private TmsWaybillInter tmsWaybillInter;

    @Resource
    private PlatformUmUserbaseinfoInter platformUmUserbaseinfoInter;

    @Resource
    private RmsAppService rmsBusiness;

    @Resource
    private AmountRoundingModeExchangeService amountRoundingModeService;

    @Resource
    private TmsExchangeService tmsExchangeService;

    @Resource
    private KafkaTemplate kafkaTemplate;
    @Resource
    private PlatformExchangeService platformExchangeService;
    @Resource
    private TcsExchangeService tcsExchangeService;

    @Resource
    private GoodsSourceMapper goodsSourceMapper;

    /**
     * 查询司机报价
     *
     * @param id 司机报价ID
     * @return 司机报价
     */

    public DriverOffer getById(String id) {
        DriverOffer driverOffer = lsdsDriverOfferMapper.getById(id);
        return driverOffer;
    }

    /**
     * 查询司机报价列表
     *
     * @param driverOfferFilter 司机报价
     * @return 司机报价
     */

    public List<DriverOffer> listByEntity(DriverOfferFilter driverOfferFilter) {
        return lsdsDriverOfferMapper.listByEntity(driverOfferFilter);
    }

    /**
     * 新增司机报价
     *
     * @param driverOffer 司机报价
     * @return 结果
     */

    public int save(DriverOffer driverOffer) {
        return lsdsDriverOfferMapper.save(driverOffer);
    }

    /**
     * 修改司机报价
     *
     * @param offer 司机报价
     * @return 结果
     */

    public int updateById(DriverOffer offer) {
        return lsdsDriverOfferMapper.modifyById(offer);
    }

    /**
     * 删除司机报价对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */

    public int removeByIds(String[] ids) {
        return lsdsDriverOfferMapper.removeByIds(ids);
    }

    /**
     * 删除司机报价信息
     *
     * @param id 司机报价ID
     * @return 结果
     */

    public int removeById(String id) {
        return lsdsDriverOfferMapper.removeById(id);
    }

    /**
     * 分页查询司机报价列表
     *
     * @param pageInfo 司机报价
     * @return 司机报价
     */
    public List<DriverOffer> page(PagingInfo<DriverOfferFilter> pageInfo) {
        List<DriverOffer> list = lsdsDriverOfferMapper.listByEntity(pageInfo.getFilterModel());
        return list;
    }

    /**
     * @Description 超载校验方法
     * <AUTHOR>
     * @Date 2021/6/10 14:40
     * @Version 1.0
     * @Param
     * @Return
     */
    public ResultMode overloadCheck(DriverOfferFilter offer, DriverGoods goods, String flag) {
        if (goods != null) {
            if (Constants.GOODS_STATUS_CLOSED.equals(goods.getDealStatus())) {
                return new ResultMode("500", "货源已撤销！");
            }
            if (Constants.GOODS_STATUS_EXPIRE.equals(goods.getDealStatus())) {
                return new ResultMode("500", "货源已过期！");
            }

            if (UtilityEnum.TransportationAllTypeEnum.INVERT_SHORT.getLsdsCode().equalsIgnoreCase(goods.getTransportationType())) {
                //货源是否过期、有效期校验接口
                Date newDate = new Date();
                if (newDate.after(goods.getValidityDate())) {
                    return new ResultMode("500", "货源已过期！");
                }
                String code = "047";
                //获取platform中的参数配置
                ResultMode<PlatformCmPlatformParameter> paraCode = platformCmPlatformParameterInter.getByParaCodes(Arrays.asList(code));
                if (!CollectionUtils.isEmpty(paraCode.getModel())) {
                    //货源快到期判定接口；结合参数配置取到天数进行比对。
                    Calendar ca = Calendar.getInstance();
                    ca.setTime(new Date());// 获取的是当天的日期，可以自己获取需要计算的时间，传进来就可以，需要注意的是日期类型。
                    ca.add(Calendar.DATE, Integer.valueOf(paraCode.getModel().get(0).getParaValue()));
                    Date addParametersDate = Calendar.getInstance().getTime();

                    if (addParametersDate.after(goods.getValidityDate())) {
                        //即将到期提示司机
                        log.info("快过期了 货源提醒！！！");
                    }
                }

            }
        }

        if (!StringUtils.equals(flag, "AUTO")) {
            TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
            if (offer.getReceivingOrdersQuantity() == null) {
                return new ResultMode(UtilityEnum.AppErrorCode.CODE_1012.getCode(), "接单数量不能为空");
            } else if (offer.getReceivingOrdersUnitPrice() == null) {
                return new ResultMode(UtilityEnum.AppErrorCode.CODE_1012.getCode(), "接单单价不能为空");
            } else if (offer.getReceivingOrdersWeight() == null) {
                return new ResultMode(UtilityEnum.AppErrorCode.CODE_1012.getCode(), "接单重量不能为空");
            } else if (StringUtils.isBlank(offer.getTractorPlateNumber())) {
                return new ResultMode(UtilityEnum.AppErrorCode.CODE_1012.getCode(), "承运牵引车车牌号不能为空");
            } else if (StringUtils.isBlank(offer.getTractorPlateCarId())) {
                return new ResultMode(UtilityEnum.AppErrorCode.CODE_1012.getCode(), "承运牵引车id不能为空");
            }
            if (StrUtil.equals(LsdsEnum.FreightTypeEnum.FREIGHT_TYPE_2.getCode(), offer.getFreightType())) {
                if (StringUtils.isBlank(offer.getReceiptAccount())) {
                    return new ResultMode(UtilityEnum.AppErrorCode.CODE_1012.getCode(), "收款银行卡号不能为空");
                } else if (StringUtils.isBlank(offer.getReceiptAccountName())) {
                    return new ResultMode(UtilityEnum.AppErrorCode.CODE_1012.getCode(), "收款户名不能为空");
                } else if (StringUtils.isBlank(offer.getReceiptBank())) {
                    return new ResultMode(UtilityEnum.AppErrorCode.CODE_1012.getCode(), "收款开户行不能为空");
                } else if (StringUtils.isBlank(offer.getTelephone())) {
                    return new ResultMode(UtilityEnum.AppErrorCode.CODE_1012.getCode(), "收款户手机号不能为空");
                } else if (StringUtils.isBlank(offer.getReceiptorIdCardNo())) {
                    return new ResultMode(UtilityEnum.AppErrorCode.CODE_1012.getCode(), "收款户身份证号码不能为空");
                }
            }

            //司机评级校验
            if (goods != null && StringUtils.isNotBlank(goods.getDriverGrade())) {
                String driverId = tokenInfo.getDriverId();
                if (driverId == null) {
                    //刚刚实名认证之后没有刷新token，没有司机id，需要手动去查
                    ResultMode<String> resultDriverIdMode = platformUmUserbaseinfoInter.getDriverIdByUserBaseId();
                    LogHelper.writeDebug("尝试获取司机id结果:{}", JSON.toJSONString(resultDriverIdMode));
                    if (resultDriverIdMode != null && !CollectionUtils.isEmpty(resultDriverIdMode.getModel())) {
                        driverId = resultDriverIdMode.getModel().get(0);
                    }
                }
                ResultMode<Double> doubleResultMode = evalDriverInfoInter.selectByDriverId(driverId);
                LogHelper.writeDebug("司机评级校验:doubleResultMode:{}", JSON.toJSONString(doubleResultMode));
                if (doubleResultMode.getSucceed() && doubleResultMode.getModel() != null) {
                    Double grade = doubleResultMode.getModel().get(0);
                    Double grade2 = null;
                    switch (goods.getDriverGrade()) {
                        case "10":
                            grade2 = new Double(5);
                            break;
                        case "20":
                            grade2 = new Double(4);
                            break;
                        case "30":
                            grade2 = new Double(3);
                            break;
                        case "40":
                            grade2 = new Double(2);
                            break;
                        case "50":
                            grade2 = new Double(1);
                            break;
                        case "60":
                            grade2 = new Double(0);
                            break;
                    }
                    //司机评级不满足
                    if (grade < grade2) {
                        return new ResultMode(UtilityEnum.AppErrorCode.CODE_1015.getCode(), UtilityEnum.AppErrorCode.CODE_1015.getDesc());
                    }

                } else {
                    return new ResultMode(UtilityEnum.AppErrorCode.CODE_1014.getCode(), UtilityEnum.AppErrorCode.CODE_1014.getDesc());
                }
            }
            //接单重量上限校验
            //获取接单重量上下限配置
            ResultMode<PlatformCmPlatformParameter> paraCodes = platformCmPlatformParameterInter.getByParaCodes(LsdsEnum.WeightLimitParams.getCodes());
            if (paraCodes.getSucceed() && paraCodes.getModel() != null && paraCodes.getModel().size() > 0) {
                Integer orderLimit = null;
                for (PlatformCmPlatformParameter parameter : paraCodes.getModel()) {
                    if (LsdsEnum.WeightLimitParams.ORDER_LIMIT.getCode().equals(parameter.getParaCode())) {
                        orderLimit = new Integer(parameter.getParaValue());
                    }
                }
                if (StringUtils.isEmpty(tokenInfo.getDriverId())) {
                    //刚刚实名认证之后没有刷新token，没有司机id，需要手动去查
                    ResultMode<String> resultDriverIdMode = platformUmUserbaseinfoInter.getDriverIdByUserBaseId();
                    LogHelper.writeDebug("尝试获取司机id结果:{}", JSON.toJSONString(resultDriverIdMode));
                    if (resultDriverIdMode != null && !CollectionUtils.isEmpty(resultDriverIdMode.getModel())) {
                        String driverId = resultDriverIdMode.getModel().get(0);
                        tokenInfo.setDriverId(driverId);
                    }
                }
                //公开询价 需要检验报价次数
                if (Constants.ENQUIRY_TYPE_OPEN.equals(goods.getEnquiryType())) {
                    DriverOfferFilter filterModel = new DriverOfferFilter();
                    filterModel.setGoodsId(goods.getId());
                    filterModel.setOfferStatus(Constants.OFFER_STATUS_WAIT_CONFIRM);
                    filterModel.setDriverId(tokenInfo.getDriverId());
                    List<DriverOffer> list = lsdsDriverOfferMapper.listByEntity(filterModel);
                    if (list.size() > 0) {
                        return new ResultMode(UtilityEnum.AppErrorCode.CODE_1017.getCode(), UtilityEnum.AppErrorCode.CODE_1017.getDesc().replace("XXX", offer.getDriverName()));
                    }
                }

                if (StringUtils.isEmpty(tokenInfo.getDriverId())) {
                    //刚刚实名认证之后没有刷新token，没有司机id，需要手动去查
                    ResultMode<String> resultDriverIdMode = platformUmUserbaseinfoInter.getDriverIdByUserBaseId();
                    LogHelper.writeDebug("尝试获取司机id结果:{}", JSON.toJSONString(resultDriverIdMode));
                    if (resultDriverIdMode != null && !CollectionUtils.isEmpty(resultDriverIdMode.getModel())) {
                        String driverId = resultDriverIdMode.getModel().get(0);
                        tokenInfo.setDriverId(driverId);
                    }
                }

                //多次接单/报价校验
                TmsWaybillFilter filter = new TmsWaybillFilter();
                filter.setDriverId(tokenInfo.getDriverId());
                filter.setGoodsId(goods.getId());
                ResultMode<Integer> resultMode = tmsWaybillInter.getDriverWaybillCount(filter);
                LogHelper.writeDebug("多次接单/报价限制:resultMode:{}", JSON.toJSONString(resultMode));
                if (resultMode.getSucceed() && resultMode.getModel() != null) {
                    Integer waybillCount = resultMode.getModel().get(0);
                    if (waybillCount < orderLimit) {
                        return Result.success();
                    } else {
                        if (Constants.ENQUIRY_TYPE_OPEN.equals(goods.getEnquiryType())) {
                            return new ResultMode(UtilityEnum.AppErrorCode.CODE_1017.getCode(), UtilityEnum.AppErrorCode.CODE_1017.getDesc().replace("XXX", offer.getDriverName()));
                        } else {
                            return new ResultMode(UtilityEnum.AppErrorCode.CODE_1018.getCode(), UtilityEnum.AppErrorCode.CODE_1018.getDesc().replace("XXX", offer.getDriverName()));
                        }
                    }
                } else {
                    return new ResultMode(UtilityEnum.AppErrorCode.CODE_1016.getCode(), UtilityEnum.AppErrorCode.CODE_1016.getDesc());
                }

            }

        }
        return Result.success();
    }

    /**
     * @Description 司机货源报价
     * <AUTHOR>
     * @Date 2021/5/31 10:03
     * @Version 1.0
     * @Param
     * @Return
     */
    public ResultMode sourceQuotation(DriverOfferFilter offerFilter) {

        LogHelper.writeDebug("司机货源报价:offerFilter:{}", JSON.toJSONString(offerFilter));
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        if (StringUtils.isEmpty(tokenInfo.getDriverId())) {
            //刚刚实名认证之后没有刷新token，没有司机id，需要手动去查
            ResultMode<String> resultDriverIdMode = platformUmUserbaseinfoInter.getDriverIdByUserBaseId();
            LogHelper.writeDebug("尝试获取司机id结果:{}", JSON.toJSONString(resultDriverIdMode));
            if (resultDriverIdMode != null && !CollectionUtils.isEmpty(resultDriverIdMode.getModel())) {
                String driverId = resultDriverIdMode.getModel().get(0);
                tokenInfo.setDriverId(driverId);
            } else {
                try {
                    throw new RuntimeException("新注册的用户,请重新登录!!");

                } catch (Exception e) {
                    e.printStackTrace();
                }
                return ResultMode.fail("新注册的用户,请重新登录!!");
            }
        }

        DriverGoods goods = driverGoodsMapper.getById(offerFilter.getGoodsId());
        DriverOffer offer = null;

        // 风控接单测算
        if (ObjectUtil.isNotNull(goods)) {
            ResultMode resultMode = rmsBusiness.checkOrderReceiving(goods, offerFilter);
            if (!resultMode.getSucceed()) {
                return resultMode;
            }
        }

        //报价校验
        ResultMode checkResult = overloadCheck(offerFilter, goods, "");
        if (!checkResult.getSucceed()) {
            return checkResult;
        }
        if (StringUtils.isEmpty(tokenInfo.getDriverId())) {
            //刚刚实名认证之后没有刷新token，没有司机id，需要手动去查
            ResultMode<String> resultDriverIdMode = platformUmUserbaseinfoInter.getDriverIdByUserBaseId();
            LogHelper.writeDebug("尝试获取司机id结果:{}", JSON.toJSONString(resultDriverIdMode));
            if (resultDriverIdMode != null && !CollectionUtils.isEmpty(resultDriverIdMode.getModel())) {
                String driverId = resultDriverIdMode.getModel().get(0);
                tokenInfo.setDriverId(driverId);
            }
        }

        //查询报价单
        DriverOfferFilter parameter = new DriverOfferFilter();
        parameter.setDriverId(tokenInfo.getDriverId());
        parameter.setGoodsId(offerFilter.getGoodsId());
        List<DriverOffer> list = lsdsDriverOfferMapper.listByEntity(parameter);

        if (Constants.ENQUIRY_RANGE_DRIVERS.equals(goods.getEnquiryRange())) {
            if (CollectionUtils.isEmpty(list)) {
                return ResultMode.fail("报价单不存在！", "500");
            }
            offer = list.get(0);

        }

        //询价范围为平台司机 需要新增报价单数据
        if (Constants.ENQUIRY_RANGE_PLATFORM_DRIVERS.equals(goods.getEnquiryRange()) || Constants.ENQUIRY_RANGE_OWN_DRIVERS.equals(goods.getEnquiryRange())) {
            offer = new DriverOffer();
            offer.setGoodsId(offerFilter.getGoodsId());
            //查询当前司机用户信息
            TcsDriverVO driver = tcsExchangeService.getDriverByDriverId(tokenInfo.getDriverId());
            if (driver != null) {
                offer.setIdCardNo(driver.getLicenseNo());
                offer.setDriverId(driver.getDriverCode());
                offer.setDriverName(driver.getDriverName());
                offer.setContactPhoneNumber(driver.getDriverPhoneNumber());

                //查询司机评级
                ResultMode<Double> doubleResultMode = evalDriverInfoInter.selectByDriverId(tokenInfo.getDriverId());
                LogHelper.writeDebug("报价司机评级:doubleResultMode:{}", JSON.toJSONString(doubleResultMode));
                if (doubleResultMode.getSucceed() && doubleResultMode.getModel() != null) {
                    offer.setDriverGrade(doubleResultMode.getModel().get(0) + "");
                }
                //查询司机运营表示和是否实名
                offer.setOperateTag("2");
                offer.setIsRealAuthentication("1");
            }
            offer.setId(UtilityClass.uuid());
            offer.setCreateBy(tokenInfo.getUserBaseId());
            offer.setCreateName(tokenInfo.getUsername());
            offer.setCreateDate(new Date());

        }
        //设置共有参数
        goods.setEnquiryTypeBasePrice(offerFilter.getReceivingOrdersUnitPrice());
        goods = calculatePrice(goods, true);
        offer.setEnquiryTypeBasePrice(goods.getFloatEnquiryTypeBasePrice());
        offer.setEnquiryTypeBaseOpenTicket(goods.getFloatEnquiryTypeBaseOpenTicket());
        offer.setFloatEnquiryTypeBaseOpenTicket(goods.getEnquiryTypeBaseOpenTicket());
        offer.setFloatEnquiryTypeBasePrice(goods.getEnquiryTypeBasePrice());
        offer.setEnquiryTypeBaseTaxRate(goods.getEnquiryTypeBaseTaxRate());
        offer.setTotalQuantityUnits(goods.getTotalQuantityUnits());
        offer.setReceivingOrdersQuantity(offerFilter.getReceivingOrdersQuantity());
        offer.setReceivingOrdersUnitPrice(offerFilter.getReceivingOrdersUnitPrice());
        offer.setTractorPlateCarId(offerFilter.getTractorPlateCarId());
        offer.setTractorPlateNumber(offerFilter.getTractorPlateNumber());
        offer.setTractorPlateColor(offerFilter.getTractorPlateColor());
        offer.setTrailerPlateCarId(offerFilter.getTrailerPlateCarId());
        offer.setTrailerPlateNumber(offerFilter.getTrailerPlateNumber());
        offer.setTrailerPlateCarColor(offerFilter.getTrailerPlateColor());
        offer.setReceivingOrdersWeight(offerFilter.getReceivingOrdersWeight());
        offer.setReceiptAccount(offerFilter.getReceiptAccount());
        offer.setReceiptAccountName(offerFilter.getReceiptAccountName());
        offer.setReceiptBank(offerFilter.getReceiptBank());
        offer.setEstimatedFreight(offer.getReceivingOrdersQuantity().multiply(offer.getFloatEnquiryTypeBasePrice()).setScale(2, goods.getRoundingMode()));
        offer.setOfferStatus(Constants.OFFER_STATUS_WAIT_CONFIRM);
        offer.setQuotationTimes("1");
        offer.setTelephone(offerFilter.getTelephone());
        offer.setIdCard(offerFilter.getReceiptorIdCardNo());

        //NOTES 车队长代报价 保存承运司机和实际报价人的信息
        offer.setDriverId(offerFilter.getDriverId());
        offer.setDriverName(offerFilter.getDriverName());
        offer.setIdCardNo(offerFilter.getIdCardNo());                     //实际承运人身份证号
        offer.setContactPhoneNumber(offerFilter.getContactPhoneNumber()); //实际承运人手机号
        offer.setBidderDriverId(tokenInfo.getDriverId());

        // 设置货源码绑定关系Id
        Opt.ofBlankAble(offerFilter.getQrCodeRelationId()).ifPresent(offer::setQrCodeRelationId);

        //询价范围为指定司机 修改报价单数据
        if (Constants.ENQUIRY_RANGE_DRIVERS.equals(goods.getEnquiryRange())) {
            lsdsDriverOfferMapper.modifyById(offer);
        }
        //询价范围为平台司机 新增报价单数据
        if (Constants.ENQUIRY_RANGE_PLATFORM_DRIVERS.equals(goods.getEnquiryRange()) || Constants.ENQUIRY_RANGE_OWN_DRIVERS.equals(goods.getEnquiryRange())) {
            lsdsDriverOfferMapper.save(offer);
        }

        BigDecimal currentRoundOfferMinPrice = offer.getFloatEnquiryTypeBaseOpenTicket();
        List<DriverOffer> driverOfferAllList = lsdsDriverOfferMapper.queryByGoodsId(goods.getGoodsId());
        if (IterUtil.isNotEmpty(driverOfferAllList)) {
            BigDecimal minPrice = driverOfferAllList.stream().map(DriverOffer::getFloatEnquiryTypeBaseOpenTicket).min(Comparator.naturalOrder()).orElse(null);
            if (minPrice != null && minPrice.compareTo(currentRoundOfferMinPrice) < 0) {
                currentRoundOfferMinPrice = minPrice;
            }
        }
        //同步更新source表
        goodsSourceMapper.updateCurrentRoundOffer(goods.getGoodsId(), currentRoundOfferMinPrice);
        //LogHelper.writeDebug("司机货源报价:offer:{},goods:{}", JSON.toJSONString(offer), JSON.toJSONString(goods));

        //发送站内信给发布货源操作人
        String templateId = UtilityEnum.SystemMsgTemplateEnum.DRIVER_QUOTATION.getCode();
        Map<String, String> param = new HashMap<String, String>();
        param.put("goodsId", goods.getGoodsId());
        param.put("startSiteCityName", goods.getStartSiteCityName());
        param.put("endSiteCityName", goods.getEndSiteCityName());
        Set<String> receiverList = new HashSet<>();
        receiverList.add(goods.getCreateBy());
        sendMsg(receiverList, templateId, param);

        //发送报价的kafak消息到tcs
        offerFilter.setIdCard(offerFilter.getReceiptorIdCardNo());
        kafkaSendToTcs(offerFilter);

        ResultMode resultMode = new ResultMode<>();
        resultMode.getModel().add(offer);
        return resultMode;
    }

    /**
     * kafka 司机扫码报价、扫司机货源收款码接单后同步消息到tcs
     * 处理代收关系
     *
     * @param offerFilter
     */
    public void kafkaSendToTcs(DriverOfferFilter offerFilter) {
        try {
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCompletion(int status) {
                        if (status != STATUS_COMMITTED) {
                            return;
                        }
                        // 事务正常提交时发送
                        sendKafkaMsg(offerFilter);
                    }
                });
            } else {
                sendKafkaMsg(offerFilter);
            }
        } catch (Exception e) {
            log.error("kafka 消息发送失败", e);
        }
    }

    private void sendKafkaMsg(DriverOfferFilter offerFilter) {
        offerFilter.setAppDriverId(JwtUtil.getInstance().getAppDriverIdByToken());
        offerFilter.setUserBaseId(JwtUtil.getInstance().getUserBaseIdByToken());
        log.info("sendKafkaMsg#司机扫码报价、扫司机货源收款码接单推送tcs:{}", JSONUtil.toJsonStr(offerFilter));
        kafkaTemplate.send(UtilityEnum.KafkaTopicNameEnum.LSDS_TO_TCS_OFFER_COLLECT_SAVE.getTopicName(), JSONUtil.toJsonStr(offerFilter));
    }


    public void sendOrderMsgToShipper(DriverGoods goods, String orderId, String waybillId) {
        String templateIdByOrder = UtilityEnum.SystemMsgTemplateEnum.DRIVER_ORDER_RECEIVING_ORDER.getCode();
        Map<String, String> paramByOrder = new HashMap<String, String>();
        paramByOrder.put("orderId", orderId);
        paramByOrder.put("waybillId", waybillId);
        paramByOrder.put("startSiteCityName", goods.getStartSiteCityName());
        paramByOrder.put("endSiteCityName", goods.getEndSiteCityName());
        Set<String> receiverListByOrder = new HashSet<>();
        receiverListByOrder.add(goods.getCreateBy());
        sendMsg(receiverListByOrder, templateIdByOrder, paramByOrder);
    }

    public void sendGoodsMsgToShipper(DriverGoods goods, String waybillId) {
        String templateIdBygoods = UtilityEnum.SystemMsgTemplateEnum.DRIVER_ORDER_RECEIVING_GOODS.getCode();
        Map<String, String> paramBygoods = new HashMap<String, String>();
        paramBygoods.put("goodsId", goods.getGoodsId());
        paramBygoods.put("waybillId", waybillId);
        paramBygoods.put("startSiteCityName", goods.getStartSiteCityName());
        paramBygoods.put("endSiteCityName", goods.getEndSiteCityName());
        Set<String> receiverListBygoods = new HashSet<>();
        receiverListBygoods.add(goods.getCreateBy());
        sendMsg(receiverListBygoods, templateIdBygoods, paramBygoods);
    }

    public void syncDriverGoodsOpertorRecord(TokenInfo tokenInfo, DriverOffer offer, DriverGoods goods) {
        GoodsRecordMapper recDao = SpringContextUtil.getBeanByClass(GoodsRecordMapper.class);
        LsdsGoodsRecord lgr = LsdsGoodsRecord.of();
        lgr.setRecordId(String.valueOf(IdUtil.generateId()));
        lgr.setGoodsId(goods.getGoodsId());
        lgr.setOperateStatus("5");
        lgr.setOperateContent("货源成交");
        //撮合货源-议价下单
        if (GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(goods.getGoodsKind())
            && offer.getReceivingOrdersUnitPrice().compareTo(goods.getEnquiryTypeBasePrice()) != 0) {
            lgr.setOperateContent("议价下单");
            String remark = String.format("运单号%s更改了(接单单价)，更改前：%s，更改后：%s",
                offer.getWaybillId(), goods.getEnquiryTypeBasePrice(), offer.getReceivingOrdersUnitPrice());
            lgr.setRemark(remark);
        }
        lgr.setUserId(tokenInfo.getUserBaseId());
        lgr.setCompanyId(tokenInfo.getCompanyId());
        lgr.setUserLoginName(tokenInfo.getLoginName());
        lgr.setUserName(tokenInfo.getUsername());
        recDao.add(lgr);
    }


    /**
     * 通过发货方开票价调整发货方基价,司机基价,司机开票价
     *
     * @param setEnquiryTypeBaseOpenTicket 发货方开票价
     * @param goods                        调整后的司机货源
     * @return DriverGoods
     */
    public DriverGoods getDriverGoods(BigDecimal setEnquiryTypeBaseOpenTicket, DriverGoods goods) {
        //计算方式 传统货运取平台税率,网络货运取网络货运主体运费留存率
        BigDecimal calculateTaxRate = new BigDecimal("0.00");

        if (Constants.FREIGHT_TYPE_TRADITION.equals(goods.getFreightType())) {
            calculateTaxRate = goods.getEnquiryTypeBaseTaxRate().divide(new BigDecimal("100"), 7, BigDecimal.ROUND_DOWN);
        }
        if (Constants.FREIGHT_TYPE_NETWORK.equals(goods.getFreightType())) {
            calculateTaxRate = goods.getFreightSurvivalRate().divide(new BigDecimal("100"), 7, BigDecimal.ROUND_DOWN);
        }

        goods.setEnquiryTypeBaseOpenTicket(setEnquiryTypeBaseOpenTicket);
        goods.setEnquiryTypeBasePrice(setEnquiryTypeBaseOpenTicket.multiply(new BigDecimal("1").subtract(calculateTaxRate)).setScale(5, BigDecimal.ROUND_DOWN));
        goods.setEnquiryType(Constants.ENQUIRY_TYPE_ASSIGN);
        goods = calculatePrice(goods, false);
        return goods;
    }

    /**
     * @Description 指定司机发布货源生成预报价单
     * <AUTHOR>
     * @Date 2021/5/31 10:03
     * @Version 1.0
     * @Param
     * @Return
     */
    public void generateDriverOffer(DriverGoods goods) {
        //指定司机id集合
        List<String> driverIds = Arrays.asList(goods.getDriverIds().split(","));
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();

        driverIds.forEach(id -> {
            //查询司机用户信息
            DriverOffer offer = new DriverOffer();
            offer.setGoodsId(goods.getId());
            offer.setId(UtilityClass.uuid());
            TcsDriverVO driver = tcsExchangeService.getDriverByDriverId(id);
            if (driver != null) {

                //发送站内信给指定司机
                String templateId = UtilityEnum.SystemMsgTemplateEnum.WAIT_QUOTATION.getCode();
                Map<String, String> param = new HashMap<String, String>();
                param.put("startSiteCityName", goods.getStartSiteCityName());
                param.put("endSiteCityName", goods.getEndSiteCityName());
                Set<String> receiverList = new HashSet<>();
                receiverList.add(driver.getUserBaseId());
                sendMsg(receiverList, templateId, param);

                offer.setIdCardNo(driver.getLicenseNo());
                offer.setDriverId(driver.getDriverCode());
                offer.setDriverName(driver.getDriverName());
                offer.setContactPhoneNumber(driver.getDriverPhoneNumber());
                //查询司机评级
                ResultMode<Double> doubleResultMode = evalDriverInfoInter.selectByDriverId(id);
                LogHelper.writeDebug("预报价单司机评级:doubleResultMode:{}", JSON.toJSONString(doubleResultMode));
                if (doubleResultMode.getSucceed() && doubleResultMode.getModel() != null) {
                    offer.setDriverGrade(doubleResultMode.getModel().get(0) + "");
                }
                //查询司机运营标识和是否实名状态
                offer.setOperateTag("2");
                offer.setIsRealAuthentication("1");
            }
            if (Constants.ENQUIRY_TYPE_OPEN.equals(goods.getEnquiryType())) {
                offer.setOfferStatus(Constants.OFFER_STATUS_WAIT_OFFER);
            }
            //询价类型为指定单价 需要结算浮动金额
            if (Constants.ENQUIRY_TYPE_ASSIGN.equals(goods.getEnquiryType())) {
                offer.setOfferStatus(Constants.OFFER_STATUS_WAIT_CONFIRM);
                offer.setEnquiryTypeBasePrice(goods.getEnquiryTypeBasePrice());
                offer.setEnquiryTypeBaseOpenTicket(goods.getEnquiryTypeBaseOpenTicket());
                offer.setFloatEnquiryTypeBaseOpenTicket(goods.getFloatEnquiryTypeBaseOpenTicket());
                offer.setFloatEnquiryTypeBasePrice(goods.getFloatEnquiryTypeBasePrice());
                offer.setEnquiryTypeBaseTaxRate(goods.getEnquiryTypeBaseTaxRate());
//                offer.setEstimatedFreight(offer.getReceivingOrdersQuantity().multiply(offer.getFloatEnquiryTypeBasePrice()).setScale(2, BigDecimal.ROUND_UP));
            }

            offer.setCreateBy(tokenInfo.getUserBaseId());
            offer.setCreateName(tokenInfo.getUsername());
            offer.setCreateDate(new Date());
            lsdsDriverOfferMapper.save(offer);
        });

    }

    public List<DriverOffer> getDriverOfferList(PagingInfo<DriverOfferFilter> pageInfo) {
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        //NOTES 参数校验
        if (StringUtils.isEmpty(tokenInfo.getDriverId())) {
            ResultMode<String> resultDriverIdMode = platformUmUserbaseinfoInter.getDriverIdByUserBaseId();

            LogHelper.writeDebug("尝试获取司机id结果:{}", JSON.toJSONString(resultDriverIdMode));
            if (resultDriverIdMode != null && !CollectionUtils.isEmpty(resultDriverIdMode.getModel())) {
                String driverId = resultDriverIdMode.getModel().get(0);
                log.info(">>>>>>>>>>>>>>>>driverId:{}", driverId);
                tokenInfo.setDriverId(driverId);
                if (StringUtils.isEmpty(tokenInfo.getDriverId())) {
                    try {
                        throw new RuntimeException("新注册的用户,请退出重新登录后再操作！！");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return null;
                }
            }

        }
        DriverOfferFilter filterModel = pageInfo.getFilterModel();
        if (tokenInfo != null && !StringUtils.isEmpty(tokenInfo.getDriverId())) {

            //NOTES QLMR-124-查询实际报价人是登录账户的报价记录
            //filterModel.setDriverId(tokenInfo.getDriverId());
            filterModel.setBidderDriverId(tokenInfo.getDriverId());
            List<DriverOffer> list = lsdsDriverOfferMapper.listByEntity(filterModel);
            return list;
        }
        return null;
    }


    /**
     * <b>@Title: <T>  CloudPlatformProperties; <T> </b>
     * <p>@Description: 费用计算公共方法 <br>
     * <p>Module ID: com.isoftstone.hig.lsds.service.transational 包<p>
     * <p>Comments: 目前的现有12种计算场景中只有“传统货运方式”时按照上浮比例计算会出现少一分钱的情况，需要继续分析且跟进<p>
     * <p>JDK version used: JDK1.8;
     * <p>Namespace:<p>
     * <p>@author：Li.zx
     * <p>@ProjectName lsdsservice</p >
     * <p>@date： 2021-06-02</p >
     * <p>Modified By：lunan
     * <p>Modified Date:2022-04-29
     * <p>Why & What is modified
     * <p>@version:      lsds 1.0;
     * <p>CopyRright (c)2017-cloud:
     */
    public DriverGoods calculatePrice(DriverGoods goods, boolean resetFlag) {
        // 货源的金额取整方式
        Integer roundingMode = goods.getRoundingMode();
        if (null == roundingMode) {
            roundingMode = amountRoundingModeService.getRoundingMode(goods.getGoodsId());
            goods.setRoundingMode(roundingMode);
        }
        //计算方式 传统货运取平台税率,网络货运取网络货运主体运费留存率
        BigDecimal calculateTaxRate = new BigDecimal("0.00");
        //上浮费用设置值
        BigDecimal feeClearValue = goods.getFeeClearValue();

        //撮合货源，税率为0
        if (!GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(goods.getGoodsKind())) {
            if (Constants.FREIGHT_TYPE_TRADITION.equals(goods.getFreightType())) {
                calculateTaxRate = goods.getEnquiryTypeBaseTaxRate().divide(new BigDecimal("100"), 7, BigDecimal.ROUND_DOWN);
            }
            if (Constants.FREIGHT_TYPE_NETWORK.equals(goods.getFreightType())) {
                calculateTaxRate = goods.getFreightSurvivalRate().divide(new BigDecimal("100"), 7, BigDecimal.ROUND_DOWN);
            }
        }
        //按比例上浮须除以百分比
        if (Constants.FEE_CLEAR_TYPE_RATIO.equals(goods.getFeeClearType())) {
            feeClearValue = goods.getFeeClearValue().divide(new BigDecimal("100"), 7, BigDecimal.ROUND_DOWN);
        }

        //基价
        goods.setEnquiryTypeBasePrice(goods.getEnquiryTypeBasePrice());
        //开票价 预约调价时不需要重置开票价
        if (resetFlag) {
            goods.setEnquiryTypeBaseOpenTicket(goods.getEnquiryTypeBasePrice().divide(new BigDecimal("1").subtract(calculateTaxRate), 5, BigDecimal.ROUND_DOWN));
        }
        //费率上浮类型：10-：按单价比例上浮(单位%)
        if (Constants.FEE_CLEAR_TYPE_RATIO.equals(goods.getFeeClearType())) {
            //询价方式：10-：公开询价
            if (Constants.ENQUIRY_TYPE_OPEN.equals(goods.getEnquiryType())) {
                //上浮后开票价  公开询价：开票价*(1+上浮税率）= 上浮后开票价
                goods.setFloatEnquiryTypeBaseOpenTicket(goods.getEnquiryTypeBaseOpenTicket().multiply(new BigDecimal("1").add(feeClearValue)).setScale(5, BigDecimal.ROUND_DOWN));
                //上浮后基价  浮动后开票价*(1-平台税率）= 上浮后基价
                goods.setFloatEnquiryTypeBasePrice(goods.getFloatEnquiryTypeBaseOpenTicket().multiply(new BigDecimal("1").subtract(calculateTaxRate)).setScale(5, BigDecimal.ROUND_DOWN));
            }
            //询价方式：20-：指定单价
            if (Constants.ENQUIRY_TYPE_ASSIGN.equals(goods.getEnquiryType())) {
                //上浮后开票价  指定价格：开票价/(1+上浮税率）=上浮后开票价
                goods.setFloatEnquiryTypeBaseOpenTicket(goods.getEnquiryTypeBaseOpenTicket().divide(new BigDecimal("1").add(feeClearValue), 5, BigDecimal.ROUND_DOWN));
                //上浮后基价  浮动后开票价*(1-平台税率）= 上浮后基价
                goods.setFloatEnquiryTypeBasePrice(goods.getFloatEnquiryTypeBaseOpenTicket().multiply(new BigDecimal("1").subtract(calculateTaxRate)).setScale(5, BigDecimal.ROUND_DOWN));
            }

        }
        //费率上浮类型：20-：按单价上浮(单位元)
        if (Constants.FEE_CLEAR_TYPE_UN_PRICE.equals(goods.getFeeClearType())) {
            //询价方式：10-：公开询价
            if (Constants.ENQUIRY_TYPE_OPEN.equals(goods.getEnquiryType())) {
                //上浮后开票价  公开询价：开票价+上浮单价 = 上浮后开票价
                goods.setFloatEnquiryTypeBaseOpenTicket(goods.getEnquiryTypeBaseOpenTicket().add(feeClearValue).setScale(5, BigDecimal.ROUND_DOWN));
                //上浮后基价 上浮开票价*（1-平台税率）
                goods.setFloatEnquiryTypeBasePrice(goods.getFloatEnquiryTypeBaseOpenTicket().multiply(new BigDecimal("1").subtract(calculateTaxRate)).setScale(5, BigDecimal.ROUND_DOWN));
            }
            //询价方式：20-：指定单价
            if (Constants.ENQUIRY_TYPE_ASSIGN.equals(goods.getEnquiryType())) {
                //上浮后开票价  指定价格：开票价-上浮单价 = 上浮后开票价
                goods.setFloatEnquiryTypeBaseOpenTicket(goods.getEnquiryTypeBaseOpenTicket().subtract(feeClearValue).setScale(5, BigDecimal.ROUND_DOWN));
                //上浮后基价  浮动后开票价-上浮单价 = 上浮后基价
                //  goods.setFloatEnquiryTypeBasePrice(goods.getFloatEnquiryTypeBaseOpenTicket().subtract(feeClearValue).setScale(2, BigDecimal.ROUND_UP));
                //上浮后基价: 上浮开票价*（1-平台税率）
                goods.setFloatEnquiryTypeBasePrice(goods.getFloatEnquiryTypeBaseOpenTicket().multiply(new BigDecimal("1").subtract(calculateTaxRate)).setScale(5, BigDecimal.ROUND_DOWN));
            }
        }
        goods.setEnquiryTypeBasePrice(goods.getEnquiryTypeBasePrice().setScale(2, roundingMode));
        goods.setEnquiryTypeBaseOpenTicket(goods.getEnquiryTypeBaseOpenTicket().setScale(2, roundingMode));
        goods.setFloatEnquiryTypeBaseOpenTicket(goods.getFloatEnquiryTypeBaseOpenTicket().setScale(2, roundingMode));
        goods.setFloatEnquiryTypeBasePrice(goods.getFloatEnquiryTypeBasePrice().setScale(2, roundingMode));
        if (feeClearValue.compareTo(new BigDecimal("0.00")) == 0) {
            goods.setFloatEnquiryTypeBasePrice(goods.getEnquiryTypeBasePrice());
        }
        return goods;
    }

    /**
     * @Description 站内消息发送
     * <AUTHOR>
     * @Date 2021/8/4 16:11
     * @Verson 1.0
     * @Return a
     */
    public void sendMsg(Set<String> receiverList, String templateId, Map<String, String> param) {
        LogHelper.writeDebug("司机货源站内消息发送请求参数 receiverList:{},templateId:{},param:{}", receiverList, templateId, param);
        try {
            //系统消息埋点,发送审核提交成功消息
            MsgInfo msgInfo = new MsgInfo();
            // 发送人 系统
            msgInfo.setSender("1");
            // 设置站内信接收人
            msgInfo.setReceiverList(receiverList);
            msgInfo.setTemplateId(templateId);
            //参数信息
            String paramStr = JSON.toJSONString(param);
            msgInfo.setTemplateParameter(paramStr);
            //将要发送的用户查询出手机号 begin by huchuyin
            Set<String> phoneList = new HashSet<String>();
            if (!CollectionUtils.isEmpty(receiverList)) {
                for (String receiverId : receiverList) {
                    try {
                        LogHelper.writeInfo("根据用户ID查询用户信息参数：{}", receiverId);
                        ResultMode<PlatformUmUserbaseinfo> resultMode = platformCommonInterClient.getUserBaseInfoByUserBaseId(receiverId);
                        LogHelper.writeInfo("根据用户ID查询用户信息参数：{}", JSON.toJSONString(resultMode));
                        if (resultMode != null && resultMode.getSucceed()
                            && !CollectionUtils.isEmpty(resultMode.getModel())) {
                            PlatformUmUserbaseinfo userbaseinfo = resultMode.getModel().get(0);
                            if (userbaseinfo != null) {
                                phoneList.add(userbaseinfo.getTelephone());
                                msgInfo.setReceiver(receiverId);
                            }
                        }
                    } catch (Exception e) {
                        LogHelper.writeError("根据用户ID查询用户信息异常", e);
                    }
                }
            }
            msgInfo.setPhoneList(phoneList);
            LogHelper.writeDebug("方法【sendMsg】司机货源站内消息:{}", msgInfo);
            //将要发送的用户查询出手机号 end
            systemMsgInter.send(msgInfo);
        } catch (Exception e) {
            LogHelper.writeError("方法【sendMsg】发送司机货源站内消息异常：", e);
        }
    }

    /**
     * @param waybillId
     * @return
     * <AUTHOR>
     * @since 2020-1-19
     * 根据运单号获取报价对象
     */
    public List<DriverOffer> getTractorPlateCarIdByWaybillId(String waybillId) {
        return lsdsDriverOfferMapper.getTractorPlateCarIdByWaybillId(waybillId);
    }

    public DriverOffer getDriverOfferByOrderId(String orderId) {
        return this.lsdsDriverOfferMapper.getDriverOfferByOrderId(orderId);
    }

    public ResultMode<DriverOffer> getDriverOfferInfoByQuery(List<String> waybillIds) {
        ResultMode<DriverOffer> resultModel = new ResultMode<DriverOffer>();

        try {
            if (CollectionUtils.isEmpty(waybillIds)) {
                resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
                resultModel.setErrMsg("waybillIds:不能为空字符");
                resultModel.setSucceed(false);
            } else {
                List<DriverOffer> list = lsdsDriverOfferMapper.getDriverOfferInfoByQuery(waybillIds);
                resultModel.setModel(list);
                resultModel.setTotal(list.size());
                resultModel.setSucceed(true);
            }

        } catch (Exception e) {

            resultModel.setErrMsg("操作失败:" + e.getMessage());
            resultModel.setSucceed(false);
            e.printStackTrace();
        }

        return resultModel;
    }

    public List<DriverOffer> getDriverOfferByWaybillId(String waybillId) {
        return lsdsDriverOfferMapper.getDriverOfferByWaybillId(waybillId);
    }

    /**
     * 处理报价单
     */
    public void handleDriverOffer(DriverOffer offer, DriverGoods goods) {
        //1、存储报价单
        //1.1询价范围为指定司机、询价方式为公开询价 修改报价单
        if (Constants.ENQUIRY_RANGE_DRIVERS.equals(goods.getEnquiryRange()) || StrUtil.equals(goods.getEnquiryType(), Constants.ENQUIRY_TYPE_OPEN)) {
            lsdsDriverOfferMapper.modifyById(offer);
        }
        //撮合货源-报价单
        else if (GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(goods.getGoodsKind())) {
            //查询是否存在报价单
            DriverOffer driverOffer = lsdsDriverOfferMapper.getById(offer.getId());
            if (ObjUtil.isNull(driverOffer)) {
                lsdsDriverOfferMapper.save(offer);
            } else {
                lsdsDriverOfferMapper.modifyById(offer);
            }
        }
        //1.2询价范围为平台司机 新增报价单
        else if (Constants.ENQUIRY_RANGE_PLATFORM_DRIVERS.equals(goods.getEnquiryRange()) || Constants.ENQUIRY_RANGE_OWN_DRIVERS.equals(goods.getEnquiryRange())) {
            lsdsDriverOfferMapper.save(offer);
        }
        LogHelper.writeDebug("司机货源接单:offer:{},goods:{}", JSON.toJSONString(offer), JSON.toJSONString(goods));
        //2、货源状态为已成交时，剩下为未确认的报价单状态更新为未成交
        if (Constants.GOODS_STATUS_DEAL.equals(goods.getDealStatus())) {
            List<DriverOffer> list = lsdsDriverOfferMapper.getDriverOfferByGoodsId(goods.getGoodsId());
            if (CollUtil.isNotEmpty(list)) {
                for (DriverOffer obj : list) {
                    obj.setOfferStatus(Constants.OFFER_STATUS_NO_DEAL);
                    obj.setModifyDate(new Date());
                    lsdsDriverOfferMapper.modifyById(obj);
                }
            }
        }
    }

    public List<DriverOffer> queryDriverOfferByGoodsIds(List<String> goodsIds) {
        return lsdsDriverOfferMapper.queryDriverOfferByGoodsIds(goodsIds);
    }

    public List<DriverOffer> getUnacceptedOrdersByGoodsId(PagingInfo<DriverOfferFilter> pageInfo) {
        DriverOfferFilter filterModel = pageInfo.getFilterModel();
        DriverGoods driverGoods = driverGoodsService.getByGoodsId(filterModel.getGoodsId());
        List<String> wabillStatusList = new ArrayList<>(Arrays.asList(TmsEnum.TmswaybillstatusWabillHigStatusEnum.wabillstatus100.getCode(),
            TmsEnum.TmswaybillstatusWabillHigStatusEnum.wabillstatus110.getCode(), TmsEnum.TmswaybillstatusWabillHigStatusEnum.wabillstatus540.getCode()));
        if (driverGoods.getEnquiryType().equals(Constants.ENQUIRY_TYPE_OPEN)) {
            wabillStatusList.add(TmsEnum.TmswaybillstatusWabillHigStatusEnum.wabillstatus120.getCode());
        }
        List<TmsWaybill> waybills = tmsExchangeService.queryWaybillList(filterModel.getGoodsId(), wabillStatusList);
        if (IterUtil.isNotEmpty(waybills)) {
            List<String> list = waybills.stream().map(TmsWaybill::getWaybillId).collect(Collectors.toList());
            filterModel.setWaybillIdList(list);
            pageInfo.setFilterModel(filterModel);
        }
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        List<DriverOffer> list = lsdsDriverOfferMapper.getUnacceptedOrdersByGoodsId(pageInfo.getFilterModel());

        //填充运单状态
        fillWaybillInfo(list, waybills);

        return list;
    }


    /**
     * 填充运单状态
     *
     * @param list
     * @param waybillList
     */
    private void fillWaybillInfo(List<DriverOffer> list, List<TmsWaybill> waybillList) {
        if (IterUtil.isEmpty(list) || IterUtil.isEmpty(waybillList)) {
            return;
        }
        Map<String, TmsWaybill> waybillMap = waybillList.stream().collect(Collectors.toMap(TmsWaybill::getWaybillId, Function.identity(), (key1, key2) -> key2));
        for (DriverOffer driverOffer : list) {
            if (StrUtil.isNotEmpty(driverOffer.getWaybillId())) {
                TmsWaybill tmsWaybill = waybillMap.get(driverOffer.getWaybillId());
                if (tmsWaybill != null) {
                    driverOffer.setWabillStatus(tmsWaybill.getWabillStatus());
                }
            }
        }
    }

    /**
     * 批量查询司机报价
     */
    public List<DriverOffer> getByIds(List<String> ids) {
        if (IterUtil.isEmpty(ids)) {
            return null;
        }
        return lsdsDriverOfferMapper.getByIds(ids);
    }

}
