package com.wanlianyida.lsds.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.UtilityClass;
import com.isoftstone.hig.common.utils.ValidateUtils;
import com.isoftstone.hig.ims.api.entity.InsuranceTypeGoodsVo;
import com.isoftstone.hig.ims.api.filter.InsuranceTypeGoodsFilter;
import com.isoftstone.hig.ims.api.inter.ImsInsuranceTypeInter;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsName;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsNameRelation;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsNameFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsNameCreateVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsNameVo;
import com.isoftstone.hig.platform.api.entity.PlatformCmDictionary;
import com.isoftstone.hig.platform.api.entity.PlatformCmGoodsCategory;
import com.isoftstone.hig.platform.api.filter.PlatformCmGoodsCategoryFilter;
import com.isoftstone.hig.platform.api.inter.PlatformCmDictionaryInter;
import com.isoftstone.hig.platform.api.inter.PlatformCmGoodsCategoryInter;
import com.wanlianyida.lsds.domain.service.LsdsGoodsNameRelationDomainService;
import com.wanlianyida.lsds.domain.service.LsdsOperationRecordDomainService;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsGoodsNameMapper;
import com.wanlianyida.lsds.infrastructure.util.NumberGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <p>
 * 货物名称管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class LsdsGoodsNameAppService {

    @Resource
    private LsdsGoodsNameMapper goodsNameMapper;

    @Resource
    private LsdsGoodsNameRelationDomainService goodsNameRelationService;

    @Resource
    private LsdsOperationRecordDomainService operationRecordService;

    @Resource
    private PlatformCmGoodsCategoryInter platformCmGoodsCategoryInter;

    @Resource
    private PlatformCmDictionaryInter platformCmDictionaryInter;

    @Resource
    private ImsInsuranceTypeInter imsInsuranceTypeInter;

    @Resource
    private NumberGenerator numberGenerator;


    /**
     * 查询货品名称列表
     *
     * @param pageInfo 分页信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    public ResultMode<LsdsGoodsNameVo> getGoodsNameListPage(PagingInfo<LsdsGoodsNameFilter> pageInfo){
        //设置分页参数
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);

        LsdsGoodsNameFilter goodsNameFilter = pageInfo.getFilterModel();
        //条件查询
        List<LsdsGoodsNameVo> goodsNameVoList = queryGoodsNameListByCondition(goodsNameFilter);

        PageInfo<LsdsGoodsNameVo> pageInfoList = new PageInfo<>(goodsNameVoList);
        Long total = pageInfoList.getTotal();
        ResultMode<LsdsGoodsNameVo> returnModel = new ResultMode<>();
        returnModel.setTotal(total.intValue());
        returnModel.setModel(goodsNameVoList);
        return returnModel;
    }


    /**
     * @Description: 条件查询货物名称
     * @Author: Mindy
     * @Date: 2023/11/20
     * @Param:
     * @return:
     */
    public List<LsdsGoodsNameVo> queryGoodsNameListByCondition(LsdsGoodsNameFilter goodsNameFilter) {

        List<LsdsGoodsNameVo> goodsNameVoList = null;
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        //操作类型 1：3PL，2：4PL
        String operType = "1";
        if(StrUtil.equals(goodsNameFilter.getOperType(),operType)){
            if(StrUtil.isEmpty(goodsNameFilter.getOpInter()) || !StrUtil.equals(LsdsEnum.OpInterEnum.OPINTER_NO01.getCode(),goodsNameFilter.getOpInter())){
                if(tokenInfo!=null){
                    goodsNameFilter.setCompanyId(tokenInfo.getCompanyId());
                }
            }
            goodsNameVoList = goodsNameMapper.findGoodsNameListOfCompany(goodsNameFilter);
        }else{
            goodsNameVoList = goodsNameMapper.findGoodsNameList(goodsNameFilter);
        }

        paddingVO(goodsNameVoList);

        return goodsNameVoList;
    }

    private void paddingVO(List<LsdsGoodsNameVo> goodsNameVoList){
        if(IterUtil.isEmpty(goodsNameVoList)){
            return;
        }
        //Set<String> platformTypeSets = goodsNameVoList.stream().filter(item->!StrUtil.isEmpty(item.getPlatformType())).map(LsdsGoodsNameVo::getPlatformType).collect(Collectors.toSet());
        //Map<String,String> platformTypeMap = getPlatformCategoryMap(new ArrayList<>(platformTypeSets));

        Set<String> goodsTypeSets = goodsNameVoList.stream().filter(item->!StrUtil.isEmpty(item.getGoodsType())).map(LsdsGoodsNameVo::getGoodsType).collect(Collectors.toSet());
        Map<String,String> goodsTypeMap = getGoodsCategoryMap(new ArrayList<>(goodsTypeSets));
        for(LsdsGoodsNameVo goodsName:goodsNameVoList){
            if(!StrUtil.isEmpty(goodsName.getPlatformType())){
                //goodsName.setPlatformTypeName(platformTypeMap.get(goodsName.getPlatformType()));
            }
            if(!StrUtil.isEmpty(goodsName.getGoodsType())){
                goodsName.setGoodsTypeName(goodsTypeMap.get(goodsName.getGoodsType()));
            }
        }
    }


   /* private Map<String,String> getPlatformCategoryMap(List<String> platformTypeList){
        if(IterUtil.isEmpty(platformTypeList)){
            return new HashMap<>();
        }
        PlatformCmPlatformCategory platformCategoryFilter = new PlatformCmPlatformCategory();
        platformCategoryFilter.setCodeList(platformTypeList);
        ResultMode<PlatformCmPlatformCategory>  platformCategoryResultMode =  platformCmPlatformCategoryInter.findPlatformCategoryByCodes(platformCategoryFilter);
        if(platformCategoryResultMode==null || IterUtil.isEmpty(platformCategoryResultMode.getModel())){
            return new HashMap<>();
        }
        return platformCategoryResultMode.getModel().stream().filter(item->!StrUtil.isEmpty(item.getGoodsCatelogCode()) && !StrUtil.isEmpty(item.getGoodsCatelogName())).collect(Collectors.toMap(PlatformCmPlatformCategory::getGoodsCatelogCode,PlatformCmPlatformCategory::getGoodsCatelogName));
    }*/

    private Map<String,String> getGoodsCategoryMap(List<String> goodsTypeList){
        if(IterUtil.isEmpty(goodsTypeList)){
            return new HashMap<>();
        }
        PlatformCmGoodsCategoryFilter goodsCategoryFilter = new PlatformCmGoodsCategoryFilter();
        goodsCategoryFilter.setCodeList(goodsTypeList);
        ResultMode<PlatformCmGoodsCategory> goodsCategoryResultMode = platformCmGoodsCategoryInter.findGoodsCategoryByCodes(goodsCategoryFilter);
        if(goodsCategoryResultMode==null || IterUtil.isEmpty(goodsCategoryResultMode.getModel())){
            return new HashMap<>();
        }
        return goodsCategoryResultMode.getModel().stream().filter(item->!StrUtil.isEmpty(item.getGoodsCatelogCode()) && !StrUtil.isEmpty(item.getGoodsCatelogName()))
            .collect(Collectors.toMap(PlatformCmGoodsCategory::getGoodsCatelogCode,PlatformCmGoodsCategory::getGoodsCatelogName,(v1,v2)->v1));
    }

    /**
     * 新增货物名称
     *
     * @param goodsNameVo 货物名称
     * @return {@link ResultMode}
     */
    public ResultMode addGoodsName(LsdsGoodsNameCreateVo goodsNameVo){
        if(StrUtil.isEmpty(goodsNameVo.getGoodsName()) || StrUtil.isEmpty(goodsNameVo.getGoodsType())){
            return ResultMode.fail("货物名称或类型不能为空");
        }
        //货物名称敏感词验证
        Map<String, String> map = new HashMap<>();
        map.put("货物名称", goodsNameVo.getGoodsName());
        Map<String, String> returnMap = ValidateUtils.sensitiveWordValid(map);
        if (MapUtil.isNotEmpty(returnMap)) {
            String errMsg = "敏感词校验不通过:" + returnMap;
            return ResultMode.fail(errMsg);
        }

        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        goodsNameVo.setCreateBy(tokenInfo.getUserBaseId());
        goodsNameVo.setCreateName(tokenInfo.getUsername());

        if(!StrUtil.isEmpty(goodsNameVo.getGoodsType())){
            //设置货物类型的上级编号
            PlatformCmGoodsCategoryFilter goodsCategoryFilter = new PlatformCmGoodsCategoryFilter();
            goodsCategoryFilter.setGoodsCatelogCode(goodsNameVo.getGoodsType());
            ResultMode<PlatformCmGoodsCategory> goodsCategoryResultMode  = platformCmGoodsCategoryInter.getParentGoodsCategory(goodsCategoryFilter);
            if(goodsCategoryResultMode!=null && !IterUtil.isEmpty(goodsCategoryResultMode.getModel())){
                PlatformCmGoodsCategory goodsCategory = goodsCategoryResultMode.getModel().get(0);
                goodsNameVo.setGoodsParentCode(goodsCategory.getGoodsCatelogCode());
            }
        }

        if(!StrUtil.isEmpty(goodsNameVo.getPlatformType())){
            //设置平台类型的上级编号
           /* PlatformCmPlatformCategory platformCategoryFilter = new PlatformCmPlatformCategory();
            platformCategoryFilter.setGoodsCatelogCode(goodsNameVo.getPlatformType());
            ResultMode<PlatformCmPlatformCategory> platformCategoryResultMode  = platformCmPlatformCategoryInter.getParentPlatformCategory(platformCategoryFilter);
            if(platformCategoryResultMode!=null && !IterUtil.isEmpty(platformCategoryResultMode.getModel())){
                PlatformCmPlatformCategory platformCategory = platformCategoryResultMode.getModel().get(0);
                goodsNameVo.setPlatformParentCode(platformCategory.getGoodsCatelogCode());
            }*/
        }


        LsdsGoodsName lsdsGoodsName = goodsNameMapper.findGoodsNameByName(goodsNameVo.getGoodsName());
        if(lsdsGoodsName!=null){
            int rows = goodsNameRelationService.countGoodsNameByGoodsNameId(goodsNameVo.getCompanyId(),lsdsGoodsName.getGoodsNameId());
            if(rows>0){
                return ResultMode.fail("货物名称已经存在!");
            }

            goodsNameVo.setGoodsNameId(lsdsGoodsName.getGoodsNameId());
        }

        //新增货物名称
        if(lsdsGoodsName==null){
            //设置货物名称
            LsdsGoodsName goodsName = setGoodsName(goodsNameVo,tokenInfo);

            goodsName.setGoodsNameId(UtilityClass.uuid());
            goodsNameMapper.insertGoodsName(goodsName);

            goodsNameVo.setGoodsNameId(goodsName.getGoodsNameId());
        }

        //新增货物名称关系表
        goodsNameRelationService.addGoodsNameRelation(goodsNameVo);

        //新增操作记录
        String operationDesc="创建";
        String remark="成功";
        operationRecordService.addOperationRecord(goodsNameVo.getGoodsNameId(), LsdsEnum.OperationTypeEnum.GOODS_NAME_OPER_TYPE.getCode(),operationDesc,remark);
        return ResultMode.success();
    }

    /**
     * 设置货物名称
     *
     * @param goodsNameVo 货物名称
     * @param tokenInfo   token
     * @return {@link LsdsGoodsName}
     */
    private LsdsGoodsName setGoodsName(LsdsGoodsNameCreateVo goodsNameVo,TokenInfo tokenInfo){
        LsdsGoodsName goodsName =  BeanUtil.toBean(goodsNameVo,LsdsGoodsName.class);

        if (StrUtil.isNotBlank(goodsNameVo.getStatus())
            && goodsNameVo.getStatus().equals(LsdsEnum.GoodsNameStatusEnum.ENABLE_STATUS.getCode())) {
            //智慧场站-创建货物状态为已启用
            goodsName.setStatus(goodsNameVo.getStatus());
        } else {
            goodsName.setStatus(LsdsEnum.GoodsNameStatusEnum.TO_BE_STATUS.getCode());
        }
        goodsName.setCreateBy(tokenInfo.getUserBaseId());
        goodsName.setCreateName(tokenInfo.getUsername());
        goodsName.setCreateCompanyId(goodsNameVo.getCompanyId());
        goodsName.setCreateCompanyName(goodsNameVo.getCompanyName());
        goodsName.setDeleteFlag(LsdsEnum.GoodsDeleteFlagEnum.NOT_DELETED_FLAG.getCode());
        goodsName.setModifyBy(tokenInfo.getUserBaseId());
        goodsName.setModifyName(tokenInfo.getUsername());
        goodsName.setCreateDate(new Date());
        goodsName.setModifyDate(new Date());
        // 生成货品编码
        goodsName.setGoodsNameNo(numberGenerator.generateIncr("GN", 8));

        return goodsName;
    }

    /**
     * 删除货物名称
     *
     * @param goodsNameVo 货物名称
     * @return {@link ResultMode}
     */
    public ResultMode deleteGoodsName(LsdsGoodsNameCreateVo goodsNameVo){
        if(StrUtil.isEmpty(goodsNameVo.getGoodsNameId())){
            return ResultMode.fail("货物名称id不能为空");
        }

        //删除货物名称
        LsdsGoodsName goodsName = new LsdsGoodsName();
        goodsName.setGoodsNameId(goodsNameVo.getGoodsNameId());
        goodsName.setDeleteFlag(LsdsEnum.GoodsDeleteFlagEnum.DELETED_FLAG.getCode());
        goodsName.setModifyDate(new Date());
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        if(tokenInfo!=null){
            goodsName.setModifyName(tokenInfo.getUsername());
            goodsName.setModifyBy(tokenInfo.getUserBaseId());
        }
        goodsNameMapper.updateGoodsName(goodsName);

        //删除货物名称关系
        LsdsGoodsNameRelation goodsNameRelation = new LsdsGoodsNameRelation();
        goodsNameRelation.setGoodsNameId(goodsNameVo.getGoodsNameId());
        goodsNameRelation.setModifyName(goodsName.getModifyName());
        goodsNameRelation.setModifyDate(new Date());
        goodsNameRelationService.deleteGoodsNameRelationByGoodsNameId(goodsNameRelation);

        //新增操作记录
        String operationDesc="删除";
        String remark="成功";
        operationRecordService.addOperationRecord(goodsName.getGoodsNameId(), LsdsEnum.OperationTypeEnum.GOODS_NAME_OPER_TYPE.getCode(),operationDesc,remark);

        return ResultMode.success();
    }

    /**
     * 禁用
     *
     * @param goodsNameVo 货物名称
     * @return {@link ResultMode}
     */
    public ResultMode disableGoodsName(LsdsGoodsNameCreateVo goodsNameVo){
        if(StrUtil.isEmpty(goodsNameVo.getGoodsNameId())){
            return ResultMode.fail("货物名称id不能为空");
        }
        LsdsGoodsName lsdsGoodsName = goodsNameMapper.findGoodsNameById(goodsNameVo.getGoodsNameId());
        if(lsdsGoodsName==null){
            return ResultMode.fail("货物名称没有找到");
        }
        if(!StrUtil.equals(LsdsEnum.GoodsNameStatusEnum.ENABLE_STATUS.getCode(),lsdsGoodsName.getStatus())){
            return ResultMode.fail("不能禁用记录！");
        }

        LsdsGoodsName goodsName = new LsdsGoodsName();
        goodsName.setGoodsNameId(goodsNameVo.getGoodsNameId());
        goodsName.setStatus(LsdsEnum.GoodsNameStatusEnum.DISABLE_STATUS.getCode());
        goodsName.setModifyDate(new Date());
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        if(tokenInfo!=null){
            goodsName.setModifyName(tokenInfo.getUsername());
            goodsName.setModifyBy(tokenInfo.getUserBaseId());
        }
        goodsNameMapper.updateGoodsName(goodsName);

        //新增操作记录
        String operationDesc="禁用";
        String remark="成功";
        operationRecordService.addOperationRecord(goodsName.getGoodsNameId(), LsdsEnum.OperationTypeEnum.GOODS_NAME_OPER_TYPE.getCode(),operationDesc,remark);
        return ResultMode.success();
    }

    /**
     * 启用
     *
     * @param goodsNameVo 货物名称
     * @return {@link ResultMode}
     */
    public ResultMode enableGoodsName(LsdsGoodsNameCreateVo goodsNameVo){
        if(StrUtil.isEmpty(goodsNameVo.getGoodsNameId())){
            return ResultMode.fail("货物名称id不能为空");
        }
        LsdsGoodsName lsdsGoodsName = goodsNameMapper.findGoodsNameById(goodsNameVo.getGoodsNameId());
        if(lsdsGoodsName==null){
            return ResultMode.fail("货物名称没有找到");
        }
        if(!StrUtil.equals(LsdsEnum.GoodsNameStatusEnum.DISABLE_STATUS.getCode(),lsdsGoodsName.getStatus())){
            return ResultMode.fail("不能启用记录！");
        }

        LsdsGoodsName goodsName = new LsdsGoodsName();
        goodsName.setGoodsNameId(goodsNameVo.getGoodsNameId());
        goodsName.setStatus(LsdsEnum.GoodsNameStatusEnum.ENABLE_STATUS.getCode());
        goodsName.setModifyDate(new Date());
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        if(tokenInfo!=null){
            goodsName.setModifyName(tokenInfo.getUsername());
            goodsName.setModifyBy(tokenInfo.getUserBaseId());
        }
        goodsNameMapper.updateGoodsName(goodsName);

        //新增操作记录
        String operationDesc="启用";
        String remark="成功";
        operationRecordService.addOperationRecord(goodsName.getGoodsNameId(), LsdsEnum.OperationTypeEnum.GOODS_NAME_OPER_TYPE.getCode(),operationDesc,remark);

        return ResultMode.success();
    }

    /**
     * 审核
     *
     * @param goodsNameVo 货物名称
     * @return {@link ResultMode}
     */
    public ResultMode auditGoodsName(LsdsGoodsNameCreateVo goodsNameVo){
        if(StrUtil.isEmpty(goodsNameVo.getGoodsNameId())){
            return ResultMode.fail("货物名称id不能为空");
        }
        LsdsGoodsName lsdsGoodsName = goodsNameMapper.findGoodsNameById(goodsNameVo.getGoodsNameId());
        if(lsdsGoodsName==null){
            return ResultMode.fail("货物名称没有找到");
        }
        if(!StrUtil.equals(LsdsEnum.GoodsNameStatusEnum.TO_BE_STATUS.getCode(),lsdsGoodsName.getStatus())){
            return ResultMode.fail("不能审核记录！");
        }

        if(!StrUtil.isEmpty(goodsNameVo.getGoodsType())){
            //设置货物类型的上级编号
            PlatformCmGoodsCategoryFilter goodsCategoryFilter = new PlatformCmGoodsCategoryFilter();
            goodsCategoryFilter.setGoodsCatelogCode(goodsNameVo.getGoodsType());
            ResultMode<PlatformCmGoodsCategory> goodsCategoryResultMode  = platformCmGoodsCategoryInter.getParentGoodsCategory(goodsCategoryFilter);
            if(goodsCategoryResultMode!=null && !IterUtil.isEmpty(goodsCategoryResultMode.getModel())){
                PlatformCmGoodsCategory goodsCategory = goodsCategoryResultMode.getModel().get(0);
                goodsNameVo.setGoodsParentCode(goodsCategory.getGoodsCatelogCode());
            }
        }

        if(!StrUtil.isEmpty(goodsNameVo.getPlatformType())){
            //设置平台类型的上级编号
            /*PlatformCmPlatformCategory platformCategoryFilter = new PlatformCmPlatformCategory();
            platformCategoryFilter.setGoodsCatelogCode(goodsNameVo.getPlatformType());
            ResultMode<PlatformCmPlatformCategory> platformCategoryResultMode  = platformCmPlatformCategoryInter.getParentPlatformCategory(platformCategoryFilter);
            if(platformCategoryResultMode!=null && !IterUtil.isEmpty(platformCategoryResultMode.getModel())){
                PlatformCmPlatformCategory platformCategory = platformCategoryResultMode.getModel().get(0);
                goodsNameVo.setPlatformParentCode(platformCategory.getGoodsCatelogCode());
            }*/
        }

        LsdsGoodsName goodsName = new LsdsGoodsName();
        goodsName.setGoodsNameId(goodsNameVo.getGoodsNameId());
        goodsName.setStatus(goodsNameVo.getStatus());
        goodsName.setAuditDate(new Date());
        goodsName.setAuditDesc(goodsNameVo.getAuditDesc());
        goodsName.setGoodsType(goodsNameVo.getGoodsType());
        goodsName.setGoodsParentCode(goodsNameVo.getGoodsParentCode());
        goodsName.setPlatformType(goodsNameVo.getPlatformType());
        goodsName.setPlatformParentCode(goodsNameVo.getPlatformParentCode());
        goodsName.setModifyDate(new Date());
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        if(tokenInfo!=null){
            goodsName.setModifyName(tokenInfo.getUsername());
            goodsName.setModifyBy(tokenInfo.getUserBaseId());
        }

        //更新审核状态
        goodsNameMapper.updateGoodsName(goodsName);

        //新增操作记录
        String operationDesc="审核";
        String remark="成功";
        operationRecordService.addOperationRecord(goodsName.getGoodsNameId(), LsdsEnum.OperationTypeEnum.GOODS_NAME_OPER_TYPE.getCode(),operationDesc,remark);

        return ResultMode.success();
    }

    /**
     * 调整平台分类
     *
     * @param goodsNameVo 货物名称
     * @return {@link ResultMode}
     */
    public ResultMode editPlatformType(LsdsGoodsNameCreateVo goodsNameVo){
        if(StrUtil.isEmpty(goodsNameVo.getGoodsNameId())){
            return ResultMode.fail("货物名称id不能为空");
        }
        LsdsGoodsName lsdsGoodsName = goodsNameMapper.findGoodsNameById(goodsNameVo.getGoodsNameId());
        if(lsdsGoodsName==null){
            return ResultMode.fail("货物名称没有找到");
        }

        if(!StrUtil.isEmpty(goodsNameVo.getPlatformType())){
            //设置平台类型的上级编号
            /*PlatformCmPlatformCategory platformCategoryFilter = new PlatformCmPlatformCategory();
            platformCategoryFilter.setGoodsCatelogCode(goodsNameVo.getPlatformType());
            ResultMode<PlatformCmPlatformCategory> platformCategoryResultMode  = platformCmPlatformCategoryInter.getParentPlatformCategory(platformCategoryFilter);
            if(platformCategoryResultMode!=null && !IterUtil.isEmpty(platformCategoryResultMode.getModel())){
                PlatformCmPlatformCategory platformCategory = platformCategoryResultMode.getModel().get(0);
                goodsNameVo.setPlatformParentCode(platformCategory.getGoodsCatelogCode());
            }*/
        }

        LsdsGoodsName goodsName = new LsdsGoodsName();
        goodsName.setGoodsNameId(goodsNameVo.getGoodsNameId());
        goodsName.setPlatformType(goodsNameVo.getPlatformType());
        goodsName.setPlatformParentCode(goodsNameVo.getPlatformParentCode());
        goodsName.setModifyDate(new Date());
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        if(tokenInfo!=null){
            goodsName.setModifyName(tokenInfo.getUsername());
            goodsName.setModifyBy(tokenInfo.getUserBaseId());
        }
        //更新审核状态
        goodsNameMapper.updateGoodsName(goodsName);

        return ResultMode.success();
    }


    /**
     * 导出货物名称
     *
     * @param goodsNameFilter 货物名称
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    public List<LsdsGoodsNameVo> exportGoodsName(LsdsGoodsNameFilter goodsNameFilter){
        List<LsdsGoodsNameVo> goodsNameVoList =  goodsNameMapper.exportGoodsName(goodsNameFilter);
        buildGoodsType(goodsNameVoList);
        return goodsNameVoList;
    }

    private void buildGoodsType(List<LsdsGoodsNameVo> goodsNameVoList){
        if(IterUtil.isEmpty(goodsNameVoList)){
            return;
        }
        Set<String> platformTypeSets = new HashSet<>();
        Set<String> goodsTypeSets = new HashSet<>();
        Set<String> goodsNameSets = new HashSet<>();
        for(LsdsGoodsNameVo goodsName:goodsNameVoList){
            if(!StrUtil.isEmpty(goodsName.getPlatformType())){
                platformTypeSets.add(goodsName.getPlatformType());
            }
            if(!StrUtil.isEmpty(goodsName.getPlatformParentCode())){
                platformTypeSets.add(goodsName.getPlatformParentCode());
            }
            if(!StrUtil.isEmpty(goodsName.getGoodsType())){
                goodsTypeSets.add(goodsName.getGoodsType());
            }
            if(!StrUtil.isEmpty(goodsName.getGoodsParentCode())){
                goodsTypeSets.add(goodsName.getGoodsParentCode());
            }
            if(!StrUtil.isEmpty(goodsName.getGoodsName())){
                goodsNameSets.add(goodsName.getGoodsName());
            }
        }

        Map<String,String> packTypeDictMap = new HashMap<>();
        //包装方式
        String parentDicId="3";
        ResultMode<Map<String, List<PlatformCmDictionary>>> dictResultMode =  platformCmDictionaryInter.getDictionaryDataByParentDicId(parentDicId);
        if(dictResultMode!=null && !IterUtil.isEmpty(dictResultMode.getModel())){
            Map<String, List<PlatformCmDictionary>> dictMap = dictResultMode.getModel().get(0);
            if(dictMap.containsKey(parentDicId)){
                packTypeDictMap = dictMap.get(parentDicId).stream().filter(item->!StrUtil.isEmpty(item.getEnumCode()) && !StrUtil.isEmpty(item.getName())).collect(Collectors.toMap(PlatformCmDictionary::getEnumCode,PlatformCmDictionary::getName));
            }
        }

        //查询保险分类：保险分类1,保险分类2,保险分类3
        Map<String,String> insuranceTypeNameMap = getInsuranceTypeName(new ArrayList<>(goodsNameSets));

        //Map<String,String> platformTypeMap = getPlatformCategoryMap(new ArrayList<>(platformTypeSets));
        Map<String,String> goodsTypeMap = getGoodsCategoryMap(new ArrayList<>(goodsTypeSets));
        for(LsdsGoodsNameVo goodsName:goodsNameVoList){
           /* if(!StrUtil.isEmpty(goodsName.getPlatformType())){
                goodsName.setPlatformTypeName(platformTypeMap.get(goodsName.getPlatformType()));
            }
            if(!StrUtil.isEmpty(goodsName.getPlatformParentCode())){
                goodsName.setPlatformParentName(platformTypeMap.get(goodsName.getPlatformParentCode()));
            }*/
            if(!StrUtil.isEmpty(goodsName.getGoodsType())){
                goodsName.setGoodsTypeName(goodsTypeMap.get(goodsName.getGoodsType()));
            }
            if(!StrUtil.isEmpty(goodsName.getGoodsParentCode())){
                goodsName.setGoodsParentName(goodsTypeMap.get(goodsName.getGoodsParentCode()));
            }
            if(!StrUtil.isEmpty(goodsName.getPackType())){
                goodsName.setPackTypeName(packTypeDictMap.get(goodsName.getPackType()));
            }
            goodsName.setInsuranceTypeName(insuranceTypeNameMap.get(goodsName.getGoodsName()));
        }
    }

    /**
     * 获取保险类型名称：保险分类1,保险分类2,保险分类3
     *
     * @param goodsNameList 商品名称列表
     * @return {@link Map}<{@link String},{@link String}>
     */
    private Map<String,String> getInsuranceTypeName(List<String> goodsNameList){
        Map<String,String> insuranceTypeNameMap = new HashMap<>();
        try {
            if(IterUtil.isEmpty(goodsNameList)){
                return insuranceTypeNameMap;
            }
            InsuranceTypeGoodsFilter insuranceTypeGoodsFilter = new InsuranceTypeGoodsFilter();
            insuranceTypeGoodsFilter.setGoodsNameList(goodsNameList);
            insuranceTypeGoodsFilter.setDelFlag("0");
            ResultMode<InsuranceTypeGoodsVo> insuranceTypeGoodsResultMode = imsInsuranceTypeInter.queryInsuranceTypeByGoodsName(insuranceTypeGoodsFilter);
            if(insuranceTypeGoodsResultMode==null || IterUtil.isEmpty(insuranceTypeGoodsResultMode.getModel())){
                return insuranceTypeNameMap;
            }
            List<InsuranceTypeGoodsVo> insuranceTypeGoodsVoList = insuranceTypeGoodsResultMode.getModel();
            Map<String, Set<String>> insuranceTypeGoodsMap= insuranceTypeGoodsVoList.stream().filter(item-> !StrUtil.isEmpty(item.getGoodsName()) && !StrUtil.isEmpty(item.getInsuranceTypeName()))
                .collect(Collectors.groupingBy(InsuranceTypeGoodsVo::getGoodsName,Collectors.mapping(InsuranceTypeGoodsVo::getInsuranceTypeName, Collectors.toSet())));
            if(MapUtil.isEmpty(insuranceTypeGoodsMap)){
                return insuranceTypeNameMap;
            }

            for(Map.Entry<String, Set<String>> entry:insuranceTypeGoodsMap.entrySet()){
                insuranceTypeNameMap.put(entry.getKey(), StringUtils.join(entry.getValue(),","));
            }
            return insuranceTypeNameMap;
        } catch (Exception e) {
            log.error("获取保险分类异常:{}",e);
        }
        return insuranceTypeNameMap;
    }

    /**
     * 查询货品名称列表
     *
     * @param goodsNameFilter 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    public ResultMode<LsdsGoodsNameVo> findGoodsNameDropdownList(LsdsGoodsNameFilter goodsNameFilter){
        log.info("findGoodsNameDropdownList#请求参数:{}", JSONUtil.toJsonStr(goodsNameFilter));
        if(ObjUtil.isNotNull(goodsNameFilter.getCompanyId())){
            goodsNameFilter.setCompanyId(goodsNameFilter.getCompanyId());
        }else {
            TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
            goodsNameFilter.setCompanyId(tokenInfo.getCompanyId());
        }
        if(StrUtil.isBlank(goodsNameFilter.getCompanyId())){
            log.info("查询货品名称列表token没有获取到");
            return ResultMode.success();
        }
        List<LsdsGoodsNameVo> goodsNameVoList = goodsNameMapper.findGoodsNameDropdownList(goodsNameFilter);
        if(IterUtil.isEmpty(goodsNameVoList)){
            return ResultMode.success();
        }

        //查询货物类型
        Map<String,String> goodsTypeDictMap = new HashMap<>();
        String parentDicId="2";
        ResultMode<Map<String, List<PlatformCmDictionary>>> dictResultMode =  platformCmDictionaryInter.getDictionaryDataByParentDicId(parentDicId);
        if(dictResultMode!=null && !IterUtil.isEmpty(dictResultMode.getModel())){
            Map<String, List<PlatformCmDictionary>> dictMap = dictResultMode.getModel().get(0);
            if(dictMap.containsKey(parentDicId)){
                goodsTypeDictMap = dictMap.get(parentDicId).stream().filter(item->!StrUtil.isEmpty(item.getEnumCode()) && !StrUtil.isEmpty(item.getName())).collect(Collectors.toMap(PlatformCmDictionary::getEnumCode,PlatformCmDictionary::getName));
            }
        }
        for(LsdsGoodsNameVo goodsNameVo:goodsNameVoList){
            if(!StrUtil.isEmpty(goodsNameVo.getGoodsType())){
                goodsNameVo.setGoodsTypeName(goodsTypeDictMap.get(goodsNameVo.getGoodsType()));
            }
        }
        return new ResultMode<>(goodsNameVoList);
    }

    /**
     * 服务伙伴创建-按公司id查询货品名称下拉列表
     *
     * @param goodsNameFilter 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    public ResultMode<LsdsGoodsNameVo> findGoodsNameListForPartner(LsdsGoodsNameFilter goodsNameFilter){
        List<LsdsGoodsNameVo> goodsNameVoList = goodsNameMapper.findGoodsNameDropdownList(goodsNameFilter);
        return new ResultMode<>(goodsNameVoList);
    }

    /**
     * 查询货品名称钱表
     *
     * @param goodsNameFilter 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    public ResultMode<LsdsGoodsNameVo> findGoodsNameList(LsdsGoodsNameFilter goodsNameFilter){
        List<LsdsGoodsNameVo> goodsNameVoList = null;
        if(StrUtil.equals("1",goodsNameFilter.getOperType())){
            TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
            if(tokenInfo==null){
                log.info("查询货品名称列表token没有获取到");
                return ResultMode.success();
            }
            goodsNameFilter.setCompanyId(tokenInfo.getCompanyId());
            goodsNameVoList = goodsNameMapper.findGoodsNameListOfCompany(goodsNameFilter);
        }else{
            goodsNameVoList = goodsNameMapper.findGoodsNameList(goodsNameFilter);
        }
        if(IterUtil.isEmpty(goodsNameVoList)){
            return ResultMode.success();
        }
        return new ResultMode<>(goodsNameVoList);
    }

    /**
     * 按名称查询货品名称
     *
     * @param goodsNameFilter 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    public ResultMode<LsdsGoodsNameVo> findGoodsNameByName(LsdsGoodsNameFilter goodsNameFilter){
        if(StrUtil.isEmpty(goodsNameFilter.getGoodsName())){
            return ResultMode.fail("货品名称为空");
        }
        LsdsGoodsNameVo goodsNameVo = null;
        if(StrUtil.isEmpty(goodsNameFilter.getCompanyId())){
            LsdsGoodsName goodsName = goodsNameMapper.findGoodsNameByName(goodsNameFilter.getGoodsName());
            if(goodsName==null){
                return ResultMode.fail("没有找到记录");
            }
            goodsNameVo = new LsdsGoodsNameVo();
            BeanUtil.copyProperties(goodsName, goodsNameVo);
        } else {
            //带企业id时，需要关联货物名称关系表
            goodsNameVo = goodsNameMapper.findGoodsNameByNameOfCompany(goodsNameFilter.getGoodsName(), goodsNameFilter.getCompanyId());
        }
        return ResultMode.success(goodsNameVo);
    }

    public void batchAddGoodsName(List<LsdsGoodsNameCreateVo> goodsNameCreateVoList) {
        goodsNameMapper.batchAddGoodsName(goodsNameCreateVoList);
    }

    public ResultMode addGoodsNameRelation(LsdsGoodsNameVo goodsNameVo) {
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        goodsNameVo.setCreateBy(tokenInfo.getUserBaseId());
        goodsNameVo.setCreateName(tokenInfo.getUsername());
        LsdsGoodsNameCreateVo goodsNameCreateVo = BeanUtil.copyProperties(goodsNameVo, LsdsGoodsNameCreateVo.class);
        int i = goodsNameRelationService.countGoodsNameByGoodsNameId(goodsNameCreateVo.getCompanyId(), goodsNameCreateVo.getGoodsNameId());
        if (i > 0) {
            return ResultMode.success();
        }
        return goodsNameRelationService.addGoodsNameRelation(goodsNameCreateVo);

    }

    public ResultMode<LsdsGoodsNameVo> findBatchGoodsNameByName(LsdsGoodsNameFilter goodsNameFilter) {
        if (CollectionUtil.isEmpty(goodsNameFilter.getGoodsNameList())) {
            return ResultMode.successList(Collections.EMPTY_LIST);
        }
        List<LsdsGoodsNameVo> list = goodsNameMapper.findBatchGoodsNameByName(goodsNameFilter.getGoodsNameList(), goodsNameFilter.getCompanyId());
        return ResultMode.successList(list);
    }
}
