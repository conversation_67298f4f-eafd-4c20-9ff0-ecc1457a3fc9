package com.wanlianyida.lsds.application.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.DateUtils;
import com.isoftstone.hig.common.utils.LogHelper;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.lsds.api.entity.LsdsCarSource;
import com.isoftstone.hig.lsds.api.filter.LsdsCarSourceFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsCarSourceVo;
import com.isoftstone.hig.platform.api.client.PlatformCommonInterClient;
import com.isoftstone.hig.platform.api.entity.PlatformUmUserbaseinfo;
import com.wanlianyida.lsds.domain.service.LsdsCarSourceDomainService;
import com.wanlianyida.lsds.infrastructure.config.FormsAuthTrader;
import com.wanlianyida.lsds.infrastructure.repository.mapper.GoodsAddressMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsCarSourceMapper;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * 车源事务处理类
 *
 * <AUTHOR>
 */
@Service
public class LsdsCarSourceAppService {

    @Autowired
    PlatformCommonInterClient platformCommonInterClient;

    @Autowired
    private LsdsCarSourceMapper lsdsCarSourceRepository;
    @Resource
    private LsdsCarSourceDomainService lsdsCarSourceDomainService;

    /**
     * 根据车源ID 主键精确查询车源ID
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     * @param carSourceId 为0时忽略
     * @param token       预留要传递的token，服务端进行判断权限功能
     * @return LsdsCarSource
     */
    public ResultMode<LsdsCarSource> lsdsCarSourceGet(String carSourceId, String token) {
        String errMsg = "";
        ResultMode<LsdsCarSource> resultModel = new ResultMode<LsdsCarSource>();
        try {
            LsdsCarSourceMapper dao = SpringContextUtil.getBeanByClass(LsdsCarSourceMapper.class);
            resultModel.getModel().add(dao.getModel(carSourceId));
        } catch (Exception ex) {
            errMsg = "获取车源信息异常";
            LogHelper.writeError(errMsg, ex);
            resultModel.setSucceed(false);
        }
        return resultModel;
    }


    /**
     * 新增车源ID 信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param vo 车源Vo
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【增加成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【增加成功】编码,ResultMode.errMsg为相应【增加成功】描述；
     * 否则ResultMode.succeed=false【增加失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【增加失败】编码,ResultMode.errMsg为相应【增加失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsCarSourceAdd(LsdsCarSourceVo vo, String token) {
        // carSourceId = "";
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
        try {
            LsdsCarSourceMapper dao = SpringContextUtil.getBeanByClass(LsdsCarSourceMapper.class);
            List<String> stringList = new ArrayList<String>(1);
            stringList.add(vo.getLsdsCarSource().getAssignCarPlateNumber());
            List<LsdsCarSource> lsdsCarSource = dao.getCarSourceListByNum(stringList);

            if (null != lsdsCarSource && lsdsCarSource.size() > 0) {
                errMsg = "当前发布中车源已存在该车牌号，请勿重复！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }

            String carSourceId = String.valueOf(IdUtil.generateId());

            //写入车源地址信息
            GoodsAddressMapper addressDao = SpringContextUtil.getBeanByClass(GoodsAddressMapper.class);
            vo.getLsdsGoodsAddress().setGoodsAddressId(String.valueOf(IdUtil.generateId()));
            vo.getLsdsGoodsAddress().setSourceId(carSourceId);
            vo.getLsdsGoodsAddress().setAddressType("4");
            addressDao.add(vo.getLsdsGoodsAddress());

            //写入车源信息
            vo.getLsdsCarSource().setCarSourceId(carSourceId);
            vo.getLsdsCarSource().setCarStatus("0");
            vo.getLsdsCarSource().setCreateBy(userInfoFromCache.getUserBaseId());
            vo.getLsdsCarSource().setCreateDate(DateUtils.getDateTime());
            vo.getLsdsCarSource().setGoodsAddressId(vo.getLsdsGoodsAddress().getGoodsAddressId());
            dao.add(vo.getLsdsCarSource());
        } catch (Exception ex) {
            errMsg = "新增车源ID 信息异常";
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
            LogHelper.writeError(errMsg, ex);
        }
        return resultmodel;
    }

    /**
     * 修改车源ID 信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param model 车源ID 信息
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【修改成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【修改成功】编码,ResultMode.errMsg为相应【修改成功】描述；
     * 否则ResultMode.succeed=false【修改失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【修改失败】编码,ResultMode.errMsg为相应【修改失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsCarSourceUpdate(LsdsCarSource model, String token) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            LsdsCarSourceMapper dao = SpringContextUtil.getBeanByClass(LsdsCarSourceMapper.class);
            resultmodel.setSucceed(dao.modify(model));
            if (!resultmodel.getSucceed()) {
                errMsg = "更新车源失败，请检验提交的数据是否正常-carSourceId：" + model.getCarSourceId();
                resultmodel.setErrMsg(errMsg);
            }
        } catch (Exception ex) {
            errMsg = "修改车源ID 信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }

    /**
     * 根据Key删除车源ID 信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param carSourceId 车源ID 信息
     * @param token       预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【删除成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【删除成功】编码,ResultMode.errMsg为相应【删除成功】描述；
     * 否则ResultMode.succeed=false【删除失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【删除失败】编码,ResultMode.errMsg为相应【删除失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsCarSourceDel(String carSourceId, String token) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            LsdsCarSourceMapper dao = SpringContextUtil.getBeanByClass(LsdsCarSourceMapper.class);
            resultmodel.setSucceed(dao.remove(carSourceId));
            if (!resultmodel.getSucceed()) {
                errMsg = "车源删除异常，删除失败";
                resultmodel.setErrMsg(errMsg);
            }
        } catch (Exception ex) {
            errMsg = "根据carSourceId删除车源ID 信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }

    /**
     * 分页获取车源ID 信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @param token    预留要传递的token，服务端进行判断权限功能
     * @return {@code  ResultMode<LsdsCarSource>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为车源ID 实体类LsdsCarSource列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsCarSource> lsdsCarSourcePaging(PagingInfo<LsdsCarSourceFilter> pageInfo, String token) {

        ResultMode<LsdsCarSource> returnmodel = new ResultMode<LsdsCarSource>();
        try {

            returnmodel = lsdsCarSourceDomainService.pagingLsdsCarSource(pageInfo, token);
            return returnmodel;

        } catch (Exception ex) {
            String errMsg = "分页获取车源ID 信息异常:" + ex.getMessage();
            returnmodel.setErrMsg(errMsg);
            LogHelper.writeError(errMsg, ex);
        }
        return returnmodel;
    }

    /**
     * 分页查询货源列表
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     * <AUTHOR>
     * 创建时间 2019/12/2
     */
    public ResultMode<LsdsCarSource> getCarSourceListPage(PagingInfo<LsdsCarSourceFilter> pageInfo) {
        ResultMode<LsdsCarSource> returnModel = new ResultMode<LsdsCarSource>();

        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        LsdsCarSourceMapper carSourceDao = SpringContextUtil.getBeanByClass(LsdsCarSourceMapper.class);
        List<LsdsCarSource> carSourceList = carSourceDao.getCarSourceListPage(pageInfo.filterModel);
        PageInfo<LsdsCarSource> pageInfoList = new PageInfo<>(carSourceList);
        Long total = pageInfoList.getTotal();
        returnModel.setTotal(total.intValue());
        returnModel.setModel(carSourceList);
        return returnModel;
    }

    /**
     * 分页获取车源ID 信息
     * 创建者: cgb
     * 创建时间: 2019/12/19
     *
     * @param strList
     * @return {@code  ResultMode<LsdsCarSource>}
     */
    public ResultMode<LsdsCarSource> getCarSourceListByNum(List<String> strList) {

        ResultMode<LsdsCarSource> returnmodel = new ResultMode<LsdsCarSource>();
        try {

            LsdsCarSourceMapper dao = SpringContextUtil.getBeanByClass(LsdsCarSourceMapper.class);
            List<LsdsCarSource> carSourceList = dao.getCarSourceListByNum(strList);
            returnmodel.setModel(carSourceList);
        } catch (Exception ex) {
            String errMsg = "查询车源信息异常:" + ex.getMessage();
            returnmodel.setErrMsg(errMsg);
            LogHelper.writeError(errMsg, ex);
        }
        return returnmodel;
    }


    /**
     * 查询车源信息
     * 创建者: cgb
     * 创建时间: 2019/12/21
     *
     * @param carSource
     * @return {@code  ResultMode<LsdsCarSource>}
     */
    public ResultMode<LsdsCarSource> getCarSourceList(LsdsCarSource carSource) {

        ResultMode<LsdsCarSource> returnmodel = new ResultMode<LsdsCarSource>();
        try {

            LsdsCarSourceMapper dao = SpringContextUtil.getBeanByClass(LsdsCarSourceMapper.class);
            List<LsdsCarSource> carSourceList = dao.getCarSourceList(carSource);
            returnmodel.setModel(carSourceList);
        } catch (Exception ex) {
            String errMsg = "查询车源信息异常:" + ex.getMessage();
            returnmodel.setErrMsg(errMsg);
            LogHelper.writeError(errMsg, ex);
        }
        return returnmodel;
    }




    /**
     *<AUTHOR>
     * @since 2021-2-4
     * 物流信息车源列表查询
     * @param lsdsCarSourceFilterPagingInfo
     * @return
     */
    public ResultMode<LsdsCarSource> findLsdsCarSource(PagingInfo<LsdsCarSourceFilter> lsdsCarSourceFilterPagingInfo){
        ResultMode<LsdsCarSource> resultMode=new ResultMode<>();
        //分页查询车辆信息
        PageHelper.startPage(lsdsCarSourceFilterPagingInfo.currentPage,lsdsCarSourceFilterPagingInfo.pageLength);
        List<LsdsCarSource> lsdsCarSources=lsdsCarSourceRepository.findCarSouceList(lsdsCarSourceFilterPagingInfo.filterModel);
        LogHelper.writeInfo("物流信息车源列表lsdsCarSources->{}", JSONObject.toJSONString(lsdsCarSources));
        PageInfo<LsdsCarSource> pageInfo=new PageInfo<>(lsdsCarSources);
        LogHelper.writeInfo("分页查看车源列表 pageInfo->{}",JSONObject.toJSONString(pageInfo));
        Long total=pageInfo.getTotal();
        resultMode.setTotal(total.intValue());
        resultMode.setModel(lsdsCarSources);
        LogHelper.writeInfo("物流信息车源列表查询结果resultMode->{}",JSONObject.toJSONString(resultMode));
        return resultMode;
    }
}
