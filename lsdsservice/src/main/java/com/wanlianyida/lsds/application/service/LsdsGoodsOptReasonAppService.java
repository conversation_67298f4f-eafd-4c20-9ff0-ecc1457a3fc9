package com.wanlianyida.lsds.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.lsds.api.command.OptReasonAddCommand;
import com.isoftstone.hig.lsds.api.command.OptReasonUpdSortCommand;
import com.isoftstone.hig.lsds.api.dto.LsdsGoodsOptReasonDTO;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsOptReason;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsOptReasonFilter;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsGoodsOptReasonMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

/**
 * 货源审核原因
 *
 * <AUTHOR>
 */
@Service
public class LsdsGoodsOptReasonAppService {

    @Resource
    private LsdsGoodsOptReasonMapper lsdsGoodsOptReasonMapper;


    public List<LsdsGoodsOptReason> queryCondition(LsdsGoodsOptReason condition) {
        if (ObjUtil.isNull(condition) || ObjUtil.isAllEmpty(ReflectUtil.getFieldsValue(condition))) {
            return null;
        }

        return lsdsGoodsOptReasonMapper.queryCondition(condition);
    }


    public PageInfo<LsdsGoodsOptReasonDTO> queryList(PagingInfo<LsdsGoodsOptReasonFilter> pagingInfo) {
        LsdsGoodsOptReasonFilter filter = pagingInfo.getFilterModel();
        Page<Object> page = PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength, true);
        LsdsGoodsOptReason condition = BeanUtil.toBean(filter, LsdsGoodsOptReason.class);
        condition.setEnable(1);
        if (StrUtil.isBlank(condition.getCompanyId())) {
            condition.setCompanyId(JwtUtil.getInstance().getCompanyIdByToken());
        }
        List<LsdsGoodsOptReason> list = queryCondition(condition);
        if (IterUtil.isEmpty(list)) {
            return null;
        }

        PageInfo<LsdsGoodsOptReasonDTO> pageInfo = new PageInfo<>();
        pageInfo.setTotal((int) page.getTotal());
        pageInfo.setList(BeanUtil.copyToList(list, LsdsGoodsOptReasonDTO.class));
        return pageInfo;
    }


    public ResultMode add(OptReasonAddCommand command) {
        LsdsGoodsOptReason condition = BeanUtil.toBean(command, LsdsGoodsOptReason.class);
        String companyId = JwtUtil.getInstance().getCompanyIdByToken();
        condition.setEnable(1);
        condition.setCompanyId(companyId);
        List<LsdsGoodsOptReason> list = queryCondition(condition);

        int sort = 0;
        if (IterUtil.isNotEmpty(list)) {
            long count = list.stream().filter(reason -> StrUtil.equals(reason.getReason(), command.getReason())).count();
            if (count > 0) {
                throw new LsdsWlydException("原因已存在");
            }

            sort = list.stream()
                .filter(reason -> StrUtil.equals(reason.getAuditStatus(), command.getAuditStatus()))
                .map(LsdsGoodsOptReason::getSort)
                .reduce(Math::max).orElse(0);
        }

        LsdsGoodsOptReason entity = assembleOptReasonAdd(command, sort, companyId);
        lsdsGoodsOptReasonMapper.insertSelective(entity);
        return ResultMode.success();
    }

    private LsdsGoodsOptReason assembleOptReasonAdd(OptReasonAddCommand command, int sort, String companyId) {
        LsdsGoodsOptReason entity = new LsdsGoodsOptReason();
        entity.setCompanyId(companyId);
        entity.setReason(command.getReason());
        entity.setReasonCategory(command.getReasonCategory());
        entity.setSort(sort + 1);
        entity.setRemark(command.getRemark());
        entity.setEnable(1);
        entity.setAuditStatus(command.getAuditStatus());

        String userBaseId = JwtUtil.getInstance().getUserBaseIdByToken();
        DateTime now = DateUtil.date();
        entity.setCreatorId(userBaseId);
        entity.setUpdaterId(userBaseId);
        entity.setCreatedDate(now);
        entity.setUpdatedDate(now);

        return entity;
    }


    public ResultMode updateSort(List<OptReasonUpdSortCommand> commandList) {
        if (IterUtil.isEmpty(commandList)) {
            throw new LsdsWlydException("更新列表为空");
        }

        commandList.stream().forEach(command -> {
            if (ObjUtil.hasEmpty(command.getId(), command.getSort())) {
                throw new LsdsWlydException("参数为空");
            }
            LsdsGoodsOptReason lsdsGoodsOptReason = lsdsGoodsOptReasonMapper.selectByPrimaryKey(command.getId());
            if (ObjUtil.isNull(lsdsGoodsOptReason)) {
                throw new LsdsWlydException("审核原因不存在，id：" + command.getId());
            }
        });

        List<LsdsGoodsOptReason> entityList = assembleOptReasonUpd(commandList);
        updateBatch(entityList);
        return ResultMode.success();
    }

    private List<LsdsGoodsOptReason> assembleOptReasonUpd(List<OptReasonUpdSortCommand> commandList) {
        List<LsdsGoodsOptReason> list = new ArrayList<>();
        String userBaseId = JwtUtil.getInstance().getUserBaseIdByToken();
        DateTime now = DateUtil.date();

        for (OptReasonUpdSortCommand command : commandList) {
            LsdsGoodsOptReason entity = new LsdsGoodsOptReason();
            entity.setId(command.getId());
            entity.setSort(command.getSort());
            entity.setUpdatedDate(now);
            entity.setUpdaterId(userBaseId);
            list.add(entity);
        }

        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBatch(List<LsdsGoodsOptReason> entityList) {
        if (IterUtil.isEmpty(entityList)) {
            return;
        }
        entityList.stream().forEach(entity -> lsdsGoodsOptReasonMapper.updateByPrimaryKeySelective(entity));
    }


    public ResultMode delete(Long id) {
        if (ObjUtil.isNull(id)) {
            throw new LsdsWlydException("id为空");
        }
        LsdsGoodsOptReason lsdsGoodsOptReason = lsdsGoodsOptReasonMapper.selectByPrimaryKey(id);
        if (ObjUtil.isNull(lsdsGoodsOptReason)) {
            throw new LsdsWlydException("审核原因不存在");
        }

        if (lsdsGoodsOptReason.getEnable() == 0) {
            throw new LsdsWlydException("审核原因已删除");
        }

        lsdsGoodsOptReasonMapper.deleteByPrimaryKey(id);
        return ResultMode.success();
    }

}
