package com.wanlianyida.lsds.application.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.*;
import com.isoftstone.hig.common.utils.inter.DistributedLocker;
import com.isoftstone.hig.lsds.api.entity.*;
import com.isoftstone.hig.lsds.api.enums.*;
import com.isoftstone.hig.lsds.api.filter.BidsFilesFilter;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsFilter;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsOfferFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsVo;
import com.isoftstone.hig.msg.api.entity.MsgInfo;
import com.isoftstone.hig.msg.api.inter.SystemMsgInter;
import com.isoftstone.hig.platform.api.client.PlatformCommonInterClient;
import com.isoftstone.hig.platform.api.entity.PlatformCmOperationMainBody;
import com.isoftstone.hig.platform.api.entity.PlatformUmCompany;
import com.isoftstone.hig.platform.api.entity.PlatformUmUserbaseinfo;
import com.isoftstone.hig.platform.api.enums.PlatformEnum;
import com.isoftstone.hig.platform.api.filter.PlatformUmCompanyFilter;
import com.isoftstone.hig.platform.api.inter.PlatformCmOperationMainBodyInter;
import com.wanlianyida.lsds.domain.service.LsdsGoodsOfferDomainService;
import com.wanlianyida.lsds.infrastructure.config.FormsAuthTrader;
import com.wanlianyida.lsds.infrastructure.enums.EnquiryTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsFeeClearTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.OfferSourceEnum;
import com.wanlianyida.lsds.infrastructure.exchange.AmountRoundingModeExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.OmsExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.lsds.infrastructure.repository.mapper.*;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import jodd.util.StringUtil;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 报价事务处理类
 *
 * <AUTHOR>
 */
@Log4j2
@Service
public class LsdsGoodsOfferAppService {

    @Autowired
    private SystemMsgInter systemMsgInter;

    @Autowired
    PlatformCommonInterClient platformCommonInterClient;

    @Resource
    DistributedLocker distributedLocker;

    @Autowired
    @Lazy
    PlatformCmOperationMainBodyInter platformCmOperationMainBodyInter;

    @Resource
    private GoodsDeductibleAppService goodsDeductibleService;



    @Resource
    private AmountRoundingModeExchangeService amountRoundingModeService;

    @Resource
    private PlatformExchangeService platformExchangeService;


    @Resource
    private LsdsGoodsOfferMapper lsdsGoodsOfferRepository;

    @Resource
    private LsdsGoodsBidFileAppService lsdsGoodsBidFileService;

    @Resource
    private OmsExchangeService omsExchangeService;
    @Resource
    private LsdsGoodsOfferDomainService lsdsGoodsOfferDomainService;
    /**
     * 货源ID生成锁
     */
    private static final String GOODS_OFFER_ID_ADD_LOCK = "lsds:goods:offer:id:add";


    public void generateSupplierOffer(LsdsGoods lsdsGoods, List<LsdsGoodsAssignSupplier> supplierList, Integer offerCurrentRounds) {
        if (ObjUtil.hasNull(lsdsGoods, offerCurrentRounds) || IterUtil.isEmpty(supplierList)) {
            return;
        }

        //第一轮、指定物流公司
        if (offerCurrentRounds == 1 && !StrUtil.equals(lsdsGoods.getEnquiryRange(), LsdsEnum.LsdsgoodsEnquiryRangeEnum.enquiryrange1.getCode())) {
            return;
        }

        //报价单数据构造
        List<LsdsGoodsOffer> offerList = assembleOfferAdd(lsdsGoods, supplierList, offerCurrentRounds);
        if (IterUtil.isNotEmpty(offerList)) {
            lsdsGoodsOfferRepository.batchAdd(offerList);
        }

    }

    private List<LsdsGoodsOffer> assembleOfferAdd(LsdsGoods lsdsGoods, List<LsdsGoodsAssignSupplier> supplierList, Integer offerCurrentRounds) {
        List<LsdsGoodsOffer> offerList = new ArrayList<>();
        //查询报价人是否是交易签约主体
        List<String> companyIdList = supplierList.stream().map(LsdsGoodsAssignSupplier::getCompanyId).collect(Collectors.toList());
        Map<String, Boolean> map = platformExchangeService.getTransactionBody(companyIdList);

        String now = DateUtils.getDateTime();
        String userBaseId = JwtUtil.getInstance().getUserBaseIdByToken();
        for (LsdsGoodsAssignSupplier supplier : supplierList) {
            LsdsGoodsOffer offer = new LsdsGoodsOffer();
            String offerId = String.valueOf(IdUtil.generateId());
            offer.setOfferId(offerId);
            offer.setGoodsAddressId(lsdsGoods.getGoodsAddressId());
            offer.setOfferRound(offerCurrentRounds);
            offer.setGoodsId(lsdsGoods.getGoodsId());
            offer.setCompanyId(supplier.getCompanyId());
            offer.setCompanyShortName(supplier.getCompanyShortName());
            offer.setTransportationType(lsdsGoods.getTransportationType());
            offer.setEnquiryTypeBasePrice(lsdsGoods.getEnquiryTypeBasePrice());
            offer.setEnquiryTypeBaseTaxRate(lsdsGoods.getEnquiryTypeBaseTaxRate());
            offer.setEnquiryTypeBaseOpenTicket(lsdsGoods.getEnquiryTypeBaseOpenTicket());
            offer.setHasPlan("21");
            offer.setStatus(1);
            offer.setReleaseDate(lsdsGoods.getReleaseDate());
            offer.setArriveDate(lsdsGoods.getArriveDate());
            offer.setSortNode(1);

            offer.setCreateBy(userBaseId);
            offer.setCreateDate(now);
            offer.setModifyBy(userBaseId);
            offer.setModifyDate(now);

            offer.setOfferStatus(OfferStatusEnum.NONE.getCode());
            offer.setBidOpeningStatus(BidOpeningStatusEnum.TO_BE.getCode());
            if (MapUtil.isNotEmpty(map) && ObjUtil.isNotNull(map.get(supplier.getCompanyId()))
                && map.get(supplier.getCompanyId())) {
                offer.setContractFlag(1);
                //判断是向下游询价货源
                if (lsdsGoods.getReleaseType() != 2 && !StrUtil.equals(lsdsGoods.getGoodsSourceType(), GoodsSourceTypeEnum.BID_OPENING.getCode())) {
                    offer.setDownOfferFlag(1);
                } else {
                    offer.setDownOfferFlag(0);
                }
            } else {
                offer.setContractFlag(0);
                offer.setDownOfferFlag(0);
            }

            offerList.add(offer);
        }

        return offerList;
    }


    /**
     * 新增货源报价信息
     * 创建者: cgb
     * 创建时间: 2019/11/20
     *
     * @param model 对货源报价ID 货源报价 我准备要接单进行承运进行报价。货主（老板）审核评估你的价格OK了再进行下单给你承运.信息
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【增加成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【增加成功】编码,ResultMode.errMsg为相应【增加成功】描述；
     * 否则ResultMode.succeed=false【增加失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【增加失败】编码,ResultMode.errMsg为相应【增加失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsGoodsOfferAdd(LsdsGoodsVo model, String token) {
        log.info("lsdsGoodsOfferAdd#报价请求参数{}",JSON.toJSONString(model));
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
            log.info("userInfoFromCache......{]",userInfoFromCache);
            //获取货源信息,判断当前货源状态是否可以报价
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoods lsdsGoods = dao.getModel(model.getLsdsGoods().getGoodsId());
            if(ObjUtil.isNull(lsdsGoods)){
                return resultmodel;
            }
            //自动下单参数赋值
            model.setExLsdsGoods(lsdsGoods);

            //如果当前货源报价轮次不为第一轮，则检查当前承运商是否在货源指定承运商列表中
            if(null != lsdsGoods.getOfferCurrentRounds() && lsdsGoods.getOfferCurrentRounds() > 1){
                //返回承运商信息
                GoodsAssignSupplierMapper supplierDao = SpringContextUtil.getBeanByClass(GoodsAssignSupplierMapper.class);
                LsdsGoodsAssignSupplier supplier = new LsdsGoodsAssignSupplier();
                supplier.setGoodsId(lsdsGoods.getGoodsId());
                supplier.setOfferRound(lsdsGoods.getOfferCurrentRounds());
                //model.getLsdsGoods().getCompanyId()为前端传递的承运商companyId,切记！
                supplier.setCompanyId(userInfoFromCache.getCompanyId());
                int isSupplier = supplierDao.existsInSupplier(supplier);
                if(isSupplier < 1){
                    errMsg = "你不在当前货源当前轮次承运商列表中！";
                    resultmodel.setErrMsg(errMsg);
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }
            }

            //判断货源当前状态是否允许报价
            if(!lsdsGoods.getDealStatus().equals(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode())){
                errMsg = "货源当前状态不允许报价，请检查货源状态是否为【已成交、已关闭、已过期】！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }

            //检查当前货源是否已过期
            String validityDate = lsdsGoods.getValidityDate();
            Calendar valCal = Calendar.getInstance();
            if(!StringUtils.isEmpty(validityDate)){
                valCal.setTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(validityDate));
                if(valCal.getTimeInMillis() < System.currentTimeMillis()){
                    errMsg = "当前货源已过期！";
                    resultmodel.setErrMsg(errMsg);
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }
            }

            //当前时间是否超过报价有效期
            Calendar enqCal = Calendar.getInstance();
            String currentOfferStartDate = lsdsGoods.getCurrentOfferStartDate();
            Integer enquiryOfferTime = lsdsGoods.getEnquiryOfferTime();
            enqCal.setTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(currentOfferStartDate));

            //发布货源时填写了报价有效时间,则验证报价时间是否过期；未填报价有效时间，则报价有效期即为货源有效期，只需验证
            //货源是否有效即可
            if (null != enquiryOfferTime && enquiryOfferTime > 0) {
                //发布货源时填写了报价有效时间
                long validMills = enqCal.getTimeInMillis() + enquiryOfferTime * 60 * 60 * 1000;
                if (validMills < System.currentTimeMillis()) {
                    errMsg = "当前报价时间已过期！";
                    resultmodel.setErrMsg(errMsg);
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }
            }

            //当前是否达到最大可报价轮次
            if (null != model.getLsdsGoods().getOfferRound() && model.getLsdsGoods().getOfferRound() > 0 && (model.getLsdsGoods().getOfferRound() < model.getLsdsGoods().getOfferCurrentRounds())) {
                errMsg = "报价轮次已超过系统配置的最大上限！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }

            LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
            //一个承运商针对一个货源一个轮次只能报价一次
            LsdsGoodsOffer lgs = new LsdsGoodsOffer();
            lgs.setGoodsId(model.getLsdsGoods().getGoodsId());
            lgs.setOfferRound(lsdsGoods.getOfferCurrentRounds());

            if(model.getLsdsGoods().getOfferSource() == OfferSourceEnum.GOODS_LIST.getSource()){
                if(userInfoFromCache!=null){
                    lgs.setCompanyId(userInfoFromCache.getCompanyId());
                }
                int existOffer = offerDao.existsCurrentOffer(lgs);
                if (existOffer > 0) {
                    errMsg = "不允许重复报价！";
                    resultmodel.setErrMsg(errMsg);
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }
            }else {
                for (LsdsGoodsOffer offer : model.getGoodsOfferList()) {
                    lgs.setCompanyId(offer.getCompanyId());
                    int existOffer = offerDao.existsCurrentOffer(lgs);
                    if (existOffer != 1) {
                        errMsg = "不允许重复报价！";
                        resultmodel.setErrMsg(errMsg);
                        resultmodel.setSucceed(false);
                        return resultmodel;
                    }
                }
            }
            if(CollUtil.isEmpty(model.getGoodsOfferList())){
                return resultmodel;
            }
            //当前轮次报价最低价
            BigDecimal minOfferPrice = offerDao.queryMinOfferPrice(lsdsGoods.getGoodsId(), lsdsGoods.getOfferCurrentRounds());
            List<String> companyIds = model.getGoodsOfferList().stream().map(LsdsGoodsOffer::getCompanyId).collect(Collectors.toList());
            Map<String, Boolean> transactionMap = platformExchangeService.getTransactionBody(companyIds);
            String temp = "";
            for (LsdsGoodsOffer offer : model.getGoodsOfferList()) {
                if(null != offer.getEnquiryTypeBasePrice() && offer.getEnquiryTypeBasePrice().compareTo(BigDecimal.ZERO) >= 0){
                    String offerId = String.valueOf(IdUtil.generateId());
                    temp = offerId;
                    offer.setOfferId(offerId);
                    offer.setCreateDate(DateUtils.getDateTime());
                    offer.setModifyDate(DateUtils.getDateTime());
                    offer.setOfferRound(lsdsGoods.getOfferCurrentRounds());
                    offer.setOfferStatus(OfferStatusEnum.QUOTE.getCode());
                    offer.setEnquiryTypeBaseTaxRate(lsdsGoods.getEnquiryTypeBaseTaxRate());
                    offer.setStatus(1);
                    offer.setGoodsId(model.getLsdsGoods().getGoodsId());
                    offer.setCreateBy(userInfoFromCache.getUserBaseId());
                    offer.setModifyBy(userInfoFromCache.getUserBaseId());
                    offer.setPriceUserBaseId(userInfoFromCache.getUserBaseId());
                    if(model.getGoodsOfferList().size() == 1 && null != model.getGoodsPlanList() && model.getGoodsPlanList().size() > 1){
                        offer.setHasPlan("11");
                    }else {
                        offer.setHasPlan("21");
                    }

                    //计算浮动价
                    calculateFeeClearPrice(offer, lsdsGoods, transactionMap);
                    //当前轮次报价最低价
                    if (minOfferPrice == null || minOfferPrice.compareTo(offer.getEnquiryTypeBaseOpenTicket()) > 0) {
                        minOfferPrice = offer.getEnquiryTypeBaseOpenTicket();
                    }
                }else{
                    errMsg = "基价数据无效！";
                    resultmodel.setErrMsg(errMsg);
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }

            }
            //如果发布货源时选择提供运输方案，则必须录入运输方案(运输方案实际为承运商报价时拆分的段信息)，否则承运方可录入可不录入
            if (!StringUtils.isEmpty(model.getLsdsGoods().getTransportIsOptions()) && model.getLsdsGoods().getTransportIsOptions().equals("11") && (null == model.getGoodsPlanList() || model.getGoodsPlanList().size() == 0)) {
                errMsg = "请提供运输方案信息！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }else {
                if (null != model.getGoodsPlanList() && model.getGoodsPlanList().size() > 0) {
                    List<LsdsGoodsAddress> planList = new ArrayList<>();
                    for (LsdsGoodsPlan plan : model.getGoodsPlanList()) {
                        //封装运输方案信息
                        String planAddressId = String.valueOf( IdUtil.generateId());
                        plan.setPlanId(String.valueOf(IdUtil.generateId()));
                        plan.setGoodsAddressId(planAddressId);
                        plan.setOfferSplitId(temp);
                        //#region 封装运输方案地址信息
                        if(!StringUtils.isEmpty(plan.getSendAddrShortName()) && !StringUtils.isEmpty(plan.getReceiveAddrShortName())){
                            LsdsGoodsAddress planAddress =  LsdsGoodsAddress.of().setGoodsAddressId(planAddressId);
                            planAddress.setSourceId(lsdsGoods.getGoodsId());
                            planAddress.setSendAddrShortName(plan.getSendAddrShortName());
                            planAddress.setSendAddrProvince(plan.getSendAddrProvince());
                            planAddress.setSendAddrCity(plan.getSendAddrCity());
                            planAddress.setSendAddrArea(plan.getSendAddrArea());
                            planAddress.setSendAddrStreet(plan.getSendAddrStreet());
                            planAddress.setSendAddrProvinceName(plan.getSendAddrProvinceName());
                            planAddress.setSendAddrCityName(plan.getSendAddrCityName());
                            planAddress.setSendAddrAreaName(plan.getSendAddrAreaName());
                            planAddress.setSendAddrStreetName(plan.getSendAddrStreetName());
                            planAddress.setSendAddrDetail(plan.getSendAddrDetail());
                            planAddress.setSendLinker(plan.getSendLinker());
                            planAddress.setSendPhoneNumber(plan.getSendPhoneNumber());
                            planAddress.setReceiveAddrShortName(plan.getReceiveAddrShortName());
                            planAddress.setReceiveAddrProvince(plan.getReceiveAddrProvince());
                            planAddress.setReceiveAddrCity(plan.getReceiveAddrCity());
                            planAddress.setReceiveAddrArea(plan.getReceiveAddrArea());
                            planAddress.setReceiveAddrStreet(plan.getReceiveAddrStreet());
                            planAddress.setReceiveAddrProvinceName(plan.getReceiveAddrProvinceName());
                            planAddress.setReceiveAddrCityName(plan.getReceiveAddrCityName());
                            planAddress.setReceiveAddrAreaName(plan.getReceiveAddrAreaName());
                            planAddress.setReceiveAddrStreetName(plan.getReceiveAddrStreetName());
                            planAddress.setReceiveAddrDetail(plan.getReceiveAddrDetail());
                            planAddress.setReceiveLinker(plan.getReceiveLinker());
                            planAddress.setReceivePhoneNumber(plan.getReceivePhoneNumber());
                            planAddress.setAddressType("3");
                            planList.add(planAddress);
                        }else{
                            errMsg = "运输方案信息错误：出发地 - 目的地 信息不完整！";
                            resultmodel.setErrMsg(errMsg);
                            resultmodel.setSucceed(false);
                            return resultmodel;
                        }
                        //#endregion
                    }
                    LsdsGoodsPlanMapper planDao = SpringContextUtil.getBeanByClass(LsdsGoodsPlanMapper.class);
                    planDao.batchAdd(model.getGoodsPlanList());
                    GoodsAddressMapper addressDao = SpringContextUtil.getBeanByClass(GoodsAddressMapper.class);
                    addressDao.insertBatch(planList);
                }

            }
            if(model.getLsdsGoods().getOfferSource() == OfferSourceEnum.GOODS_LIST.getSource()){
                //新增报价信息
                offerDao.batchAdd(model.getGoodsOfferList());
            }else {
                if (StrUtil.equals(lsdsGoods.getGoodsIsSplit(), LsdsEnum.LsdsgoodsGoodsIsSplitEnum.goodsissplit1.getCode())) {
                    //多段，先删除，再新增
                    model.getGoodsOfferList().forEach(offer -> offerDao.deleteByOffer(offer));
                    offerDao.batchAdd(model.getGoodsOfferList());
                } else {
                    //指定物流公司的更新报价单
                    model.getGoodsOfferList().forEach(e ->{
                        LsdsGoodsOffer update = new LsdsGoodsOffer();
                        update.setGoodsId(model.getLsdsGoods().getGoodsId());
                        update.setCompanyId(e.getCompanyId());
                        update.setOfferStatus(OfferStatusEnum.QUOTE.getCode());
                        update.setOfferRound(lsdsGoods.getOfferCurrentRounds());
                        update.setEnquiryTypeBaseTaxRate(e.getEnquiryTypeBaseTaxRate());
                        update.setEnquiryTypeBasePrice(e.getEnquiryTypeBasePrice());
                        update.setEnquiryTypeBaseOpenTicket(e.getEnquiryTypeBaseOpenTicket());
                        update.setModifyDate(e.getModifyDate());
                        update.setModifyBy(e.getModifyBy());
                        update.setPriceUserBaseId(e.getPriceUserBaseId());
                        update.setRealityCarrierEnquiryTypeBasePrice(e.getRealityCarrierEnquiryTypeBasePrice());
                        update.setRealityCarrierEnquiryTypeBaseOpenTicket(e.getRealityCarrierEnquiryTypeBaseOpenTicket());
                        offerDao.updateByGoodsId(update);
                    });
                }
            }

            //同步更新source表
            GoodsSourceMapper goodsSourceMapper = SpringContextUtil.getBeanByClass(GoodsSourceMapper.class);
            goodsSourceMapper.updateCurrentRoundOffer(model.getLsdsGoods().getGoodsId(), minOfferPrice);

            //报价成功，发送站内信通知发货方
            Map<String, String> param = new HashMap();
            String userId = lsdsGoods.getSubmitBy();
            String companyId = lsdsGoods.getCompanyId();
            String templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_REVICE_OFFER.getCode();
            param.put("goodsNo",lsdsGoods.getGoodsId());
            param.put("sendShortName",lsdsGoods.getSendAddrShortName());
            param.put("receiveShortName",lsdsGoods.getReceiveAddrShortName());
            //发送站内信
            sendMsg(userId,companyId,templateId,param);
        } catch (Exception ex) {
            ex.printStackTrace();
            errMsg = "新增货源报价信息异常";
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
            LogHelper.writeError(errMsg, ex);
        }
        return resultmodel;
    }

    /**
     * 计算浮动价格
     * @param offer 报价信息
     * @param goods 货源信息
     * @param transactionMap 报价企业是否交易签约主体
     */
    public void calculateFeeClearPrice(LsdsGoodsOffer offer, LsdsGoods goods, Map<String, Boolean> transactionMap) {
        BigDecimal enquiryTypeBasePrice = offer.getEnquiryTypeBasePrice();
        BigDecimal enquiryTypeBaseOpenTicket = offer.getEnquiryTypeBaseOpenTicket();
        offer.setRealityCarrierEnquiryTypeBasePrice(enquiryTypeBasePrice);
        offer.setRealityCarrierEnquiryTypeBaseOpenTicket(enquiryTypeBaseOpenTicket);

        //非公开询价
        if (!StrUtil.equals(goods.getEnquiryType(), EnquiryTypeEnum.PUBLIC.getRfqType())) {
            return;
        }
        //发货方是万联易达 || 交易签约主体
        if (StrUtil.equals(offer.getCompanyId(), "2") || (MapUtil.isNotEmpty(transactionMap) && transactionMap.get(offer.getCompanyId()))) {
            return;
        }

        BigDecimal enquiryTypeBaseTaxRate = offer.getEnquiryTypeBaseTaxRate().divide(new BigDecimal("100"), 7, BigDecimal.ROUND_DOWN);
        BigDecimal feeClearValue = goods.getFeeClearValue();
        //按单价比例上浮(单位%)
        if (StrUtil.equals(goods.getFeeClearType(), GoodsFeeClearTypeEnum.TYPE10.getRfqType())) {
            feeClearValue = goods.getFeeClearValue().divide(new BigDecimal("100"), 7, BigDecimal.ROUND_DOWN);
            //上浮后开票价 = 开票价*(1+上浮税率）
            offer.setEnquiryTypeBaseOpenTicket(enquiryTypeBaseOpenTicket.multiply(new BigDecimal("1").add(feeClearValue)).setScale(5, BigDecimal.ROUND_DOWN));
            //上浮后基价 = 上浮后开票价*(1-平台税率
            offer.setEnquiryTypeBasePrice(offer.getEnquiryTypeBaseOpenTicket().multiply(new BigDecimal("1").subtract(enquiryTypeBaseTaxRate)).setScale(5, BigDecimal.ROUND_DOWN));
        }
        //按单价上浮(单位元)
        else if (StrUtil.equals(goods.getFeeClearType(), GoodsFeeClearTypeEnum.TYPE20.getRfqType())) {
            //上浮后开票价 = 开票价+上浮单价
            offer.setEnquiryTypeBaseOpenTicket(enquiryTypeBaseOpenTicket.add(feeClearValue).setScale(5, BigDecimal.ROUND_DOWN));
            //上浮后基价 = 上浮后开票价*（1-平台税率）
            offer.setEnquiryTypeBasePrice(offer.getEnquiryTypeBaseOpenTicket().multiply(new BigDecimal("1").subtract(enquiryTypeBaseTaxRate)).setScale(5, BigDecimal.ROUND_DOWN));
        }
    }

    /**
     * 自动生成订单
     *
     * @param lsdsGoods  货源信息
     * @param offerCompanyId  报价企业id
     */
    public ResultMode autoPlaceOrder(LsdsGoods lsdsGoods, String offerCompanyId) {
        if (ObjUtil.isNull(lsdsGoods.getRoundingMode())) {
            int roundingMode = amountRoundingModeService.getRoundingMode(lsdsGoods.getGoodsId());
            lsdsGoods.setRoundingMode(roundingMode);
        }
        //查询最新的offer信息
        LsdsGoodsOffer filter = new LsdsGoodsOffer();
        filter.setGoodsId(lsdsGoods.getGoodsId());
        filter.setOfferRound(lsdsGoods.getOfferCurrentRounds());
        filter.setCompanyId(offerCompanyId);
        LsdsGoodsOffer lsdsGoodsOffer = lsdsGoodsOfferRepository.selectCompanyOffer(filter);
        return omsExchangeService.addOpenBookOrder(lsdsGoods, lsdsGoodsOffer);
    }

    /**
     * 校验是否满足直接下单条件
     * 企业货源；询价方式=指定单价；询价范围=邀请承运商为当前登录企业；运输类型=公路整车、公路集卡、多式联运/物流项目未拆分运输段、铁路、水运；非下游询价
     *
     * @param lsdsGoods 货源信息
     * @return ture-满足直接下单，false-不满足
     */
    public boolean checkAutoPlaceOrder(LsdsGoods lsdsGoods) {
        //非企业货源
        if (!StrUtil.equals(lsdsGoods.getGoodsSourceType(), GoodsSourceTypeEnum.DEFAULT.getCode())) {
            return false;
        }
        //非指定承运商
        if (!StrUtil.equals(lsdsGoods.getEnquiryRange(), LsdsEnum.LsdsgoodsEnquiryRangeEnum.enquiryrange1.getCode())) {
            return false;
        }
        //非指定单价
        if (!StrUtil.equals(lsdsGoods.getEnquiryType(), LsdsEnum.LsdsgoodsEnquiryTypeEnum.enquirytype2.getCode())) {
            return false;
        }
        //多式联运、物流项目 拆分运输段
        if (StrUtil.equalsAny(lsdsGoods.getTransportationType(),
            LsdsEnum.LsdsgoodsTransportationTypeEnum.transportationtype5.getLsdsCode(),
            LsdsEnum.LsdsgoodsTransportationTypeEnum.transportationtype6.getLsdsCode())) {
            if (StrUtil.equals(lsdsGoods.getGoodsIsSplit(), LsdsEnum.LsdsgoodsGoodsIsSplitEnum.goodsissplit1.getCode())) {
                return false;
            }
        }
        //下游询价
        if (lsdsGoods.getReleaseType() == 2) {
            return false;
        }
        //邀请承运商>1 || 不为当前登录企业
        GoodsAssignSupplierMapper supplierDao = SpringContextUtil.getBeanByClass(GoodsAssignSupplierMapper.class);
        LsdsGoodsAssignSupplier filter = new LsdsGoodsAssignSupplier();
        filter.setGoodsId(lsdsGoods.getGoodsId());
        filter.setOfferRound(lsdsGoods.getOfferCurrentRounds());
        List<LsdsGoodsAssignSupplier> supplierList = supplierDao.getPreviousSupplierList(filter);
        if (IterUtil.isEmpty(supplierList) || supplierList.size() > 1) {
            return false;
        }
        String companyId = JwtUtil.getInstance().getCompanyIdByToken();
        if (!StrUtil.equals(IterUtil.getFirst(supplierList).getCompanyId(), companyId)) {
            return false;
        }

        return true;
    }

    /**
     * 新增货源报价信息
     * 创建者: cgb
     * 创建时间: 2019/11/20
     *
     * @param model 对货源报价ID 货源报价 我准备要接单进行承运进行报价。货主（老板）审核评估你的价格OK了再进行下单给你承运.信息
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【增加成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【增加成功】编码,ResultMode.errMsg为相应【增加成功】描述；
     * 否则ResultMode.succeed=false【增加失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【增加失败】编码,ResultMode.errMsg为相应【增加失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsGoodsOfferAddDown(LsdsGoodsVo model, String token) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
            //加锁
            distributedLocker.lock(GOODS_OFFER_ID_ADD_LOCK + model.getLsdsGoods().getGoodsId(), 3);
            //获取货源信息,判断当前货源状态是否可以报价
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoods lsdsGoods = dao.getModel(model.getLsdsGoods().getGoodsId());
            //如果当前货源报价轮次不为第一轮，则检查当前承运商是否在货源指定承运商列表中
            if(null != lsdsGoods.getOfferCurrentRounds() && lsdsGoods.getOfferCurrentRounds() > 1){
                //返回承运商信息
                GoodsAssignSupplierMapper supplierDao = SpringContextUtil.getBeanByClass(GoodsAssignSupplierMapper.class);
                LsdsGoodsAssignSupplier supplier = new LsdsGoodsAssignSupplier();
                supplier.setGoodsId(lsdsGoods.getGoodsId());
                supplier.setOfferRound(lsdsGoods.getOfferCurrentRounds());
                //model.getLsdsGoods().getCompanyId()为前端传递的承运商companyId,切记！
                supplier.setCompanyId(userInfoFromCache.getCompanyId());
                int isSupplier = supplierDao.existsInSupplier(supplier);
                if(isSupplier < 1){
                    errMsg = "你不在当前货源当前轮次承运商列表中！";
                    resultmodel.setErrMsg(errMsg);
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }
            }

            //判断货源当前状态是否允许报价
            if(!lsdsGoods.getDealStatus().equals(LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode())){
                errMsg = "货源当前状态不允许报价，请检查货源状态是否为【已成交、已关闭、已过期】！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }

            //检查当前货源是否已过期
            String validityDate = lsdsGoods.getValidityDate();
            Calendar valCal = Calendar.getInstance();
            if(!StringUtils.isEmpty(validityDate)){
                valCal.setTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(validityDate));
                if(valCal.getTimeInMillis() < System.currentTimeMillis()){
                    errMsg = "当前货源已过期！";
                    resultmodel.setErrMsg(errMsg);
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }
            }

            //当前时间是否超过报价有效期
            Calendar enqCal = Calendar.getInstance();
            String currentOfferStartDate = lsdsGoods.getCurrentOfferStartDate();
            Integer enquiryOfferTime = lsdsGoods.getEnquiryOfferTime();
            enqCal.setTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(currentOfferStartDate));

            //发布货源时填写了报价有效时间,则验证报价时间是否过期；未填报价有效时间，则报价有效期即为货源有效期，只需验证
            //货源是否有效即可
            if (null != enquiryOfferTime && enquiryOfferTime > 0) {
                //发布货源时填写了报价有效时间
                long validMills = enqCal.getTimeInMillis() + enquiryOfferTime * 60 * 60 * 1000;
                if (validMills < System.currentTimeMillis()) {
                    errMsg = "当前报价时间已过期！";
                    resultmodel.setErrMsg(errMsg);
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }
            }

            //当前是否达到最大可报价轮次
            if (null != model.getLsdsGoods().getOfferRound() && model.getLsdsGoods().getOfferRound() > 0 && (model.getLsdsGoods().getOfferRound() < model.getLsdsGoods().getOfferCurrentRounds())) {
                errMsg = "报价轮次已超过系统配置的最大上限！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }

            LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);

            //判断 并发报价   多人报价判定当前轮次中有被中标  直接退出
            if(!StringUtils.isEmpty(lsdsGoods.getChildGoodsId())) {
                LsdsGoodsOffer tempLsdsGoodsOffer = new LsdsGoodsOffer();
                tempLsdsGoodsOffer.setGoodsId(model.getLsdsGoods().getChildGoodsId());
                tempLsdsGoodsOffer.setOfferRound(lsdsGoods.getOfferCurrentRounds());
                List<Map<String, String>> lsdsGoodsOffer = offerDao.getCurrentOfferdMap(tempLsdsGoodsOffer);
                if (null != lsdsGoodsOffer && null != lsdsGoodsOffer && lsdsGoodsOffer.size() > 0) {
                    for (Map<String, String> stringStringMap : lsdsGoodsOffer) {
                        if ("2".equals(stringStringMap.get("status"))) {
                            errMsg = "货源已经进行了下游询价，请勿重复提交！";
                            resultmodel.setErrMsg(errMsg);
                            resultmodel.setSucceed(false);
                            return resultmodel;
                        }
                    }
                }
            }

            //一个承运商针对一个货源一个轮次只能报价一次
            LsdsGoodsOffer lgs = new LsdsGoodsOffer();
            lgs.setGoodsId(model.getLsdsGoods().getGoodsId());
            lgs.setOfferRound(lsdsGoods.getOfferCurrentRounds());
            if(model.getLsdsGoods().getOfferSource() == OfferSourceEnum.GOODS_LIST.getSource()){
                if(userInfoFromCache!=null){
                    lgs.setCompanyId(userInfoFromCache.getCompanyId());
                }
                int existOffer = offerDao.existsCurrentOffer(lgs);
                if (existOffer > 0) {
                    errMsg = "不允许重复报价！";
                    resultmodel.setErrMsg(errMsg);
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }
            }else {
                for (LsdsGoodsOffer offer : model.getGoodsOfferList()) {
                    lgs.setCompanyId(offer.getCompanyId());
                    int existOffer = offerDao.existsCurrentOffer(lgs);
                    if (existOffer != 1) {
                        errMsg = "不允许重复报价！";
                        resultmodel.setErrMsg(errMsg);
                        resultmodel.setSucceed(false);
                        return resultmodel;
                    }
                }
            }

            //当前轮次报价最低价
            BigDecimal minOfferPrice = offerDao.queryMinOfferPrice(lsdsGoods.getGoodsId(), lsdsGoods.getOfferCurrentRounds());
            if(null != model.getGoodsOfferList() && model.getGoodsOfferList().size() > 0) {
                String temp = "";
                for (LsdsGoodsOffer offer : model.getGoodsOfferList()) {
                    if(null != offer.getEnquiryTypeBasePrice() && offer.getEnquiryTypeBasePrice().compareTo(BigDecimal.ZERO) >= 0){
                        String offerId = String.valueOf(IdUtil.generateId());
                        String childOfferId = offer.getOfferId();
                        temp = offerId;
                        offer.setOfferId(offerId);
                        offer.setChildOfferId(childOfferId);
                        offer.setCreateDate(DateUtils.getDateTime());
                        offer.setModifyDate(DateUtils.getDateTime());
                        offer.setOfferRound(lsdsGoods.getOfferCurrentRounds());
                        offer.setEnquiryTypeBaseTaxRate(lsdsGoods.getEnquiryTypeBaseTaxRate());
                        offer.setStatus(1);
                        offer.setCompanyId(userInfoFromCache.getCompanyId());
                        offer.setGoodsId(model.getLsdsGoods().getGoodsId());
                        offer.setCreateBy(userInfoFromCache.getUserBaseId());
                        offer.setModifyBy(userInfoFromCache.getUserBaseId());
                        offer.setPriceUserBaseId(userInfoFromCache.getUserBaseId());
                        if(model.getGoodsOfferList().size() == 1 && null != model.getGoodsPlanList() && model.getGoodsPlanList().size() > 1){
                            offer.setHasPlan("11");
                        }else {
                            offer.setHasPlan("21");
                        }
                        //当前轮次报价最低价
                        if (minOfferPrice == null || minOfferPrice.compareTo(offer.getEnquiryTypeBaseOpenTicket()) > 0) {
                            minOfferPrice = offer.getEnquiryTypeBaseOpenTicket();
                        }
                    }else{
                        errMsg = "基价数据无效！";
                        resultmodel.setErrMsg(errMsg);
                        resultmodel.setSucceed(false);
                        return resultmodel;
                    }

                }
                //如果发布货源时选择提供运输方案，则必须录入运输方案(运输方案实际为承运商报价时拆分的段信息)，否则承运方可录入可不录入
                if (!StringUtils.isEmpty(model.getLsdsGoods().getTransportIsOptions()) && model.getLsdsGoods().getTransportIsOptions().equals("11") && (null == model.getGoodsPlanList() || model.getGoodsPlanList().size() == 0)) {
                    errMsg = "请提供运输方案信息！";
                    resultmodel.setErrMsg(errMsg);
                    resultmodel.setSucceed(false);
                    return resultmodel;
                }else {
                    if (null != model.getGoodsPlanList() && model.getGoodsPlanList().size() > 0) {
                        List<LsdsGoodsAddress> planList = new ArrayList<>();
                        for (LsdsGoodsPlan plan : model.getGoodsPlanList()) {
                            //封装运输方案信息
                            String planAddressId = String.valueOf(IdUtil.generateId());
                            plan.setPlanId(String.valueOf(IdUtil.generateId()));
                            plan.setGoodsAddressId(planAddressId);
                            plan.setOfferSplitId(temp);
                            //#region 封装运输方案地址信息
                            if(!StringUtils.isEmpty(plan.getSendAddrShortName()) && !StringUtils.isEmpty(plan.getReceiveAddrShortName())){
                                LsdsGoodsAddress planAddress =  LsdsGoodsAddress.of();
                                planAddress.setGoodsAddressId(planAddressId);
                                planAddress.setSourceId(lsdsGoods.getGoodsId());
                                planAddress.setSendAddrShortName(plan.getSendAddrShortName());
                                planAddress.setSendAddrProvince(plan.getSendAddrProvince());
                                planAddress.setSendAddrCity(plan.getSendAddrCity());
                                planAddress.setSendAddrArea(plan.getSendAddrArea());
                                planAddress.setSendAddrStreet(plan.getSendAddrStreet());
                                planAddress.setSendAddrProvinceName(plan.getSendAddrProvinceName());
                                planAddress.setSendAddrCityName(plan.getSendAddrCityName());
                                planAddress.setSendAddrAreaName(plan.getSendAddrAreaName());
                                planAddress.setSendAddrStreetName(plan.getSendAddrStreetName());
                                planAddress.setSendAddrDetail(plan.getSendAddrDetail());
                                planAddress.setSendLinker(plan.getSendLinker());
                                planAddress.setSendPhoneNumber(plan.getSendPhoneNumber());
                                planAddress.setReceiveAddrShortName(plan.getReceiveAddrShortName());
                                planAddress.setReceiveAddrProvince(plan.getReceiveAddrProvince());
                                planAddress.setReceiveAddrCity(plan.getReceiveAddrCity());
                                planAddress.setReceiveAddrArea(plan.getReceiveAddrArea());
                                planAddress.setReceiveAddrStreet(plan.getReceiveAddrStreet());
                                planAddress.setReceiveAddrProvinceName(plan.getReceiveAddrProvinceName());
                                planAddress.setReceiveAddrCityName(plan.getReceiveAddrCityName());
                                planAddress.setReceiveAddrAreaName(plan.getReceiveAddrAreaName());
                                planAddress.setReceiveAddrStreetName(plan.getReceiveAddrStreetName());
                                planAddress.setReceiveAddrDetail(plan.getReceiveAddrDetail());
                                planAddress.setReceiveLinker(plan.getReceiveLinker());
                                planAddress.setReceivePhoneNumber(plan.getReceivePhoneNumber());
                                planAddress.setAddressType("3");
                                planList.add(planAddress);
                            }else{
                                errMsg = "运输方案信息错误：出发地 - 目的地 信息不完整！";
                                resultmodel.setErrMsg(errMsg);
                                resultmodel.setSucceed(false);
                                return resultmodel;
                            }
                            //#endregion
                        }
                        LsdsGoodsPlanMapper planDao = SpringContextUtil.getBeanByClass(LsdsGoodsPlanMapper.class);
                        planDao.batchAdd(model.getGoodsPlanList());
                        GoodsAddressMapper addressDao = SpringContextUtil.getBeanByClass(GoodsAddressMapper.class);
                        addressDao.insertBatch(planList);
                    }

                }

                if(model.getLsdsGoods().getOfferSource() == OfferSourceEnum.GOODS_LIST.getSource()){
                    //新增报价信息
                    offerDao.batchAdd(model.getGoodsOfferList());
                }else {
                    //指定物流公司的更新报价单
                    model.getGoodsOfferList().forEach(e ->{
                        LsdsGoodsOffer update = new LsdsGoodsOffer();
                        update.setGoodsId(model.getLsdsGoods().getGoodsId());
                        update.setCompanyId(e.getCompanyId());
                        update.setOfferStatus(OfferStatusEnum.QUOTE.getCode());
                        update.setOfferRound(lsdsGoods.getOfferCurrentRounds());
                        update.setEnquiryTypeBaseTaxRate(e.getEnquiryTypeBaseTaxRate());
                        update.setEnquiryTypeBasePrice(e.getEnquiryTypeBasePrice());
                        update.setEnquiryTypeBaseOpenTicket(e.getEnquiryTypeBaseOpenTicket());
                        update.setChildOfferId(e.getChildOfferId());
                        update.setRealityCarrierCompanyId(e.getRealityCarrierCompanyId());
                        update.setRealityCarrierCompanyShortName(e.getRealityCarrierCompanyShortName());
                        update.setRealityCarrierEnquiryTypeBasePrice(e.getRealityCarrierEnquiryTypeBasePrice());
                        update.setRealityCarrierEnquiryTypeBaseOpenTicket(e.getRealityCarrierEnquiryTypeBaseOpenTicket());
                        update.setModifyDate(e.getModifyDate());
                        update.setModifyBy(e.getModifyBy());
                        update.setPriceUserBaseId(e.getPriceUserBaseId());
                        offerDao.updateByGoodsId(update);
                    });
                }

                //同步更新source表
                GoodsSourceMapper goodsSourceMapper = SpringContextUtil.getBeanByClass(GoodsSourceMapper.class);
                goodsSourceMapper.updateCurrentRoundOffer(model.getLsdsGoods().getGoodsId(), minOfferPrice);

                //报价成功，发送站内信通知发货方
                Map<String, String> param = new HashMap<String, String>();
                String userId = lsdsGoods.getCreateBy();
                String companyId = lsdsGoods.getCompanyId();
                String templateId = UtilityEnum.SystemMsgTemplateEnum.GOODS_REVICE_OFFER.getCode();
                param.put("goodsNo",lsdsGoods.getGoodsId());
                param.put("sendShortName",lsdsGoods.getSendAddrShortName());
                param.put("receiveShortName",lsdsGoods.getReceiveAddrShortName());
                //发送站内信
                sendMsg(userId,companyId,templateId,param);

            }
        } catch (Exception ex) {
            errMsg = "新增下游询价货源报价信息异常";
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
            LogHelper.writeError(errMsg, ex);
        }finally{
            distributedLocker.unlock(GOODS_OFFER_ID_ADD_LOCK  + model.getLsdsGoods().getGoodsId());
        }
        return resultmodel;
    }

    /**
     * 修改货源信息
     * 创建者: cgb
     * 创建时间: 2019/11/23
     *
     * @param model 货源报价实体类
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【修改成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【修改成功】编码,ResultMode.errMsg为相应【修改成功】描述；
     * 否则ResultMode.succeed=false【修改失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【修改失败】编码,ResultMode.errMsg为相应【修改失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsGoodsOfferUpdate(LsdsGoodsOffer model, String token) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            LsdsGoodsOfferMapper dao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
            resultmodel.setSucceed(dao.modify(model));
            if (!resultmodel.getSucceed()){
                errMsg = "更新货源报价信息失败，请检验提交的数据是否正常-offerId：" + model.getOfferId();
                resultmodel.setErrMsg(errMsg);
            }
        } catch (Exception ex) {
            errMsg = "修改货源报价信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }

    /**
     * 删除货源
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param offerId 货源报价实体类ID主键
     * @param token   预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【删除成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【删除成功】编码,ResultMode.errMsg为相应【删除成功】描述；
     * 否则ResultMode.succeed=false【删除失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【删除失败】编码,ResultMode.errMsg为相应【删除失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsGoodsOfferDel(String offerId, String token) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            LsdsGoodsOfferMapper dao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
            resultmodel.setSucceed(dao.remove(offerId));
            if (!resultmodel.getSucceed()){
                errMsg = "货源报价删除异常，删除失败";
                resultmodel.setErrMsg(errMsg);
            }
        } catch (Exception ex) {
            errMsg = "根据offerId删除货源报价信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }

    /**
     * 承运人货源报价列表
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @param token    预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<LsdsGoodsOffer>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源报价实体类LsdsGoodsOffer列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsGoodsOffer> lsdsGoodsOfferPaging(PagingInfo<LsdsGoodsOfferFilter> pageInfo, String token) {

        ResultMode<LsdsGoodsOffer> returnmodel = new ResultMode<LsdsGoodsOffer>();
        try {
            returnmodel = lsdsGoodsOfferDomainService.pagingLsdsGoodsOffer(pageInfo, token);
            return returnmodel;
        } catch (Exception ex) {
            String errMsg = "承运人货源报价列表查询异常:" + ex.getMessage();
            returnmodel.setErrMsg(errMsg);
            LogHelper.writeError(errMsg, ex);
        }
        return returnmodel;
    }


    /**
     * 根据货源编号查询货源详细信息
     * 创建者: cgb
     * 创建时间: 2019/11/20
     *
     * @param model 货源编号  货源信息表实体类
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<LsdsGoodsVo>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源详细信息实体类LsdsGoodsVo数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsGoodsVo> lsdsGoodsGetDetails(LsdsGoods model, String token) {
        String errMsg = "";
        ResultMode<LsdsGoodsVo> resultmodel = new ResultMode<LsdsGoodsVo>();
        StringBuffer sb = new StringBuffer();
        LsdsGoodsVo vo =  new LsdsGoodsVo();
        LsdsGoodsOffer lgf = new LsdsGoodsOffer();
        try {
            //获取货源信息
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoods lg = LsdsGoods.of();
            lg.setGoodsId(model.getGoodsId());
            lg.setCompanyId(model.getCompanyId());
            LsdsGoods lsdsGoods = null;
            if(null != model && !StringUtils.isEmpty(model.getCompanyId())){
               //if("2".equals(model.getCompanyId())){
                if(StringUtil.isNotBlank(model.getCompanyId()) && platformCmOperationMainBodyInter.isTransactionBody(model.getCompanyId())){
                    lsdsGoods = dao.getGoodsDetailsNoUpOffer(lg);
                }else{
                    lsdsGoods = dao.getGoodsDetailsForOffer(lg);
                }
                //金额取整
                amountRoundingModeService.setLsdsGoodsScale(lsdsGoods);
            }
            if (null != lsdsGoods) {



                vo.setLsdsGoods(lsdsGoods);

                //返回分段信息
                GoodsSplitMapper splitDao = SpringContextUtil.getBeanByClass(GoodsSplitMapper.class);
                List<LsdsGoodsSplit> splitList = splitDao.getSplitList(model.getGoodsId());
                if (null != splitList && splitList.size() > 0) {
                    for (LsdsGoodsSplit split : splitList) {
                        if (!StringUtils.isEmpty(lsdsGoods.getEnquiryTypeBaseTaxRate())) {
                            split.setExGoodsRate(lsdsGoods.getEnquiryTypeBaseTaxRate());
                        }
                    }
                    vo.setGoodsSplitList(splitList);
                }

                //返回报价列表信息、运输方案列表信息
                LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
                LsdsGoodsOffer offer = new LsdsGoodsOffer();
                offer.setGoodsId(model.getGoodsId());
                offer.setCompanyId(model.getCompanyId());
                List<LsdsGoodsOffer> offerList = offerDao.getOfferList(offer);
                if (null != offerList && offerList.size() > 0 && null != model.getOfferCurrentRounds() && model.getOfferCurrentRounds() > 0) {
                    Map<String, List<LsdsGoodsOffer>> offerMap = new HashMap<>();
                    Map<String, List<LsdsGoodsPlan>> planMap = new HashMap<>();
                    for (int i = 1; i <= model.getOfferCurrentRounds(); i++) {
                        String temp = String.valueOf(i);
                        List<LsdsGoodsOffer> tempOfferList = offerList.stream().filter(s -> s.getOfferRound().toString().equals(temp)).collect(Collectors.toList());
                        if (null != tempOfferList && tempOfferList.size() > 0) {
                            offerMap.put(temp, tempOfferList);
                            LsdsGoodsPlanMapper planDao = SpringContextUtil.getBeanByClass(LsdsGoodsPlanMapper.class);
                            List<LsdsGoodsPlan> tempPlanList = planDao.getPlanListByOfferId(tempOfferList.get(0).getOfferId());
                            if(null != tempPlanList && tempPlanList.size() > 0){
                                planMap.put(temp, tempPlanList);
                            }
                        }
                    }
                    vo.setGoodsOfferListMap(offerMap);
                    vo.setGoodsOfferPlanListMap(planMap);
                }

                // 展示亏涨吨参数配置
                LsdsGoodsDeductible lsdsGoodsDeductible =
                    goodsDeductibleService.wrapGoodsDeductible(lsdsGoods.getOtherKuiTonsRatio(), lsdsGoods.getGoodsId());
                lsdsGoods.setLsdsGoodsDeductible(lsdsGoodsDeductible);

                //查询招标文件
                List<BidsFilesFilter> bidsFilesFilter = lsdsGoodsBidFileService.getBidsFilesFilter(model.getGoodsId(), LsdsAttachmentTypeEnum.CALL_BIDS.getType());
                if(CollUtil.isNotEmpty(bidsFilesFilter)){
                    vo.getLsdsGoods().setBidsFiles(bidsFilesFilter);
                }
                //中标文件
                buildWinBids(model.getGoodsId(),vo);
                //是否自动生成订单标识
                vo.getLsdsGoods().setAutoPlaceOrderFlag(checkAutoPlaceOrder(vo.getLsdsGoods()));

                resultmodel.getModel().add(vo);
            }else{
                errMsg = "货源不存在！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }
        } catch (Exception ex) {
            errMsg = "获取货源详细信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setSucceed(false);
            resultmodel = null;
        }
        return resultmodel;
    }

    /**
     * 根据货源编号查询货源详细信息
     * 创建者: cgb
     * 创建时间: 2019/11/20
     *
     * @param model 货源编号  货源信息表实体类
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<LsdsGoodsVo>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源详细信息实体类LsdsGoodsVo数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsGoodsVo> lsdsGoodsGetDetailsDown(LsdsGoods model, String token) {
        String errMsg = "";
        ResultMode<LsdsGoodsVo> resultmodel = new ResultMode<LsdsGoodsVo>();
        StringBuffer sb = new StringBuffer();
        LsdsGoodsVo vo =  new LsdsGoodsVo();
        LsdsGoodsOffer lgf = new LsdsGoodsOffer();
        try {
            //获取货源信息
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoods lg = LsdsGoods.of();
            lg.setGoodsId(model.getGoodsId()).setCompanyId(model.getCompanyId());
            LsdsGoods lsdsGoods = null;
            if(null != model && !StringUtils.isEmpty(model.getCompanyId())){
               // if("2".equals(model.getCompanyId())){
                if(StringUtil.isNotBlank(model.getCompanyId()) && platformCmOperationMainBodyInter.isTransactionBody(model.getCompanyId())){
                    lsdsGoods = dao.getGoodsDetailsNoUpOffer(lg);
                }else{
                    lsdsGoods = dao.getGoodsDetailsForOffer(lg);
                }
                //金额取整
                amountRoundingModeService.setLsdsGoodsScale(lsdsGoods);
            }
            if (null != lsdsGoods) {
                vo.setLsdsGoods(lsdsGoods);
                //返回分段信息
                GoodsSplitMapper splitDao = SpringContextUtil.getBeanByClass(GoodsSplitMapper.class);
                List<LsdsGoodsSplit> splitList = splitDao.getSplitList(model.getGoodsId());
                if (null != splitList && splitList.size() > 0) {
                    for (LsdsGoodsSplit split : splitList) {
                        if (!StringUtils.isEmpty(lsdsGoods.getEnquiryTypeBaseTaxRate())) {
                            split.setExGoodsRate(lsdsGoods.getEnquiryTypeBaseTaxRate());
                        }
                    }
                    vo.setGoodsSplitList(splitList);
                }

                //返回报价列表信息、运输方案列表信息
                LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
                LsdsGoodsOffer offer = new LsdsGoodsOffer();
                offer.setGoodsId(model.getGoodsId());
                offer.setCompanyId(model.getCompanyId());
                List<LsdsGoodsOffer> offerList = offerDao.getOfferList(offer);
                if (null != offerList && offerList.size() > 0 && null != model.getOfferCurrentRounds() && model.getOfferCurrentRounds() > 0) {
                    Map<String, List<LsdsGoodsOffer>> offerMap = new HashMap<>();
                    Map<String, List<LsdsGoodsPlan>> planMap = new HashMap<>();
                    for (int i = 1; i <= model.getOfferCurrentRounds(); i++) {
                        String temp = String.valueOf(i);
                        List<LsdsGoodsOffer> tempOfferList = offerList.stream().filter(s -> s.getOfferRound().toString().equals(temp)).collect(Collectors.toList());
                        if (null != tempOfferList && tempOfferList.size() > 0) {
                            offerMap.put(temp, tempOfferList);
                            LsdsGoodsPlanMapper planDao = SpringContextUtil.getBeanByClass(LsdsGoodsPlanMapper.class);
                            List<LsdsGoodsPlan> tempPlanList = planDao.getPlanListByOfferId(tempOfferList.get(0).getOfferId());
                            if(null != tempPlanList && tempPlanList.size() > 0){
                                planMap.put(temp, tempPlanList);
                            }
                        }
                    }
                    vo.setGoodsOfferListMap(offerMap);
                    vo.setGoodsOfferPlanListMap(planMap);
                }
                resultmodel.getModel().add(vo);
            }else{
                errMsg = "货源不存在！";
                resultmodel.setErrMsg(errMsg);
                resultmodel.setSucceed(false);
                return resultmodel;
            }
        } catch (Exception ex) {
            errMsg = "获取货源详细信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setSucceed(false);
            resultmodel = null;
        }
        return resultmodel;
    }

    /**
     * 根据货源编号查询货源详细信息
     * 创建者: cgb
     * 创建时间: 2019/11/20
     * @param model 货源编号  货源信息表实体类
     * @return {@code ResultMode<LsdsGoodsVo>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源详细信息实体类LsdsGoodsVo数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsGoods> lsdsGoodsDetails(LsdsGoods model) {
        String errMsg = "";
        ResultMode<LsdsGoods> resultmodel = new ResultMode<LsdsGoods>();
        try {
            //获取货源信息
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoods lsdsGoods = dao.getModel(model.getGoodsId());
            //金额取整
            amountRoundingModeService.setLsdsGoodsScale(lsdsGoods);

            resultmodel.getModel().add(lsdsGoods);
        } catch (Exception ex) {
            errMsg = "获取货源详信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setSucceed(false);
            resultmodel = null;
        }
        return resultmodel;
    }


    /**
     * 进入下一轮报价
     * 创建者: cgb
     * 创建时间: 2019/11/30
     *
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @param model 货源编号  货源信息表实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【报价成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【报价成功】编码,ResultMode.errMsg为相应【报价成功】描述；
     * 否则ResultMode.succeed=false【报价失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【报价失败】编码,ResultMode.errMsg为相应【报价失败】描述。
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> offerToNetRound(LsdsGoodsVo model, String token) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            //更新货源当前报价轮次
            GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            goodsDao.updateRound(model.getLsdsGoods());
            GoodsSourceMapper goodsSourceMapper = SpringContextUtil.getBeanByClass(GoodsSourceMapper.class);
            goodsSourceMapper.updateRound(model.getLsdsGoods().getGoodsId(),model.getLsdsGoods().getOfferCurrentRounds());
            goodsSourceMapper.updateRoundExtend(model.getLsdsGoods().getGoodsId());

            //如果用户勾选承运商，则更新或插入勾选承运商信息到承运商信息表中，如果不勾选，则之前有承运商则对所有指定承运商开放，否则对所有平台3pl开放
            //承运商报价时，需要查询当前货源当前轮次承运人信息，如果有承运人信息：包括在其中，则报价对其开发，否则关闭；如果没有：当前报价轮次对所有人开发
            GoodsAssignSupplierMapper supplierDao = SpringContextUtil.getBeanByClass(GoodsAssignSupplierMapper.class);
            LsdsGoodsAssignSupplier sup = new LsdsGoodsAssignSupplier();
            sup.setGoodsId(model.getLsdsGoods().getGoodsId());
            sup.setOfferRound(model.getLsdsGoods().getOfferCurrentRounds());
            List<LsdsGoodsAssignSupplier> supplierList = supplierDao.getPreviousSupplierList(sup);
            //supplierList.size()>0 指定了承运商，反之没有（即对所有承运商开放）
            //上一轮指定了承运商
            if (null != supplierList && supplierList.size() > 0) {
                //进入下一轮勾选了承运商
                if (null != model.getAssignSupplierList() && model.getAssignSupplierList().size() > 0) {
                    for (LsdsGoodsAssignSupplier supplier : model.getAssignSupplierList()) {
                        supplier.setModifyDate(DateUtils.getDateTime());
                    }
                    supplierDao.batchUpdate(model.getAssignSupplierList());
                } else {
                    for (LsdsGoodsAssignSupplier supplier : supplierList) {
                        supplier.setModifyDate(DateUtils.getDateTime());
                        supplier.setOfferRound(model.getLsdsGoods().getOfferCurrentRounds() + 1);
                    }
                    supplierDao.batchUpdate(model.getAssignSupplierList());
                }
            } else {
                //上一轮指定了承运商没有指定承运商，本轮指定了承运商，则新增承运商，否则不处理（即本轮报价任然对所有3pl开发）
                if (null != model.getAssignSupplierList() && model.getAssignSupplierList().size() > 0) {
                    for (LsdsGoodsAssignSupplier supplier : model.getAssignSupplierList()) {
                        supplier.setModifyDate(DateUtils.getDateTime());
                    }
                    supplierDao.batchAdd(model.getAssignSupplierList());
                }
            }
        } catch (Exception ex) {
            errMsg = "进入下一轮报价异常！";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }




    /**
     * 货源列表(我要承运)
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     * <AUTHOR>
     * 创建时间 2019/11/29 14:43
     */
    public ResultMode<LsdsGoods> getGoodsListPage(PagingInfo<LsdsGoodsFilter> pageInfo) {
        ResultMode<LsdsGoods> returnModel = new ResultMode<LsdsGoods>();
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        log.info("调试参数:{}", JSON.toJSONString(pageInfo));
        List<LsdsGoods> goodsList = goodsDao.getGoodsListOpen(pageInfo.filterModel);
        PageInfo<LsdsGoods> pageInfoList = new PageInfo<>(goodsList);
        Long total = pageInfoList.getTotal();
        returnModel.setTotal(total.intValue());
        returnModel.setModel(goodsList);
        return returnModel;
    }


    /**
     * App货源列表(找货)
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     * <AUTHOR>
     * 创建时间 2019/11/29 14:43
     */
    public ResultMode<LsdsGoods> getGoodsListPageForAPP(PagingInfo<LsdsGoodsFilter> pageInfo) {
        ResultMode<LsdsGoods> returnModel = new ResultMode<LsdsGoods>();
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength, "goods_id desc");
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<LsdsGoods> goodsList = goodsDao.getGoodsListOpenForApp(pageInfo.filterModel);

        //金额取整
        amountRoundingModeService.setLsdsGoodsScale(goodsList);

        PageInfo<LsdsGoods> pageInfoList = new PageInfo<>(goodsList);
        Long total = pageInfoList.getTotal();
        returnModel.setTotal(total.intValue());
        returnModel.setModel(goodsList);
        return returnModel;
    }


    /**
     * 货源列表(我要承运)
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     * <AUTHOR>
     * 创建时间 2019/11/29 14:43
     */
    public ResultMode<LsdsGoods> getGoodsOfferListPage(PagingInfo<LsdsGoodsFilter> pageInfo) {
        ResultMode<LsdsGoods> returnModel = new ResultMode<LsdsGoods>();
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<LsdsGoods> goodsList = goodsDao.getGoodsListSupplierNew(pageInfo.filterModel);
        PageInfo<LsdsGoods> pageInfoList = new PageInfo<>(goodsList);
        pageInfoList.getList().forEach(e ->{
            if(StrUtil.equals(e.getEnquiryRange(), LsdsEnum.LsdsgoodsEnquiryRangeEnum.enquiryrange1.getCode())
                && ObjUtil.isNotNull(e.getExOfferLeftTime()) && e.getExOfferLeftTime() > 0
                && StrUtil.isBlank(e.getChildGoodsId())
                && ObjUtil.isNotNull(e.getOfferCurrentRounds()) && e.getOfferCurrentRounds() == 1
                && ObjUtil.isNotNull(e.getOfferStatus()) && e.getOfferStatus() == 1){
                e.setDownEnquiryButton(true);
            }
        });

        Long total = pageInfoList.getTotal();
        returnModel.setTotal(total.intValue());
        returnModel.setModel(goodsList);
        return returnModel;
    }

    /**
     * 下游询价时，平台3pl使用下游承运商的报价作为自己给发货人的报价
     * 创建者: cgb
     * 创建时间: 2019/12/17
     *
     * @param vo    货源VO
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @returns ResultMode<String>
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> lsdsGoodsOfferToSender(LsdsGoodsVo vo, String token) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            //更新货源状态为已成交
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
            LsdsGoods lsdsGoods = dao.getParentGoods(vo.getLsdsGoods().getGoodsId());
            if (null != lsdsGoods && null != lsdsGoods.getGoodsId()) {
                if (null != vo.getGoodsOfferList() && vo.getGoodsOfferList().size() > 0) {
                    //更新父货源对应下单的报价信息状态
                    for (LsdsGoodsOffer offer : vo.getGoodsOfferList()) {
                        String offerId = offer.getOfferId();
                        offer.setChildOfferId(offerId);
                        offer.setOfferId(String.valueOf(IdUtil.generateId()));
                        offer.setGoodsId(lsdsGoods.getGoodsId());
                        offer.setCreateDate(DateUtils.getDateTime());
                        offer.setStatus(1);
                        offer.setCompanyId(lsdsGoods.getCompanyId());
                        offer.setCompanyShortName(lsdsGoods.getCompanyShortName());
                        offer.setModifyDate(DateUtils.getDateTime());
                    }
                    offerDao.batchAdd(vo.getGoodsOfferList());
                }
            }
        } catch (Exception ex) {
            errMsg = "修改货源编号  货源信息表信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }

    /**
     * 根据货源编号  货源信息表主键精确查询货源编号  货源信息表
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     * @param goodsId
     * @return LsdsGoods
     */
    public ResultMode<LsdsGoods> lsdsGoodsGet(String goodsId) {
        String errMsg = "";
        ResultMode<LsdsGoods> resultmodel = new ResultMode<LsdsGoods>();
        try {
            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
            LsdsGoods lsdsGoods = dao.getModel(goodsId);
            //金额取整
            amountRoundingModeService.setLsdsGoodsScale(lsdsGoods);
            resultmodel.setTotal(1);
            resultmodel.getModel().add(lsdsGoods);
        } catch (Exception ex) {
            errMsg = "获取货源编号  货源信息表信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }

    public void sendMsg(String userId,String companyId,String templateId,Map<String, String> param ) {
        try {
            PlatformUmCompanyFilter companyFilter = new PlatformUmCompanyFilter();
            companyFilter.setCompanyId(companyId);
            ResultMode<PlatformUmCompany> companyResultMode = platformCommonInterClient.getCompanyAndAdminInfoByCompanyId(companyFilter);
            String parentUserId = companyResultMode.getModel().get(0).getExUserBaseId();
            Set<String> receiverList = new HashSet<>(2);
            Set<String> phoneList = new HashSet<>(2);
            if(!StringUtils.isEmpty(userId)){
                receiverList.add(userId);
                //设置用户手机号
                ResultMode<PlatformUmUserbaseinfo> userModel = platformCommonInterClient.getUserBaseInfoByUserBaseId(userId);
                if(null != userModel && null != userModel.getModel() && null != userModel.getModel().get(0) && !StringUtils.isEmpty(userModel.getModel().get(0).getTelephone())){
                    phoneList.add(userModel.getModel().get(0).getTelephone());
                }
            }
            if(!StringUtils.isEmpty(parentUserId) && !parentUserId.equals(userId)){
                receiverList.add(parentUserId);
                //设置用户手机号
                ResultMode<PlatformUmUserbaseinfo> userModel = platformCommonInterClient.getUserBaseInfoByUserBaseId(parentUserId);
                if(null != userModel && null != userModel.getModel() && null != userModel.getModel().get(0) && !StringUtils.isEmpty(userModel.getModel().get(0).getTelephone())){
                    phoneList.add(userModel.getModel().get(0).getTelephone());
                }
            }
            //系统消息埋点,发送审核提交成功消息
            MsgInfo msgInfo = new MsgInfo();
            // 发送人 系统
            msgInfo.setSender("1");
            // 接收人的手机号
            msgInfo.setPhoneList(phoneList);
            // 设置站内信接收人
            msgInfo.setReceiverList(receiverList);
            msgInfo.setTemplateId(templateId);
            //参数信息
            String paramStr = JSON.toJSONString(param);
            msgInfo.setTemplateParameter(paramStr);
            systemMsgInter.send(msgInfo);
        } catch (Exception e) {
            LogHelper.writeError("方法【sendMsg】发送消息出现异常：", e);
        }
    }

    /**
     * 查询工作台承运方货源统计信息
     * 创建者：cgb
     * @param lsdsGoods 货源实体
     * @return {@code ResultMode<Map<String, Object>>}
     */
    public ResultMode<Map<String, String>> lsdsGoodsSupplierView(LsdsGoods lsdsGoods) {
        ResultMode<Map<String, String>> returnModel = new ResultMode<Map<String, String>>();
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<Map<String,String>> mapList = goodsDao.lsdsGoodsSupplierView(lsdsGoods);
        returnModel.setModel(mapList);
        returnModel.setTotal(1);
        return returnModel;
    }

    /**
     * 查询工作台货源大厅货源列表信息
     * 创建者：cgb
     * 创建时间：2020/3/24
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoods>}
     */
    public ResultMode<LsdsGoods> getBenchGoodsListPage(PagingInfo<LsdsGoodsFilter> pageInfo) {
        ResultMode<LsdsGoods> returnModel = new ResultMode<LsdsGoods>();
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength,"lg.goods_id desc");
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<LsdsGoods> goodsList = goodsDao.getBenchGoodsListPage(pageInfo.filterModel);
        PageInfo<LsdsGoods> pageInfoList = new PageInfo<>(goodsList);
        Long total = pageInfoList.getTotal();
        returnModel.setTotal(total.intValue());
        returnModel.setModel(goodsList);
        return returnModel;
    }



    /**
     * 导出我要承运-报价列表信息
     * <AUTHOR>
     * 创建时间 2020/6/12
     * @param filter 查询条件
     */
    public List<LsdsGoods> getExportGoodsOfferList(LsdsGoodsFilter filter) {
        GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
        List<LsdsGoods> goodsList = goodsDao.getGoodsListSupplierNew(filter);

        //取配置数据
        Map<String, String> unitsMap = platformExchangeService.getPricingUnitsMap(PlatformEnum.EnableStatusEnum.ENABLE.getCode());

        for(LsdsGoods lsdsGoods: goodsList){
            String weightSum = (Objects.isNull(lsdsGoods.getWeightSum()) || (lsdsGoods.getWeightSum()).compareTo(BigDecimal.ZERO) == 0) ? "" : " | " + lsdsGoods.getWeightSum().stripTrailingZeros().toPlainString() +"吨";
            String volumeSum =  (Objects.isNull(lsdsGoods.getVolumeSum()) || (lsdsGoods.getVolumeSum()).compareTo(BigDecimal.ZERO) == 0) ? "" : " | " + lsdsGoods.getVolumeSum().stripTrailingZeros().toPlainString() +"立方";
            String totalGoods = (null == lsdsGoods.getTotalGoods() || lsdsGoods.getTotalGoods() == 0) ? "" : " | " + lsdsGoods.getTotalGoods() +"件";
            lsdsGoods.setExGoodsInfo(lsdsGoods.getGoodsName()+ " | " + lsdsGoods.getTotalQuantity().stripTrailingZeros().toPlainString()+unitsMap.get(lsdsGoods.getTotalQuantityUnits()) + weightSum+volumeSum + totalGoods);
            String exOfferStatus = lsdsGoods.getOfferStatus() == 1 ? "未报价(第"+ lsdsGoods.getOfferCurrentRounds() +"轮)" : lsdsGoods.getOfferStatus().toString();
            lsdsGoods.setExOfferStatus(exOfferStatus);
        }
        return goodsList;
    }

    /**
     * 获取平台公司id值封装
     * @return  String
     */
    public String  getPlatformCompanyIdStr() {
        List<PlatformCmOperationMainBody> listBody= platformCmOperationMainBodyInter.getAllTransaction().getModel();
        String platformCompanyId="";
        for (int i=0;i<listBody.size();i++){
            platformCompanyId=platformCompanyId+listBody.get(i).getCompanyId()+",";
        }
        platformCompanyId=platformCompanyId.substring(0,platformCompanyId.length()-1);
        return platformCompanyId;
    }

    /**
     * oms对货源子单下单成功后，同步承运商子订单信息到lsds
     * 创建者: cgb
     * 创建时间: 2020/6/23
     * @param record
     * @returns ResultMode<String>
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultMode<String> syncOmsChildOrders(String record) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            LogHelper.writeInfo("oms子订单下单成功，开始同步数据到lsds！");
            if (StringUtil.isNotBlank(record)){
                JSONArray array = JSONArray.parseArray(record);
                if(null != array && array.size() > 0){
                    List<LsdsGoodsOfferOrder> addOfferOrder = new ArrayList<>(16);
                    List<LsdsGoodsOfferOrder> updateOfferOrder = new ArrayList<>(16);
                    GoodsMapper goodsDao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
                    LsdsGoodsOfferOrderMapper offerOrderDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferOrderMapper.class);
                    LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
                    List<LsdsGoodsOffer> offerList = null;
                    String tempGoodsId = null;
                    LsdsGoods lsdsGoods = null;
                    String tempOrderId = null;
                    for(Object obj: array){
                        JSONObject jsonObject = JSONObject.parseObject(obj.toString());
                        LsdsGoodsOfferOrder lsdsGoodsOfferOrder = new LsdsGoodsOfferOrder();
                        lsdsGoodsOfferOrder.setOfferOrderId(String.valueOf(IdUtil.generateId()));
                        lsdsGoodsOfferOrder.setGoodsId(jsonObject.get("goodsId").toString());
                        lsdsGoodsOfferOrder.setCompanyId(jsonObject.get("carrierCompany").toString());
                        JSONArray jsonArray = (JSONArray)jsonObject.get("orderIds");
                        //获取货源信息
                        if(null == tempGoodsId || (!StringUtils.isEmpty(tempGoodsId) && !tempGoodsId.equals(jsonObject.get("goodsId").toString()))){
                            tempGoodsId = jsonObject.get("goodsId").toString();
                            lsdsGoods = goodsDao.getModel(tempGoodsId);
                        }
                        if(!StringUtils.isEmpty(lsdsGoods.getChildGoodsId())){
                            //获取使用下游询价成交的报价信息
                            LsdsGoodsOffer lsdsGoodsOffer = new LsdsGoodsOffer();
                            lsdsGoodsOffer.setGoodsId(jsonObject.get("goodsId").toString());
                            offerList = offerDao.getDealOfferList(lsdsGoodsOffer);
                            if(null != offerList && offerList.size() > 0){
                                tempOrderId = lsdsGoods.getParentOrderId();
                                String [] offerIdArr = new String[offerList.size()];
                                for(int i = 0;i<offerList.size();i++){
                                    offerIdArr[i] = offerList.get(i).getChildOfferId();
                                }
                              //  List<LsdsGoodsOffer> childOfferList = offerDao.getOfferListById(offerIdArr);

                                String  platformCompanyIdStr=getPlatformCompanyIdStr();
                                //List<LsdsGoodsOffer> offerList = offerDao.getOfferListById(offerIdArr);
                                LsdsGoodsOffer lsOffer= new LsdsGoodsOffer();
                                lsOffer.setOfferIdArr(offerIdArr);
                                lsOffer.setPlatformCompanyId(platformCompanyIdStr);
                                List<LsdsGoodsOffer>  childOfferList = offerDao.getOfferListById(lsdsGoodsOffer);

                                List<LsdsGoodsOffer> currentOfferList = childOfferList.stream().filter(p -> (p.getCompanyId()).equals(jsonObject.get("carrierCompany").toString())).collect(Collectors.toList());
                                if(null != currentOfferList && currentOfferList.size() > 0){
                                    if(currentOfferList.get(0).getStatus() == 2){
                                        lsdsGoodsOfferOrder.setGoodsId(currentOfferList.get(0).getGoodsId());
                                    }
                                }
                            }
                        }

                        StringBuffer ordersId = new StringBuffer();
                        for (int i = 0; i < jsonArray.size(); i++) {
                            if(i == 0){
                                ordersId.append(jsonArray.get(i));
                            }else{
                                ordersId.append("/" + jsonArray.get(i));
                            }
                        }
                        lsdsGoodsOfferOrder.setOrdersId(ordersId.toString());
                        if(!StringUtils.isEmpty(tempOrderId)){
                            tempOrderId = tempOrderId + "/" + ordersId;
                        }

                        //查询子订单记录是否已存在
                        LsdsGoodsOfferOrder existOfferOrder = offerOrderDao.getOfferOrder(lsdsGoodsOfferOrder);
                        if(null != existOfferOrder){
                            lsdsGoodsOfferOrder.setOfferOrderId(existOfferOrder.getOfferOrderId());
                            updateOfferOrder.add(lsdsGoodsOfferOrder);
                        }else {
                            addOfferOrder.add(lsdsGoodsOfferOrder);
                        }
                    }

                    //下游询价业务实际承运商对平台3pl报价最终被成交的，同步一条订单信息给平台3pl
                    if(!StringUtils.isEmpty(lsdsGoods.getChildGoodsId()) && null != offerList && offerList.size() > 0){
                        if(!StringUtils.isEmpty(tempGoodsId)){
                            LsdsGoodsOfferOrder lsdsGoodsOfferAdminOrder = new LsdsGoodsOfferOrder();
                            lsdsGoodsOfferAdminOrder.setGoodsId(tempGoodsId);
                            lsdsGoodsOfferAdminOrder.setCompanyId("2");
                            lsdsGoodsOfferAdminOrder.setOrdersId(tempOrderId);
                            LsdsGoodsOfferOrder existOfferAdminOrder = offerOrderDao.getOfferOrder(lsdsGoodsOfferAdminOrder);
                            if(null != existOfferAdminOrder){
                                lsdsGoodsOfferAdminOrder.setOfferOrderId(existOfferAdminOrder.getOfferOrderId());
                                updateOfferOrder.add(lsdsGoodsOfferAdminOrder);
                            }else {
                                lsdsGoodsOfferAdminOrder.setOfferOrderId(String.valueOf(IdUtil.generateId()));
                                addOfferOrder.add(lsdsGoodsOfferAdminOrder);
                            }
                        }
                    }

                    if(null != addOfferOrder && addOfferOrder.size() > 0){
                        offerOrderDao.addBatch(addOfferOrder);
                    }
                    if(null != updateOfferOrder && updateOfferOrder.size() > 0){
                        offerOrderDao.updateBatch(updateOfferOrder);
                    }
                }
                LogHelper.writeInfo("oms子订单数据同步成功！");
            }

        } catch (Exception ex) {
            errMsg = "同步oms子订单信息失败！";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }
    //#endregion

    /**
     * 根据报价id查询成交报价信息
     */
    public LsdsGoodsOffer getOfferByOfferId(String offerId) {
        LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);

        return offerDao.getModel(offerId);
    }

    /**
     * 更新报价单状态
     * @param updateGoodsOfferStatus
     */
    public void updateOfferStatus(UpdateGoodsOfferStatusEntity updateGoodsOfferStatus){
        if(StrUtil.isBlank(updateGoodsOfferStatus.getGoodsId())){
            return;
        }
        lsdsGoodsOfferRepository.updateOfferStatusByGoodsId(updateGoodsOfferStatus);
    }

    //填充中标文件
    private void buildWinBids(String goodsId, LsdsGoodsVo vo) {
        LsdsGoodsOfferMapper offerDao = SpringContextUtil.getBeanByClass(LsdsGoodsOfferMapper.class);
        List<LsdsGoodsOffer> tradedOffer = offerDao.selectGoodsOffer(goodsId, OfferStatusEnum.TRADED.getCode());
        if(CollUtil.isEmpty(tradedOffer)) {
            return;
        }
        for (LsdsGoodsOffer offer : tradedOffer) {
            Integer showWinBids = offer.getShowWinBids();
            if(ShowWinBidsEnum.SHOW.getShow() != showWinBids){
                return;
            }
            List<BidsFilesFilter> winBidsFilesFilter = lsdsGoodsBidFileService.getBidsFilesFilter(goodsId, LsdsAttachmentTypeEnum.WIN_BIDS.getType());
            if(CollUtil.isNotEmpty(winBidsFilesFilter)){
                vo.getLsdsGoods().setWinBidsFiles(winBidsFilesFilter);
            }
        }
    }
}
