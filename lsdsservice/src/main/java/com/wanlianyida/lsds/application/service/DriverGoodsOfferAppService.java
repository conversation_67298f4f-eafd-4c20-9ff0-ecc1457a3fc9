package com.wanlianyida.lsds.application.service;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.UtilityClass;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.DriverOffer;
import com.isoftstone.hig.lsds.api.filter.DriverOfferFilter;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.wanlianyida.lsds.application.model.command.DriverGoodsQuoteCommand;
import com.wanlianyida.lsds.domain.model.entity.GoodsSourceEntity;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.lsds.infrastructure.repository.mapper.DriverGoodsMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.DriverOfferMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.GoodsSourceMapper;
import com.wanlianyida.lsds.infrastructure.util.AopSpringFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;

import javax.annotation.Resource;

/**
 * 司机货源报价
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DriverGoodsOfferAppService {

    @Resource
    private DriverGoodsMapper driverGoodsMapper;

    @Resource
    private DriverOfferMapper lsdsDriverOfferMapper;

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private DriverGoodsValidatorAppService driverGoodsValidatorBusiness;

    @Resource
    private DriverGoodsAsyncAppService driverGoodsAsyncBusiness;

    @Resource
    private DriverOfferAppService driverOfferService;

    @Resource
    private GoodsSourceMapper goodsSourceMapper;


    /**
     * 司机货源报价（新方法）
     */
    public ResultMode goodsQuote(DriverOfferFilter offerFilter) {
        DriverGoods driverGoods = driverGoodsMapper.getById(offerFilter.getGoodsId());
        TokenInfo tokenInfo = platformExchangeService.getDriverTokenInfo();

        DriverGoodsQuoteCommand driverGoodsQuoteVo = new DriverGoodsQuoteCommand();
        driverGoodsQuoteVo.setOfferFilter(offerFilter);
        driverGoodsQuoteVo.setDriverGoods(driverGoods);
        driverGoodsQuoteVo.setTokenInfo(tokenInfo);

        //校验
        ResultMode resultMode = driverGoodsValidatorBusiness.goodsQuoteValidator(driverGoodsQuoteVo);
        if (!resultMode.isSucceed()) {
            return resultMode;
        }

        //报价单封装
        DriverOffer driverOffer = assembleQuoteDriverOffer(driverGoodsQuoteVo);

        //入库操作
        AopSpringFactory.getAopProxy(this).goodsQuoteBatchCollections(driverGoodsQuoteVo);

        //异步操作
        driverGoodsAsyncBusiness.handleAsyncMessageOfGoodsQuote(driverGoodsQuoteVo);

        return ResultMode.success(driverOffer);
    }

    /**
     * 司机货源报价-报价单封装
     */
    private DriverOffer assembleQuoteDriverOffer(DriverGoodsQuoteCommand driverGoodsQuoteVo) {
        DriverOffer driverOffer = driverGoodsQuoteVo.getOffer();
        DriverGoods driverGoods = driverGoodsQuoteVo.getDriverGoods();
        DriverOfferFilter offerFilter = driverGoodsQuoteVo.getOfferFilter();
        TokenInfo tokenInfo = driverGoodsQuoteVo.getTokenInfo();
        DateTime now = DateUtil.date();

        //新增报价单
        if (ObjUtil.isNull(driverOffer)) {
            driverOffer = new DriverOffer();
            driverGoodsQuoteVo.setOfferAdd(driverOffer);

            driverOffer.setId(UtilityClass.uuid());
            driverOffer.setGoodsId(driverGoods.getGoodsId());
            driverOffer.setCreateBy(tokenInfo.getUserBaseId());
            driverOffer.setCreateName(tokenInfo.getUsername());
            driverOffer.setCreateDate(now);
            driverOffer.setDriverGrade(offerFilter.getDriverGrade());

            //查询司机运营表示和是否实名
            driverOffer.setOperateTag("2");
            driverOffer.setIsRealAuthentication("1");

        } else {
            //更新报价单
            driverGoodsQuoteVo.setOfferUpd(driverOffer);
        }

        //设置共有参数
        driverGoods.setEnquiryTypeBasePrice(offerFilter.getReceivingOrdersUnitPrice());
        driverGoods = driverOfferService.calculatePrice(driverGoods, true);
        driverOffer.setEnquiryTypeBasePrice(driverGoods.getFloatEnquiryTypeBasePrice());
        driverOffer.setEnquiryTypeBaseOpenTicket(driverGoods.getFloatEnquiryTypeBaseOpenTicket());
        driverOffer.setFloatEnquiryTypeBaseOpenTicket(driverGoods.getEnquiryTypeBaseOpenTicket());
        driverOffer.setFloatEnquiryTypeBasePrice(driverGoods.getEnquiryTypeBasePrice());
        driverOffer.setEnquiryTypeBaseTaxRate(driverGoods.getEnquiryTypeBaseTaxRate());
        driverOffer.setTotalQuantityUnits(driverGoods.getTotalQuantityUnits());
        driverOffer.setReceivingOrdersQuantity(offerFilter.getReceivingOrdersQuantity());
        driverOffer.setReceivingOrdersUnitPrice(offerFilter.getReceivingOrdersUnitPrice());
        driverOffer.setTractorPlateCarId(offerFilter.getTractorPlateCarId());
        driverOffer.setTractorPlateNumber(offerFilter.getTractorPlateNumber());
        driverOffer.setTractorPlateColor(offerFilter.getTractorPlateColor());
        driverOffer.setTrailerPlateCarId(offerFilter.getTrailerPlateCarId());
        driverOffer.setTrailerPlateNumber(offerFilter.getTrailerPlateNumber());
        driverOffer.setTrailerPlateCarColor(offerFilter.getTrailerPlateColor());
        driverOffer.setReceivingOrdersWeight(offerFilter.getReceivingOrdersWeight());
        driverOffer.setReceiptAccount(offerFilter.getReceiptAccount());
        driverOffer.setReceiptAccountName(offerFilter.getReceiptAccountName());
        driverOffer.setReceiptBank(offerFilter.getReceiptBank());
        driverOffer.setEstimatedFreight(driverOffer.getReceivingOrdersQuantity().multiply(driverOffer.getFloatEnquiryTypeBasePrice()).setScale(2, driverGoods.getRoundingMode()));
        driverOffer.setOfferStatus(Constants.OFFER_STATUS_WAIT_CONFIRM);
        driverOffer.setQuotationTimes("1");
        driverOffer.setTelephone(offerFilter.getTelephone());
        driverOffer.setIdCard(offerFilter.getReceiptorIdCardNo());

        //NOTES 车队长代报价 保存承运司机和实际报价人的信息
        driverOffer.setDriverId(offerFilter.getDriverId());
        driverOffer.setDriverName(offerFilter.getDriverName());
        //实际承运人身份证号
        driverOffer.setIdCardNo(offerFilter.getIdCardNo());
        //实际承运人手机号
        driverOffer.setContactPhoneNumber(offerFilter.getContactPhoneNumber());
        driverOffer.setBidderDriverId(tokenInfo.getDriverId());
        // 设置货源码绑定关系Id
        Opt.ofBlankAble(offerFilter.getQrCodeRelationId()).ifPresent(driverOffer::setQrCodeRelationId);
        driverOffer.setModifyBy(tokenInfo.getUserBaseId());
        driverOffer.setModifyName(tokenInfo.getUsername());
        driverOffer.setModifyDate(now);

        BigDecimal currentRoundOfferMinPrice = driverOffer.getFloatEnquiryTypeBaseOpenTicket();
        List<DriverOffer> driverOfferAllList = lsdsDriverOfferMapper.queryByGoodsId(driverGoods.getGoodsId());
        if (IterUtil.isNotEmpty(driverOfferAllList)) {
            BigDecimal minPrice = driverOfferAllList.stream().map(DriverOffer::getFloatEnquiryTypeBaseOpenTicket).min(Comparator.naturalOrder()).orElse(null);
            if (minPrice != null && minPrice.compareTo(currentRoundOfferMinPrice) < 0) {
                currentRoundOfferMinPrice = minPrice;
            }
        }
        GoodsSourceEntity goodsSourceUpd = new GoodsSourceEntity();
        goodsSourceUpd.setGoodsId(driverGoods.getGoodsId());
        goodsSourceUpd.setCurrentRoundOfferMinPrice(currentRoundOfferMinPrice);
        driverGoodsQuoteVo.setGoodsSource(goodsSourceUpd);

        return driverOffer;
    }

    /**
     * 司机货源报价-报价单入库
     */
    @Transactional(rollbackFor = {Exception.class})
    public void goodsQuoteBatchCollections(DriverGoodsQuoteCommand driverGoodsQuoteVo) {
        if (ObjUtil.isNotNull(driverGoodsQuoteVo.getOfferAdd())) {
            lsdsDriverOfferMapper.save(driverGoodsQuoteVo.getOfferAdd());
        }
        if (ObjUtil.isNotNull(driverGoodsQuoteVo.getOfferUpd())) {
            lsdsDriverOfferMapper.modifyById(driverGoodsQuoteVo.getOfferUpd());
        }
        GoodsSourceEntity goodsSource = driverGoodsQuoteVo.getGoodsSource();
        if (ObjUtil.isNotNull(goodsSource)) {
            goodsSourceMapper.updateCurrentRoundOffer(goodsSource.getGoodsId(), goodsSource.getCurrentRoundOfferMinPrice());
        }
    }

}
