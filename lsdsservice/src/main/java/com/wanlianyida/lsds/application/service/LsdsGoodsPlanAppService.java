package com.wanlianyida.lsds.application.service;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.LogHelper;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsPlan;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsPlanFilter;
import com.wanlianyida.lsds.domain.service.LsdsGoodsPlanDomainService;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsGoodsPlanMapper;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 服务公共类主要实现业务规则，处理业务逻辑 可以再进行创建多个，这主要为了方便一起生成
 *
 * <AUTHOR>
 */
@Service
public class LsdsGoodsPlanAppService {
    @Resource
    private LsdsGoodsPlanDomainService lsdsGoodsPlanDomainService;

    /**
     * 根据方案ID主键精确查询方案ID
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param planId 为0时忽略
     * @param token  预留要传递的token，服务端进行判断权限功能
     * @return LsdsGoodsPlan
     */
    public LsdsGoodsPlan lsdsGoodsPlanGet(String planId, String token) {
        String errMsg = "";
        LsdsGoodsPlan model = null;
        try {
            LsdsGoodsPlanMapper dao = SpringContextUtil.getBeanByClass(LsdsGoodsPlanMapper.class);
            model = dao.getModel(planId);
        } catch (Exception ex) {
            errMsg = "获取方案ID信息异常";
            LogHelper.writeError(errMsg, ex);
            model = null;
        }
        return model;
    }


    /**
     * 新增方案ID信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param model 方案ID信息
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【增加成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【增加成功】编码,ResultMode.errMsg为相应【增加成功】描述；
     * 否则ResultMode.succeed=false【增加失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【增加失败】编码,ResultMode.errMsg为相应【增加失败】描述。
     */
    public ResultMode<String> lsdsGoodsPlanAdd(LsdsGoodsPlan model, String token) {
        // planId = "";
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            String planId = String.valueOf(IdUtil.generateId());
            model.setPlanId(planId);
            LsdsGoodsPlanMapper dao = SpringContextUtil.getBeanByClass(LsdsGoodsPlanMapper.class);
            resultmodel.setSucceed(dao.add(model));
            if (resultmodel.getSucceed()) {
                resultmodel.getModel().add(planId);
            }
        } catch (Exception ex) {
            errMsg = "新增方案ID信息异常";
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
            LogHelper.writeError(errMsg, ex);
        }
        return resultmodel;
    }

    /**
     * 修改方案ID信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param model 方案ID信息
     * @param token 预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【修改成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【修改成功】编码,ResultMode.errMsg为相应【修改成功】描述；
     * 否则ResultMode.succeed=false【修改失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【修改失败】编码,ResultMode.errMsg为相应【修改失败】描述。
     */
    public ResultMode<String> lsdsGoodsPlanUpdate(LsdsGoodsPlan model, String token) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            LsdsGoodsPlanMapper dao = SpringContextUtil.getBeanByClass(LsdsGoodsPlanMapper.class);
            resultmodel.setSucceed(dao.modify(model));
            if (!resultmodel.getSucceed()) {
                errMsg = "更新失败，请检验提交的数据是否正常-planId：" + model.getPlanId();
                resultmodel.setErrMsg(errMsg);
            }
        } catch (Exception ex) {
            errMsg = "修改方案ID信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }

    /**
     * 根据Key删除方案ID信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param planId 方案ID信息
     * @param token  预留要传递的token，服务端进行判断权限功能
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【删除成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【删除成功】编码,ResultMode.errMsg为相应【删除成功】描述；
     * 否则ResultMode.succeed=false【删除失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【删除失败】编码,ResultMode.errMsg为相应【删除失败】描述。
     */
    public ResultMode<String> lsdsGoodsPlanDel(String planId, String token) {
        String errMsg = "";
        ResultMode<String> resultmodel = new ResultMode<String>();
        try {
            LsdsGoodsPlanMapper dao = SpringContextUtil.getBeanByClass(LsdsGoodsPlanMapper.class);
            resultmodel.setSucceed(dao.remove(planId));
            if (!resultmodel.getSucceed()) {
                errMsg = "内部执行删除异常，删除失败";
                resultmodel.setErrMsg(errMsg);
            }
        } catch (Exception ex) {
            errMsg = "根据planId删除方案ID信息异常";
            LogHelper.writeError(errMsg, ex);
            resultmodel.setErrMsg(errMsg);
            resultmodel.setSucceed(false);
        }
        return resultmodel;
    }

    /**
     * 分页获取方案ID信息
     * 创建者: 李健华
     * 创建时间: 2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @param token    预留要传递的token，服务端进行判断权限功能
     * @return {@code  ResultMode<LsdsGoodsPlan>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为方案ID实体类LsdsGoodsPlan列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    public ResultMode<LsdsGoodsPlan> lsdsGoodsPlanPaging(PagingInfo<LsdsGoodsPlanFilter> pageInfo, String token) {

        ResultMode<LsdsGoodsPlan> returnmodel = new ResultMode<LsdsGoodsPlan>();
        try {

            returnmodel = lsdsGoodsPlanDomainService.pagingLsdsGoodsPlan(pageInfo, token);
            return returnmodel;

        } catch (Exception ex) {
            String errMsg = "分页获取方案ID信息异常:" + ex.getMessage();
            returnmodel.setErrMsg(errMsg);
            LogHelper.writeError(errMsg, ex);
        }
        return returnmodel;
    }

    //#endregion
}
