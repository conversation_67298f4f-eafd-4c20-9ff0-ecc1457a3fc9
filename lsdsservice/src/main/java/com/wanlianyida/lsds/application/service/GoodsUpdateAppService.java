package com.wanlianyida.lsds.application.service;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.lsds.domain.model.bo.GoodsCreateOrderStatusChangeBO;
import com.wanlianyida.lsds.domain.service.CreateOrderStatusDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 货源状态变更
 */
@Slf4j
@Service
public class GoodsUpdateAppService {

    @Resource
    private CreateOrderStatusDomainService createOrderStatusDomainService;

    /**
     * 更新货源、报价单状态
     * @param createOrderStatusChangeBO
     * @return
     */
    public ResultMode<String> updateAfterCreateOrder(GoodsCreateOrderStatusChangeBO createOrderStatusChangeBO){
        return createOrderStatusDomainService.handleBiz(createOrderStatusChangeBO);
    }
}
