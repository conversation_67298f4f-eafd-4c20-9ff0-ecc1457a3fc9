package com.wanlianyida.lsds.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.isoftstone.hig.common.utils.inter.DistributedLocker;
import com.isoftstone.hig.lsds.api.dto.LsdsMatchmakingConfigDTO;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.LsdsAppGoods;
import com.isoftstone.hig.lsds.api.enums.BusiStatusEnum;
import com.isoftstone.hig.lsds.api.enums.BusiTypeEnum;
import com.isoftstone.hig.lsds.api.enums.GoodsKindEnum;
import com.isoftstone.hig.lsds.api.enums.SourceTypeEnum;
import com.isoftstone.hig.lsds.api.filter.DriverGoodsFilter;
import com.isoftstone.hig.lsds.api.query.LsdsMatchmakingConfigQuery;
import com.isoftstone.hig.platform.api.entity.PlatformCmDictionary;
import com.isoftstone.hig.platform.api.entity.PlatformCmOperationMainBody;
import com.isoftstone.hig.platform.api.entity.PlatformUmCompany;
import com.isoftstone.hig.tms.api.entity.TmsOrder;
import com.isoftstone.hig.tms.api.entity.TmsOrderAddress;
import com.isoftstone.hig.tms.api.enums.AdvancePaymentFlagEnum;
import com.isoftstone.hig.tms.api.mvcvo.TmsOrderAndAddressVO;
import com.wanlianyida.lsds.domain.service.LsdsMatchmakingDomainService;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.TmsExchangeService;
import com.wanlianyida.lsds.infrastructure.repository.mapper.DriverGoodsMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsAppGoodsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Service
public class LsdsAppGoodsAppService {

    private static String handleMatchmakingGoodsRedisKey = "handleMatchmakingGoodsRedisKey:";

    @Resource
    private DriverGoodsMapper driverGoodsMapper;

    @Resource
    private LsdsAppGoodsMapper lsdsAppGoodsMapper;

    @Resource
    private TmsExchangeService tmsExchangeService;

    @Resource
    private DistributedLocker distributedLocker;

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private LsdsMatchmakingDomainService lsdsMatchmakingService;



    public void handleMatchmakingGoods(String busCode) {
        boolean lockFlag = distributedLocker.tryLock(handleMatchmakingGoodsRedisKey+busCode, TimeUnit.SECONDS,0,180);
        if(!lockFlag){
            return;
        }
        //先查询
        LsdsAppGoods lsdsAppGoods = lsdsAppGoodsMapper.selectInfo(busCode);
        if(ObjUtil.isNotNull(lsdsAppGoods)){
            //更新状态
            log.info("handleMatchmakingGoods#更新撮合货源状态：{}",busCode);
            this.updateStatus(busCode);
            return;
        }
        LsdsAppGoods appGoods = null;
        if(StrUtil.startWith(busCode,"DFQ")){
            //查询司机货源详情
            appGoods = this.buildGoodsInfo(busCode);
        }else {
            //查询订单详情
            appGoods = this.buildOrderInfo(busCode);
        }
        if(ObjUtil.isNotNull(appGoods)){
            log.info("handleMatchmakingGoods#新增货源：{}", JSONUtil.toJsonStr(appGoods));
            lsdsAppGoodsMapper.insertAppGoods(appGoods);
        }
    }

    /**
     * 更新状态
     * @param busCode
     */
    private void updateStatus(String busCode) {
        String status = BusiStatusEnum.YX.getStatus();
        Date expireDate = null;
        if(StrUtil.startWith(busCode,"DFQ")){
            DriverGoods driverGoods = driverGoodsMapper.getByGoodsId(busCode);
            if(ObjUtil.isNull(driverGoods)){
                log.info("updateStatus#货源号:{}没查询到货源信息",busCode);
                return;
            }
            status = StrUtil.containsAny(driverGoods.getDealStatus(),"30","60") ?
                BusiStatusEnum.YX.getStatus() : BusiStatusEnum.WX.getStatus();
        }else {
            LsdsMatchmakingConfigQuery query = new LsdsMatchmakingConfigQuery();
            query.setBusId(busCode);
            LsdsMatchmakingConfigDTO config = lsdsMatchmakingService.queryConfig(query);
            if(ObjUtil.isNull(config)){
                log.info("非撮合订单不处理：{}",busCode);
                return;
            }
            TmsOrderAndAddressVO tmsOrderAndAddressVO = tmsExchangeService.getTmsOrderAndAddress(busCode);
            if(ObjUtil.isNull(tmsOrderAndAddressVO) || ObjUtil.isNull(tmsOrderAndAddressVO.getTmsOrder())){
                return;
            }
            TmsOrder tmsOrder = tmsOrderAndAddressVO.getTmsOrder();
            Integer orderExpireFlag = tmsOrder.getOrderExpireFlag();
            if(Integer.valueOf(0).equals(orderExpireFlag)){
                BigDecimal remainingQuantity = tmsOrder.getRemainingQuantity();
                Opt.ofNullable(remainingQuantity).orElse(new BigDecimal(0));
                if(remainingQuantity.compareTo(BigDecimal.ZERO) <= 0){
                    status =  BusiStatusEnum.WX.getStatus();
                }
            }
            if(StrUtil.equals(tmsOrder.getOrderStatus(),"800") && StrUtil.equals(tmsOrder.getExecutingStatus(),"6")){
                status =  BusiStatusEnum.YX.getStatus();
            }
            expireDate = config.getExpireDate();
            if(expireDate.before(new Date())){
                status =  BusiStatusEnum.WX.getStatus();
            }

        }
        log.info("updateStatus更新状态：业务单号：{}，更新状态为：{}",busCode,status,expireDate);
        lsdsAppGoodsMapper.updateAppGoodsStatus(busCode,status,expireDate);
    }


    /**
     * 司机货源
     * @param goodsId
     */
    private LsdsAppGoods buildGoodsInfo(String goodsId) {
        DriverGoods driverGoods = driverGoodsMapper.getByGoodsId(goodsId);
        if(ObjUtil.isNull(driverGoods)){
            log.info("buildGoodsInfo:货源号:{}没查询到司机货源",goodsId);
            return null;
        }
        if(ObjUtil.equals(driverGoods.getGoodsKind(),10) && !StrUtil.equals(driverGoods.getEnquiryRange(),"30")){
            return null;
        }
        LsdsAppGoods appGoods = new LsdsAppGoods();
        appGoods.setBusiCode(goodsId);

        Integer goodsKind = driverGoods.getGoodsKind();
        appGoods.setBusiType(GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(goodsKind) ? BusiTypeEnum.MATCHMAKING_GOODS.getType() : BusiTypeEnum.DRIVER_GOODS.getType());
        String status = StrUtil.containsAny(driverGoods.getDealStatus(),"30","60") ?
            BusiStatusEnum.YX.getStatus() : BusiStatusEnum.WX.getStatus();
        appGoods.setBusiStatus(status);
        appGoods.setSendAddrProvince(driverGoods.getSendAddrProvince());
        appGoods.setSendAddrProvinceName(driverGoods.getSendAddrProvinceName());
        appGoods.setSendAddrCity(driverGoods.getSendAddrCity());
        appGoods.setSendAddrCityName(driverGoods.getSendAddrCityName());
        appGoods.setSendAddrArea(driverGoods.getSendAddrArea());
        appGoods.setSendAddrAreaName(driverGoods.getSendAddrAreaName());

        appGoods.setReceiveAddrProvince(driverGoods.getReceiveAddrProvince());
        appGoods.setReceiveAddrProvinceName(driverGoods.getReceiveAddrProvinceName());
        appGoods.setReceiveAddrCity(driverGoods.getReceiveAddrCity());
        appGoods.setReceiveAddrCityName(driverGoods.getReceiveAddrCityName());
        appGoods.setReceiveAddrArea(driverGoods.getReceiveAddrArea());
        appGoods.setReceiveAddrAreaName(driverGoods.getReceiveAddrAreaName());

        appGoods.setGoodsType(driverGoods.getGoodsType());
        appGoods.setCreatorId(driverGoods.getCreateBy());
        appGoods.setCreatedDate(driverGoods.getCreateDate());
        appGoods.setUpdaterId(driverGoods.getCreateBy());
        appGoods.setUpdatedDate(driverGoods.getCreateDate());
//        appGoods.setSourceType(convertSourceTypeFromDriverGoods(driverGoods)+"");
        appGoods.setVersionCode("0");
        appGoods.setReleaseDate(driverGoods.getReleaseDate());
        appGoods.setAssignCarType(driverGoods.getAssignCarType());
        appGoods.setStartSiteCityName(driverGoods.getStartSiteCityName());
        appGoods.setEndSiteCityName(driverGoods.getEndSiteCityName());
        appGoods.setGoodsName(driverGoods.getGoodsName());
        appGoods.setValidityDate(driverGoods.getValidityDate());
        appGoods.setFreightType(driverGoods.getFreightType());
        return appGoods;
    }

    /**
     * 查询订单信息
     * @param orderId
     */
    private LsdsAppGoods buildOrderInfo(String orderId) {
        TmsOrderAndAddressVO tmsOrderAndAddressVO = tmsExchangeService.getTmsOrderAndAddress(orderId);
        if(ObjUtil.isNull(tmsOrderAndAddressVO) || ObjUtil.isNull(tmsOrderAndAddressVO.getTmsOrder()) || ObjUtil.isNull(tmsOrderAndAddressVO.getTmsOrderAddress())){
            log.info("buildOrderInfo:订单号:{}没查询到司机货源",orderId);
            return null;
        }
        if(!StrUtil.equals(tmsOrderAndAddressVO.getTmsOrder().getTransportationType(),"110")){
            return null;
        }
        //只展示撮合订单
        LsdsMatchmakingConfigQuery query = new LsdsMatchmakingConfigQuery();
        query.setBusId(orderId);
        LsdsMatchmakingConfigDTO config = lsdsMatchmakingService.queryConfig(query);
        if(ObjUtil.isNull(config)){
            log.info("buildOrderInfo 订单号:{}没有查询到撮合订单",orderId);
            return null;
        }
        LsdsAppGoods appGoods = new LsdsAppGoods();
        TmsOrder tmsOrder = tmsOrderAndAddressVO.getTmsOrder();
        TmsOrderAddress tmsOrderAddress = tmsOrderAndAddressVO.getTmsOrderAddress();
        appGoods.setBusiCode(orderId);
        appGoods.setBusiType(BusiTypeEnum.MATCHMAKING_ORDER.getType());
        appGoods.setBusiStatus(BusiStatusEnum.YX.getStatus());
        if(ObjUtil.isNotNull(tmsOrderAddress)){
            appGoods.setSendAddrProvince(tmsOrderAddress.getSendAddrProvince());
            appGoods.setSendAddrProvinceName(tmsOrderAddress.getSendAddrProvinceName());
            appGoods.setSendAddrCity(tmsOrderAddress.getSendAddrCity());
            appGoods.setSendAddrCityName(tmsOrderAddress.getSendAddrCityName());
            appGoods.setSendAddrArea(tmsOrderAddress.getSendAddrArea());
            appGoods.setSendAddrAreaName(tmsOrderAddress.getSendAddrAreaName());
            appGoods.setStartSiteCityName(tmsOrderAddress.getSendAddrShorthand());

            appGoods.setEndSiteCityName(tmsOrderAddress.getReceiveAddrShorthand());
            appGoods.setReceiveAddrProvince(tmsOrderAddress.getReceiveAddrProvince());
            appGoods.setReceiveAddrProvinceName(tmsOrderAddress.getReceiveAddrProvinceName());
            appGoods.setReceiveAddrCity(tmsOrderAddress.getReceiveAddrCity());
            appGoods.setReceiveAddrCityName(tmsOrderAddress.getReceiveAddrCityName());
            appGoods.setReceiveAddrArea(tmsOrderAddress.getReceiveAddrArea());
            appGoods.setReceiveAddrAreaName(tmsOrderAddress.getReceiveAddrAreaName());
        }

        appGoods.setGoodsType(tmsOrder.getGoodsType());
        appGoods.setCreatorId(tmsOrder.getCreateBy());
        appGoods.setCreatedDate(tmsOrder.getCreateDate());
        appGoods.setUpdaterId(tmsOrder.getCreateBy());
        appGoods.setUpdatedDate(tmsOrder.getCreateDate());
//        appGoods.setSourceType( SourceTypeEnum.PTCH.getSourceType()+"");
        appGoods.setVersionCode("0");
        appGoods.setReleaseDate(tmsOrder.getReleaseDate());
        appGoods.setAssignCarType(tmsOrder.getAssignCarType());
        appGoods.setGoodsName(tmsOrder.getGoodsName());
        appGoods.setFreightType("1");
        //查询撮合信息
        appGoods.setValidityDate(config.getExpireDate());
        return appGoods;
    }


    /**
     * 司机货源类型转化
     * @param driverGoods
     * @return
     */
    public Integer convertSourceTypeFromDriverGoods(DriverGoods driverGoods,List<String> sfIds){
        Integer sourceType = SourceTypeEnum.QT.getSourceType();

        if(StrUtil.equals(driverGoods.getFreightType(),"2")){
            //网络货运主体
            String networkMainBodyId = driverGoods.getNetworkMainBodyId();
            if(sfIds.contains(networkMainBodyId)){
                sourceType = SourceTypeEnum.WH_SF.getSourceType();
                return sourceType;
            }
            PlatformCmOperationMainBody platformCmOperationMainBody = platformExchangeService.getMainBody(networkMainBodyId);
            if(!ObjUtil.isNull(platformCmOperationMainBody)){
                String type = platformCmOperationMainBody.getType();
                if(StrUtil.equalsAny(type,"2","3")){
                    sourceType = SourceTypeEnum.PTCY.getSourceType();
                }
                if(StrUtil.equals(type,"1")){
                    sourceType = SourceTypeEnum.DFCY.getSourceType();
                }
            }
        }else {
            //发货企业
            String companyName = driverGoods.getCompanyName();
            sourceType = SourceTypeEnum.CT.getSourceType();
            if(StrUtil.contains(companyName,"万联")){
                sourceType = SourceTypeEnum.WLYD_CT.getSourceType();
            }
        }
        return sourceType;
    }


    /**
     * 列表查询
     * @param pageInfo
     * @return
     */

    public PageInfo<DriverGoods> list(PagingInfo<DriverGoodsFilter> pageInfo) {
        DriverGoodsFilter filter = pageInfo.getFilterModel();
        List<LsdsAppGoods> list = lsdsAppGoodsMapper.list(filter);
        if(CollUtil.isEmpty(list)){
            return new PageInfo<>();
        }
        String contractLimitDicId = "20250307000000000001";
        List<PlatformCmDictionary> platformDicList = platformExchangeService.getByParentDic(contractLimitDicId);
        List<String> collect = platformDicList.stream().map(p -> p.getEnumCode()).collect(Collectors.toList());

        PageInfo<LsdsAppGoods> pageData = new PageInfo<>(list);
        PageInfo<DriverGoods> returnPageData = new PageInfo();
        List<DriverGoods> driverGoods = BeanUtil.copyToList(pageData.getList(), DriverGoods.class);
        if(CollUtil.isNotEmpty(driverGoods)){
            driverGoods.forEach(d ->{
                //查询剩余数量
                this.appendBaseInfo(d,collect);
            });
        }
        List<DriverGoods> sortDriverGoods = driverGoods.stream().sorted((a, b) -> {
            if(a.getSourceType().compareTo(b.getSourceType()) >= 0){
                return 1;
            }else {
                return -1;
            }
        }).collect(Collectors.toList());
        returnPageData.setTotal(pageData.getTotal());
        returnPageData.setList(sortDriverGoods);
        return returnPageData;
    }

    /**
     * 填充其它信息
     * @param driverGoods
     * @param collect
     */
    private void appendBaseInfo(DriverGoods driverGoods, List<String> collect) {
        driverGoods.setId(driverGoods.getGoodsId());
        String goodsId = driverGoods.getGoodsId();
        if(StrUtil.startWith(goodsId,"DFQ")) {
            DriverGoods byGoodsId = driverGoodsMapper.getByGoodsId(goodsId);
            if(ObjUtil.isNotNull(byGoodsId)){
                driverGoods.setRemainingQuantity(byGoodsId.getRemainingQuantity());
                driverGoods.setEnquiryTypeBasePrice(byGoodsId.getEnquiryTypeBasePrice());
                driverGoods.setTotalQuantityUnits(byGoodsId.getTotalQuantityUnits());
                driverGoods.setTransportationType(byGoodsId.getTransportationType());
                driverGoods.setAdvancePaymentFlag(byGoodsId.getAdvancePaymentFlag());
                driverGoods.setAdvancePayment(byGoodsId.getAdvancePayment());
                driverGoods.setEnquiryType(byGoodsId.getEnquiryType());
                driverGoods.setFloatEnquiryTypeBasePrice(byGoodsId.getEnquiryTypeBasePrice());
                PlatformUmCompany platformUmCompany= platformExchangeService.getCompanyByModel(byGoodsId.getCompanyId());
                driverGoods.setCompanyName(platformUmCompany.getCompanyName());
                driverGoods.setCompanyId(byGoodsId.getCompanyId());
                driverGoods.setNetworkMainBodyId(byGoodsId.getNetworkMainBodyId());
                driverGoods.setNetworkMainBodyName(byGoodsId.getNetworkMainBodyName());
            }
        }else {
            TmsOrder tmsOrder = tmsExchangeService.getTmsOrder(goodsId);
            if(ObjUtil.isNotNull(tmsOrder)){
                driverGoods.setRemainingQuantity(tmsOrder.getRemainingQuantity());
                driverGoods.setTotalQuantityUnits(tmsOrder.getOrderQuantityUnits());
                driverGoods.setTransportationType(tmsOrder.getTransportationType());
                driverGoods.setAdvancePaymentFlag(tmsOrder.getAdvancePaymentFlag());
                driverGoods.setAdvancePayment(tmsOrder.getAdvancePayment());
                driverGoods.setEnquiryType("20");
                PlatformUmCompany platformUmCompany= platformExchangeService.getCompanyByModel(tmsOrder.getCarrierCompanyId());
                driverGoods.setCompanyName(platformUmCompany.getCompanyName());
                LsdsMatchmakingConfigQuery query = new LsdsMatchmakingConfigQuery();
                query.setBusId(goodsId);
                LsdsMatchmakingConfigDTO config = lsdsMatchmakingService.queryConfig(query);
                if(ObjUtil.isNotNull(config)){
                    driverGoods.setEnquiryTypeBasePrice(config.getPrice());
                    driverGoods.setFloatEnquiryTypeBasePrice(config.getPrice());
                    driverGoods.setAdvancePaymentFlag(AdvancePaymentFlagEnum.NO_USE.getCode());
                    driverGoods.setAdvancePayment(BigDecimal.ZERO);
                }
            }
        }
        driverGoods.setSourceType(convertSourceTypeFromDriverGoods(driverGoods,collect));
    }
}
