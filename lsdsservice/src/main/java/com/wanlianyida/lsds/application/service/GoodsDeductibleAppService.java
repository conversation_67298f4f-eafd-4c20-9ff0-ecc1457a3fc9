package com.wanlianyida.lsds.application.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsDeductible;
import com.wanlianyida.lsds.infrastructure.enums.GoodsDeductibleEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;

import javax.annotation.Resource;

/**
 * 亏涨吨新参数处理
 *
 * <AUTHOR>
 * @Date 2023/5/12 10:51
 */
@Slf4j
@Service
public class GoodsDeductibleAppService {

    @Resource
    private DcsAppService dcsBusiness;

    /**
     * 校验亏涨吨参数
     *
     * @param lsdsGoodsDeductible
     * @param resultModel
     * @return void
     * <AUTHOR>
     * @Date 2023/5/16 16:17
     */
    public void checkParams (LsdsGoodsDeductible lsdsGoodsDeductible, ResultMode resultModel) {
        if (ObjectUtil.isEmpty(lsdsGoodsDeductible)) {
            resultModel.setErrMsg("亏涨吨免赔参数:数据格式不能为空");
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_NOTNULL.getCode());
            resultModel.setSucceed(false);
        } else {
            if (lsdsGoodsDeductible.getEnable() == GoodsDeductibleEnum.SwitchStatus.OPEN.getCode()
                && StringUtils.isEmpty(lsdsGoodsDeductible.getDeductibleValue())) {
                resultModel.setErrMsg("亏涨吨免赔参数:数据格式不能为空");
                resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_NOTNULL.getCode());
                resultModel.setSucceed(false);
            }
        }
    }

    /*
     * 亏涨吨对象返回 并兼容历史数据
     *
     * @param otherKuiTonsRatio   历史亏吨免赔系数
     * @param goodsId 货源id
     * @return com.isoftstone.hig.lsds.api.entity.LsdsGoodsDeductible
     * <AUTHOR>
     * @Date 2023/5/16 11:51
     */
    public LsdsGoodsDeductible wrapGoodsDeductible (BigDecimal otherKuiTonsRatio, String goodsId) {
        // 去ES中查询对应亏涨吨对象
        LsdsGoodsDeductible lsdsGoodsDeductible = dcsBusiness.getLsdsGoodsDeductible(goodsId);
        log.info("原亏吨免赔系数：{}，新亏涨吨对象：{}", otherKuiTonsRatio, JSONUtil.toJsonStr(lsdsGoodsDeductible));

        // 新数据，亏涨吨对象 一定有值
        if (ObjectUtil.isNotEmpty(lsdsGoodsDeductible)) {
            return lsdsGoodsDeductible;
        } else if (ObjectUtil.isNotEmpty(otherKuiTonsRatio)) {
            lsdsGoodsDeductible = new LsdsGoodsDeductible();
            // 历史数据 亏吨免赔系数一定有值
            lsdsGoodsDeductible.setBizId(goodsId);
            lsdsGoodsDeductible.setEnable(GoodsDeductibleEnum.SwitchStatus.OPEN.getCode());
            lsdsGoodsDeductible.setDeductibleType(GoodsDeductibleEnum.DeductibleType.RATIO.getCode());
            lsdsGoodsDeductible.setDeductibleValue(otherKuiTonsRatio);
            lsdsGoodsDeductible.setShowMsg(GoodsDeductibleEnum.SwitchStatus.CLOSE.getCode());
            lsdsGoodsDeductible.setGoodsLostValue(null);
            lsdsGoodsDeductible.setAutoCompute(GoodsDeductibleEnum.ComputeType.MANUAL.getCode());
        } else {
            // 都没有值则数据异常
            log.info("原亏吨免赔系数、新亏涨吨对象，都为null");
            lsdsGoodsDeductible = null;
        }

        return lsdsGoodsDeductible;
    }

}
