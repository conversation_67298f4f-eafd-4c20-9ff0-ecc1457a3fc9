package com.wanlianyida.lsds.application.service;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.common.utils.UtilityClass;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.common.utils.maputils.WlydMapUtil;
import com.isoftstone.hig.ims.api.constants.ImsEnums;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsAttention;
import com.isoftstone.hig.lsds.api.filter.DriverGoodsFilter;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsAttentionCancelFilter;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsAttentionFilter;
import com.isoftstone.hig.lsds.api.mvcvo.GetGoodsAttentionListVo;
import com.isoftstone.hig.lsds.api.mvcvo.GoodsRecommendReqVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsAttentionCreateVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsAttentionVO;
import com.isoftstone.hig.oms.api.entity.OmsOrderAddress;
import com.isoftstone.hig.oms.api.inter.OmsOrderAddressInter;
import com.isoftstone.hig.oms.api.inter.OmsOrderInter;
import com.isoftstone.hig.platform.api.client.PlatformCommonInterClient;
import com.isoftstone.hig.platform.api.entity.PlatformUmCompany;
import com.isoftstone.hig.platform.api.filter.PlatformUmCompanyFilter;
import com.isoftstone.hig.platform.api.inter.PlatformUmUserbaseinfoInter;
import com.isoftstone.hig.platform.api.mvcvo.OrderContainer;
import com.isoftstone.hig.platform.api.mvcvo.PlatformQrCodeRelationVO;
import com.isoftstone.hig.qrs.api.constants.QrsEnum;
import com.isoftstone.hig.qrs.api.filter.QrsIdentifyCodeGoodsFilter;
import com.isoftstone.hig.qrs.api.inter.QrsIdentifyCodeGoodsInter;
import com.isoftstone.hig.qrs.api.vo.QrsIdentifyCodeGoodsConfigVO;
import com.isoftstone.hig.qrs.api.vo.QrsIdentifyCodeGoodsRelationVO;
import com.isoftstone.hig.qrs.api.vo.QrsIdentifyCodeGoodsReserveVO;
import com.isoftstone.hig.tms.api.entity.TmsEnum;
import com.isoftstone.hig.tms.api.entity.TmsOrder;
import com.isoftstone.hig.tms.api.inter.TmsOrderContainerInter;
import com.isoftstone.hig.tms.api.inter.TmsOrderInter;
import com.isoftstone.hig.tms.api.mvcvo.TmsOrderContainerVO;
import com.isoftstone.hig.tms.api.query.TmsOrderFilter;
import com.plumelog.core.util.StringUtils;
import com.wanlianyida.lsds.infrastructure.repository.mapper.DriverGoodsMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsGoodsAttentionMapper;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 货源关注Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-03
 */

@Slf4j
@Service
public class LsdsGoodsAttentionAppService {
    @Resource
    private LsdsGoodsAttentionMapper lsdsGoodsAttentionMapper;

    @Resource
    private DriverGoodsMapper driverGoodsMapper;

    @Resource
    private TmsOrderContainerInter tmsOrderContainerInter;

    @Resource
    private TmsOrderInter tmsOrderInter;

    @Resource
    private LsdsKafkaSender lsdsKafkaSender;

    @Resource
    private PlatformUmUserbaseinfoInter platformUmUserbaseinfoInter;

    @Resource
    private PlatformCommonInterClient platformCommonInterClient;

    @Resource
    private OmsOrderInter omsOrderInter;

    @Resource
    private DriverGoodsAppService driverGoodsService;

    @Resource
    private QrsIdentifyCodeGoodsInter qrsIdentifyCodeGoodsInter;
    @Resource
    private OmsOrderAddressInter omsOrderAddressInter;
    @Resource
    private GoodsRecommendAppService goodsRecommendAdaptor;

    /**
     * 查询货源关注
     *
     * @param attentionId 货源关注主键
     * @return 货源关注
     */
    public LsdsGoodsAttention selectLsdsGoodsAttentionByAttentionId(String attentionId) {
        return lsdsGoodsAttentionMapper.selectLsdsGoodsAttentionByAttentionId(attentionId);
    }

    /**
     * 查询货源关注列表
     *
     * @param lsdsGoodsAttention 货源关注
     * @return 货源关注
     */

    public List<LsdsGoodsAttention> selectLsdsGoodsAttentionList(LsdsGoodsAttention lsdsGoodsAttention) {
        return lsdsGoodsAttentionMapper.selectLsdsGoodsAttentionList(lsdsGoodsAttention);
    }

    /**
     * 新增货源关注
     *
     * @param lsdsGoodsAttentionCreateVo 货源关注
     * @return 结果
     */

    public ResultMode addGoodsAttention(LsdsGoodsAttentionCreateVo lsdsGoodsAttentionCreateVo) {
        if (StrUtil.isEmpty(lsdsGoodsAttentionCreateVo.getDriverId())) {
            return ResultMode.fail("司机id不能为空");
        }
        if (StrUtil.isEmpty(lsdsGoodsAttentionCreateVo.getGoodsType())) {
            return ResultMode.fail("货源类型不能为空");
        }
        if (StrUtil.equals(lsdsGoodsAttentionCreateVo.getGoodsType(), LsdsEnum.LsdsgoodsAttentionGoodsType.GOODS_TYPE_DRIVER.getCode())) {
            if (StrUtil.isEmpty(lsdsGoodsAttentionCreateVo.getGoodsId())) {
                return ResultMode.fail("关注司机货源时，货源id不能为空");
            }
            LsdsGoodsAttention lsdsGoodsAttentionFilter = new LsdsGoodsAttention();
            lsdsGoodsAttentionFilter.setDriverId(lsdsGoodsAttentionCreateVo.getDriverId());
            lsdsGoodsAttentionFilter.setGoodsType(lsdsGoodsAttentionCreateVo.getGoodsType());
            lsdsGoodsAttentionFilter.setGoodsId(lsdsGoodsAttentionCreateVo.getGoodsId());
            lsdsGoodsAttentionFilter.setDeleteFlag(LsdsEnum.LsdsgoodsAttentionDeleteFlag.DELETE_FLAG_0.getCode());
            List<LsdsGoodsAttention> goodsAttentionList = lsdsGoodsAttentionMapper.selectLsdsGoodsAttentionList(lsdsGoodsAttentionFilter);
            if (!IterUtil.isEmpty(goodsAttentionList)) {
                return ResultMode.fail("货源已关注过，不用再关注！");
            }
        } else if (StrUtil.equals(lsdsGoodsAttentionCreateVo.getGoodsType(), LsdsEnum.LsdsgoodsAttentionGoodsType.GOODS_TYPE_ORDER.getCode())) {
            if (StrUtil.isEmpty(lsdsGoodsAttentionCreateVo.getOrderId())) {
                return ResultMode.fail("关注绑码订单时，订单id不能为空");
            }
            LsdsGoodsAttention lsdsGoodsAttentionFilter = new LsdsGoodsAttention();
            lsdsGoodsAttentionFilter.setDriverId(lsdsGoodsAttentionCreateVo.getDriverId());
            lsdsGoodsAttentionFilter.setGoodsType(lsdsGoodsAttentionCreateVo.getGoodsType());
            lsdsGoodsAttentionFilter.setOrderId(lsdsGoodsAttentionCreateVo.getOrderId());
            lsdsGoodsAttentionFilter.setDeleteFlag(LsdsEnum.LsdsgoodsAttentionDeleteFlag.DELETE_FLAG_0.getCode());
            List<LsdsGoodsAttention> goodsAttentionList = lsdsGoodsAttentionMapper.selectLsdsGoodsAttentionList(lsdsGoodsAttentionFilter);
            if (!IterUtil.isEmpty(goodsAttentionList)) {
                return ResultMode.fail("货源已关注过，不用再关注！");
            }
        }
        LsdsGoodsAttention lsdsGoodsAttention = WlydMapUtil.map(lsdsGoodsAttentionCreateVo, LsdsGoodsAttention.class);
        lsdsGoodsAttention.setAttentionType(LsdsEnum.LsdsgoodsAttentionType.ATTENTION_TYPE_20.getCode());
        lsdsGoodsAttention.setDeleteFlag(LsdsEnum.LsdsgoodsAttentionDeleteFlag.DELETE_FLAG_0.getCode());
        lsdsGoodsAttention.setAttentionTime(new Date());
        lsdsGoodsAttention.setGoodsId(StrUtil.isEmpty(lsdsGoodsAttention.getGoodsId()) ? "0" : lsdsGoodsAttention.getGoodsId());
        lsdsGoodsAttention.setOrderId(StrUtil.isEmpty(lsdsGoodsAttention.getOrderId()) ? "0" : lsdsGoodsAttention.getOrderId());
        lsdsGoodsAttention.setAttentionId(UtilityClass.uuid());

        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        if (tokenInfo != null) {
            lsdsGoodsAttention.setUserId(tokenInfo.getUserBaseId());
        }

        int rows = lsdsGoodsAttentionMapper.insertLsdsGoodsAttention(lsdsGoodsAttention);
        return ResultMode.success(rows);
    }

    private String getDriverIdByUserBaseId() {
        try {
            ResultMode<String> resultDriverIdMode = platformUmUserbaseinfoInter.getDriverIdByUserBaseId();
            log.info("获取司机id结果:{}", JSON.toJSONString(resultDriverIdMode));
            if (resultDriverIdMode != null && !IterUtil.isEmpty(resultDriverIdMode.getModel())) {
                String driverId = resultDriverIdMode.getModel().get(0);
                return driverId;
            }
        } catch (Exception e) {
            log.error("获取司机id异常:{}", e);
        }
        return "";
    }

    /**
     * 修改货源关注
     *
     * @param lsdsGoodsAttention 货源关注
     * @return 结果
     */
    public int updateLsdsGoodsAttention(LsdsGoodsAttention lsdsGoodsAttention) {
        return lsdsGoodsAttentionMapper.updateLsdsGoodsAttention(lsdsGoodsAttention);
    }

    /**
     * 批量删除货源关注
     *
     * @param attentionIds 需要删除的货源关注主键
     * @return 结果
     */
    public int deleteLsdsGoodsAttentionByAttentionIds(String[] attentionIds) {
        return lsdsGoodsAttentionMapper.deleteLsdsGoodsAttentionByAttentionIds(attentionIds);
    }

    /**
     * 删除货源关注信息
     *
     * @param attentionId 货源关注主键
     * @return 结果
     */
    public int deleteLsdsGoodsAttentionByAttentionId(String attentionId) {
        return lsdsGoodsAttentionMapper.deleteLsdsGoodsAttentionByAttentionId(attentionId);
    }


    /**
     * 长期关注
     *
     * @param lsdsGoodsAttention
     * @return {@link ResultMode}
     */
    public ResultMode longTermAttentionGoods(LsdsGoodsAttention lsdsGoodsAttention) {
        if (StrUtil.isEmpty(lsdsGoodsAttention.getAttentionId())) {
            return ResultMode.fail("传入参数关注id不能为空");
        }
        LsdsGoodsAttention updGoodsAttention = new LsdsGoodsAttention();
        updGoodsAttention.setAttentionId(lsdsGoodsAttention.getAttentionId());
        updGoodsAttention.setAttentionType(LsdsEnum.LsdsgoodsAttentionType.ATTENTION_TYPE_10.getCode());
        updGoodsAttention.setModifyDate(new Date());
        return ResultMode.success(lsdsGoodsAttentionMapper.updateLsdsGoodsAttention(updGoodsAttention));
    }

    /**
     * 取消关注
     *
     * @param lsdsGoodsAttention
     * @return {@link ResultMode}
     */
    public ResultMode cancelGoodsAttention(LsdsGoodsAttention lsdsGoodsAttention) {
        if (StrUtil.isEmpty(lsdsGoodsAttention.getAttentionId())) {
            return ResultMode.fail("传入参数关注id不能为空");
        }
        LsdsGoodsAttention updGoodsAttention = new LsdsGoodsAttention();
        updGoodsAttention.setAttentionId(lsdsGoodsAttention.getAttentionId());
        updGoodsAttention.setDeleteFlag(LsdsEnum.LsdsgoodsAttentionDeleteFlag.DELETE_FLAG_1.getCode());
        updGoodsAttention.setModifyDate(new Date());
        return ResultMode.success(lsdsGoodsAttentionMapper.updateLsdsGoodsAttention(updGoodsAttention));
    }


    /**
     * 自动取消关注
     *
     * @param attentionCancelFilter
     */
    public void autoCancelGoodsAttention(LsdsGoodsAttentionCancelFilter attentionCancelFilter) {
        /**
         *
         * 1、非长期关注的处理,删除所有已关注的“非长期关注”的记录
         * 2、长期关注的处理
         * 如果是司机货源，判断货源是否有效，无效的货源删除记录
         * 如果是绑定二维码的订单，判断订单完成/取消，已完成/取消的订单删除记录
         * 3、操作类型 1司机接单自动取消 2货源失效自动取消 3解绑二维码
         */

        log.info("自动取消关注参数：{}", JSONUtil.toJsonStr(attentionCancelFilter));
        if (StrUtil.isEmpty(attentionCancelFilter.getOperType())) {
            return;
        }
        if (StrUtil.equals(attentionCancelFilter.getOperType(), "1")) {
            if (!StrUtil.isEmpty(attentionCancelFilter.getDriverId())) {
                lsdsGoodsAttentionMapper.deleteGoodsAttentionByDriverId(attentionCancelFilter.getDriverId());
            }
        } else if (StrUtil.equals(attentionCancelFilter.getOperType(), "2")) {
            if (!StrUtil.isEmpty(attentionCancelFilter.getGoodsId())) {
                lsdsGoodsAttentionMapper.deleteInvalidDriverGoods(attentionCancelFilter.getGoodsId());
            }
            return;
        } else if (StrUtil.equals(attentionCancelFilter.getOperType(), "3")) {
            if (!StrUtil.isEmpty(attentionCancelFilter.getOrderId())) {
                lsdsGoodsAttentionMapper.deleteInvalidOrderGoods(attentionCancelFilter.getOrderId(), attentionCancelFilter.getQrCodeId());
            }
            return;
        }

        //处理司机长期关注无效记录
        handleDriverInvalidRecords(attentionCancelFilter.getDriverId());

        //处理无效货源
        if (!StrUtil.isEmpty(attentionCancelFilter.getGoodsId())) {
            List<String> goodsIds = new ArrayList<>();
            goodsIds.add(attentionCancelFilter.getGoodsId());
            handleInvalidDriverGoods(goodsIds);
        }
    }

    /**
     * 处理司机无效记录
     *
     * @param driverId 司机id
     */
    private void handleDriverInvalidRecords(String driverId) {
        //判断司机长期关注的记录
        if (StrUtil.isEmpty(driverId)) {
            return;
        }
        LsdsGoodsAttention goodsAttention = new LsdsGoodsAttention();
        goodsAttention.setAttentionType(LsdsEnum.LsdsgoodsAttentionType.ATTENTION_TYPE_10.getCode());
        goodsAttention.setDriverId(driverId);
        goodsAttention.setDeleteFlag(LsdsEnum.LsdsgoodsAttentionDeleteFlag.DELETE_FLAG_0.getCode());
        List<LsdsGoodsAttention> attentionList = lsdsGoodsAttentionMapper.selectLsdsGoodsAttentionList(goodsAttention);
        if (IterUtil.isEmpty(attentionList)) {
            return;
        }
        Map<String, List<LsdsGoodsAttention>> map = attentionList.stream().collect(Collectors.groupingBy(item -> item.getGoodsType()));
        List<LsdsGoodsAttention> driverGoodsAttentionList = map.get(LsdsEnum.LsdsgoodsAttentionGoodsType.GOODS_TYPE_DRIVER.getCode());
        if (!IterUtil.isEmpty(driverGoodsAttentionList)) {
            Set<String> goodsIds = driverGoodsAttentionList.stream().filter(item -> !StrUtil.isEmpty(item.getGoodsId())).map(LsdsGoodsAttention::getGoodsId).collect(Collectors.toSet());
            handleInvalidDriverGoods(new ArrayList<>(goodsIds));
        }
    }

    /**
     * 处理失效的货源
     *
     * @param goodsIds 商品id
     */
    private void handleInvalidDriverGoods(List<String> goodsIds) {
        try {
            if (IterUtil.isEmpty(goodsIds)) {
                return;
            }
            List<DriverGoods> driverGoodsList = driverGoodsService.getDriverGoodsListByGoodsIdList(goodsIds);
            if (!IterUtil.isEmpty(driverGoodsList)) {
                for (DriverGoods driverGoods : driverGoodsList) {
                    if (StrUtil.equals(driverGoods.getDealStatus(), "30") || StrUtil.equals(driverGoods.getDealStatus(), "60")) { //发布中、部分成交
                        continue;
                    }
                    lsdsGoodsAttentionMapper.deleteInvalidDriverGoods(driverGoods.getGoodsId());
                }
            }
        } catch (Exception e) {
            log.error("处理失效的货源异常:{}", e);
        }
    }

    /**
     * 分页查询货源关注信息
     * <p>
     * 查询条件 @param  pageInfo
     *
     * @return 结果
     */
    public ResultMode<LsdsGoodsAttentionVO> selectPageLsdsGoodsAttentionData(PagingInfo<LsdsGoodsAttentionFilter> pageInfo) {
        LsdsGoodsAttentionFilter filterModel = pageInfo.filterModel;
        /*PlatformUmUserbaseinfo userInfoFromCache = FormsAuthTrader.getUserInfoFromCache();
        if (Optional.ofNullable(filterModel).isPresent() && userInfoFromCache!=null) {
            String exDriverId = userInfoFromCache.getExDriverId();
            if (StringUtils.isNotEmpty(exDriverId)){
                filterModel.setDriverId(exDriverId);
            }
            log.info("exDriverId司机id:{}",exDriverId);
        }*/
        if (StrUtil.isEmpty(filterModel.getDriverId())) {
            return ResultMode.fail("司机id不能为空");
        }
        int currentPage = pageInfo.getCurrentPage();
        int pageLength = pageInfo.getPageLength();

        filterModel.setCurrentPage((currentPage - 1) * pageLength);
        filterModel.setPageLength(pageLength);

        Integer total = lsdsGoodsAttentionMapper.getTotalByFilter(filterModel);
        List<LsdsGoodsAttention> datalist = lsdsGoodsAttentionMapper.getPageDataByFilter(filterModel);
        if (IterUtil.isEmpty(datalist)) {
            return ResultMode.successPageList(Collections.emptyList(), total);
        }
        //填充优先服务标识
        datalist.forEach(d ->{
            if(StrUtil.startWith(d.getGoodsId(),"DFQ")){
                DriverGoods driverGoods = driverGoodsMapper.getByGoodsId(d.getGoodsId());
                if(!ObjUtil.isNotNull(driverGoods)){
                    d.setPremiumServStatus(driverGoods.getPremiumServStatus());
                }
            }
        });
        List<LsdsGoodsAttentionVO> respList = paddingVO(datalist);
        return ResultMode.successPageList(respList, total);
    }

    private List<LsdsGoodsAttentionVO> paddingVO(List<LsdsGoodsAttention> goodsAttentionList) {
        Map<String, List<LsdsGoodsAttention>> map = goodsAttentionList.stream().collect(Collectors.groupingBy(item -> item.getGoodsType()));

        List<LsdsGoodsAttentionVO> respList = new ArrayList<>();
        //司机货源的关注信息
        List<LsdsGoodsAttention> driverGoodsAttentionList = map.get(LsdsEnum.LsdsgoodsAttentionGoodsType.GOODS_TYPE_DRIVER.getCode());
        if (!IterUtil.isEmpty(driverGoodsAttentionList)) {
            List<LsdsGoodsAttentionVO> attentionVOList = getDriverGoodsInfoOfPage(driverGoodsAttentionList);
            respList.addAll(attentionVOList);
        }

        //绑码订单的关注信息
        List<LsdsGoodsAttention> orderGoodsAttentionList = map.get(LsdsEnum.LsdsgoodsAttentionGoodsType.GOODS_TYPE_ORDER.getCode());
        if (!IterUtil.isEmpty(orderGoodsAttentionList)) {
            List<LsdsGoodsAttentionVO> attentionVOList = getBindOrderInfoOfPage(orderGoodsAttentionList);
            respList.addAll(attentionVOList);
        }

        //排序attention_type desc,attention_time desc
        respList.sort(Comparator.comparing(LsdsGoodsAttentionVO::getAttentionType).reversed().thenComparing(LsdsGoodsAttentionVO::getCreateDate).reversed());

        return respList;
    }


    /**
     * 获取司机货源
     *
     * @param driverGoodsAttentionList
     * @return {@link List}<{@link LsdsGoodsAttentionVO}>
     */
    private List<LsdsGoodsAttentionVO> getDriverGoodsInfoOfPage(List<LsdsGoodsAttention> driverGoodsAttentionList) {
        if (IterUtil.isEmpty(driverGoodsAttentionList)) {
            return Collections.emptyList();
        }
        //查询司机货源信息
        Set<String> goodsIds = driverGoodsAttentionList.stream().filter(item -> !StrUtil.isEmpty(item.getGoodsId())).map(LsdsGoodsAttention::getGoodsId).collect(Collectors.toSet());


        List<DriverGoods> driverGoodsList = driverGoodsService.getDriverGoodsListByGoodsIdList(new ArrayList<>(goodsIds));
//        if(!CollectionUtils.isEmpty(driverGoodsList)){
//
//            driverGoodsList.stream().forEach(e->{
//                if(e.getStartLineId()!=null&&!"".equals(e.getStartLineId())){
//                    String startLineId = e.getStartLineId();
//                    CrmCompanyLineAddressFilter filter = new CrmCompanyLineAddressFilter();
//                    filter.setLineId(startLineId);
//                    ResultMode<CrmCompanyLineAddress> startResult = crmCompanyLineAddressFeignInter.getLineAddressByLineId(filter);
//                    log.info("startResult...............{}",JSON.toJSONString(startResult));
//                    if(startResult.getSucceed()&&!CollectionUtils.isEmpty(startResult.getModel())){
//                        CrmCompanyLineAddress crmCompanyLineAddress  =startResult.getModel().get(0);
//                        com.isoftstone.hig.lsds.api.mvcvo.CrmCompanyLineAddress crmCompanyLineAddress1 = new com.isoftstone.hig.lsds.api.mvcvo.CrmCompanyLineAddress();
//                        BeanUtil.copyProperties(crmCompanyLineAddress1, crmCompanyLineAddress, true);
//                        e.setStartLineAddress(crmCompanyLineAddress1);
//                    }
//                }
//
//                if(e.getEndLineId()!=null&&!"".equals(e.getEndLineId())){
//                    String endLineId = e.getEndLineId();
//                    CrmCompanyLineAddressFilter filter = new CrmCompanyLineAddressFilter();
//                    filter.setLineId(endLineId);
//                    ResultMode<CrmCompanyLineAddress> endResult = crmCompanyLineAddressFeignInter.getLineAddressByLineId(filter);
//                    log.info("endResult...............{}",JSON.toJSONString(endResult));
//                    if(endResult.getSucceed()&&!CollectionUtils.isEmpty(endResult.getModel())){
//                        CrmCompanyLineAddress crmCompanyLineAddress  = endResult.getModel().get(0);
//                        com.isoftstone.hig.lsds.api.mvcvo.CrmCompanyLineAddress crmCompanyLineAddress1 = new com.isoftstone.hig.lsds.api.mvcvo.CrmCompanyLineAddress();
//                        BeanUtil.copyProperties(crmCompanyLineAddress1, crmCompanyLineAddress, true);
//                        e.setEndLineAddress(crmCompanyLineAddress1);
//                    }
//                }
//            });
//        }
        //获取司机报价次数
        Map<String, String> quotationTimesMap = getDriverGoodsQuotationTimes(new ArrayList<>(goodsIds));

        //对象转换
        List<LsdsGoodsAttentionVO> driverRespList = WlydMapUtil.mapList(driverGoodsList, LsdsGoodsAttentionVO.class);

        Map<String, LsdsGoodsAttention> attentionMap = driverGoodsAttentionList.stream().collect(Collectors.toMap(LsdsGoodsAttention::getGoodsId, v -> v, (v1, v2) -> v1));
        for (LsdsGoodsAttentionVO goodsAttention : driverRespList) {
            if (!MapUtil.isEmpty(quotationTimesMap) && quotationTimesMap.containsKey(goodsAttention.getGoodsId())) {
                goodsAttention.setQuotationTimes(quotationTimesMap.get(goodsAttention.getGoodsId()));
            } else {
                goodsAttention.setQuotationTimes("0");
            }
            goodsAttention.setOperType("0");
            goodsAttention.setStartSiteCityName(goodsAttention.getStartSiteCityName());
            goodsAttention.setEndSiteCityName(goodsAttention.getEndSiteCityName());
            goodsAttention.setSendAddrArea(goodsAttention.getSendAddrArea());
            goodsAttention.setSendAddrAreaName(goodsAttention.getSendAddrAreaName());
            goodsAttention.setReceiveAddrArea(goodsAttention.getReceiveAddrArea());
            goodsAttention.setReceiveAddrAreaName(goodsAttention.getReceiveAddrAreaName());
            LsdsGoodsAttention srcAttention = attentionMap.get(goodsAttention.getGoodsId());
            if (srcAttention != null) {
                goodsAttention.setAttentionType(srcAttention.getAttentionType());
                goodsAttention.setAttentionId(srcAttention.getAttentionId());
                goodsAttention.setCreateDate(srcAttention.getAttentionTime());
            }
            //统一剩余重量单位
            goodsAttention.setPlanQuantityUnit(goodsAttention.getTotalQuantityUnits());
        }
        return driverRespList;
    }

    /**
     * 获取绑码订单
     *
     * @param orderGoodsAttentionList
     * @return {@link List}<{@link LsdsGoodsAttentionVO}>
     */
    private List<LsdsGoodsAttentionVO> getBindOrderInfoOfPage(List<LsdsGoodsAttention> orderGoodsAttentionList) {
        if (IterUtil.isEmpty(orderGoodsAttentionList)) {
            return Collections.emptyList();
        }
        Set<String> orderIds = orderGoodsAttentionList.stream().filter(item -> !StrUtil.isEmpty(item.getOrderId())).map(LsdsGoodsAttention::getOrderId).collect(Collectors.toSet());
        TmsOrderFilter tmsOrderFilter = new TmsOrderFilter();
        tmsOrderFilter.setOrderIds(new ArrayList<>(orderIds));
        ResultMode<TmsOrder> orderResultMode = tmsOrderInter.getBindOrderInfoByOrderIds(tmsOrderFilter);
        if (orderResultMode == null || IterUtil.isEmpty(orderResultMode.getModel())) {
            return Collections.emptyList();
        }
        List<LsdsGoodsAttentionVO> orderRespList = getBindOrderInfo(orderResultMode.getModel());
        Map<String, LsdsGoodsAttention> attentionMap = orderGoodsAttentionList.stream().collect(Collectors.toMap(LsdsGoodsAttention::getOrderId, v -> v, (v1, v2) -> v1));
        for (LsdsGoodsAttentionVO goodsAttention : orderRespList) {
            LsdsGoodsAttention srcAttention = attentionMap.get(goodsAttention.getBusinessId());
            if (srcAttention != null) {
                goodsAttention.setAttentionType(srcAttention.getAttentionType());
                goodsAttention.setAttentionId(srcAttention.getAttentionId());
                goodsAttention.setCreateDate(srcAttention.getAttentionTime());
            }
            if (!StringUtils.isEmpty(goodsAttention.getBusinessId()) && !goodsAttention.getBusinessId().startsWith("DFQ")) {
                OmsOrderAddress omsOrderAddress = omsOrderInter.getOmsOrderAddressInfo(goodsAttention.getBusinessId());
                goodsAttention.setStartSiteCityName(omsOrderAddress.getSendAddrShorthand());
                goodsAttention.setEndSiteCityName(omsOrderAddress.getReceiveAddrShorthand());
                goodsAttention.setSendAddrAreaName(omsOrderAddress.getSendAddrAreaName());
                goodsAttention.setReceiveAddrAreaName(omsOrderAddress.getReceiveAddrAreaName());
                goodsAttention.setSendAddrProvinceName(omsOrderAddress.getSendAddrProvinceName());
                goodsAttention.setReceiveAddrProvinceName(omsOrderAddress.getReceiveAddrProvinceName());
                goodsAttention.setSendAddrCityName(omsOrderAddress.getSendAddrCityName());
                goodsAttention.setSendAddrCity(omsOrderAddress.getSendAddrCity());
                goodsAttention.setReceiveAddrCityName(omsOrderAddress.getReceiveAddrCityName());
                goodsAttention.setReceiveAddrCity(omsOrderAddress.getReceiveAddrCity());
            }

        }
        return orderRespList;
    }


    /**
     * 获取司机的报价次数
     *
     * @param goodsIds 商品id
     * @return {@link Map}<{@link String},{@link String}>
     */
    private Map<String, String> getDriverGoodsQuotationTimes(List<String> goodsIds) {
        String driverId = "";
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        if (StrUtil.isEmpty(tokenInfo.getDriverId())) {
            //没有司机id，需要手动去查
            driverId = getDriverIdByUserBaseId();
            if (!StrUtil.isEmpty(driverId)) {
                tokenInfo.setDriverId(driverId);
            }
        }
        driverId = tokenInfo.getDriverId();

        //查询报价次数
        Map<String, String> quotationTimesMap = new HashMap<>();
        if (!StrUtil.isEmpty(driverId) && !IterUtil.isEmpty(goodsIds)) {
            List<DriverGoods> quotationTimesList = driverGoodsMapper.getDriverGoodsQuotationTimes(driverId, new ArrayList<>(goodsIds));
            if (!IterUtil.isEmpty(quotationTimesList)) {
                quotationTimesMap = quotationTimesList.stream().filter(item -> !StrUtil.isEmpty(item.getGoodsId())).collect(Collectors.toMap(DriverGoods::getGoodsId, DriverGoods::getQuotationTimes));
            }
        }

        return quotationTimesMap;
    }

    /**
     * 获取渠道货源绑码订单
     *
     * @param orderList
     * @param driverId
     * @return {@link List}<{@link LsdsGoodsAttentionVO}>
     */
    public List<LsdsGoodsAttentionVO> getChannelGoodsBindOrderInfo(List<TmsOrder> orderList, String driverId) {
        if (IterUtil.isEmpty(orderList)) {
            return Collections.emptyList();
        }
        Set<String> orderIds = orderList.stream().filter(item -> !StrUtil.isEmpty(item.getOrderId())).map(TmsOrder::getOrderId).collect(Collectors.toSet());
        Map<String, TmsOrder> orderMap = orderList.stream().filter(item -> !StrUtil.isEmpty(item.getOrderId())).collect(Collectors.toMap(TmsOrder::getOrderId, v -> v, (v1, v2) -> v1));

        // qrs新接口根据业务单号批量查有效调价
        QrsIdentifyCodeGoodsFilter qrsFilter = new QrsIdentifyCodeGoodsFilter();
        // 只查货源码上的调价即可
        qrsFilter.setIdentifyCodeType(QrsEnum.IdentifyCodeTypeEnum.COMPANY_GOODS_CODE.getCode());
        qrsFilter.setBizOrderType(QrsEnum.BizOrderTypeEnum.ORDER.getCode());
        qrsFilter.setBizOrderNos(orderIds);
        qrsFilter.setIsRecommend(false);
        ResultMode<QrsIdentifyCodeGoodsRelationVO> res = qrsIdentifyCodeGoodsInter.queryBindCodeRelationByBusinessIds(qrsFilter);
        if (!res.getSucceed() || IterUtil.isEmpty(res.getModel())) {
            return Collections.emptyList();
        }
        /**
        // res.getModel()按bizOrderNo分组
        Map<String, List<QrsIdentifyCodeGoodsRelationVO>> resMap = res.getModel().stream().collect(Collectors.groupingBy(QrsIdentifyCodeGoodsRelationVO::getBizOrderNo));
        // resMap中每个key对应的集合中，找出最新的一条记录
        Map<String, QrsIdentifyCodeGoodsRelationVO> resMapNew = resMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, item -> item.getValue().stream().max(Comparator.comparing(QrsIdentifyCodeGoodsRelationVO::getCreatedDate)).get()));
        // qrCodeList中只保留resMapNew中key对应的记录
        List<QrsIdentifyCodeGoodsRelationVO> qrCodeList = resMapNew.values().stream().collect(Collectors.toList());
         **/
        List<QrsIdentifyCodeGoodsRelationVO> qrCodeList = res.getModel();
        List<LsdsGoodsAttentionVO> respList = new ArrayList<>();
        // 抽出公共处理方法
        dealQrCodes (qrCodeList, orderMap, respList);
        /**
        // 公共方法只处理货源码，收款码的再处理
        for (LsdsGoodsAttentionVO attentionVO: respList) {
            if (resMapNew.get(attentionVO.getGoodsId()) != null &&
                StrUtil.equals(resMapNew.get(attentionVO.getGoodsId()).getIdentifyCodeType(), QrsEnum.IdentifyCodeTypeEnum.COMPANY_RESERVE_CODE.getCode())) {
                TmsOrder order = orderMap.get(attentionVO.getGoodsId());
                if (order != null) {
                    attentionVO.setRemainingQuantity(order.getRemainingQuantity());
                    attentionVO.setAccessibleWeight(order.getRemainingQuantity());
                }
            }
        }
         **/
        //填充关注类型
        fillAttentionType(driverId, LsdsEnum.LsdsgoodsAttentionGoodsType.GOODS_TYPE_ORDER.getCode(), respList);
        return respList;
    }

    /**
     * 获取绑码订单
     *
     * @param orderList
     * @return {@link List}<{@link LsdsGoodsAttentionVO}>
     */
    private List<LsdsGoodsAttentionVO> getBindOrderInfo(List<TmsOrder> orderList) {
        if (IterUtil.isEmpty(orderList)) {
            return Collections.emptyList();
        }
        Set<String> orderIds = orderList.stream().filter(item -> !StrUtil.isEmpty(item.getOrderId())).map(TmsOrder::getOrderId).collect(Collectors.toSet());
        Map<String, TmsOrder> orderMap = orderList.stream().filter(item -> !StrUtil.isEmpty(item.getOrderId())).collect(Collectors.toMap(TmsOrder::getOrderId, v -> v, (v1, v2) -> v1));

        // qrs新接口根据业务单号批量查绑码关系、配置、有效调价（只推荐原始单且询价范围是平台司机的：传定制参数）
        QrsIdentifyCodeGoodsFilter qrsFilter = new QrsIdentifyCodeGoodsFilter();
        qrsFilter.setIdentifyCodeType(QrsEnum.IdentifyCodeTypeEnum.COMPANY_GOODS_CODE.getCode());
        qrsFilter.setBizOrderType(QrsEnum.BizOrderTypeEnum.ORDER.getCode());
        qrsFilter.setBizOrderNos(orderIds);
        qrsFilter.setIsRecommend(true);
        ResultMode<QrsIdentifyCodeGoodsRelationVO> res = qrsIdentifyCodeGoodsInter.queryBindCodeRelationByBusinessIds(qrsFilter);
        if (!res.getSucceed() || IterUtil.isEmpty(res.getModel())) {
            return Collections.emptyList();
        }
        List<QrsIdentifyCodeGoodsRelationVO> qrCodeList = res.getModel();
        List<LsdsGoodsAttentionVO> respList = new ArrayList<>();
        // 抽出公共处理方法
        dealQrCodes (qrCodeList, orderMap, respList);
        return respList;
    }

    private void dealQrCodes (List<QrsIdentifyCodeGoodsRelationVO> qrCodeList, Map<String, TmsOrder> orderMap, List<LsdsGoodsAttentionVO> respList) {
        for (QrsIdentifyCodeGoodsRelationVO relationVO : qrCodeList) {
            if (!orderMap.containsKey(relationVO.getBizOrderNo())) {
                continue;
            }
            TmsOrder order = orderMap.get(relationVO.getBizOrderNo());
            if (!order.getCarrierCompanyId().equals(relationVO.getBindCompanyId())){
                //过滤服务单
                continue;
            }

            // 查oms订单地址
            ResultMode<OmsOrderAddress> omsOrderAddressResultMode = omsOrderAddressInter.getByOrderId(relationVO.getBizOrderNo());
            if (!omsOrderAddressResultMode.getSucceed() || IterUtil.isEmpty(omsOrderAddressResultMode.getModel()) ||
                IterUtil.getFirst(omsOrderAddressResultMode.getModel()) == null) {
                continue;
            }
            OmsOrderAddress address = IterUtil.getFirst(omsOrderAddressResultMode.getModel());
            QrsIdentifyCodeGoodsConfigVO configVO = relationVO.getQrsIdentifyCodeGoodsConfigVO();
            QrsIdentifyCodeGoodsReserveVO reserveVO = relationVO.getQrsIdentifyCodeGoodsReserveVO();
            // 无奈的将新表的字段硬塞进原来的出参对象中
            PlatformQrCodeRelationVO qrCode = new PlatformQrCodeRelationVO();
            qrCode.setSendAddrProvinceName(address.getSendAddrProvinceName());
            qrCode.setSendAddrCityName(address.getSendAddrCityName());
            qrCode.setSendAddrAreaName(address.getSendAddrAreaName());
            qrCode.setReceiveAddrProvinceName(address.getReceiveAddrProvinceName());
            qrCode.setReceiveAddrCityName(address.getReceiveAddrCityName());
            qrCode.setReceiveAddrAreaName(address.getReceiveAddrAreaName());
            qrCode.setBusinessId(relationVO.getBizOrderNo());
            qrCode.setBusinessType(relationVO.getBizOrderType());
            qrCode.setBindStatus(relationVO.getBindStatus());
            qrCode.setUntieType(relationVO.getUniteType());
            qrCode.setPlanVolume(relationVO.getPlanQuantity());
            qrCode.setAccessibleWeight(relationVO.getRemainQuantity());
            if (null != configVO) {
                qrCode.setSettlementWeight(configVO.getSettlementWeightType());
                qrCode.setFreightType(configVO.getFreightType());
                qrCode.setNetworkFreightId(configVO.getNetworkFreightId());
                qrCode.setIfPrompt(configVO.getIfPrompt());
                qrCode.setPrompt(configVO.getPrompt());
                qrCode.setEnquiryRange(configVO.getEnquiryRange());
                qrCode.setPriceDesensitizeTag(configVO.getDesensitizeTag());
            }
            if (null != reserveVO) {
                qrCode.setWaybillPrice(reserveVO.getWaybillPrice());
                qrCode.setEnquiryTypeBasePrice(reserveVO.getEnquiryTypeBasePrice());
                qrCode.setFloatEnquiryTypeBasePrice(reserveVO.getFloatEnquiryTypeBasePrice());
                qrCode.setFloatEnquiryTypeBaseOpenTicket(reserveVO.getFloatEnquiryTypeBaseOpenTicket());
                qrCode.setValuationType(reserveVO.getValuationType());
                qrCode.setPriceStartTime(reserveVO.getPriceStartTime());
                qrCode.setPriceEndTime(reserveVO.getPriceEndTime());
            }
            // 继续原逻辑
            TmsOrder tmsOrder = orderMap.get(qrCode.getBusinessId());
            qrCode.setGoodsName(tmsOrder.getGoodsName());
            qrCode.setGoodsId(tmsOrder.getGoodsId());
            qrCode.setTransportationType(tmsOrder.getTransportationType());
            qrCode.setShipperCompanyId(tmsOrder.getShipperCompanyId());
            qrCode.setShipperName(tmsOrder.getShipperName());
            if (tmsOrder.getWeightSum() != null) {
                qrCode.setWeightSum(String.valueOf(tmsOrder.getWeightSum()));
            }
            if (tmsOrder.getVolumeSum() != null) {
                qrCode.setVolumeSum(String.valueOf(tmsOrder.getVolumeSum()));
            }
            if (tmsOrder.getTotalGoods() != null) {
                qrCode.setTotalGoods(String.valueOf(tmsOrder.getTotalGoods()));
            }
            qrCode.setReleaseDate(tmsOrder.getReleaseDate());
            qrCode.setArriveDate(tmsOrder.getArriveDate());
            qrCode.setOrderStatus(tmsOrder.getOrderStatus());
            qrCode.setOtherKuiTonsRatio(tmsOrder.getOtherKuiTonsRatio());
            qrCode.setOtherRemark(tmsOrder.getOtherRemark());
            //指定信息
            qrCode.setContainerType(tmsOrder.getContainerType());
            qrCode.setContainerNumber(tmsOrder.getContainerNumber());
            qrCode.setAssignCarType(tmsOrder.getAssignCarType());
            if (tmsOrder.getAssignCarLength() != null) {
                qrCode.setAssignCarLength(String.valueOf(tmsOrder.getAssignCarLength()));
            }
            qrCode.setAssignCarPlateNumber(tmsOrder.getAssignCarPlateNumber());
            qrCode.setPackType(tmsOrder.getPackType());

            //接单次数
            qrCode.setCountNum(tmsOrder.getCountNum());
            //集卡信息
            if (TmsEnum.TmsorderOrderTransportationTypeEnum.transportationType111.getCode().equals(tmsOrder.getTransportationType())) {
                ResultMode<TmsOrderContainerVO> orderContainerResultMode = tmsOrderContainerInter.containerByOrderId(tmsOrder.getOrderId());

                if (orderContainerResultMode != null && !IterUtil.isEmpty(orderContainerResultMode.getModel())) {
                    TmsOrderContainerVO tmsOrderContainer = orderContainerResultMode.getModel().get(0);
                    if (tmsOrderContainer != null) {
                        OrderContainer orderContainer = new OrderContainer();
                        BeanUtils.copyProperties(tmsOrderContainer, orderContainer);
                        qrCode.setOrderContainer(orderContainer);
                    }
                }
            }
            //补充有效期、剩余量、单价、单位、询价方式、地址简称、订单有效期、绑码关系id（找货和首页的找货要显示）
            Opt.ofNullable(reserveVO).ifPresent(reserve -> qrCode.setEnquiryTypeBasePrice(reserve.getWaybillPrice()));
            Opt.ofNullable(reserveVO).ifPresent(reserve -> qrCode.setFloatEnquiryTypeBasePrice(reserve.getWaybillPrice()));
            Opt.ofNullable(reserveVO).ifPresent(reserve -> qrCode.setTotalQuantityUnits(reserveVO.getValuationType()));
            qrCode.setRemainingQuantity(relationVO.getRemainQuantity());
            //绑码订单的数据不存在公开询价的全是指定单价（绑码订单有默认的预约调价记录上的生效期内的单价）
            qrCode.setEnquiryType(TmsEnum.EnquiryTypeEnum.TWENTY.getCode());
            qrCode.setStartSiteCityName(address.getSendAddrShorthand());
            qrCode.setEndSiteCityName(address.getReceiveAddrShorthand());
            qrCode.setValidityDate(order.getOrderExpireDate());

            LsdsGoodsAttentionVO attentionVO = WlydMapUtil.map(qrCode, LsdsGoodsAttentionVO.class);
            //统一剩余重量单位
            Opt.ofNullable(reserveVO).ifPresent(reserve -> attentionVO.setPlanQuantityUnit(reserve.getValuationType()));
            attentionVO.setCreateDate(tmsOrder.getCreateDate());
            attentionVO.setOperType("1");
            attentionVO.setAttentionType("0");
            //绑码订单的货物信息给空
            attentionVO.setGoodsInfo(null);
            //订单绑码关系id
            attentionVO.setQrCodeRelationId(String.valueOf(relationVO.getId()));
            respList.add(attentionVO);
        }
    }

    /**
     * 填充关注类型
     *
     * @param list 列表
     */
    private void fillAttentionType(String driverId, String goodsType, List<LsdsGoodsAttentionVO> list) {
        if (StrUtil.isEmpty(driverId)) {
            return;
        }
        List<LsdsGoodsAttention> attentionList = null;
        if (StrUtil.equals(goodsType, LsdsEnum.LsdsgoodsAttentionGoodsType.GOODS_TYPE_DRIVER.getCode())) {
            Set<String> goodsIds = list.stream().filter(item -> !StrUtil.isEmpty(item.getGoodsId())).map(LsdsGoodsAttentionVO::getGoodsId).collect(Collectors.toSet());
            attentionList = lsdsGoodsAttentionMapper.getGoodsAttentionByGoodsType(driverId, goodsType, new ArrayList<>(goodsIds), null);
            if (IterUtil.isEmpty(attentionList)) {
                return;
            }
            Map<String, LsdsGoodsAttention> attentionMap = attentionList.stream().filter(item -> !StrUtil.isEmpty(item.getGoodsId())).collect(Collectors.toMap(LsdsGoodsAttention::getGoodsId, v -> v, (v1, v2) -> v1));
            for (LsdsGoodsAttentionVO recommendResp : list) {
                if (StrUtil.isEmpty(recommendResp.getGoodsId())) {
                    continue;
                }
                if (attentionMap.containsKey(recommendResp.getGoodsId())) {
                    recommendResp.setAttentionType(attentionMap.get(recommendResp.getGoodsId()).getAttentionType());
                } else {
                    recommendResp.setAttentionType(LsdsEnum.LsdsgoodsAttentionType.ATTENTION_TYPE_00.getCode());
                }
            }
        } else {
            Set<String> orderIds = list.stream().filter(item -> !StrUtil.isEmpty(item.getBusinessId())).map(LsdsGoodsAttentionVO::getBusinessId).collect(Collectors.toSet());
            attentionList = lsdsGoodsAttentionMapper.getGoodsAttentionByGoodsType(driverId, goodsType, null, new ArrayList<>(orderIds));
            if (IterUtil.isEmpty(attentionList)) {
                return;
            }
            Map<String, LsdsGoodsAttention> attentionMap = attentionList.stream().filter(item -> !StrUtil.isEmpty(item.getOrderId())).collect(Collectors.toMap(LsdsGoodsAttention::getOrderId, v -> v, (v1, v2) -> v1));
            for (LsdsGoodsAttentionVO recommendResp : list) {
                if (StrUtil.isEmpty(recommendResp.getBusinessId())) {
                    continue;
                }
                if (attentionMap.containsKey(recommendResp.getBusinessId())) {
                    recommendResp.setAttentionType(attentionMap.get(recommendResp.getBusinessId()).getAttentionType());
                } else {
                    recommendResp.setAttentionType(LsdsEnum.LsdsgoodsAttentionType.ATTENTION_TYPE_00.getCode());
                }
            }
        }
    }

    /**
     * 获取货源推荐信息
     *
     * @param recommendReq 推荐
     * @return {@link ResultMode}<{@link LsdsGoodsAttentionVO}>
     */
    public ResultMode<LsdsGoodsAttentionVO> getGoodsRecommendInfo(GoodsRecommendReqVo recommendReq) {
        log.info("【货源推荐】：请求参数：{}", JSONObject.toJSONString(recommendReq));
        //查询地址
        GoodsRecommendAppService.AddressInfo addressInfo = goodsRecommendAdaptor.findAddress(recommendReq);
        if (addressInfo == null) {
            return ResultMode.success();
        }
        // 司机端货源大厅区分是否选择查询条件的出发地或目的地：选择了就用该条件过滤最后一次进行中运单的地址数据
        if (StrUtil.isNotEmpty(recommendReq.getSendAddrCity())) {
            if (!StrUtil.equals(recommendReq.getSendAddrCity(), addressInfo.getSendAddrCity())) {
                return ResultMode.success();
            }
        }
        if (StrUtil.isNotEmpty(recommendReq.getReceiveAddrCity())) {
            if (!StrUtil.equals(recommendReq.getReceiveAddrCity(), addressInfo.getReceiveAddrCity())) {
                return ResultMode.success();
            }
        }

        //获取司机货源
        List<LsdsGoodsAttentionVO> recommendRespList = new ArrayList<>();
        List<LsdsGoodsAttentionVO> driverGoodsList = goodsRecommendAdaptor.getGoodsRecommendInfo(recommendReq, addressInfo);
        if (IterUtil.isNotEmpty(driverGoodsList)) {
            recommendRespList.addAll(driverGoodsList);
        }

        //获取绑码订单
        List<LsdsGoodsAttentionVO> bindOrderList = goodsRecommendAdaptor.getBindOrderRecommend(recommendReq, addressInfo);
        if (!IterUtil.isEmpty(bindOrderList)) {
            recommendRespList.addAll(bindOrderList);
        }

        recommendRespList.forEach(r -> {
            String goodsId = r.getGoodsId();
            if(StrUtil.startWith(goodsId,"DFQ")){
                //填充优先服务标识
                DriverGoods driverGoods = driverGoodsService.getByGoodsId(goodsId);
                if(ObjUtil.isNotNull(driverGoods)){
                    r.setPremiumServStatus(driverGoods.getPremiumServStatus());
                    r.setCompanyId(driverGoods.getCompanyId());
                    r.setGoodsKind(driverGoods.getGoodsKind());
                }
            }
        });

        //合并记录，时间降序,取前10条
        recommendRespList.sort(Comparator.comparing(LsdsGoodsAttentionVO::getCreateDate).reversed());
        if (recommendRespList.size() > 3) {
            recommendRespList = recommendRespList.subList(0, 3);
        }
        return new ResultMode<>(recommendRespList);
    }


    /**
     * 获取司机货品推荐
     *
     * @return {@link List}<{@link LsdsGoodsAttentionVO}>
     */
    public List<DriverGoods> getDriverGoodsRecommend(DriverGoodsFilter driverGoodsFilter) {
        //查询司机货源15天内的前10条
        //15天前的日期
        Date date = DateUtil.offsetDay(new Date(), -14);
        String strDate = DateUtil.format(date, "yyyy-MM-dd") + " 00:00:00";
        Date createDate = DateUtil.parse(strDate, "yyyy-MM-dd HH:mm:ss");
        driverGoodsFilter.setCreateDate(createDate);
        return driverGoodsMapper.getDriverGoodsRecommend(driverGoodsFilter);
    }

    public List<LsdsGoodsAttentionVO> transferDriverGoodsRecommend(List<DriverGoods> driverGoodsList, GoodsRecommendReqVo recommendReq) {
        if (IterUtil.isEmpty(driverGoodsList)) {
            return Collections.emptyList();
        }

        Set<String> goodsIds = driverGoodsList.stream().filter(item -> !StrUtil.isEmpty(item.getGoodsId())).map(DriverGoods::getGoodsId).collect(Collectors.toSet());

        //查询报价次数
        Map<String, String> quotationTimesMap = getDriverGoodsQuotationTimes(new ArrayList<>(goodsIds));


        //查询公司联系人
        Map<String, String> companyPhoneMap = new HashMap<>();
        Set<String> companyIds = driverGoodsList.stream().filter(item -> !StrUtil.isEmpty(item.getCompanyId())).map(DriverGoods::getCompanyId).collect(Collectors.toSet());
        PlatformUmCompanyFilter companyFilter = new PlatformUmCompanyFilter();
        companyFilter.setExCompanyIdList(new ArrayList<>(companyIds));
        ResultMode<PlatformUmCompany> companyResultMode = platformCommonInterClient.getCompanyLicenseTypeInfoByKeys(companyFilter);
        if (companyResultMode != null && !IterUtil.isEmpty(companyResultMode.getModel())) {
            List<PlatformUmCompany> companyList = companyResultMode.getModel();
            log.info("公司信息:{}", JSONUtil.toJsonStr(companyList));
            companyPhoneMap = companyList.stream().filter(item -> !StrUtil.isEmpty(item.getCompanyId()) && !StrUtil.isEmpty(item.getPhone())).collect(Collectors.toMap(PlatformUmCompany::getCompanyId, PlatformUmCompany::getPhone));
        }

        //对象转换
        List<LsdsGoodsAttentionVO> driverRespList = WlydMapUtil.mapList(driverGoodsList, LsdsGoodsAttentionVO.class);
        for (LsdsGoodsAttentionVO driverGoods : driverRespList) {
            if (!MapUtil.isEmpty(quotationTimesMap) && quotationTimesMap.containsKey(driverGoods.getGoodsId())) {
                driverGoods.setQuotationTimes(quotationTimesMap.get(driverGoods.getGoodsId()));
            } else {
                driverGoods.setQuotationTimes("0");
            }
            driverGoods.setOperType("0");
            driverGoods.setAttentionType("0");
            driverGoods.setCompanyContactPhone(companyPhoneMap.get(driverGoods.getCompanyId()));
        }

        //填充关注类型
        fillAttentionType(recommendReq.getDriverId(), LsdsEnum.LsdsgoodsAttentionGoodsType.GOODS_TYPE_DRIVER.getCode(), driverRespList);

        return driverRespList;
    }

    /**
     * 获取绑码订单推荐
     *
     * @return {@link List}<{@link LsdsGoodsAttentionVO}>
     */
    public List<TmsOrder> getBindOrderRecommend(TmsOrderFilter orderFilter) {
        //查询绑码订单前10条
        ResultMode<TmsOrder> orderResultMode = tmsOrderInter.getBindOrderRecommend(orderFilter);
        if (orderResultMode == null || IterUtil.isEmpty(orderResultMode.getModel())) {
            return Collections.emptyList();
        }
        return orderResultMode.getModel();
    }


    public List<LsdsGoodsAttentionVO> transferBindOrderRecommend(List<TmsOrder> orderList, GoodsRecommendReqVo recommendReq) {
        List<LsdsGoodsAttentionVO> orderRespList = getBindOrderInfo(orderList);
        if (IterUtil.isEmpty(orderRespList)) {
            return Collections.emptyList();
        }

        //填充关注类型
        fillAttentionType(recommendReq.getDriverId(), LsdsEnum.LsdsgoodsAttentionGoodsType.GOODS_TYPE_ORDER.getCode(), orderRespList);
        return orderRespList;

    }

    /**
     * 发送取消货源关注kafka消息
     *
     * @param waybillId
     */
    public void sendCancelGoodsAttentionMsg(String driverId, String waybillId, String goodsId) {
        LsdsGoodsAttentionCancelFilter attentionCancelFilter = new LsdsGoodsAttentionCancelFilter();
        try {
            attentionCancelFilter.setWaybillId(waybillId);
            attentionCancelFilter.setOperType("1");
            attentionCancelFilter.setDriverId(driverId);
            attentionCancelFilter.setGoodsId(goodsId);
            String topic = SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.AUTO_CANCEL_GOODS_ATTENTION.getTopicName();
            lsdsKafkaSender.send(topic, JSONUtil.toJsonStr(attentionCancelFilter));
        } catch (Exception e) {
            log.error("发送取消货源关注kafka消息:{}", JSONUtil.toJsonStr(attentionCancelFilter));
            log.error("发送取消货源关注kafka消息异常:{}", e);
        }
    }

    public void sendCancelGoodsAttentionMsg(String goodsId) {
        if (StrUtil.isEmpty(goodsId)) {
            return;
        }
        LsdsGoodsAttentionCancelFilter attentionCancelFilter = new LsdsGoodsAttentionCancelFilter();
        try {
            attentionCancelFilter.setOperType("2");
            attentionCancelFilter.setGoodsId(goodsId);
            String topic = SpringContextUtil.getKafkaTopicPrefix() + UtilityEnum.KafkaTopicNameEnum.AUTO_CANCEL_GOODS_ATTENTION.getTopicName();
            lsdsKafkaSender.send(topic, JSONUtil.toJsonStr(attentionCancelFilter));
        } catch (Exception e) {
            log.error("发送取消货源关注kafka消息:{}", JSONUtil.toJsonStr(attentionCancelFilter));
            log.error("发送取消货源关注kafka消息异常:{}", e);
        }
    }

    /**
     * 查询货源关注列表
     *
     * @param getGoodsAttentionListVo
     * @return
     */
    public List<LsdsGoodsAttention> getGoodsAttentionInfoList(GetGoodsAttentionListVo getGoodsAttentionListVo) {
        log.info("查询货源关注列表->getGoodsAttentionInfoList请求参数：{}", JSONUtil.toJsonStr(getGoodsAttentionListVo));
        LsdsGoodsAttention lsdsGoodsAttention = new LsdsGoodsAttention();
        BeanUtils.copyProperties(getGoodsAttentionListVo, lsdsGoodsAttention);
        lsdsGoodsAttention.setDeleteFlag(ImsEnums.DelFlag.DEL_FLAG_FALSE.getCode());
        return lsdsGoodsAttentionMapper.selectLsdsGoodsAttentionList(lsdsGoodsAttention);
    }
}
