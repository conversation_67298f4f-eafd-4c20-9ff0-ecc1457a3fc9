package com.wanlianyida.lsds.infrastructure.exchange;

import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.lsds.api.entity.BizExtendVO;
import com.isoftstone.hig.platform.api.entity.*;
import com.isoftstone.hig.platform.api.entity.PlatformCmOperationMainBody;
import com.isoftstone.hig.platform.api.entity.PlatformUmCompany;
import com.isoftstone.hig.platform.api.entity.PlatformUmUserbaseinfo;
import com.isoftstone.hig.platform.api.mvcvo.PlatformPricingTypeConfigVo;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.PlatformTaxRateFilter;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 平台交换层
 */
public interface PlatformExchangeService {

    /**
     * 查询计价配置数据
     * @param status
     * @return
     */
    List<PlatformPricingTypeConfigVo> queryPricingTypeConfigEnum(String status);

    /**
     * 类型映射
     * @param status
     * @return
     */
    Map<String, String> getPricingUnitsMap(String status);

    /**
     * 数量取值方式
     */
    String getQuantityValueMethod(String pricingType);

    /**
     * 获取税率
     * @param freightType
     * @param networkMainBodyId
     * @param companyId
     * @return
     */
    BigDecimal getTaxRate(String freightType, String networkMainBodyId, String companyId);


    /**
     * 查询枚举值
     * @param paramCode
     * @return
     */
    String getParamValue(String paramCode);

    /**
     * 查询企业是否交易主体
     */
    HashMap<String, Boolean> getTransactionBody(List<String> companyIdList);

    /**
     * 查询企业信息
     */
    List<PlatformUmCompany> getCompanyByIds(List<String> companyIds);


    /**
     * 查询企业信息
     * @param companyId
     * @return
     */
    PlatformUmCompany getCompanyByModel(String companyId);

    /**
     * 获取用户基本信息
     * @param userBaseId
     * @return
     */
    PlatformUmUserbaseinfo getUserInfoByUserId(String userBaseId);

    /**
     * 根据颜色编号查询颜色名称
     */
    String getCarColorNameByColorCode(String carColor);

    /**
     * 获取取当前登录用户信息
     */
    PlatformUmUserbaseinfo getDriverUserInfoByUserBaseId();

    /**
     * 获取司机的tokenInfo
     */
    TokenInfo getDriverTokenInfo();

    /**
     * 查询网组题
     * @param operationMainBodyId
     * @return
     */
    PlatformCmOperationMainBody getMainBody(String operationMainBodyId);

    /**
     * 查询业务类型、差价率和成本价
     */
    BizExtendVO getTaxRate(PlatformTaxRateFilter filter);
    ResultMode<JSONObject> getPlatformUmDriverBindBankAndCar(String driverId);

    /**
     * 查询字典值
     * @param parentDicId
     * @return
     */
    List<PlatformCmDictionary> getByParentDic(String parentDicId);

    /**
     * 查询是否满足自动审核
     * @param model
     * @return
     */
    Boolean judgeAutoAudit(Map<String, String> model);

    /**
     * 查询主体
     * @param companyMainFilter
     * @return
     */
    PlatformUmCompanyMain getCompanyMainModel(PlatformUmCompanyMain companyMainFilter);

    /**
     * 查询主体信息
     * @param companyId
     * @param type
     * @return
     */
    List<PlatformUmCompanyMain> selectCompanyMains(String companyId,String type);

    /**
     * 判断是否是交易签约主体
     * @param companyId
     * @return
     */
    boolean isTransactionBody(String companyId);

    /**
     * 是否是平台企业
     * @param companyId
     * @return
     */
    boolean checkPlatformIs3PL(String companyId);

    /**
     * 查询主体列表
     * @param pagingInfo
     * @return
     */
    List<PlatformCmOperationMainBody> listMainBodyPage(PagingInfo pagingInfo);

    /**
     *
     * @return 查询交易主体
     */
    List<PlatformCmOperationMainBody> getAllTransaction();

    /**
     * 查询参数值
     */
    List<PlatformCmPlatformParameter> getByParaCodes(List<String> paraCodesDefaultLoad);

    /**
     * 查询敏感词
     */
    List<PlatformCmSensitiveWord> getAllPlatformCmSensitiveWord();

    /**
     * 查询登录信息
     * @param userBaseId
     * @return
     */
    PlatformUmLogininfo getLoginInfoByUserBaseId(String userBaseId);
}
