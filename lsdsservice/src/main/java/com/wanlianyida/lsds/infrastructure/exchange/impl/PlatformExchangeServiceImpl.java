package com.wanlianyida.lsds.infrastructure.exchange.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.isoftstone.hig.annotations.LogPrintPoint;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.lsds.api.entity.BizExtendVO;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.platform.api.client.PlatformCommonInterClient;
import com.isoftstone.hig.platform.api.entity.*;
import com.isoftstone.hig.platform.api.enums.PlatformEnum;
import com.isoftstone.hig.platform.api.filter.CompanyTaxRateFilter;
import com.isoftstone.hig.platform.api.filter.PlatformCmOperationMainBodyFilter;
import com.isoftstone.hig.platform.api.filter.PlatformCmSensitiveWordFilter;
import com.isoftstone.hig.platform.api.filter.PlatformUmCompanyFilter;
import com.isoftstone.hig.platform.api.inter.*;
import com.isoftstone.hig.platform.api.mvcdto.PlatformPricingTypeEnumFilterDto;
import com.isoftstone.hig.platform.api.mvcvo.CompanyTaxRateVO;
import com.isoftstone.hig.platform.api.mvcvo.PlatformPricingTypeConfigVo;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.PlatformTaxRateFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 平台交换层
 */
@Slf4j
@Component
public class PlatformExchangeServiceImpl implements PlatformExchangeService {

    @Autowired
    private PlatformPricingTypeConfigInter platformPricingTypeConfigInter;
    @Autowired
    private PlatformCmOperationMainBodyInter platformCmOperationMainBodyInter;
    @Resource
    private PlatformCmPlatformParameterInter platformCmPlatformParameterInter;
    @Resource
    private PlatformUmCompanyInter platformUmCompanyInter;
    @Resource
    private PlatformUmUserbaseinfoInter platformUmUserbaseinfoInter;
    @Resource
    private PlatformCmDictionaryInter platformCmDictionaryInter;

    @Resource
    private PlatformFreightDiffRateConfigInter platformFreightDiffRateConfigInter;
    @Resource
    private PlatformUmDriverInter platformUmDriverInter;

    @Resource
    private PlatformCommonInterClient platformCommonInterClient;

    @Resource
    private PlatformCmSensitiveWordInter platformCmSensitiveWordInter;

    @Resource
    private PlatformUmLogininfoInter platformUmLogininfoInter;

    /**
     * 查询计价配置数据
     * @param status
     * @return
     */
    @LogPrintPoint(ret = true)
    @Override
    public List<PlatformPricingTypeConfigVo> queryPricingTypeConfigEnum(String status) {
        PlatformPricingTypeEnumFilterDto filterDto = new PlatformPricingTypeEnumFilterDto();
        filterDto.setEnableStatus(status);
        ResultMode<PlatformPricingTypeConfigVo> resultMode = platformPricingTypeConfigInter.queryPricingTypeConfigEnum(filterDto);
        if (Objects.isNull(resultMode) || !resultMode.isSucceed() || ObjectUtil.isEmpty(resultMode.getModel())) {
            return new ArrayList<>();
        }
        return resultMode.getModel();
    }

    /**
     * 计价类型map
     * @return
     */
    @LogPrintPoint(ret = true)
    @Override
    public Map<String, String> getPricingUnitsMap(String status) {
        //取配置数据
        List<PlatformPricingTypeConfigVo> platformPricingTypeConfigVos = this.queryPricingTypeConfigEnum(status);
        return platformPricingTypeConfigVos.stream()
            .collect(Collectors.toMap(PlatformPricingTypeConfigVo::getPricingType, PlatformPricingTypeConfigVo::getPricingTypeName));
    }

    /**
     * 数量取值方式
     */
    @Override
    public String getQuantityValueMethod(String pricingType) {
        //取配置数据
        List<PlatformPricingTypeConfigVo> platformPricingTypeConfigVos = this.queryPricingTypeConfigEnum(PlatformEnum.EnableStatusEnum.DEFAULT.getCode());
        return platformPricingTypeConfigVos.stream().filter(vo -> vo.getPricingType().equals(pricingType))
            .findFirst().get().getQuantityValueMethod();
    }

    @Override
    public BigDecimal getTaxRate(String freightType, String networkMainBodyId, String companyId) {
        //计算方式 传统货运取平台税率, 网络货运取网络货运主体运费留存率
        //货运类型:1-传统模式(默认)、 2-网络模式
        BigDecimal taxRate = new BigDecimal("0.00");
        if (StrUtil.equals(Constants.FREIGHT_TYPE_TRADITION, freightType)) {
            //传统货运 平台税率
            String paraId = "20191128200737100001";
            ResultMode<PlatformCmPlatformParameter> parameterResultMode = platformCmPlatformParameterInter.getPlatformParameterByParaId(paraId);
            if (!parameterResultMode.isSucceed() || IterUtil.isEmpty(parameterResultMode.getModel())) {
                log.info("没有获取到平台税率:" + paraId);
                return null;
            }
            taxRate = new BigDecimal(IterUtil.getFirst(parameterResultMode.getModel()).getParaValue());
        } else if (StrUtil.equals(Constants.FREIGHT_TYPE_NETWORK, freightType)) {
            //网络货运 留存运费率
            ResultMode<BigDecimal> taxRateResultMode = platformCmOperationMainBodyInter.getTaxRate(networkMainBodyId, companyId);
            if (!taxRateResultMode.isSucceed() || IterUtil.isEmpty(taxRateResultMode.getModel())) {
                log.info("没有获取到留存运费率:NetworkMainBodyId=" + networkMainBodyId + ",RealityShipperId=" + companyId);
                return null;
            }
            taxRate = IterUtil.getFirst(taxRateResultMode.getModel());
        }
        return taxRate;
    }

    /**
     * 查询参数值
     * @param paramCode
     * @return
     */
    @Override
    public String getParamValue(String paramCode) {
        if (StrUtil.isBlank(paramCode)) {
            return null;
        }
        ResultMode<PlatformCmPlatformParameter> resultMode = platformCmPlatformParameterInter.getByParaCodes(Arrays.asList(paramCode));
        if (ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())) {
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel()).getParaValue();
    }

    /**
     * 查询企业是否交易主体
     */
    @Override
    public HashMap<String, Boolean> getTransactionBody(List<String> companyIdList) {
        log.info("getTransactionBody#获取交易主体入参companyIdList:{}", JSONUtil.toJsonStr(companyIdList));
        if (IterUtil.isEmpty(companyIdList)) {
            return null;
        }

        String companyIds = IterUtil.join(companyIdList.iterator(), StrUtil.COMMA);
        List<PlatformCmOperationMainBody> list = platformCmOperationMainBodyInter.getMainBodyInfo(companyIds).getModel();
        log.info("获取交易主体返回信息:{}", JSONUtil.toJsonStr(list));
        if (IterUtil.isEmpty(list)) {
            return null;
        }
        HashMap<String, Boolean> map = new HashMap<>();
        list.stream().forEach(body -> {
            if (StrUtil.equalsAny(body.getType(), "1", "3")) {
                map.put(body.getCompanyId(), true);
            }
        });
        return map;
    }

    /**
     * 查询企业信息
     */
    @Override
    public List<PlatformUmCompany> getCompanyByIds(List<String> companyIds) {
        if (IterUtil.isEmpty(companyIds)) {
            return null;
        }
        PlatformUmCompanyFilter filter = new PlatformUmCompanyFilter();
        filter.setExCompanyIdList(companyIds);
        ResultMode<PlatformUmCompany> resultMode = platformUmCompanyInter.getCompanyByModel(filter);
        if (ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && IterUtil.isNotEmpty(resultMode.getModel())) {
            return resultMode.getModel();
        }

        return null;
    }


    /**
     * 查询企业信息
     * @param companyId
     * @return
     */
    @Override
    public PlatformUmCompany getCompanyByModel(String companyId) {
        if (StrUtil.isBlank(companyId)) {
            return new PlatformUmCompany();
        }
        PlatformUmCompanyFilter model = new PlatformUmCompanyFilter();
        model.setCompanyId(companyId);
        ResultMode<PlatformUmCompany> resultMode = platformUmCompanyInter.getCompanyByModel(model);
        if (ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())) {
            return new PlatformUmCompany();
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    /**
     * 获取用户基本信息
     * @param userBaseId
     * @return
     */
    @Override
    public PlatformUmUserbaseinfo getUserInfoByUserId(String userBaseId) {
        if (StrUtil.isBlank(userBaseId)) {
            return new PlatformUmUserbaseinfo();
        }
        ResultMode<PlatformUmUserbaseinfo> resultMode = platformUmUserbaseinfoInter.getUserInfoByUserId(userBaseId);
        if (ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())) {
            return new PlatformUmUserbaseinfo();
        }
        return resultMode.getModel().get(0);
    }

    /**
     * 根据颜色编号查询颜色名称
     */
    @Override
    public String getCarColorNameByColorCode(String carColor) {
        String parentDicId = "267";
        ResultMode<PlatformCmDictionary> dictionaryResultMode = platformCmDictionaryInter.getDictByParentIdAndEnumCode(parentDicId, carColor);
        if (dictionaryResultMode != null && !IterUtil.isEmpty(dictionaryResultMode.getModel())) {
            PlatformCmDictionary dictionary = IterUtil.getFirst(dictionaryResultMode.getModel());
            if (dictionary != null) {
                return dictionary.getName();
            }
        }
        return null;
    }

    /**
     * 获取取当前登录用户信息
     */
    @Override
    public PlatformUmUserbaseinfo getDriverUserInfoByUserBaseId() {
        try {
            ResultMode<PlatformUmUserbaseinfo> resultMode = platformUmUserbaseinfoInter.getDriverUserInfoByUserBaseId();
            if (ObjectUtil.isNotEmpty(resultMode) && IterUtil.isNotEmpty(resultMode.getModel())) {
                return resultMode.getModel().get(0);
            }
        } catch (Exception e) {
            log.error("getDriverUserInfoByUserBaseId#查询当前用户信息失败：", e);
        }
        return null;
    }

    /**
     * 获取司机的tokenInfo
     */
    @Override
    public TokenInfo getDriverTokenInfo() {
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        if (StrUtil.isBlank(tokenInfo.getDriverId())) {
            //刚刚实名认证之后没有刷新token，没有司机id，需要手动去查
            PlatformUmUserbaseinfo platformUmUserbaseinfo = this.getDriverUserInfoByUserBaseId();
            log.debug("获取司机信息:{}", JSONUtil.toJsonStr(platformUmUserbaseinfo));
            if (ObjUtil.isNull(platformUmUserbaseinfo)) {
                throw new LsdsWlydException("新注册的用户,请退出重新登录！！");
            }
            tokenInfo.setDriverId(platformUmUserbaseinfo.getExDriverId());
            tokenInfo.setUsername(platformUmUserbaseinfo.getUsername());
            tokenInfo.setClentId(platformUmUserbaseinfo.getClentId());
        }
        return tokenInfo;
    }


    /**
     * 查询网组题
     *
     * @param operationMainBodyId
     * @return
     */
    @Override
    public PlatformCmOperationMainBody getMainBody(String operationMainBodyId) {
        if (StrUtil.isBlank(operationMainBodyId)) {
            return null;
        }
        ResultMode<PlatformCmOperationMainBody> resultMode = platformCmOperationMainBodyInter.getMainBodyByMainId(operationMainBodyId);
        log.info("getMainBody#查询请求参数：{},查询结果:{}", operationMainBodyId, JSONUtil.toJsonStr(resultMode));
        if (ObjUtil.isNull(resultMode) || !resultMode.isSucceed()) {
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }


    /**
     * 查询业务类型、差价率和成本价
     */
    @LogPrintPoint(ret = true)
    @Override
    public BizExtendVO getTaxRate(PlatformTaxRateFilter filter) {
        CompanyTaxRateFilter companyTaxRateFilter = BeanUtil.toBean(filter, CompanyTaxRateFilter.class);
        ResultMode<CompanyTaxRateVO> resultMode = platformFreightDiffRateConfigInter.getTaxRate(companyTaxRateFilter);
        if (!resultMode.isSucceed()) {
            throw new LsdsWlydException(resultMode.getErrMsg());
        }
        if (IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }
        CompanyTaxRateVO companyTaxRateVO = IterUtil.getFirst(resultMode.getModel());
        BizExtendVO bizExtendVO = new BizExtendVO();
        bizExtendVO.setFreightDiffRate(companyTaxRateVO.getTaxRate());
        bizExtendVO.setCostPrice(companyTaxRateVO.getCostRate());
        bizExtendVO.setTrafficClassification(StrUtil.toStringOrNull(companyTaxRateVO.getBizType()));
        return bizExtendVO;
    }

    @Override
    public ResultMode<JSONObject> getPlatformUmDriverBindBankAndCar(String driverId) {
        PlatformUmDriver model = new PlatformUmDriver();
        model.setDriverId(driverId);
        return platformUmDriverInter.getPlatformUmDriverBindBankAndCar(model);
    }

    /**
     * 查询字典值
     * @param parentDicId
     * @return
     */
    @Override
    @LogPrintPoint
    public List<PlatformCmDictionary> getByParentDic(String parentDicId) {
        if(StrUtil.isBlank(parentDicId)){
            return new ArrayList<>();
        }
        ResultMode<PlatformCmDictionary> resultMode = platformCmDictionaryInter.getByParentDic(parentDicId);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            log.info("getByParentDic#查询请求参数：{}，结果：{}",parentDicId,JSONUtil.toJsonStr(resultMode));
            return new ArrayList<>();
        }
        return resultMode.getModel();
    }

    /**
     * 查询是否满足自动审核
     * @param map
     * @return
     */
    @Override
    @LogPrintPoint
    public Boolean judgeAutoAudit(Map<String, String> map) {
        if(CollUtil.isEmpty(map)){
            return false;
        }
        ResultMode<Boolean> resultMode = platformUmCompanyInter.judgeAutoAudit(map);
        if(ObjUtil.isNull(resultMode)){
            return null;
        }
        if(!resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            throw new LsdsWlydException(resultMode.getErrMsg());
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    /**
     * 查询主体信息
     * @param companyMainFilter
     * @return
     */
    @LogPrintPoint
    @Override
    public PlatformUmCompanyMain getCompanyMainModel(PlatformUmCompanyMain companyMainFilter) {
        if(ObjUtil.isNull(companyMainFilter)){
            return getDefaultValue();
        }
        ResultMode<PlatformUmCompanyMain> resultMode = platformUmCompanyInter.getCompanyMainModel(companyMainFilter);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            log.warn("getCompanyMainModel#查询结果：{}",JSONUtil.toJsonStr(resultMode));
            return getDefaultValue();
        }
        PlatformUmCompanyMain companyMain = IterUtil.getFirst(resultMode.getModel());

        String notNullFeeClearType = Opt.ofNullable(companyMain.getFeeClearType()).orElse("10");
        BigDecimal notNullFeeClearValue = Opt.ofNullable(companyMain.getFeeClearValue()).orElse(new BigDecimal(0));

        companyMain.setFeeClearType(notNullFeeClearType);
        companyMain.setFeeClearValue(notNullFeeClearValue);
        return companyMain;
    }

    private PlatformUmCompanyMain getDefaultValue() {
        PlatformUmCompanyMain main = new PlatformUmCompanyMain();
        main.setFeeClearType("10");
        main.setFeeClearValue(new BigDecimal(0));
        return main;
    }

    @Override
    @LogPrintPoint
    public List<PlatformUmCompanyMain> selectCompanyMains(String companyId,String type) {
        if(StrUtil.isBlank(companyId)){
            return null;
        }
        ResultMode<PlatformUmCompanyMain> resultMode = platformUmCompanyInter.selectPlatformUmCompanyMainsEnableByCompanyIdOrSocialCreditCode(companyId, "", type);
        if(ObjUtil.isNull(resultMode)){
            return null;
        }
        if(!resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            throw new LsdsWlydException(resultMode.getErrMsg());
        }
        return resultMode.getModel();
    }

    /**
     * 查询是否是交易签约主体
     * @param companyId
     * @return
     */
    @Override
    public boolean isTransactionBody(String companyId) {
        if(StrUtil.isBlank(companyId)){
            return false;
        }
        return platformCmOperationMainBodyInter.isTransactionBody(companyId);
    }

    @Override
    public boolean checkPlatformIs3PL(String companyId) {
        if(StrUtil.isBlank(companyId)){
            return false;
        }
        PlatformUmCompanyFilter companyFilter = new PlatformUmCompanyFilter();
        companyFilter.setCompanyId(companyId);
        ResultMode<PlatformUmCompany> resultMode = platformCommonInterClient.getCompanyAndAdminInfoByCompanyId(companyFilter);
        if (resultMode != null && !CollUtil.isEmpty(resultMode.getModel())){
            log.info("平台3PL标识PlatformFlag:{},调平台是否成功：{}",resultMode.getModel().get(0).getPlatformFlag(),resultMode.getSucceed());
            //平台3PL标识,0不是3PL 1是3PL
            return resultMode.getSucceed() && StrUtil.equals(resultMode.getModel().get(0).getPlatformFlag(),"1");
        }
        return false;
    }

    @LogPrintPoint
    @Override
    public List<PlatformCmOperationMainBody> listMainBodyPage(PagingInfo pagingInfo) {
        PlatformCmOperationMainBodyFilter filter = new PlatformCmOperationMainBodyFilter();
        filter.setType("1"); //交易签约主体类型 1
        pagingInfo.setFilterModel(filter);
        ResultMode<PlatformCmOperationMainBody> resultMode = platformCmOperationMainBodyInter.listMainBodyPage(pagingInfo);
        if(ObjUtil.isNull(resultMode)){
            return null;
        }
        if(!resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            throw new LsdsWlydException(resultMode.getErrMsg());
        }
        return resultMode.getModel();
    }

    /**
     * 查询所以主体信息
     * @return
     */
    @Override
    public List<PlatformCmOperationMainBody> getAllTransaction() {
        ResultMode<PlatformCmOperationMainBody> resultMode=platformCmOperationMainBodyInter.getAllTransaction();
        if(ObjUtil.isNull(resultMode)){
            return null;
        }
        if(!resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            throw new LsdsWlydException(resultMode.getErrMsg());
        }
        return resultMode.getModel();
    }

    /**
     * 查询参数值
     * @param paraCodesDefaultLoad
     * @return
     */
    @Override
    public List<PlatformCmPlatformParameter> getByParaCodes(List<String> paraCodesDefaultLoad) {
        ResultMode<PlatformCmPlatformParameter> resultMode =  platformCmPlatformParameterInter.getByParaCodes(paraCodesDefaultLoad);
        if(ObjUtil.isNull(resultMode)){
            return null;
        }
        if(!resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            throw new LsdsWlydException(resultMode.getErrMsg());
        }
        return resultMode.getModel();
    }

    /**
     * 查询所有敏感词
     * @return
     */
    @Override
    public List<PlatformCmSensitiveWord> getAllPlatformCmSensitiveWord() {
        ResultMode<PlatformCmSensitiveWord> resultMode = platformCmSensitiveWordInter.getAllPlatformCmSensitiveWord(new PlatformCmSensitiveWordFilter());
        if(ObjUtil.isNull(resultMode)){
            return null;
        }
        if(!resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            throw new LsdsWlydException(resultMode.getErrMsg());
        }

        return resultMode.getModel();
    }

    /**
     * 查询登录信息
     * @param userBaseId
     * @return
     */
    @Override
    public PlatformUmLogininfo getLoginInfoByUserBaseId(String userBaseId) {
        if(StrUtil.isBlank(userBaseId)){
            return null;
        }
        ResultMode<PlatformUmLogininfo> resultMode = platformUmLogininfoInter.getLoginInfoByUserBaseId(userBaseId);
        if(ObjUtil.isNull(resultMode)){
            return null;
        }
        if(!resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            throw new LsdsWlydException(resultMode.getErrMsg());
        }

        return IterUtil.getFirst(resultMode.getModel());
    }


}
