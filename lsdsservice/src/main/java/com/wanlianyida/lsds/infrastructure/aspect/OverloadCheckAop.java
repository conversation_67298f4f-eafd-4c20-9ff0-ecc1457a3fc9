package com.wanlianyida.lsds.infrastructure.aspect;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.exception.WlydException;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.DriverOffer;
import com.isoftstone.hig.lsds.api.filter.DriverOfferFilter;
import com.isoftstone.hig.platform.api.inter.PlatformCmOperationMainBodyInter;
import com.isoftstone.hig.platform.api.mvcvo.SettlementVerificationVo;
import com.isoftstone.hig.tms.api.entity.TmsEnum;
import com.wanlianyida.lsds.application.service.DriverGoodsAppService;
import com.wanlianyida.lsds.application.service.DriverOfferAppService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Aspect
@Component
@Slf4j
public class OverloadCheckAop {

    @Autowired
    PlatformCmOperationMainBodyInter platformCmOperationMainBodyInter;
    @Autowired
    DriverGoodsAppService driverGoodsService;
    @Autowired
    DriverOfferAppService driverOfferService;

    @Pointcut("@annotation(com.isoftstone.hig.annotations.OverloadCheck)")
    public void asAnnotation() {
    }

    // 方法用途:在AnnotationDemo注解之前执行，标识一个前置增强方法，相当于BeforeAdvice的功能
    @Before("asAnnotation()")
    public void beforeRun(JoinPoint joinPoint) throws Exception {
        String name = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        log.info("OverloadCheckAop#方法名:{}", name);
        log.info("OverloadCheckAop#传入参数:{}", JSON.toJSONString(args));

        DriverOfferFilter offerFilter = (DriverOfferFilter) args[0];
        DriverGoods driverGoods = driverGoodsService.getByGoodsId(offerFilter.getGoodsId());
        List<SettlementVerificationVo> reqlist = new ArrayList<>();
        SettlementVerificationVo vo;
        String endCueWords = "请修正";
        switch (name) {
            // 司机货源报价（司机）、司机货源接单（司机）
            case "sourceQuotation":
            case "goodsQuote":
            case "orderReceiving":
                vo = new SettlementVerificationVo();
                vo.setCarPlateNo(offerFilter.getTractorPlateNumber());
                vo.setCarColor(offerFilter.getTractorPlateColor());
                vo.setCarPlateNoG(offerFilter.getTrailerPlateNumber());
                vo.setCarColorG(offerFilter.getTrailerPlateColor());
                vo.setFreightType(driverGoods.getFreightType());
                vo.setOperationMainBodyId(driverGoods.getNetworkMainBodyId());
                vo.setWeightType("10");
                vo.setSettlementWeight(offerFilter.getReceivingOrdersWeight().multiply(new BigDecimal("1000")));
                vo.setWaybillId(offerFilter.getGoodsId());

                reqlist.add(vo);
                break;
            // 短倒再来一单、
            case "placeOrder":
                if (!"112".equals(driverGoods.getTransportationType())) {
                    // 司机货源派单（货主），报价时校验
                    log.info("OverloadCheckAop#不校验:{}", JSON.toJSONString(offerFilter));
                    return;
                }
                DriverOfferFilter driverOfferFilter = new DriverOfferFilter();
                driverOfferFilter.setGoodsId(offerFilter.getGoodsId());
                driverOfferFilter.setDriverId(offerFilter.getDriverId());
                // 根据货源号查询报价单数据
                DriverOffer offer = driverOfferService.listByEntity(driverOfferFilter).get(0);

                vo = new SettlementVerificationVo();
                vo.setCarPlateNo(offer.getTractorPlateNumber());
                vo.setCarPlateNoG(offer.getTrailerPlateNumber());
                vo.setCarColor(offerFilter.getTractorPlateColor());
                vo.setCarColorG(offerFilter.getTrailerPlateColor());
                vo.setFreightType(driverGoods.getFreightType());
                vo.setOperationMainBodyId(driverGoods.getNetworkMainBodyId());
                vo.setWeightType("10");
                vo.setSettlementWeight(offer.getReceivingOrdersWeight().multiply(new BigDecimal("1000")));
                vo.setWaybillId(offer.getGoodsId());
                reqlist.add(vo);
                endCueWords = "请重新接单/派单";
                break;
            default:
                throw new WlydException(">>>>>校验超载方法错误！>>>>>>>>>>>>>>>>");
        }
        log.info("OverloadCheckAop#请求超载参数:{}", JSONUtil.toJsonStr(reqlist));
        ResultMode<SettlementVerificationVo> resultMode = platformCmOperationMainBodyInter.checkOverload(reqlist);
        log.info("OverloadCheckAop#请求超载返回结果:{}", JSONUtil.toJsonStr(resultMode));
        if (!resultMode.isSucceed()) {
            log.info("OverloadCheckAop#超载校验失败");
            throw new WlydException("超载校验失败！");
        }
        List<SettlementVerificationVo> settlementVerificationVos = resultMode.getModel();
        String promptWords = assemblePromptWords(settlementVerificationVos, endCueWords);
        if (StrUtil.isNotEmpty(promptWords)) {
            throw new WlydException(promptWords);
        }
    }


    private String assemblePromptWords(List<SettlementVerificationVo> verificationVoList, String endCueWords) {
        if (IterUtil.isEmpty(verificationVoList)) {
            return null;
        }
        String promptWords = verificationVoList.stream().filter(item -> item.getOverloadFlag()).
            map(item -> {
                if (!StrUtil.isEmpty(item.getCarPlateNoG())) {
                    //有挂车：京A33345(黄) | 京A3444挂(黄)最大允许载重55吨，您已超载2(吨)，请修正。
                    return String.format("%s(%s) | %s(%s)最大允许载重%s吨，您已超载%s(吨)，%s。",
                        item.getCarPlateNo(),
                        TmsEnum.CarColorTypeEnum.getValueBykey(item.getCarColor()),
                        item.getCarPlateNoG(),
                        TmsEnum.CarColorTypeEnum.getValueBykey(item.getCarColorG()),
                        item.getAllowableLoad().stripTrailingZeros().toPlainString(),
                        item.getOverloadSettlementWeight().stripTrailingZeros().toPlainString(),
                        endCueWords);
                } else {
                    //无挂车：京A33345(黄)最大允许载重36吨，您已超载3(吨)，请修正。
                    return String.format("%s(%s)最大允许载重%s吨，您已超载%s(吨)，%s。",
                        item.getCarPlateNo(),
                        TmsEnum.CarColorTypeEnum.getValueBykey(item.getCarColor()),
                        item.getAllowableLoad().stripTrailingZeros().toPlainString(),
                        item.getOverloadSettlementWeight().stripTrailingZeros().toPlainString(),
                        endCueWords);
                }
            }).
            collect(Collectors.joining(""));
        return promptWords;
    }
}
