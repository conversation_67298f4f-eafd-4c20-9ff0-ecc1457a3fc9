package com.wanlianyida.lsds.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.wanlianyida.lsds.domain.model.bo.GoodsSourceListBO;
import com.wanlianyida.lsds.domain.model.condition.GoodsSourceListCondition;
import com.wanlianyida.lsds.domain.model.entity.GoodsAddressEntity;
import com.wanlianyida.lsds.domain.model.entity.GoodsAssignSupplierEntity;
import com.wanlianyida.lsds.domain.model.entity.GoodsExtendEntity;
import com.wanlianyida.lsds.domain.model.entity.IdentifyCodeRelEntity;
import com.wanlianyida.lsds.domain.repository.GoodsListRepository;
import com.wanlianyida.lsds.infrastructure.enums.GoodsPublishTypeEnum;
import com.wanlianyida.lsds.infrastructure.repository.mapper.*;
import com.wanlianyida.lsds.infrastructure.repository.po.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 货源
 */
@Slf4j
@Component
public class GoodsListRepositoryImpl implements GoodsListRepository {

    @Resource
    private GoodsSourceMapper goodsSourceMapper;

    @Resource
    private DriverGoodsExtendMapper goodsExtendMapper;

    @Resource
    private GoodsAssignSupplierMapper supplierMapper;

    @Resource
    private GoodsAddressMapper goodsAddressMapper;

    @Resource
    private IdentifyCodeRelMapper identifyCodeRelMapper;

    /**
     * 获取货源列表
     */
    @Override
    public List<GoodsSourceListBO> queryList(PagingInfo<GoodsSourceListCondition> pageInfo) {
        List<GoodsSourcePO> goodsSourcePOS = goodsSourceMapper.queryList(pageInfo);
        if (CollUtil.isEmpty(goodsSourcePOS)){
            return Collections.emptyList();
        }
        //获取货源id集合
        List<String> goodsIds = goodsSourcePOS.stream().map(GoodsSourcePO::getGoodsId).collect(Collectors.toList());
        //获取货源扩展表信息
        LambdaQueryWrapper<DriverGoodsExtendPO> driverGoodsExtendWrapper = Wrappers.lambdaQuery();
        driverGoodsExtendWrapper.in(DriverGoodsExtendPO::getGoodsId, goodsIds);
        List<DriverGoodsExtendPO> driverGoodsExtendPOS = goodsExtendMapper.selectList(driverGoodsExtendWrapper);
        //将driverGoodsExtendPOS转换成map,key是goodsId,value是DriverGoodsExtendPO对象
        Map<String, DriverGoodsExtendPO> driverGoodsExtendMap = driverGoodsExtendPOS.stream().collect(Collectors.toMap(DriverGoodsExtendPO::getGoodsId, driverGoodsExtendPO -> driverGoodsExtendPO));
        //获取地址信息
        List<String> goodsAddressIds = goodsSourcePOS.stream().map(GoodsSourcePO::getGoodsAddressId).collect(Collectors.toList());
        LambdaQueryWrapper<GoodsAddressPO> goodsAddressWrapper = Wrappers.lambdaQuery();
        goodsAddressWrapper.in(GoodsAddressPO::getGoodsAddressId, goodsAddressIds);
        List<GoodsAddressPO> goodsAddressPOS = goodsAddressMapper.selectList(goodsAddressWrapper);
        //转换成map
        Map<String, GoodsAddressPO> goodsAddressMap = goodsAddressPOS.stream().collect(Collectors.toMap(GoodsAddressPO::getGoodsAddressId, goodsAddressPO -> goodsAddressPO));
        //获取货物指定物流供应商管理表
        LambdaQueryWrapper<GoodsAssignSupplierPO> goodsAssignSupplierWrapper = Wrappers.lambdaQuery();
        goodsAssignSupplierWrapper.in(GoodsAssignSupplierPO::getGoodsId, goodsIds);
        List<GoodsAssignSupplierPO> goodsAssignSupplierPOS = supplierMapper.selectList(goodsAssignSupplierWrapper);
        //将goodsAssignSupplierPOS转换成map,key是goodsId,value是GoodsAssignSupplierPO对象
        Map<String, List<GoodsAssignSupplierPO>> goodsAssignSupplierMap = goodsAssignSupplierPOS.stream().collect(Collectors.groupingBy(GoodsAssignSupplierPO::getGoodsId));

        Map<String, List<IdentifyCodeRelEntity>> IdentifyCodeRelMap = new HashMap<>();
        //先获取发布主体类型是司机货源的货源单ID
        List<String> driverGoodsIds = goodsSourcePOS.stream().filter(goodsSourcePO -> GoodsPublishTypeEnum.DRIVER.getType().equals(goodsSourcePO.getPublisherType())).map(GoodsSourcePO::getGoodsId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(driverGoodsIds)) {
            //查询货源码、收款码
            List<IdentifyCodeRelEntity> identifyCodeRelList = queryBindIdentifyCodeRel(driverGoodsIds, JwtUtil.getInstance().getCompanyIdByToken());
            if (IterUtil.isNotEmpty(identifyCodeRelList)) {
                IdentifyCodeRelMap = identifyCodeRelList.stream().collect(Collectors.groupingBy(IdentifyCodeRelEntity::getGoodsId));
            }
        }
        //再获取发布主体类型是企业货源的货源单ID
        List<GoodsSourceListBO> goodsSourceListBOS = BeanUtil.copyToList(goodsSourcePOS, GoodsSourceListBO.class);
        //讲三个map填充到GoodsSourceListBO属性中
        Map<String, List<IdentifyCodeRelEntity>> finalIdentifyCodeRelMap = IdentifyCodeRelMap;
        goodsSourceListBOS.forEach(goodsSourceListBO -> {
            //对上述三个属性赋值进行优化,如果不为null进行转换
            if (driverGoodsExtendMap.get(goodsSourceListBO.getGoodsId()) != null) {
                goodsSourceListBO.setGoodsExtendEntity(BeanUtil.copyProperties(driverGoodsExtendMap.get(goodsSourceListBO.getGoodsId()), GoodsExtendEntity.class));
            }
            if (goodsAssignSupplierMap.get(goodsSourceListBO.getGoodsId()) != null) {
                goodsSourceListBO.setGoodsAssignSupplierEntityList(BeanUtil.copyToList(goodsAssignSupplierMap.get(goodsSourceListBO.getGoodsId()), GoodsAssignSupplierEntity.class));
            }
            if (goodsAddressMap.get(goodsSourceListBO.getGoodsAddressId()) != null) {
                goodsSourceListBO.setGoodsAddressEntity(BeanUtil.copyProperties(goodsAddressMap.get(goodsSourceListBO.getGoodsAddressId()), GoodsAddressEntity.class));
            }
            //货源码、收款码
            if (MapUtil.isNotEmpty(finalIdentifyCodeRelMap) && finalIdentifyCodeRelMap.get(goodsSourceListBO.getGoodsId()) != null) {
                goodsSourceListBO.setQrsCodeRelations(finalIdentifyCodeRelMap.get(goodsSourceListBO.getGoodsId()));
            }
        });
        return goodsSourceListBOS;
    }

    /**
     * 根据货源号查询 货源码与收款码
     */
    public List<IdentifyCodeRelEntity> queryBindIdentifyCodeRel(List<String> goodsIdList, String companyId) {
        if (IterUtil.isEmpty(goodsIdList)) {
            return null;
        }

        LambdaQueryWrapper<IdentifyCodeRelPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(IdentifyCodeRelPO::getGoodsId, goodsIdList);
        queryWrapper.eq(StrUtil.isNotBlank(companyId), IdentifyCodeRelPO::getCompanyId, companyId);
        queryWrapper.eq(IdentifyCodeRelPO::getDelFlag, "11");
        return BeanUtil.copyToList(identifyCodeRelMapper.selectList(queryWrapper), IdentifyCodeRelEntity.class);
    }

    /**
     * 获取货源总数
     */
    @Override
    public int queryCount(PagingInfo<GoodsSourceListCondition> pageInfo) {
        return goodsSourceMapper.queryCount(pageInfo);
    }

}
