package com.wanlianyida.lsds.infrastructure.exchange.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.ims.api.command.GoodsGuaranteeRegistrationCommand;
import com.isoftstone.hig.ims.api.command.GuaranteeRegistrationCheckCommand;
import com.isoftstone.hig.ims.api.constants.ImsEnums;
import com.isoftstone.hig.ims.api.entity.GuaranteeRegistrationStatusVo;
import com.isoftstone.hig.ims.api.entity.GuaranteeRegistrationVo;
import com.isoftstone.hig.ims.api.inter.ImsGuaranteeRegistrationInter;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.wanlianyida.lsds.domain.model.entity.InsuranceEntity;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.exchange.ImsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * ims保险服务
 */
@Slf4j
@Service
public class ImsExchangeServiceImpl implements ImsExchangeService {


    @Resource
    private ImsGuaranteeRegistrationInter imsGuaranteeRegistrationInter;

    /**
     * 保险登记
     */
    @Override
    public void guaranteeRegistration(InsuranceEntity insurance) {

        //检查是否可以购买保险服务
        if (!checkRegistration(insurance.getBuyGuarantee(), insurance.getGoodsId())) {
            log.debug("保险登记：无意向/不能购买保险保障服务");
            return;
        }

        //组装保险登记
        GoodsGuaranteeRegistrationCommand command = getImsRegistration(insurance);
        log.info("保险登记入参：{}", JSON.toJSONString(command));
        ResultMode<GuaranteeRegistrationVo> registerResultMode = imsGuaranteeRegistrationInter.goodsGuaranteeRegistration(command);
        log.info("保险登记结果：{}", JSON.toJSONString(registerResultMode));
        if (ObjectUtil.isEmpty(registerResultMode)) {
            String errMsg = "保险登记请求异常";
            throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_BCOM0400, errMsg);
        }
        if (!registerResultMode.isSucceed()) {
            throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_BCOM0500, registerResultMode.getErrMsg());
        }
    }

    /**
     * 检查是否可以购买保险服务
     *
     * @param buyGuarantee
     * @return
     */
    private Boolean checkRegistration(String buyGuarantee, String goodsId) {
        if (!StrUtil.equals(buyGuarantee, ImsEnums.BuyGuarantee.TRUE.getCode())) {
            log.info("保险登记：无意向购买保险保障服务");
            return false;
        }
        //检查是否可以购买保险服务
        GuaranteeRegistrationStatusVo guaranteeRegistrationStatus = getGuaranteeRegistrationStatus(goodsId);
        if (!StrUtil.equals(guaranteeRegistrationStatus.getStatus(), ImsEnums.GuaranteeRegistrationEnum.AVAILABLE.getCode())) {
            log.info("保险登记：无法购买保险保障服务");
            return false;
        }
        return true;
    }


    /**
     * 组装保险登记
     */
    private GoodsGuaranteeRegistrationCommand getImsRegistration(InsuranceEntity insurance) {
        GoodsGuaranteeRegistrationCommand command = BeanUtil.toBean(insurance, GoodsGuaranteeRegistrationCommand.class);
        //业务批次号类型（区分订单、货源码、绑定关系id）
        command.setNumberType(ImsEnums.BatchNumberTypeEnum.GOODS.getType());
        //业务批次号
        command.setBizId(insurance.getGoodsId());
        //总数量
        command.setPlanQuantity(insurance.getTotalQuantity());
        //总数量单位 10.吨 20.车 30.柜
        command.setQuantityUnits(insurance.getTotalQuantityUnits());
        //起始地省名称
        command.setStartProvince(insurance.getSendAddrProvinceName());
        //起始地省编码
        command.setStartProvinceCode(insurance.getSendAddrProvince());
        //目的地省名称
        command.setEndProvince(insurance.getReceiveAddrProvinceName());
        //目的地省编码
        command.setEndProvinceCode(insurance.getReceiveAddrProvince());

        if (Constants.GOODS_STATUS_NOT_RELEASED.equals(insurance.getDealStatus())) {
            command.setIsEffective(ImsEnums.EffectiveStatusEnum.NONEFFECTIVE.getCode());
        } else {
            command.setIsEffective(ImsEnums.EffectiveStatusEnum.EFFECTIVE.getCode());
        }
        return command;
    }

    /**
     * 检查是否可以购买保险服务
     *
     * @param goodsId
     * @return
     */
    private GuaranteeRegistrationStatusVo getGuaranteeRegistrationStatus(String goodsId) {
        GuaranteeRegistrationCheckCommand registrationFilter = new GuaranteeRegistrationCheckCommand();
        registrationFilter.setBizId(goodsId);
        registrationFilter.setNumberType(ImsEnums.BatchNumberTypeEnum.GOODS.getType());
        //发布货源无需校验剩余数量
        registrationFilter.setSendCar(Boolean.TRUE);
        ResultMode<GuaranteeRegistrationStatusVo> checkRegistration = imsGuaranteeRegistrationInter.checkRegistration(registrationFilter);
        log.info("DriverGoodsService#getGuaranteeRegistrationStatus--->检查是否可以购买保险服务：{}", JSONUtil.toJsonStr(checkRegistration));
        if (ObjectUtil.isEmpty(checkRegistration)) {
            String errMsg = "检查是否可以购买保险服务请求异常";
            throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_BCOM0400, errMsg);
        }
        if (!checkRegistration.isSucceed() || IterUtil.isEmpty(checkRegistration.getModel())) {
            throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_BCOM0500, checkRegistration.getErrMsg());
        }
        GuaranteeRegistrationStatusVo guaranteeRegistrationStatus = IterUtil.getFirst(checkRegistration.getModel());
        return guaranteeRegistrationStatus;
    }
}
