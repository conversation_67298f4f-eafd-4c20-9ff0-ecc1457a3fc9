package com.wanlianyida.lsds.infrastructure.exchange.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.qrs.api.filter.QrsInvitationIdentifyCodeFilter;
import com.isoftstone.hig.qrs.api.inter.QrsInvitationIdentifyCodeInter;
import com.isoftstone.hig.qrs.api.vo.QrsGoodsIdentifyCodeVO;
import com.isoftstone.hig.qrs.api.vo.QrsInvitationIdentifyCodeVO;
import com.wanlianyida.lsds.infrastructure.exchange.QrsExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.QrsGoodsIdentifyCodeBO;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.QrsInvitationIdentifyCodeBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 线路主数据信息交换服务
 *
 * <AUTHOR>
 * @since 20231221
 */
@Slf4j
@Service
public class QrsExchangeServiceImpl implements QrsExchangeService {

    @Resource
    private QrsInvitationIdentifyCodeInter qrsInvitationIdentifyCodeInter;

    @Override
    public QrsInvitationIdentifyCodeBO getByCompanyId(String companyId) {
        QrsInvitationIdentifyCodeFilter filter = new QrsInvitationIdentifyCodeFilter();
        if (StrUtil.isEmpty(companyId)){
            log.error("调用qrs服务:查询业务邀请码列表 参数异常");
            return null;
        }
        filter.setBusId(companyId);
        filter.setInvitationSource("10");
        ResultMode<QrsInvitationIdentifyCodeVO> resultMode = qrsInvitationIdentifyCodeInter.queryList(filter);
        if (resultMode == null || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }
        return BeanUtil.toBean(IterUtil.getFirst(resultMode.getModel()), QrsInvitationIdentifyCodeBO.class, CopyOptions.create().ignoreNullValue().ignoreError());
    }

    @Override
    public QrsGoodsIdentifyCodeBO queryGoodsIdentifyCodeDetail(Long id) {
        if (id == null) {
            log.error("调用qrs服务:查询货源码详情 参数异常");
            return null;
        }
        ResultMode<QrsGoodsIdentifyCodeVO> resultMode = null;
        if (resultMode == null || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }
        return BeanUtil.toBean(IterUtil.getFirst(resultMode.getModel()), QrsGoodsIdentifyCodeBO.class, CopyOptions.create().ignoreNullValue().ignoreError());
    }
}
