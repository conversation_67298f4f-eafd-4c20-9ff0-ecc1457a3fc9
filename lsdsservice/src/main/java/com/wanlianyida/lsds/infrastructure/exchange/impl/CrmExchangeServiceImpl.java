package com.wanlianyida.lsds.infrastructure.exchange.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.crm.api.entity.CrmCompanyLineAddress;
import com.isoftstone.hig.crm.api.filter.CrmCompanyLineAddressFilter;
import com.isoftstone.hig.crm.api.filter.CrmCompanyLineFilter;
import com.isoftstone.hig.crm.api.inter.CrmCompanyLineAddressInter;
import com.wanlianyida.lsds.infrastructure.exchange.CrmExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.CrmCompanyLineAddressBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 线路主数据信息交换服务
 *
 * <AUTHOR>
 * @since 20231221
 */
@Slf4j
@Service
public class CrmExchangeServiceImpl implements CrmExchangeService {

    @Resource
    private CrmCompanyLineAddressInter crmCompanyLineAddressInter;

    @Override
    public CrmCompanyLineAddressBO getCrmCompanyLine(String lineId){
        if(StrUtil.isEmpty(lineId)){
            return null;
        }
        CrmCompanyLineAddressFilter filter = new CrmCompanyLineAddressFilter();
        filter.setLineId(lineId);
        ResultMode<CrmCompanyLineAddress> lineAddressModel = crmCompanyLineAddressInter.getLineAddressByLineId(filter);
        if(lineAddressModel==null || IterUtil.isEmpty(lineAddressModel.getModel())){
            return null;
        }
        CrmCompanyLineAddress crmCompanyLineAddress = IterUtil.getFirst(lineAddressModel.getModel());
        CrmCompanyLineAddressBO exchangeData = BeanUtil.toBean(crmCompanyLineAddress, CrmCompanyLineAddressBO.class, CopyOptions.create().ignoreNullValue().ignoreError());
        return exchangeData;
    }

    @Override
    public ResultMode createCrmCompanyLine(String lineShortname, String sendAddrId, String receiveAddrId, String lineSource, String companyId) {
        //根据地址判断线路是否存在,存在返回，不存在创建线路
        CrmCompanyLineFilter filter = new CrmCompanyLineFilter();
        filter.setLineShortName(lineShortname);
        filter.setSendAddrId(sendAddrId);
        filter.setReceiveAddrId(receiveAddrId);
        filter.setLineSource(lineSource);
        filter.setCompanyId(companyId);
        return crmCompanyLineAddressInter.crmCompanyLineAdd(filter);
    }
}
