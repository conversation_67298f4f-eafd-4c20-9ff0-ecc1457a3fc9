package com.wanlianyida.lsds.infrastructure.exchange;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.woa.api.vo.WoaLogisticsPlanVO;
import com.isoftstone.hig.woa.api.vo.WoaOfferPlanVO;
import com.isoftstone.hig.woa.api.vo.WoaWaybillClueCntVO;
import com.isoftstone.hig.woa.api.vo.WoaWaybillClueVO;
import com.isoftstone.hig.woa.filter.WoaWaybillClueFilter;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.WoaLogisticsPlanBO;

import java.util.List;

public interface WoaExchangeService {

    List<WoaLogisticsPlanBO> selectByGoodsIds(List<String> goodsIds);

    WoaLogisticsPlanVO getLogisticsPlanByPlanId(Long planId);

    void goodsIdWriteBack(Long planId, String goodsId);

    WoaOfferPlanVO getOfferPlanByPlanId(Long offerPlanId);

    void goodsIdWriteBackToOfferPlan(Long offerPlanId, String goodsId, String goodsType);

    /**
     * 司机端意向货源分页查询（首页、货源大厅）
     * 基于身份证号
     * 仅7天有效（当前时间小于等于【接单时间+7*24小时】时，展示；当前时间大于【接单时间+7*24小时】时，不展示）
     *
     * @param pagingInfo 分页查询参数
     * @return {@link ResultMode}<{@link WoaWaybillClueVO}>
     */
    List<WoaWaybillClueVO> appChannelQueryPage(PagingInfo<WoaWaybillClueFilter> pagingInfo);

    /**
     * 推荐运力增值服务统计
     *
     * @param bizIds
     * @return {@link List }<{@link WoaWaybillClueCntVO }>
     */
    List<WoaWaybillClueCntVO> cntByBizIds(List<String> bizIds);
}
