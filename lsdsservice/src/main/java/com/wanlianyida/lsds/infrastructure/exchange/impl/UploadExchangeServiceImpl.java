package com.wanlianyida.lsds.infrastructure.exchange.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.upload.api.inter.FileUploadInter;
import com.wanlianyida.lsds.infrastructure.exchange.UploadExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * upload服务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UploadExchangeServiceImpl implements UploadExchangeService {

    @Resource
    private FileUploadInter uploadInter;

    /**
     * 转化为可访问的url
     * @param urls
     * @return
     */
    public Map<String, String> convertUrl(List<String> urls) {
        if (IterUtil.isEmpty(urls)) {
            return new HashMap<>();
        }
        return uploadInter.convertUrlNew(urls);
    }

    /**
     * 下载文件
     * @param path
     * @return
     */
    @Override
    public byte[] downloadFile(String path) {
        if(StrUtil.isBlank(path)){
            return null;
        }
        return uploadInter.downloadFile(path);
    }

    @Override
    public String uploadWithSubType(MultipartFile file, String attachmentEnum, String subType) {
        ResultMode<String> resultMode = uploadInter.uploadWithSubType(file, attachmentEnum, subType);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            return null;
        }
        return resultMode.getModel().get(0);
    }


}
