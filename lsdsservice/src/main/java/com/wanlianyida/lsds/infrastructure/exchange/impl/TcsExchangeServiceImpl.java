package com.wanlianyida.lsds.infrastructure.exchange.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.dto.PlateNoAndColorDTO;
import com.isoftstone.hig.lsds.api.dto.TcsQueryCarDTO;
import com.isoftstone.hig.lsds.api.entity.TcsCar;
import com.isoftstone.hig.tcs.api.filter.TcsCarFilter;
import com.isoftstone.hig.tcs.api.filter.TcsCarPlateNoAndColorFilter;
import com.isoftstone.hig.tcs.api.filter.TcsDriverFilter;
import com.isoftstone.hig.tcs.api.inter.TcsCarBusinessInter;
import com.isoftstone.hig.tcs.api.inter.TcsDriverBusinessInter;
import com.isoftstone.hig.tcs.api.mvcvo.TcsCarStatusVO;
import com.isoftstone.hig.tcs.api.mvcvo.TcsCarTractorVO;
import com.isoftstone.hig.tcs.api.mvcvo.TcsCarVO;
import com.isoftstone.hig.tcs.api.mvcvo.TcsDriverVO;
import com.wanlianyida.lsds.infrastructure.exchange.TcsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * TCS运力服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class TcsExchangeServiceImpl implements TcsExchangeService {
    @Resource
    private TcsCarBusinessInter tcsCarBusinessInter;
    @Resource
    private TcsDriverBusinessInter tcsDriverBusinessInter;

    /**
     * 根据车牌号获取车牌颜色
     */
    @Override
    public Integer getCarColor(String plateNumber) {
        if (StrUtil.isEmpty(plateNumber)) {
            return null;
        }
        TcsCarFilter query = new TcsCarFilter();
        query.setPlateNo(plateNumber);
        ResultMode<TcsCarVO> cmdCarResultMode = tcsCarBusinessInter.queryBasicInfoByFilter(query);
        log.info("查询cmd车辆:plateNumber={},result={}", plateNumber, JSONUtil.toJsonStr(cmdCarResultMode));
        if (cmdCarResultMode != null && !IterUtil.isEmpty(cmdCarResultMode.getModel())) {
            TcsCarVO cmdCar = IterUtil.getFirst(cmdCarResultMode.getModel());
            return cmdCar.getPlateColor();
        }
        return null;
    }


    /**
     * 根据id查询车辆信息
     */
    @Override
    public String getCarStatus(String plateNumber, String carColor) {
        if (StrUtil.isEmpty(plateNumber) || StrUtil.isEmpty(carColor)) {
            return null;
        }
        TcsCarPlateNoAndColorFilter query = new TcsCarPlateNoAndColorFilter();
        query.setPlateNo(plateNumber);
        query.setPlateColor(Integer.valueOf(carColor));
        ResultMode<TcsCarStatusVO> resultMode = tcsCarBusinessInter.queryDriverCarStatusByFilter(query);
        if (ObjUtil.isNotNull(resultMode) && resultMode.getSucceed() && IterUtil.isNotEmpty(resultMode.getModel())) {
            TcsCarStatusVO car = IterUtil.getFirst(resultMode.getModel());
            return car.getCarStatus();
        }

        return null;
    }

    @Override
    public Map<String, TcsCarTractorVO> queryCarByPlateNoAndColors(List<TcsCarPlateNoAndColorFilter> query) {
        log.info("tcsCarBusinessInter.queryCarAndTrailerByFilter请求参数:{}", JSON.toJSONString(query));
        ResultMode<TcsCarTractorVO> resultMode = tcsCarBusinessInter.queryCarAndTrailerByFilter(query);
        log.info("tcsCarBusinessInter.queryCarAndTrailerByFilter返回参数:{}",JSON.toJSONString(resultMode));
        if (resultMode != null && !IterUtil.isEmpty(resultMode.getModel())) {
            return resultMode.getModel().stream().collect(Collectors.toMap(item -> item.getPlateNo() + item.getPlateColor(), Function.identity()));
        }
        return MapUtil.empty();
    }


    @Override
    public Map<String, List<TcsCar>> queryCurrentDriverCar() {
        ResultMode<TcsCarVO> resultMode = tcsCarBusinessInter.queryCurrentDriverCar();
        if (ObjUtil.isNull(resultMode) || !resultMode.isSucceed()) {
            return MapUtil.empty();
        }
        return resultMode.getModel().stream().map(data -> {
            TcsCar car = new TcsCar();
            car.setCarPlateNo(data.getPlateNo());
            car.setCarColor(data.getPlateColor());
            car.setCarType(data.getCarType());
            car.setTrailerType(data.getTrailerType());
            return car;
        }).collect(Collectors.groupingBy(TcsCar::getTrailerType));
    }

    /**
     * 根据身份证号反查driverId
     */
    @Override
    public String getDriverIdByIdCardNo(String idCardNo) {
        if (StrUtil.isBlank(idCardNo)) {
            return null;
        }
        ResultMode<TcsDriverVO> resultMode = tcsDriverBusinessInter.queryBasicInfoByLicenseNo(idCardNo);
        if (null != resultMode && resultMode.isSucceed() && IterUtil.isNotEmpty(resultMode.getModel())) {
            TcsDriverVO dto = IterUtil.getFirst(resultMode.getModel());
            return dto.getDriverId();
        }
        return null;
    }


    @Override
    public TcsDriverVO getDriverByDriverId(String driverId) {
        TcsDriverFilter query = new TcsDriverFilter();
        query.setDriverCode(driverId);
        ResultMode<TcsDriverVO> driverResultMode = tcsDriverBusinessInter.queryBasicInfoByDriverCode(driverId);
        if (driverResultMode.getSucceed() && driverResultMode.getModel() != null && driverResultMode.getModel().size() > 0) {
            return driverResultMode.getModel().get(0);
        }
        return null;
    }

    @Override
    public List<TcsDriverVO> queryDriverByDriverIds(List<String> driverIds) {
        TcsDriverFilter filter = new TcsDriverFilter();
        filter.setDriverCodeList(driverIds);
        ResultMode<TcsDriverVO> resultMode = tcsDriverBusinessInter.queryBasicListByDriverCodeList(filter);
        if (ObjUtil.isNull(resultMode) || !resultMode.isSucceed()) {
            return Collections.emptyList();
        }
        return resultMode.getModel();
    }

    @Override
    public Map<String, TcsDriverVO> getDriverMapByDriverIds(List<String> driverIds) {
        List<TcsDriverVO> driverList = queryDriverByDriverIds(driverIds);
        if (CollUtil.isEmpty(driverList)) {
            return MapUtil.empty();
        }
        return driverList.stream().collect(Collectors.toMap(TcsDriverVO::getDriverCode, Function.identity()));
    }

    @Override
    public List<TcsQueryCarDTO> queryBasicInfoByFilter(List<PlateNoAndColorDTO> plateNoAndColorDTOs) {
        if(CollUtil.isEmpty(plateNoAndColorDTOs)){
            return null;
        }
        TcsCarFilter filter = new TcsCarFilter();
        filter.setPlateNoAndColorList(BeanUtil.copyToList(plateNoAndColorDTOs, TcsCarPlateNoAndColorFilter.class));
        ResultMode<TcsCarVO> resultMode = tcsCarBusinessInter.queryBasicInfoByFilter(filter);
        if(ObjUtil.isNull(resultMode) || CollUtil.isEmpty(resultMode.getModel()) || !resultMode.isSucceed()){
            return null;
        }
        List<TcsCarVO> model = resultMode.getModel();
        return BeanUtil.copyToList(model,TcsQueryCarDTO.class);
    }
}
