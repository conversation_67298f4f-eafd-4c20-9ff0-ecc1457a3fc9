package com.wanlianyida.lsds.infrastructure.exchange.impl;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.lsds.api.entity.LsdsGoods;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsOffer;
import com.isoftstone.hig.oms.api.entity.OmsOrder;
import com.isoftstone.hig.oms.api.entity.OmsOrderAddress;
import com.isoftstone.hig.oms.api.enums.OmsEnum;
import com.isoftstone.hig.oms.api.inter.OmsOrderAddressInter;
import com.isoftstone.hig.oms.api.inter.OmsOrderInter;
import com.isoftstone.hig.oms.api.mvcvo.OmsOrderVo;
import com.wanlianyida.lsds.infrastructure.exchange.OmsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import javax.annotation.Resource;

@Slf4j
@Service
public class OmsExchangeServiceImpl implements OmsExchangeService {

    @Resource
    private OmsOrderInter omsOrderInter;

    @Resource
    private OmsOrderAddressInter omsOrderAddressInter;

    @Override
    public ResultMode addOpenBookOrder(LsdsGoods lsdsGoods, LsdsGoodsOffer lsdsGoodsOffer) {
        OmsOrderVo omsOrderVo = assembleOmsOrderVo(lsdsGoods, lsdsGoodsOffer);
        return omsOrderInter.addOpenBookOrderNew(omsOrderVo);
    }

    /**
     * 下单参数封装
     */
    private OmsOrderVo assembleOmsOrderVo(LsdsGoods lsdsGoods, LsdsGoodsOffer lsdsGoodsOffer) {
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        String now = DateUtil.now();
        //订单信息
        OmsOrderVo omsOrderVo = new OmsOrderVo();
        omsOrderVo.setGoodsId(lsdsGoods.getGoodsId());
        omsOrderVo.setFreightType(lsdsGoods.getFreightType());
        omsOrderVo.setNetworkMainBodyId(lsdsGoods.getNetworkMainBodyId());
        omsOrderVo.setNetworkMainBodyName(lsdsGoods.getNetworkMainBodyName());
        omsOrderVo.setTransactionContractingBodyId(lsdsGoods.getTransactionContractingBodyId());
        omsOrderVo.setTransactionContractingBodyName(lsdsGoods.getBodyName());
        omsOrderVo.setOperationMainBodyId(lsdsGoods.getOperationMainBodyId());
        omsOrderVo.setCompanyId(lsdsGoods.getCompanyId());
        omsOrderVo.setContractId(lsdsGoods.getContractId());
        omsOrderVo.setContractType(lsdsGoods.getContractType());
        omsOrderVo.setContractName(lsdsGoods.getContractName());
        omsOrderVo.setGoodsType(lsdsGoods.getGoodsType());
        omsOrderVo.setGoodsName(lsdsGoods.getGoodsName());
        omsOrderVo.setGoodsDesc(lsdsGoods.getGoodsDesc());
        omsOrderVo.setWeightSum(lsdsGoods.getWeightSum());
        omsOrderVo.setWeightUnit(StrUtil.isNotBlank(lsdsGoods.getWeightUnit()) ? Integer.valueOf(lsdsGoods.getWeightUnit()) : null);
        omsOrderVo.setVolumeSum(lsdsGoods.getVolumeSum());
        omsOrderVo.setTotalGoods(lsdsGoods.getTotalGoods());
        omsOrderVo.setPackType(lsdsGoods.getPackType());
        omsOrderVo.setTransportationType(lsdsGoods.getTransportationType());
        omsOrderVo.setEnquiryType(lsdsGoods.getEnquiryType());
        omsOrderVo.setEnquiryRange(StrUtil.isNotBlank(lsdsGoods.getEnquiryRange()) ? Integer.valueOf(lsdsGoods.getEnquiryRange()) : null);
        omsOrderVo.setAssignCarType(lsdsGoods.getAssignCarType());
        omsOrderVo.setAssignCarLength(lsdsGoods.getAssignCarLength());
        omsOrderVo.setAssignCarPlateNumber(lsdsGoods.getAssignCarPlateNumber());
        omsOrderVo.setReleaseDate(lsdsGoods.getReleaseDate());
        omsOrderVo.setArriveDate(lsdsGoods.getArriveDate());
        omsOrderVo.setValidityDate(lsdsGoods.getValidityDate());
        omsOrderVo.setOtherIsInvoice(lsdsGoods.getOtherIsInvoice());
        omsOrderVo.setOtherKuiTonsRatio(lsdsGoods.getOtherKuiTonsRatio());
        omsOrderVo.setOtherReceiptType(lsdsGoods.getOtherReceiptType());
        omsOrderVo.setOtherClearType(lsdsGoods.getOtherClearType());
        omsOrderVo.setOtherClearWeight(lsdsGoods.getOtherClearWeight());
        omsOrderVo.setClearMonthDay(lsdsGoods.getClearMonthDay());
        omsOrderVo.setOtherRemark(lsdsGoods.getOtherRemark());
        omsOrderVo.setFeeClearType(lsdsGoods.getFeeClearType());
        omsOrderVo.setFeeClearValue(lsdsGoods.getFeeClearValue());
        omsOrderVo.setCustomerId(lsdsGoods.getCustomerId());
        omsOrderVo.setCustomerType(lsdsGoods.getCustomerType());
        omsOrderVo.setCustomerName(lsdsGoods.getCustomerName());
        omsOrderVo.setContainerType(lsdsGoods.getContainerType());
        omsOrderVo.setContainerNumber(lsdsGoods.getContainerAmount());
        omsOrderVo.setChargeType(lsdsGoods.getChargeType());
        omsOrderVo.setSalesContractNumber(lsdsGoods.getSalesContractNumber());
        omsOrderVo.setTransportMileage(lsdsGoods.getTransportMileage());
        omsOrderVo.setTakeUpTime(lsdsGoods.getTakeUpTime());
        omsOrderVo.setLineId(lsdsGoods.getLineId());
        omsOrderVo.setAdvancePaymentFlag(lsdsGoods.getAdvancePaymentFlag());
        omsOrderVo.setAdvancePayment(lsdsGoods.getAdvancePayment());
        omsOrderVo.setTransportCategory(lsdsGoods.getTransportCategory());
        omsOrderVo.setCarrierAppraiseStatus("21");
        omsOrderVo.setShipperAppraiseStatus("21");
        omsOrderVo.setNeedChangeDeal(OmsEnum.OmsorderNeedChangeDealEnum.needchangedeal21.getCode());
        omsOrderVo.setOrderAgreement(OmsEnum.OmsorderOrderAgreementEnum.orderagreement0.getCode());
        omsOrderVo.setOrderAppointCarrier(OmsEnum.OmsorderOrderAppointCarrierEnum.orderappointcarrier21.getCode());
        omsOrderVo.setOrderPayStatus(OmsEnum.OmsOrderPayStatusEnum.omsOrderPayStatus2.getCode());
        omsOrderVo.setOrderSource(OmsEnum.OrderSourceEnum.ORDER_SOURCE2.getCode());
        omsOrderVo.setOrderSplit(OmsEnum.OmsorderOrderSplitEnum.ordersplit0.getCode());
        omsOrderVo.setOrderStatus(OmsEnum.OmsorderOrderStatusEnum.orderstatus200.getCode());
        omsOrderVo.setOrderType(OmsEnum.OmsorderOrderTypeEnum.ordertype1.getCode());
        omsOrderVo.setManyQuoteHour(null);
        omsOrderVo.setRemittanceBill("");
        omsOrderVo.setCreateBy(tokenInfo.getUserBaseId());
        omsOrderVo.setCreateDate(now);
        omsOrderVo.setModifyBy(tokenInfo.getUserBaseId());
        omsOrderVo.setModifyDate(now);
        //数量、价格
        BigDecimal totalQuantity = lsdsGoods.getTotalQuantity();
        BigDecimal enquiryTypeBaseOpenTicket = lsdsGoods.getEnquiryTypeBaseOpenTicket();
        Integer roundingMode = lsdsGoods.getRoundingMode();
        BigDecimal orderAmount = totalQuantity.multiply(enquiryTypeBaseOpenTicket).setScale(2, roundingMode);
        omsOrderVo.setRoundingMode(roundingMode);
        omsOrderVo.setOrderQuantityUnits(lsdsGoods.getTotalQuantityUnits());
        omsOrderVo.setOrderQuantity(totalQuantity);
        omsOrderVo.setEnquiryTypeBaseTaxRate(lsdsGoods.getEnquiryTypeBaseTaxRate());
        omsOrderVo.setEnquiryTypeBasePrice(lsdsGoods.getEnquiryTypeBasePrice());
        omsOrderVo.setEnquiryTypeBaseOpenTicket(lsdsGoods.getEnquiryTypeBaseOpenTicket());
        omsOrderVo.setRealityCarrierEnquiryTypeBasePrice(lsdsGoods.getEnquiryTypeBasePrice());
        omsOrderVo.setRealityCarrierEnquiryTypeBaseOpenTicket(lsdsGoods.getEnquiryTypeBaseOpenTicket());
        omsOrderVo.setOrderAmount(orderAmount);
        //托运、承运信息
        omsOrderVo.setShipperCompanyId(lsdsGoods.getCompanyId());
        omsOrderVo.setShipperName(lsdsGoods.getCompanyShortName());
        omsOrderVo.setShipperUserBaseId(lsdsGoods.getCreateBy());
        omsOrderVo.setRealityShipperCompanyId(lsdsGoods.getCompanyId());
        omsOrderVo.setRealityShipperName(lsdsGoods.getCompanyShortName());
        omsOrderVo.setRealityShipperUserBaseId(lsdsGoods.getCreateBy());
        omsOrderVo.setCarrierCompanyId(lsdsGoodsOffer.getCompanyId());
        omsOrderVo.setCarrierName(lsdsGoodsOffer.getCompanyShortName());
        omsOrderVo.setCarrierUserBaseId(lsdsGoodsOffer.getPriceUserBaseId());
        omsOrderVo.setRealityCarrierCompanyId(lsdsGoodsOffer.getCompanyId());
        omsOrderVo.setRealityCarrierName(lsdsGoodsOffer.getCompanyShortName());
        omsOrderVo.setRealityCarrierUserBaseId(lsdsGoodsOffer.getPriceUserBaseId());
        omsOrderVo.setPriceUserBaseId(lsdsGoodsOffer.getPriceUserBaseId());
        omsOrderVo.setExOfferIds(new String[]{lsdsGoodsOffer.getOfferId()});
        //地址信息
        OmsOrderAddress orderAddress = new OmsOrderAddress();
        orderAddress.setEndReceiveLinker(lsdsGoods.getReceiveLinker());
        orderAddress.setEndReceivePhoneNumber(lsdsGoods.getReceivePhoneNumber());
        orderAddress.setReceiveAddrProvince(lsdsGoods.getReceiveAddrProvince());
        orderAddress.setReceiveAddrCity(lsdsGoods.getReceiveAddrCity());
        orderAddress.setReceiveAddrArea(lsdsGoods.getReceiveAddrArea());
        orderAddress.setReceiveAddrStreet(lsdsGoods.getReceiveAddrStreet());
        orderAddress.setReceiveAddrProvinceName(lsdsGoods.getReceiveAddrProvinceName());
        orderAddress.setReceiveAddrCityName(lsdsGoods.getReceiveAddrCityName());
        orderAddress.setReceiveAddrAreaName(lsdsGoods.getReceiveAddrCityName());
        orderAddress.setReceiveAddrStreetName(lsdsGoods.getReceiveAddrStreetName());
        orderAddress.setReceiveAddrDetail(lsdsGoods.getReceiveAddrDetail());
        orderAddress.setReceiveAddrShorthand(lsdsGoods.getReceiveAddrShortName());
        orderAddress.setStartSendLinker(lsdsGoods.getSendLinker());
        orderAddress.setStartSendPhoneNumber(lsdsGoods.getSendPhoneNumber());
        orderAddress.setSendAddrProvince(lsdsGoods.getSendAddrProvince());
        orderAddress.setSendAddrCity(lsdsGoods.getSendAddrCity());
        orderAddress.setSendAddrArea(lsdsGoods.getSendAddrArea());
        orderAddress.setsendAddrStreet(lsdsGoods.getSendAddrStreet());
        orderAddress.setSendAddrProvinceName(lsdsGoods.getSendAddrProvinceName());
        orderAddress.setSendAddrCityName(lsdsGoods.getSendAddrCityName());
        orderAddress.setSendAddrAreaName(lsdsGoods.getSendAddrCityName());
        orderAddress.setsendAddrStreetName(lsdsGoods.getSendAddrStreetName());
        orderAddress.setSendAddrDetail(lsdsGoods.getSendAddrDetail());
        orderAddress.setSendAddrShorthand(lsdsGoods.getSendAddrShortName());
        omsOrderVo.setOrderAddress(orderAddress);

        return omsOrderVo;
    }


    @Override
    public OmsOrder getOmsOrder(String orderId) {
        if(StrUtil.isBlank(orderId)){
            return null;
        }
        return omsOrderInter.getOmsOrderByOrderId(orderId);
    }

    @Override
    public OmsOrderAddress getByOrderId(String orderId) {
        if(StrUtil.isBlank(orderId)){
            return null;
        }
        ResultMode<OmsOrderAddress> resultMode = omsOrderAddressInter.getByOrderId(orderId);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed()){
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    @Override
    public void addCloseOrder(OmsOrderVo omsOrderVo) {
        omsOrderInter.addCloseOrder(omsOrderVo);
    }
}
