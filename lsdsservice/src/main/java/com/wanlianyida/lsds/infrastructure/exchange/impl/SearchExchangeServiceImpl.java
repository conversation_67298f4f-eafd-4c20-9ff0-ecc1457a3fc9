package com.wanlianyida.lsds.infrastructure.exchange.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.entity.BizExtendVO;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsDeductible;
import com.wanlianyida.lsds.infrastructure.enums.TmsBizExtendKeyEnum;
import com.wanlianyida.lsds.infrastructure.exchange.SearchExchangeService;
import com.wanlianyida.search.command.DcsPartPayCommand;
import com.wanlianyida.search.command.TmsBizExtendCommand;
import com.wanlianyida.search.dto.FreeTonDTO;
import com.wanlianyida.search.inter.DcsPartPayInter;
import com.wanlianyida.search.inter.FreeTonInter;
import com.wanlianyida.search.inter.TmsBizExtendInter;
import com.wanlianyida.search.query.FreeTonQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * search搜素服务
 */
@Slf4j
@Service
public class SearchExchangeServiceImpl implements SearchExchangeService {
    /**
     * 业务类型(10货源20运单)
     */
    private static final String BIZ_TYPE_GOODS = "10";
    /**
     * 支付状态（0未支付1已支付）
     */
    private static final String PAY_STATUS_UNPAID = "0";
    /**
     * 分次付款类型：10 预付 20回单付 30到付
     */
    private static final String TYPE_ADVANCE_PAYMENT = "10";
    private static final String TYPE_RECEIPT_PAYMENT = "20";
    private static final String TYPE_SIGN_PAYMENT = "30";


    @Resource
    private DcsPartPayInter dcsPartPayInter;

    @Resource
    private TmsBizExtendInter tmsBizExtendInter;
    @Resource
    private FreeTonInter freeTonInter;

    /**
     * 保存分次支付信息
     */
    @Override
    public void saveDcsPartPay(String goodsId, BigDecimal advancePayment, BigDecimal receiptAmount) {
        List<DcsPartPayCommand> dataList = new ArrayList<>();
        //预付
        if (advancePayment != null && advancePayment.compareTo(BigDecimal.ZERO) > 0) {
            DcsPartPayCommand dcsPartPayCommand = new DcsPartPayCommand();
            dcsPartPayCommand.setBizType(BIZ_TYPE_GOODS);
            dcsPartPayCommand.setBizId(goodsId);
            dcsPartPayCommand.setType(TYPE_ADVANCE_PAYMENT);
            dcsPartPayCommand.setAmount(advancePayment);
            dcsPartPayCommand.setPayStatus(PAY_STATUS_UNPAID);
            dataList.add(dcsPartPayCommand);
        }
        //回单付
        if (receiptAmount != null && receiptAmount.compareTo(BigDecimal.ZERO) > 0) {
            DcsPartPayCommand dcsPartPayCommand = new DcsPartPayCommand();
            dcsPartPayCommand.setBizType(BIZ_TYPE_GOODS);
            dcsPartPayCommand.setBizId(goodsId);
            dcsPartPayCommand.setType(TYPE_RECEIPT_PAYMENT);
            dcsPartPayCommand.setAmount(receiptAmount);
            dcsPartPayCommand.setPayStatus(PAY_STATUS_UNPAID);
            dataList.add(dcsPartPayCommand);
        }
        if (CollUtil.isEmpty(dataList)) {
            return;
        }
        dcsPartPayInter.saveDcsPartpay(dataList);
    }

    @Override
    public void saveTmsBizExtend(BizExtendVO vo) {
        if (ObjUtil.isNull(vo) || StrUtil.isBlank(vo.getBizId())) {
            return;
        }

        List<TmsBizExtendCommand> dataList = new ArrayList<>();
        //运输业务分类
        if (StrUtil.isNotBlank(vo.getTrafficClassification())) {
            TmsBizExtendCommand tmsBizExtendCommand = new TmsBizExtendCommand();
            tmsBizExtendCommand.setBizId(vo.getBizId());
            tmsBizExtendCommand.setBizKey(TmsBizExtendKeyEnum.TRAFFIC_CLASSIFICATION.getKey());
            tmsBizExtendCommand.setBizValue(vo.getTrafficClassification());
            dataList.add(tmsBizExtendCommand);
        }
        //运费差价率
        if (ObjUtil.isNotNull(vo.getFreightDiffRate())) {
            TmsBizExtendCommand tmsBizExtendCommand = new TmsBizExtendCommand();
            tmsBizExtendCommand.setBizId(vo.getBizId());
            tmsBizExtendCommand.setBizKey(TmsBizExtendKeyEnum.FREIGHT_DIFF_RATE.getKey());
            tmsBizExtendCommand.setBizValue(vo.getFreightDiffRate().toPlainString());
            dataList.add(tmsBizExtendCommand);
        }
        //运费差价率成本价
        if (ObjUtil.isNotNull(vo.getCostPrice())) {
            TmsBizExtendCommand tmsBizExtendCommand = new TmsBizExtendCommand();
            tmsBizExtendCommand.setBizId(vo.getBizId());
            tmsBizExtendCommand.setBizKey(TmsBizExtendKeyEnum.COST_PRICE.getKey());
            tmsBizExtendCommand.setBizValue(vo.getCostPrice().toPlainString());
            dataList.add(tmsBizExtendCommand);
        }
        if (CollUtil.isEmpty(dataList)) {
            return;
        }
        tmsBizExtendInter.save(dataList);
    }

    @Override
    public Map<String, LsdsGoodsDeductible> queryFreeTon(List<String> goodsIds) {
        if (CollUtil.isEmpty(goodsIds)) {
            return MapUtil.empty();
        }
        FreeTonQuery query = new FreeTonQuery();
        query.setBizIdList(goodsIds);
        ResultMode<FreeTonDTO> resultMode = freeTonInter.queryFreeTon(query);
        if (ObjUtil.isNotNull(resultMode) && resultMode.getSucceed() && CollUtil.isNotEmpty(resultMode.getModel())) {
            List<FreeTonDTO> freeTonDTOList = resultMode.getModel();
            return freeTonDTOList.stream().collect(Collectors.toMap(FreeTonDTO::getBizId, x->BeanUtil.toBean(x, LsdsGoodsDeductible.class)));
        }

        return MapUtil.empty();
    }

}
