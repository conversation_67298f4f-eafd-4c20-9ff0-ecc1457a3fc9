package com.wanlianyida.lsds.infrastructure.config;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.HttpHelpUtil;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 控制器基类，进行权限拦截器控制 (这个也C#有区别，只要在InitWebMvcConfig.java中进行注解即可，不需要更多的配置，
 * 以及继承后是没有用的，一定要注解程序自动内部在序列化时自动增加这个如C#的委托事件，java的即为监听事件)
 *
 * <AUTHOR>
 */

public class BaseController extends HandlerInterceptorAdapter {

    private static final String OPTIONS = "OPTIONS";

    /**
     * preHandle是调用Controller【之前被调用】， 当返回false后，会跳过之后的拦截器，并且不会执行所有拦截器的postHandle，
     * 并调返回true的用拦截器的afterCompletion方法。
     *
     * @param request  请求
     * @param response 响应
     * @param handler  请求头
     * @return boolean
     * @throws Exception 异常
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        if (OPTIONS.equals(request.getMethod())) {
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "*");
            response.setHeader("Access-Control-Max-Age", "3600");
            response.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization");
        } else {
            // 验证是否已经登录，如果还没有登录要跳转到登录页面
            ResultMode<String> resultMode = FormsAuthTrader.AnalysisToken(request);
            if (!resultMode.getSucceed()){
                buildResult(response, resultMode);
                return false;
            }
            return true;
        }
        return super.preHandle(request, response, handler);
    }

    /**
     * 构建错误信息
     *
     * @param response   响应
     * @param resultJson JSON对象
     * @throws IOException io异常
     */
    private void buildResult(HttpServletResponse response, ResultMode<String> resultJson) throws IOException {
        // 重置response
        response.reset();
        // 设置编码格式
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json;charset=UTF-8");
        PrintWriter pw = response.getWriter();
//        resultJson.errMsg = CommonStatusCodeEnum.NoLogin.getDesc() + ", 请先登录再进行请求！";
//        resultJson.succeed = false;
//        resultJson.errCode = CommonStatusCodeEnum.NoLogin.getCode() + "";
        String jsonStr = HttpHelpUtil.objectToJson(resultJson).toJSONString();
        pw.write(jsonStr);
        pw.flush();
        pw.close();
    }

    /**
     * postHandle是调用Controller【之后被调用】，但是在渲染View页面之前。
     *
     * @param request      请求
     * @param response     响应
     * @param handler      请求头
     * @param modelAndView 视图对象
     * @throws Exception 异常
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {

        super.postHandle(request, response, handler, modelAndView);
    }

    /**
     * afterCompletion是调用完Controller接口，并执行上面的PostHadle后再执行此方法，
     * 也即在PostHandle进行页面渲染后进行调用 ，渲染View页面最后调用。但要preHandle()返回为true才会执行
     * 返回true的拦截器都会调用该拦截器的afterCompletion方法，顺序相反。
     *
     * @param request  请求
     * @param response 响应
     * @param handler  请求头
     * @param ex       异常
     * @throws Exception 异常
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {

        super.afterCompletion(request, response, handler, ex);
    }

    @Override
    public void afterConcurrentHandlingStarted(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        super.afterConcurrentHandlingStarted(request, response, handler);
    }

}



