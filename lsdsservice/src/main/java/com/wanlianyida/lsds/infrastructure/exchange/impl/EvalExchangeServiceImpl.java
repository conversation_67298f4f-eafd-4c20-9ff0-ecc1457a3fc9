package com.wanlianyida.lsds.infrastructure.exchange.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.eval.api.entity.EvalDriverInfo;
import com.isoftstone.hig.eval.api.inter.EvalDriverInfoInter;
import com.wanlianyida.lsds.infrastructure.exchange.EvalExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

/**
 * eval
 */
@Slf4j
@Component
public class EvalExchangeServiceImpl implements EvalExchangeService {

    @Resource
    private EvalDriverInfoInter evalDriverInfoInter;

    /**
     * 查询评价司机信息
     *
     * @param driverId
     * @return
     */
    @Override
    public List<Double> selectByDriverId(String driverId) {
        ResultMode<Double> resultMode = evalDriverInfoInter.selectByDriverId(driverId);
        log.info("selectByDriverId -> 请求参数：{},响应：{}", driverId, JSONUtil.toJsonStr(resultMode));
        if (Objects.isNull(resultMode) || !resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return new ArrayList<>();
        }
        return resultMode.getModel();
    }

    /**
     * 查询评价司机信息
     * @param driverIds
     * @return
     */
    @Override
    public List<EvalDriverInfo> selectByDriverIds(List<String> driverIds) {
        if(CollUtil.isEmpty(driverIds)){
            return new ArrayList<>();
        }
        ResultMode<EvalDriverInfo> resultMode = evalDriverInfoInter.selectByDriverIds(driverIds);
        if (Objects.isNull(resultMode) || !resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return new ArrayList<>();
        }
        return resultMode.getModel();
    }


}
