package com.wanlianyida.lsds.infrastructure.exchange.impl;

import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.hig.rms.api.inter.DriverGoodsPublishCalculateInter;
import com.wanlianyida.hig.rms.api.po.DriverGoodsPublishRuleParma;
import com.wanlianyida.lsds.infrastructure.exchange.RmsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 发布司机货源风控
 *
 * <AUTHOR>
 * @date 2024/04/09
 */
@Slf4j
@Component
public class RmsExchangeServiceImpl implements RmsExchangeService {

    @Resource
    private DriverGoodsPublishCalculateInter driverGoodsPublishCalculateInter;

    /**
     * 发布司机货源风控
     *
     * @param ruleParma
     * @return {@link String}
     */
    @Override
    public String driverGoodsPublishCalculate(DriverGoodsPublishRuleParma ruleParma) {
        ResultMode resultMode = driverGoodsPublishCalculateInter.driverGoodsPublishCalculate(ruleParma);
        log.info("发布司机货源风控结果：{}", JSON.toJSONString(resultMode));
        if (!resultMode.isSucceed()) {
            return resultMode.getErrMsg();
        }
        return null;
    }

}
