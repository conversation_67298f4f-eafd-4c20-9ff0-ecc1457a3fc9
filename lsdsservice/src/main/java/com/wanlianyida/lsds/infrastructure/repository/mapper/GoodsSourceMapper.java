package com.wanlianyida.lsds.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.lsds.domain.model.bo.GoodsExpiredDataBO;
import com.wanlianyida.lsds.domain.model.bo.OfferPriceBO;
import com.wanlianyida.lsds.domain.model.condition.GoodsSourceListCondition;
import com.wanlianyida.lsds.infrastructure.repository.po.GoodsSourcePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 货源表 Mapper 接口
 */
@Mapper
public interface GoodsSourceMapper extends BaseMapper<GoodsSourcePO> {

    List<GoodsExpiredDataBO> queryExpiredGoods();

    void batchUpdateExpiredGoods(@Param("goodsIdList") List<String> goodsIdList);

    List<GoodsSourcePO> queryList(PagingInfo<GoodsSourceListCondition> pageInfo);

    int queryCount(PagingInfo<GoodsSourceListCondition> pageInfo);

    void batchUpdateExpiredChildGoods(List<String> goodsIdList);

    List<GoodsExpiredDataBO> queryExpiredChildGoods(List<String> goodsIdList);

    /**
     * 更新当前货源报价次数
     * @param goodsId
     * @param offerCurrentRounds
     */
    void updateRound(@Param("goodsId") String goodsId,@Param("offerCurrentRounds") Integer offerCurrentRounds);

    /**
     * 更新货源扩展表
     * @param goodsId
     */
    void updateRoundExtend(@Param("goodsId") String goodsId);

    /**
     * 批量更新货源信息
     */
    void batchUpdateSelective(List<GoodsSourcePO> goodsSourcePOS);

    /**
     * 批量插入货源信息
     */
    void batchInsertSelective(List<GoodsSourcePO> goodsSourcePOS);

    /**
     * 清空货源码信息
     * @param unbindGoodsIds
     */
    void clearCodeInfo(@Param("list") List<String> unbindGoodsIds);

    List<OfferPriceBO> queryCompanyOfferPrice(List<String> companyGoodsIds, String platformCompanyId);

    /**
     * 更新当前轮次报价数量\当前轮次报价最低价
     */
    void updateCurrentRoundOffer(@Param("goodsId") String goodsId,@Param("currentRoundOfferMinPrice") BigDecimal currentRoundOfferMinPrice);

}
