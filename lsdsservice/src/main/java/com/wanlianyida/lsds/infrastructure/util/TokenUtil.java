package com.wanlianyida.lsds.infrastructure.util;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.RedisUtil;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.platform.api.entity.PlatformUmLogininfo;
import com.isoftstone.hig.platform.api.inter.AuthorityNoLoginInter;
import com.isoftstone.hig.platform.api.mvcvo.LoginVo;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class TokenUtil {

    @Value("${config.token.network-url}")
    String networkUrl;
    @Value("${config.token.login-username}")
    String loginUserName;
    @Value("${config.token.login-pwd}")
    String loginPwd;

    @Resource
    private AuthorityNoLoginInter authorityNoLoginInter;

    /**
     * 获取临时令牌
     * <p>
     * 模拟登陆接口，获取模拟 Token
     *
     * @return
     */
    public String getToken() {
        String key = StrUtil.join(":", "lsds", "mock", "token");
        String token = RedisUtil.get(key);
        Boolean validPass = validToken(token);
        if (validPass) {
            return token;
        }
        try {
            PlatformUmLogininfo model = new PlatformUmLogininfo();
            model.setLoginName(loginUserName);
            model.setPassword(loginPwd);
            model.setExLoginFlag(2);
            ResultMode<LoginVo> resultMode = authorityNoLoginInter.login(model);
            if (resultMode.isSucceed() && null != IterUtil.getFirst(resultMode.getModel())) {
                token = IterUtil.getFirst(resultMode.getModel()).getToken();
                RedisUtil.set(key, token);
            }
        } catch (Exception e) {
            log.error("尝试静默登录失败", e);
        }

        return token;
    }

    /**
     * 获取默认 TokenInfo
     */
    public TokenInfo getDefaultTokenInfo() {
        String token = getToken();
        Claims claimByToken = JwtUtil.getInstance().getClaimByToken(token);
        return JSONUtil.toBean(claimByToken.getSubject(), TokenInfo.class);
    }

    private Boolean validToken(String token) {

        if (StrUtil.isBlank(token)) {
            return false;
        }

        // 获取签名信息
        Claims claims = JwtUtil.getInstance().getClaimByToken(token);
        // 判断签名是否存在或过期
        boolean b = claims == null || claims.isEmpty() || JwtUtil.getInstance().isTokenExpired(claims.getExpiration());
        if (b) {
            return false;
        }

        //解析token
        String subject = claims.getSubject();
        TokenInfo tokenInfo = JSONUtil.toBean(subject, TokenInfo.class);
        //校验token未操作超时
        if (tokenInfo.getFlag() != 1) {
            String lastOperationRedisKey = UtilityEnum.RedisKeyEnum.LAST_OPERATION_TIME_PREFIX.getCode() + tokenInfo.getUserBaseId();
            String str = RedisUtil.get(lastOperationRedisKey);
            if (StrUtil.isBlank(str)) {
                return false;
            }
            String expirationTimeStr = SpringContextUtil.getApplicationContext().getEnvironment().getProperty("web.allow.unoperated.time");
            if (StrUtil.isBlank(expirationTimeStr)) {
                RedisUtil.set(lastOperationRedisKey, String.valueOf(System.currentTimeMillis()), 1800);
            } else {
                RedisUtil.set(lastOperationRedisKey, String.valueOf(System.currentTimeMillis()), Integer.parseInt(expirationTimeStr));
            }
        }

        return true;
    }


    /**
     * 获取临时令牌
     * <p>
     * 模拟登陆接口，获取模拟 Token
     *
     * @return
     */
    public static String getTemporaryToken() {
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setUserBaseId("sysadmin");
        tokenInfo.setCompanyId("sysadmin");
        tokenInfo.setLoginId("sysadmin");
        tokenInfo.setLoginName("sysadmin");
        tokenInfo.setUsername("sysadmin");
        tokenInfo.setCompanyName("平台");
        tokenInfo.setCompanyShortName("平台");
        tokenInfo.setLoginVersionCode(System.currentTimeMillis());
        tokenInfo.setFlag(1);
        String tokenInfoString = JSON.toJSONString(tokenInfo);
        return JwtUtil.getInstance().generateToken(tokenInfoString);
    }

}
