package com.wanlianyida.lsds.infrastructure.exchange;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.CrmCompanyLineAddressBO;

/**
 * 线路主数据信息交换服务
 *
 * <AUTHOR>
 * @since 20231221
 */
public interface CrmExchangeService {

    /**
     * 获取线路信息
     *
     * @param lineId 线路ID
     * @return 车辆基础信息
     */
    CrmCompanyLineAddressBO getCrmCompanyLine(String lineId);

    /**
     * 创建线路
     *
     * @param lineShortname 线路简称
     * @param sendAddrId 发出地id
     * @param receiveAddrId 目的地id
     * @param lineSource 线路来源
     * @param companyId 企业id
     * @return ResultMode
     */
    ResultMode createCrmCompanyLine(String lineShortname, String sendAddrId, String receiveAddrId, String lineSource, String companyId);


}
