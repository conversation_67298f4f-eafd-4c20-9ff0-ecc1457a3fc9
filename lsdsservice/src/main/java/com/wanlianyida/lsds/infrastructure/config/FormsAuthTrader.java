package com.wanlianyida.lsds.infrastructure.config;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.*;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.platform.api.entity.PlatformCmDictionary;
import com.isoftstone.hig.platform.api.entity.PlatformUmUserbaseinfo;
import com.isoftstone.hig.platform.api.filter.PlatformCmDictionaryFilter;
import com.isoftstone.hig.platform.api.inter.PlatformCmDictionaryInter;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

/**
 * 权限控制类
 *
 * <AUTHOR>
 */
@Slf4j
public class FormsAuthTrader {

    /**
     * 解释Token 并判断是否过期
     *
     * @param request 请求
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【没有过期】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【没有过期】编码,ResultMode.errMsg为相应【没有过期】描述；
     * 否则ResultMode.succeed=false【已过期】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【已过期】编码,ResultMode.errMsg为相应【已过期】描述。
     */
    public static ResultMode<String> AnalysisToken(HttpServletRequest request) {

        ResultMode<String> resultMode = new ResultMode<>();

        final String token = request.getHeader(JwtUtil.getInstance().getTokenHeader());
        LogHelper.writeInfo(token);
        try {

            if (!Optional.ofNullable(token).isPresent()) {
                resultMode.setErrCode(CommonStatusCodeEnum.BUSS_ERROR_BCOM0401.getCode());
                resultMode.setErrMsg(CommonStatusCodeEnum.BUSS_ERROR_BCOM0401.getMsg());
                resultMode.setSucceed(false);
                return resultMode;
            }

            // 获取签名信息
            Claims claims = JwtUtil.getInstance().getClaimByToken(token);
            LogHelper.writeInfo("TOKEN: " + claims);
            // 判断签名是否存在或过期
            boolean b = claims == null
                    || claims.isEmpty()
                    || JwtUtil.getInstance().isTokenExpired(claims.getExpiration());
            if (b) {
                resultMode.setErrCode(CommonStatusCodeEnum.USER_ERROR_EXPIRE.getCode());
                resultMode.setErrMsg(CommonStatusCodeEnum.USER_ERROR_EXPIRE.getMsg());
                resultMode.setSucceed(false);
                return resultMode;
            }

            //解析token
            String subject = claims.getSubject();
            TokenInfo tokenInfo = JSON.parseObject(subject, TokenInfo.class);
            //校验token未操作超时
            if (tokenInfo.getFlag() != 1) {
                String lastOperationRedisKey = UtilityEnum.RedisKeyEnum.LAST_OPERATION_TIME_PREFIX.getCode() + tokenInfo.getUserBaseId();
                String str = RedisUtil.get(lastOperationRedisKey);
                if (StringUtils.isBlank(str)) {
                    resultMode.setErrCode(CommonStatusCodeEnum.USER_ERROR_EXPIRE.getCode());
                    resultMode.setErrMsg(CommonStatusCodeEnum.USER_ERROR_EXPIRE.getMsg());
                    resultMode.setSucceed(false);
                    return resultMode;
                }
                String expirationTimeStr = SpringContextUtil.getApplicationContext().getEnvironment().getProperty("web.allow.unoperated.time");
                if (StringUtils.isBlank(expirationTimeStr)) {
                    RedisUtil.set(lastOperationRedisKey, String.valueOf(System.currentTimeMillis()), 1800);
                } else {
                    RedisUtil.set(lastOperationRedisKey, String.valueOf(System.currentTimeMillis()), Integer.parseInt(expirationTimeStr));
                }
            }
            //调用以下方法进行判断权限
//            resultMode = IsRequestPathUrlByUserRole(request, tokenInfoString);
        } catch (Exception e) {
            LogHelper.writeError("当前类:FormsAuthTrader.AnalysisToken()" + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "异常：" + e.getMessage(), e);
            resultMode.setErrCode(CommonStatusCodeEnum.BUSS_ERROR_BCOM0500.getCode());
            resultMode.setErrMsg(CommonStatusCodeEnum.BUSS_ERROR_BCOM0500.getMsg());
            resultMode.setSucceed(false);
            return resultMode;
        }

        return resultMode;

    }

    /**
     * 获取用户信息的公共方法
     *
     * @return PlatformUmUserbaseinfo
     * <AUTHOR>
     * 创建时间 2019/10/23 20:22
     */
    public static PlatformUmUserbaseinfo getUserInfoFromCache() {
        PlatformUmUserbaseinfo userInfo = null;
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String tokenValue = request.getHeader(JwtUtil.getInstance().getTokenHeader());
            Claims claims = JwtUtil.getInstance().getClaimByToken(tokenValue);
            if(claims==null){
                return null;
            }
            String subject = claims.getSubject();
            TokenInfo tokenInfo = JSON.parseObject(subject, TokenInfo.class);
            String userBaseId = tokenInfo.getUserBaseId();
            String redisKey = UtilityEnum.RedisKeyEnum.PLATFORM_LOGINED_USER_INFO_PREFIX.getCode() + userBaseId;
            String userInfoJsonString = RedisUtil.get(redisKey);
            if(StrUtil.isEmpty(userInfoJsonString)){
                //货主端用户信息
                redisKey = UtilityEnum.RedisKeyEnum.APP_LOGINED_DRIVER_INFO_PREFIX.getCode() + userBaseId;
                userInfoJsonString = RedisUtil.get(redisKey);
            }
            userInfo = JSON.parseObject(userInfoJsonString, PlatformUmUserbaseinfo.class);

        } catch (Exception ex) {
            LogHelper.writeError("当前类:FormsAuthTrader.getUserInfoFromCache()" + Thread.currentThread().getStackTrace()[1].getMethodName()
                    + "异常：" + ex.getMessage(), ex);
        }
        return userInfo;
    }

    /**
     * 获取枚举值
     * @param parentdicId
     * @return
     */
    public static List<PlatformCmDictionary> dic(PlatformCmDictionaryInter platformCmDictionaryInter, String parentdicId){
        String dictListString = RedisUtil.get(UtilityEnum.RedisKeyEnum.PLATFORM_DICTIONARY.getCode());
        List<PlatformCmDictionary> dictList = null;
        dictList = JSON.parseArray(dictListString, PlatformCmDictionary.class);
        if(Collections.isEmpty(dictList)){
            log.error("换成没有获取到枚举数据，查询数据库");
            dictList = getPlatformCmDictionary(platformCmDictionaryInter,parentdicId);
        }

        if(StringUtils.isEmpty(parentdicId)){
            return new ArrayList();
        }
        return dictList.stream().filter(lt -> lt.getParentdicId().equals(parentdicId)).collect(Collectors.toList());
    }

    /**
     * 获取字典数据
     *
     * @param dictionaryId      根枚举id
     * @return PlatformCmDictionary
     * <AUTHOR>
     * 创建时间 2019/12/13 11:30
     */
    private static List<PlatformCmDictionary> getPlatformCmDictionary(PlatformCmDictionaryInter platformCmDictionaryInter, String dictionaryId) {
        PagingInfo<PlatformCmDictionaryFilter> pageInfo = new PagingInfo<>();
        PlatformCmDictionaryFilter filter = new PlatformCmDictionaryFilter();
        filter.setParentdicId(dictionaryId);
        pageInfo.setPageLength(1000);
        pageInfo.setCurrentPage(1);
        pageInfo.setFilterModel(filter);
        ResultMode<PlatformCmDictionary> resultMode = platformCmDictionaryInter.platformCmDictionaryAllPaging(pageInfo);
        if(resultMode == null || Collections.isEmpty(resultMode.getModel())){
            return null;
        }
        List<PlatformCmDictionary> dictList = resultMode.getModel();
        List<PlatformCmDictionary>  resultList = dictList.stream().filter(lt -> lt.getParentdicId().equals(dictionaryId)).collect(Collectors.toList());
        return resultList;
    }

    /**
     * 获取字典编码列表
     * @param parentDicId 父级Id
     */
    public static List<String> getDictionaryEnumCodeList(PlatformCmDictionaryInter platformCmDictionaryInter, String parentDicId){
        if (StrUtil.isBlank(parentDicId) || platformCmDictionaryInter == null) {
            return null;
        }
        List<PlatformCmDictionary> dictionaryList = dic(platformCmDictionaryInter, parentDicId);
        if (IterUtil.isEmpty(dictionaryList)) {
            return null;
        }

        return dictionaryList.stream().map(PlatformCmDictionary::getEnumCode).collect(Collectors.toList());
    }

}
