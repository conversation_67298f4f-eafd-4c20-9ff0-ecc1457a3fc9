package com.wanlianyida.lsds.infrastructure.exchange;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.entity.LsdsGoods;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsOffer;
import com.isoftstone.hig.oms.api.entity.OmsOrder;
import com.isoftstone.hig.oms.api.entity.OmsOrderAddress;
import com.isoftstone.hig.oms.api.mvcvo.OmsOrderVo;

public interface OmsExchangeService {

    /**
     * 生成订单
     */
    ResultMode addOpenBookOrder(LsdsGoods lsdsGoods, LsdsGoodsOffer lsdsGoodsOffer);

    /**
     * 查询订单详情
     * @param orderId
     * @return
     */
    OmsOrder getOmsOrder(String orderId);

    /**
     * 订单地址信息
     * @param orderId
     * @return
     */
    OmsOrderAddress getByOrderId(String orderId);


    /**
     * 生成订单
     * @param omsOrderVo
     */
    void addCloseOrder(OmsOrderVo omsOrderVo);
}
