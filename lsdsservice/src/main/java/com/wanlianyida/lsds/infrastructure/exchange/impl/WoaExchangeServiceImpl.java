package com.wanlianyida.lsds.infrastructure.exchange.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.IterUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.woa.api.inter.WoaLogisticsPlanGoodsInter;
import com.isoftstone.hig.woa.api.inter.WoaLogisticsPlanInter;
import com.isoftstone.hig.woa.api.inter.WoaOfferPlanInter;
import com.isoftstone.hig.woa.api.inter.WoaWaybillClueInter;
import com.isoftstone.hig.woa.api.vo.WoaLogisticsPlanGoodsVO;
import com.isoftstone.hig.woa.api.vo.WoaLogisticsPlanVO;
import com.isoftstone.hig.woa.api.vo.WoaOfferPlanVO;
import com.isoftstone.hig.woa.api.vo.WoaWaybillClueCntVO;
import com.isoftstone.hig.woa.api.vo.WoaWaybillClueVO;
import com.isoftstone.hig.woa.filter.WoaLogisticsPlanFilter;
import com.isoftstone.hig.woa.filter.WoaOfferPlanFilter;
import com.isoftstone.hig.woa.filter.WoaWaybillClueFilter;
import com.wanlianyida.lsds.infrastructure.exchange.WoaExchangeService;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.WoaLogisticsPlanBO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

/**
 * 智慧场站接口服务
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Component
public class WoaExchangeServiceImpl implements WoaExchangeService {

    @Resource
    private WoaLogisticsPlanGoodsInter woaLogisticsPlanGoodsInter;

    @Resource
    private WoaLogisticsPlanInter woaLogisticsPlanInter;
    @Resource
    private WoaOfferPlanInter woaOfferPlanInter;

    @Resource
    private WoaWaybillClueInter woaWaybillClueInter;

    @Override
    public List<WoaLogisticsPlanBO> selectByGoodsIds(List<String> goodsIds) {
        ResultMode<WoaLogisticsPlanGoodsVO> planVOResultMode = woaLogisticsPlanGoodsInter.selectByGoodsIds(goodsIds);
        if (IterUtil.isEmpty(planVOResultMode.getModel())) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(planVOResultMode.getModel(), WoaLogisticsPlanBO.class, CopyOptions.create().ignoreError());
    }

    @Override
    public WoaLogisticsPlanVO getLogisticsPlanByPlanId(Long planId) {
        WoaLogisticsPlanFilter woaLogisticsPlanFilter = new WoaLogisticsPlanFilter();
        woaLogisticsPlanFilter.setPlanId(planId);
        ResultMode<WoaLogisticsPlanVO> logisticsPlanVOResultMode = woaLogisticsPlanInter.selectByPrimaryKey(woaLogisticsPlanFilter);
        return IterUtil.getFirst(logisticsPlanVOResultMode.getModel());
    }

    @Override
    public void goodsIdWriteBack(Long planId, String goodsId) {
        WoaLogisticsPlanFilter woaLogisticsPlanFilter = new WoaLogisticsPlanFilter();
        woaLogisticsPlanFilter.setPlanId(planId);
        woaLogisticsPlanFilter.setGoodsId(goodsId);
        woaLogisticsPlanInter.goodsIdWriteBack(woaLogisticsPlanFilter);
    }

    /**
     * 司机端意向货源分页查询（首页、货源大厅）
     * 基于身份证号
     * 仅7天有效（当前时间小于等于【接单时间+7*24小时】时，展示；当前时间大于【接单时间+7*24小时】时，不展示）
     *
     * @param pagingInfo 分页查询参数
     * @return {@link ResultMode}<{@link WoaWaybillClueVO}>
     */
    @Override
    public List<WoaWaybillClueVO> appChannelQueryPage(PagingInfo<WoaWaybillClueFilter> pagingInfo) {
        ResultMode<WoaWaybillClueVO> resultMode = woaWaybillClueInter.appChannelQueryPage(pagingInfo);
        if (resultMode == null || !resultMode.getSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return Collections.emptyList();
        }
        return resultMode.getModel();
    }

    /**
     * 推荐运力增值服务统计
     *
     * @param bizIds
     * @return {@link List }<{@link WoaWaybillClueCntVO }>
     */
    @Override
    public List<WoaWaybillClueCntVO> cntByBizIds(List<String> bizIds) {
        if (IterUtil.isEmpty(bizIds)) {
            return Collections.emptyList();
        }
        ResultMode<WoaWaybillClueCntVO> resultMode = woaWaybillClueInter.cntByBizIds(bizIds);
        if (resultMode.getSucceed() && IterUtil.isNotEmpty(resultMode.getModel())) {
            return resultMode.getModel();
        }
        return Collections.emptyList();
    }

    @Override
    public WoaOfferPlanVO getOfferPlanByPlanId(Long offerPlanId) {
        WoaOfferPlanFilter filter =  new WoaOfferPlanFilter();
        filter.setId(offerPlanId);
        ResultMode<WoaOfferPlanVO> resultMode = woaOfferPlanInter.selectByPrimaryKey(filter);
        return IterUtil.getFirst(resultMode.getModel());
    }

    @Override
    public void goodsIdWriteBackToOfferPlan(Long offerPlanId, String goodsId, String goodsType) {
        WoaOfferPlanFilter filter =  new WoaOfferPlanFilter();
        filter.setId(offerPlanId);
        filter.setGoodsId(goodsId);
        filter.setGoodsType(goodsType);
        woaOfferPlanInter.goodsIdWriteBackToOfferPlan(filter);
    }
}
