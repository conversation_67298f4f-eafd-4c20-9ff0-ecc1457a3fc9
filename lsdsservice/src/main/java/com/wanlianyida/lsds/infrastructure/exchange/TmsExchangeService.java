package com.wanlianyida.lsds.infrastructure.exchange;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.tms.api.entity.TmsOrder;
import com.isoftstone.hig.tms.api.entity.TmsWaybill;
import com.isoftstone.hig.tms.api.mvcvo.TmsNearbyTransResDTO;
import com.isoftstone.hig.tms.api.mvcvo.TmsOrderAndAddressVO;
import com.isoftstone.hig.tms.api.query.GetNeatByTransQuery;
import com.isoftstone.hig.tms.api.query.TmsOrderFilter;

import java.math.BigDecimal;
import java.util.List;

/**
 * TMS 服务接口
 *
 * <AUTHOR>
 * @since 2024/3/15 15:25
 */
public interface TmsExchangeService {

    /**
     * 剩余量校验
     * @param subQuantity  差值
     * @param remainingQuantity  剩余数量
     */
    ResultMode remainingQuantityCheck(BigDecimal subQuantity, BigDecimal remainingQuantity);

    /**
     * 数量差值计算
     *
     * @param quantityUnits  货源计价类型
     * @param waybillQuantityUnits  下单计价类型
     * @param receivingQuantity  接单数
     * @param receivingWeight  接单重量/吨
     */
    BigDecimal calculateSubQuantity(String quantityUnits, String waybillQuantityUnits, BigDecimal receivingQuantity, BigDecimal receivingWeight);

    /**
     * 查询运单信息
     */
    TmsWaybill getWaybillInfo(String waybillId);

    /**
     * 根据货源id以及运单状态查询运单集合
     */
    List<TmsWaybill> queryWaybillList(String goodsId, List<String> wabillStatusList);

    /**
     * 批量查订单
     * @param tmsOrderIdFilter
     * @return {@link List }<{@link TmsOrder }>
     */
    List<TmsOrder> getOrderListByOrderIds(TmsOrderFilter tmsOrderIdFilter);

    /**
     * 获取司机货源接单限制
     */
    Integer getDriverWaybillCount(String driverId, String goodsId);

    /**
     * 查询订单信息
     * @param orderId
     * @return
     */
    TmsOrderAndAddressVO getTmsOrderAndAddress(String orderId);

    /**
     * 查询订单
     */
    TmsOrder getTmsOrder(String orderId);


    /**
     * 查询附近运力
     * @param query
     * @return
     */
    List<TmsNearbyTransResDTO> getNeatByTrans(GetNeatByTransQuery query);
}
