package com.wanlianyida.lsds.infrastructure.exchange.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.isoftstone.hig.annotations.LogPrintPoint;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.tms.api.entity.TmsOrder;
import com.isoftstone.hig.tms.api.entity.TmsWaybill;
import com.isoftstone.hig.tms.api.enums.RemainingQuantityCheckTypeEnum;
import com.isoftstone.hig.tms.api.enums.TmsWaybillSourceEnum;
import com.isoftstone.hig.tms.api.inter.TmsOrderInter;
import com.isoftstone.hig.tms.api.inter.TmsWaybillGenerateInter;
import com.isoftstone.hig.tms.api.inter.TmsWaybillInter;
import com.isoftstone.hig.tms.api.mvcvo.TmsNearbyTransResDTO;
import com.isoftstone.hig.tms.api.mvcvo.TmsOrderAndAddressVO;
import com.isoftstone.hig.tms.api.query.*;
import com.wanlianyida.lsds.infrastructure.exchange.TmsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

/**
 * TMS 服务实现类
 *
 * <AUTHOR>
 * @since 2024/3/15 15:26
 */
@Slf4j
@Service
public class TmsExchangeServiceImpl implements TmsExchangeService {

    @Resource
    private TmsWaybillGenerateInter tmsWaybillGenerateInter;

    @Resource
    private TmsWaybillInter tmsWaybillInter;

    @Resource
    private TmsOrderInter tmsOrderInter;

    /**
     * 剩余量校验
     * @param subQuantity  差值
     * @param remainingQuantity  剩余数量
     */
    @Override
    public ResultMode remainingQuantityCheck(BigDecimal subQuantity, BigDecimal remainingQuantity) {
        RemainingQuantityCheckFilter filter = new RemainingQuantityCheckFilter();
        filter.setType(RemainingQuantityCheckTypeEnum.ORDER_RECEIVING.getCode());
        filter.setSource(TmsWaybillSourceEnum.ORDER.getCode());
        filter.setSubQuantity(subQuantity);
        filter.setRemainingQuantity(remainingQuantity);

        return tmsWaybillGenerateInter.remainingQuantityCheck(filter);
    }

    /**
     * 数量差值计算
     *
     * @param quantityUnits  货源计价类型
     * @param waybillQuantityUnits  下单计价类型
     * @param receivingQuantity  接单数
     * @param receivingWeight  接单重量/吨
     */
    @Override
    public BigDecimal calculateSubQuantity(String quantityUnits, String waybillQuantityUnits, BigDecimal receivingQuantity, BigDecimal receivingWeight) {
        GetRemainingQuantityFilter filter = new GetRemainingQuantityFilter();
        filter.setType(RemainingQuantityCheckTypeEnum.ORDER_RECEIVING.getCode());
        filter.setQuantityUnits(quantityUnits);
        filter.setWaybillQuantityUnits(waybillQuantityUnits);
        filter.setCurrentQuantity(receivingQuantity);
        filter.setReceivingWeight(receivingWeight);

        tmsWaybillGenerateInter.calculateSubQuantity(filter);
        return filter.getSubQuantity();
    }

    /**
     * 查询运单信息
     */
    @Override
    public TmsWaybill getWaybillInfo(String waybillId) {
        if (StrUtil.isBlank(waybillId)) {
            return null;
        }

        TmsWaybillFilter filter = new TmsWaybillFilter();
        filter.setWaybillId(waybillId);
        ResultMode<TmsWaybill> resultMode = tmsWaybillInter.getModelById(filter);
        if (resultMode == null || !resultMode.getSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }

        return IterUtil.getFirst(resultMode.getModel());
    }

    @Override
    public List<TmsWaybill> queryWaybillList(String goodsId, List<String> wabillStatusList) {
        TmsWaybillFilter filter = new TmsWaybillFilter();
        filter.setGoodsId(goodsId);
        filter.setWabillStatusList(wabillStatusList);
        ResultMode<TmsWaybill> resultMode = tmsWaybillInter.queryWaybillList(filter);
        if (resultMode == null || !resultMode.getSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return new ArrayList<>();
        }
        return resultMode.getModel();
    }

    /**
     * 批量查订单
     * @param tmsOrderIdFilter
     * @return {@link List }<{@link TmsOrder }>
     */
    @Override
    public List<TmsOrder> getOrderListByOrderIds(TmsOrderFilter tmsOrderIdFilter) {
        ResultMode<TmsOrder> resultMode = tmsOrderInter.getOrderListByOrderIds(tmsOrderIdFilter);
        if (resultMode == null || !resultMode.getSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return Collections.emptyList();
        }
        return resultMode.getModel();
    }

    /**
     * 获取司机货源接单限制
     */
    @Override
    public Integer getDriverWaybillCount(String driverId, String goodsId) {
        if (StrUtil.hasBlank(driverId, goodsId)) {
            return null;
        }

        TmsWaybillFilter filter = new TmsWaybillFilter();
        filter.setDriverId(driverId);
        filter.setGoodsId(goodsId);
        ResultMode<Integer> resultMode = tmsWaybillInter.getDriverWaybillCount(filter);
        if (ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    /**
     * 查询订单信息
     * @param orderId
     * @return
     */
    @Override
    public TmsOrderAndAddressVO getTmsOrderAndAddress(String orderId) {
        if(StrUtil.isBlank(orderId)){
            return null;
        }
        ResultMode<TmsOrderAndAddressVO> resultMode = tmsOrderInter.getTmsOrderAndAddress(orderId);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed()){
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    public TmsOrder getTmsOrder(String orderId) {
        if(StrUtil.isBlank(orderId)){
            return null;
        }
        TmsOrderFilter filter = new TmsOrderFilter();
        filter.setOrderId(orderId);
        ResultMode<TmsOrder> resultMode = tmsOrderInter.getOrderInfo(filter);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed()){
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    /**
     * 查询附近运力
     * @param query
     * @return
     */
    @LogPrintPoint
    @Override
    public List<TmsNearbyTransResDTO> getNeatByTrans(GetNeatByTransQuery query) {
        if(ObjUtil.isNull(query) || ObjUtil.isAllEmpty(ReflectUtil.getFields(query.getClass()))){
            return new ArrayList<>();
        }
        ResultMode<TmsNearbyTransResDTO> resultMode = tmsWaybillInter.getNeatByTrans(query);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            log.info("getNeatByTrans#查询附近运力异常：{}", JSONUtil.toJsonStr(resultMode));
            return new ArrayList<>();
        }
        return resultMode.getModel();
    }
}
