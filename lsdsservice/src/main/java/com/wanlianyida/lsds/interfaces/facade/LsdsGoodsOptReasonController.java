package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.command.OptReasonAddCommand;
import com.isoftstone.hig.lsds.api.command.OptReasonDelCommand;
import com.isoftstone.hig.lsds.api.command.OptReasonUpdSortCommand;
import com.isoftstone.hig.lsds.api.dto.LsdsGoodsOptReasonDTO;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsOptReasonFilter;
import com.wanlianyida.lsds.application.service.LsdsGoodsOptReasonAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 货源审核原因
 * <AUTHOR>
 */
@RequestMapping(value = "/optReason")
@RestController
public class LsdsGoodsOptReasonController   {

    @Resource
    private LsdsGoodsOptReasonAppService lsdsGoodsOptReasonService;

    @PostMapping(value = "/queryList")
    public ResultMode<LsdsGoodsOptReasonDTO> queryList(@RequestBody @Validated PagingInfo<LsdsGoodsOptReasonFilter> pagingInfo) {
        PageInfo<LsdsGoodsOptReasonDTO> pageInfo = lsdsGoodsOptReasonService.queryList(pagingInfo);
        if (ObjUtil.isNull(pageInfo) || IterUtil.isEmpty(pageInfo.getList())) {
            return ResultMode.success();
        }

        return ResultMode.successPageList(pageInfo.getList(), (int) pageInfo.getTotal());
    }

    @PostMapping(value = "/add")
    public ResultMode add(@RequestBody @Validated  OptReasonAddCommand command) {
        return lsdsGoodsOptReasonService.add(command);
    }

    @PostMapping(value = "/updateSort")
    public ResultMode updateSort(@RequestBody @Validated List<OptReasonUpdSortCommand> commandList) {
        return lsdsGoodsOptReasonService.updateSort(commandList);
    }

    @PostMapping(value = "/delete")
    public ResultMode delete(@RequestBody @Validated OptReasonDelCommand command) {
        return lsdsGoodsOptReasonService.delete(command.getId());
    }

}
