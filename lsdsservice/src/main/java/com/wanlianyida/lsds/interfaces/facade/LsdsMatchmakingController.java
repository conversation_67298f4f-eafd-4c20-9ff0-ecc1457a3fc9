package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.command.LsdsMatchmakingConfigAddCommand;
import com.isoftstone.hig.lsds.api.command.LsdsMatchmakingConfigUpdateCommand;
import com.isoftstone.hig.lsds.api.dto.LsdsMatchmakingConfigDTO;
import com.isoftstone.hig.lsds.api.dto.LsdsMatchmakingRecordDTO;
import com.isoftstone.hig.lsds.api.query.LsdsMatchmakingConfigQuery;
import com.isoftstone.hig.lsds.api.query.LsdsMatchmakingRecordQuery;
import com.wanlianyida.lsds.domain.service.LsdsMatchmakingDomainService;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.isoftstone.hig.lsds.api.util.Constants.MATCHMAKING_GOODS_CHANGE_TOPIC;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年11月14日 19:33
 */
@RequestMapping("/matchmaking")
@RestController
public class LsdsMatchmakingController {

    @Resource
    private LsdsMatchmakingDomainService matchmakingService;

    @Resource
    private LsdsKafkaSender lsdsKafkaSender;

    @PostMapping("/addConfig")
    public ResultMode addConfig(@Validated @RequestBody LsdsMatchmakingConfigAddCommand command) {
        matchmakingService.addConfig(command);
        return ResultMode.success();
    }

    @PostMapping("/updateConfig")
    public ResultMode updateConfig(@Validated @RequestBody LsdsMatchmakingConfigUpdateCommand command) {
        matchmakingService.updateConfig(command);
        //添加完订单发消息
        if (!StrUtil.startWith(command.getBusId(), "DFQ")) {
            lsdsKafkaSender.send(MATCHMAKING_GOODS_CHANGE_TOPIC, command.getBusId());
        }
        return ResultMode.success();
    }

    @PostMapping("/queryConfig")
    public ResultMode<LsdsMatchmakingConfigDTO> queryConfig(@Validated @RequestBody LsdsMatchmakingConfigQuery query) {
        LsdsMatchmakingConfigDTO result = matchmakingService.queryConfig(query);
        return ResultMode.success(result);
    }

    @PostMapping("/queryConfigRecord")
    public ResultMode<LsdsMatchmakingRecordDTO> queryConfigRecord(@Validated @RequestBody PagingInfo<LsdsMatchmakingRecordQuery> query) {
        return matchmakingService.queryConfigRecord(query);
    }
}
