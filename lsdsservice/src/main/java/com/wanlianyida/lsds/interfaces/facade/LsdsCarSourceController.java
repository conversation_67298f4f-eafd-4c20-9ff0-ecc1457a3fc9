
package com.wanlianyida.lsds.interfaces.facade;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsCarSource;
import com.isoftstone.hig.lsds.api.filter.LsdsCarSourceFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsCarSourceVo;
import com.wanlianyida.lsds.application.service.LsdsCarSourceAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 创建描述：车源ID 控制器 类的后面Controller其实可以不用，只是为了容易区分
 *
 * <AUTHOR>
 * 创建时间 2019/11/15 14:56:33
 */
@RequestMapping(value = "/LsdsCarSource")
@RestController
public class LsdsCarSourceController   {

    @Autowired
    private LsdsCarSourceAppService lsdsCarSourceTran;

    /**
     * 根据实体车源ID 添加记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param vo 车源ID 实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【增加成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【增加成功】编码,ResultMode.errMsg为相应【增加成功】描述；
     * 否则ResultMode.succeed=false【增加失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【增加失败】编码,ResultMode.errMsg为相应【增加失败】描述。
     */
    @RequestMapping(value = "/lsdsCarSourceAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsCarSourceAdd(@RequestBody LsdsCarSourceVo vo) {

        ResultMode<String> resultModel = new ResultMode<String>();

        if (null != vo && null != vo.getLsdsCarSource() && null != vo.getLsdsGoodsAddress()) {
            //#region 判断验证数据
            if (StringUtils.isEmpty(vo.getLsdsGoodsAddress().getSendAddrProvince())) {
                resultModel.setErrMsg("sendAddrProvince:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(vo.getLsdsGoodsAddress().getSendAddrCity())) {
                resultModel.setErrMsg("sendAddrCity:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
//            if (StringUtils.isEmpty(vo.getLsdsGoodsAddress().getSendAddrArea())) {
//                resultModel.setErrMsg("sendAddrArea:" + "不能为空字符");
//                resultModel.setSucceed(false);
//            }
            if (StringUtils.isEmpty(vo.getLsdsGoodsAddress().getSendAddrProvinceName())) {
                resultModel.setErrMsg("sendAddrProvinceName:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(vo.getLsdsGoodsAddress().getSendAddrCityName())) {
                resultModel.setErrMsg("sendAddrCityName:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
//            if (StringUtils.isEmpty(vo.getLsdsGoodsAddress().getSendAddrAreaName())) {
//                resultModel.setErrMsg("sendAddrAreaName:" + "不能为空字符");
//                resultModel.setSucceed(false);
//            }

            if (StringUtils.isEmpty(vo.getLsdsGoodsAddress().getReceiveAddrProvince())) {
                resultModel.setErrMsg("receiveAddrProvince:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(vo.getLsdsGoodsAddress().getReceiveAddrCity())) {
                resultModel.setErrMsg("receiveAddrCity:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
//            if (StringUtils.isEmpty(vo.getLsdsGoodsAddress().getReceiveAddrArea())) {
//                resultModel.setErrMsg("receiveAddrArea:" + "不能为空字符");
//                resultModel.setSucceed(false);
//            }
            if (StringUtils.isEmpty(vo.getLsdsGoodsAddress().getReceiveAddrProvinceName())) {
                resultModel.setErrMsg("receiveAddrProvinceName:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(vo.getLsdsGoodsAddress().getReceiveAddrCityName())) {
                resultModel.setErrMsg("receiveAddrCityName:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
//            if (StringUtils.isEmpty(vo.getLsdsGoodsAddress().getReceiveAddrAreaName())) {
//                resultModel.setErrMsg("receiveAddrAreaName:" + "不能为空字符");
//                resultModel.setSucceed(false);
//            }
            if (StringUtils.isEmpty(vo.getLsdsCarSource().getLinker())) {
                resultModel.setErrMsg("linker:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(vo.getLsdsCarSource().getLinkerPhoneNumber())) {
                resultModel.setErrMsg("linkerPhoneNumber:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(vo.getLsdsCarSource().getAssignCarPlateNumber())) {
                resultModel.setErrMsg("assignCarPlateNumber:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(vo.getLsdsCarSource().getAssignCarType())) {
                resultModel.setErrMsg("assignCarType:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            //#endregion
        } else {
            resultModel.setSucceed(false);
        }
        if (!resultModel.getSucceed()) {
            return resultModel;
        } else {
            resultModel = lsdsCarSourceTran.lsdsCarSourceAdd(vo, "");
        }
        //#endregion
        return resultModel;
    }

    /**
     * 根据车源ID 主键删除记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param carSourceId 车源ID 主键
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【删除成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【删除成功】编码,ResultMode.errMsg为相应【删除成功】描述；
     * 否则ResultMode.succeed=false【删除失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【删除失败】编码,ResultMode.errMsg为相应【删除失败】描述。
     */
    @RequestMapping(value = "/lsdsCarSourceDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsCarSourceDel(@RequestParam("carSourceId") String carSourceId) {
        ResultMode<String> resultModel = new ResultMode<String>();
        if (StringUtils.isEmpty(carSourceId)) {
            resultModel.setSucceed(false);
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
        } else {
            resultModel = lsdsCarSourceTran.lsdsCarSourceDel(carSourceId, "");
        }
        return resultModel;
    }

    /**
     * 根据车源ID 实体修改记录，内部以ID主键修改
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param model 车源ID 实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【修改成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【修改成功】编码,ResultMode.errMsg为相应【修改成功】描述；
     * 否则ResultMode.succeed=false【修改失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【修改失败】编码,ResultMode.errMsg为相应【修改失败】描述。
     */
    @RequestMapping(value = "/lsdsCarSourceUpdate", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsCarSourceUpdate(@RequestBody LsdsCarSource model) {
        ResultMode<String> resultModel = new ResultMode<String>();
        if (null != model) {
            if (StringUtils.isEmpty(model.getCarSourceId())) {
                resultModel.setErrMsg("carSourceId:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
        } else {
            resultModel.setSucceed(false);
        }
        if (!resultModel.getSucceed()) {
            return resultModel;
        } else {
            resultModel = lsdsCarSourceTran.lsdsCarSourceUpdate(model, "");
        }
        return resultModel;
    }

    /**
     * 车源ID 分页查询
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsCarSource>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为车源ID 实体类LsdsCarSource列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/lsdsCarSourcePaging", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsCarSource> lsdsCarSourcePaging(PagingInfo<LsdsCarSourceFilter> pageInfo) {
        ResultMode<LsdsCarSource> resultModel = new ResultMode<LsdsCarSource>();
        if (null != pageInfo) {
            resultModel = lsdsCarSourceTran.lsdsCarSourcePaging(pageInfo, "");
        } else {
            resultModel.setSucceed(false);
        }
        if (!resultModel.getSucceed()) {
            return resultModel;
        }
        //#endregion
        return resultModel;

    }


    /**
     * 车源信息分页查询
     * 创建者：cgb
     * 创建时间：2019/11/30
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/getCarSourceListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsCarSource> getCarSourceListPage(@RequestBody PagingInfo<LsdsCarSourceFilter> pageInfo) {
        ResultMode<LsdsCarSource> resultModel = new ResultMode<LsdsCarSource>();
        resultModel = lsdsCarSourceTran.getCarSourceListPage(pageInfo);
        return resultModel;
    }

    /**
     * <AUTHOR>
     * @since 2021-2-4
     * 物流信息车源列表查询
     * @param lsdsCarSourceFilterPagingInfo
     * @return
     */

    @RequestMapping(value = "/findLsdsCarSource",produces ={"application/json;charset=UTF-8"},method = RequestMethod.POST)
    public ResultMode<LsdsCarSource> findLsdsCarSource(@RequestBody @Autowired PagingInfo<LsdsCarSourceFilter> lsdsCarSourceFilterPagingInfo) {
        ResultMode<LsdsCarSource> resultMode=lsdsCarSourceTran.findLsdsCarSource(lsdsCarSourceFilterPagingInfo);
        return resultMode;
    }

    /**
     * 根据货源ID查询货源详细信息（包含货源主信息、货源分段信息、承运商信息）
     * 创建者：cgb
     * 创建时间：2019/11/20
     *
     * @param carSourceId 货源ID
     * @return {@code  ResultMode<LsdsCarSource>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为车源ID 实体类LsdsCarSource数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/lsdsCarSourceGet", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsCarSource> lsdsCarSourceGet(@RequestParam("carSourceId") String carSourceId) {
        ResultMode<LsdsCarSource> resultModel = new ResultMode<LsdsCarSource>();
        if (StringUtils.isEmpty(carSourceId)) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        } else {
            resultModel = lsdsCarSourceTran.lsdsCarSourceGet(carSourceId, "");
        }
        return resultModel;
    }


    /**
     * 根据车牌号查询车源信息（批量）
     * 创建者：cgb
     * 创建时间：2019/12/19
     *
     * @param strList
     * @return {@code  ResultMode<LsdsCarSource>}
     */
    @RequestMapping(value = "/getCarSourceListByNum", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsCarSource> getCarSourceListByNum(@RequestBody List<String> strList) {
        ResultMode<LsdsCarSource> resultModel = new ResultMode<LsdsCarSource>();
        if (null == strList || strList.size() == 0) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg("查询参数车牌号不能为空!");
            resultModel.setSucceed(false);
        } else {
            resultModel = lsdsCarSourceTran.getCarSourceListByNum(strList);
        }
        return resultModel;
    }


    /**
     * 查询车源信息
     * 创建者: cgb
     * 创建时间: 2019/12/21
     *
     * @return {@code  ResultMode<LsdsCarSource>}
     */
    @RequestMapping(value = "/getCarSourceListForHomePage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsCarSource> getCarSourceListForHomePage() {
        ResultMode<LsdsCarSource> resultModel = new ResultMode<LsdsCarSource>();
        LsdsCarSource carSource = new LsdsCarSource();
        //状态为发布中
        carSource.setCarStatus("0");
        //最多返回5条数据
        carSource.setExLimit(4);
        resultModel = lsdsCarSourceTran.getCarSourceList(carSource);
        return resultModel;
    }


}
