package com.wanlianyida.lsds.interfaces.facade;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.mvcvo.GoodsRecommendReqVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsAttentionVO;
import com.wanlianyida.lsds.application.service.LsdsGoodsAttentionAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 货源推荐
 *
 * <AUTHOR>
 * @date 2023/03/03 10:00:25
 */
@RequestMapping("/goodsRecommend")
@RestController
@Slf4j
public class GoodsRecommendController   {


    @Resource
    private LsdsGoodsAttentionAppService goodsAttentionServiceTran;

    @RequestMapping("/getGoodsRecommendInfo")
    public ResultMode<LsdsGoodsAttentionVO> getGoodsRecommendInfo(@RequestBody GoodsRecommendReqVo recommendReq) {
        try {
            return goodsAttentionServiceTran.getGoodsRecommendInfo(recommendReq);
        } catch (Exception e) {
            log.error("查询货源推荐异常:{}",e);
            return ResultMode.success();
        }
    }
}
