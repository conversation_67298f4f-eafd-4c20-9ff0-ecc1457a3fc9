//=========================================================
//===       此类是由代码工具生成，框架开发者
//===       框架开发者Create By: 李健华
//===       Create Date: 2019/11/15 14:56:33
//=========================================================
package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.LogHelper;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.framework.component.file.utils.ExcelUtil;
import com.isoftstone.hig.lsds.api.entity.LsdsGoods;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsOffer;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsFilter;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsOfferFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsVo;
import com.wanlianyida.lsds.application.service.LsdsGoodsOfferAppService;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 创建描述：对货源报价ID 货源报价 我准备要接单进行承运进行报价。货主（老板）审核评估你的价格OK了再进行下单给你承运.控制器 类的后面Controller其实可以不用，只是为了容易区分
 *
 * <AUTHOR>
 * 创建时间 2019/11/15 14:56:33
 */
@RequestMapping(value = "/LsdsGoodsOffer")
@RestController
public class LsdsGoodsOfferController {

    @Autowired
    private LsdsGoodsOfferAppService lsdsGoodsOfferTran;

    /**
     * 货源报价
     */
    @RequestMapping(value = "/lsdsGoodsOfferAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsOfferAdd(@RequestBody @ApiParam LsdsGoodsVo model) {

        ResultMode<String> resultModel = new ResultMode<String>();
        if (null != model.getGoodsOfferList() && model.getGoodsOfferList().size() > 0) {
            for (LsdsGoodsOffer offer : model.getGoodsOfferList()) {
                //#region 判断验证数据
                if (StringUtils.isEmpty(offer.getGoodsId())) {
                    resultModel.getModel().add("goodsId:" + "不能为空字符");
                    resultModel.setSucceed(false);
                }
                if (StringUtils.isEmpty(offer.getOfferRound()) || offer.getOfferRound() == 0) {
                    resultModel.getModel().add("offerRound:" + "数据格式不能为零");
                    resultModel.setSucceed(false);
                }
                if (StringUtils.isEmpty(offer.getCompanyId())) {
                    resultModel.getModel().add("companyId:" + "不能为空字符");
                    resultModel.setSucceed(false);
                }
                if (StringUtils.isEmpty(offer.getSortNode()) || offer.getSortNode() == 0) {
                    resultModel.getModel().add("sortNode:" + "不能为空字符");
                    resultModel.setSucceed(false);
                }

                if (!resultModel.getSucceed()) {
                    resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
                    resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
                    resultModel.setSucceed(false);
                }
                //#endregion
            }
            resultModel = lsdsGoodsOfferTran.lsdsGoodsOfferAdd(model, "");
            if (!resultModel.isSucceed()) {
                return resultModel;
            }

            //校验是否满足直接下单条件
            boolean checkAuto = lsdsGoodsOfferTran.checkAutoPlaceOrder(model.getExLsdsGoods());
            if (checkAuto) {
                //满足条件，自动下单
                resultModel = lsdsGoodsOfferTran.autoPlaceOrder(model.getExLsdsGoods(), IterUtil.getFirst(model.getGoodsOfferList()).getCompanyId());
                if (!resultModel.isSucceed()) {
                    return resultModel;
                }
            }
        }
        return resultModel;
    }

    /**
     * 根据实体对货源报价ID 货源报价 我准备要接单进行承运进行报价。货主（老板）审核评估你的价格OK了再进行下单给你承运.添加记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param model 货源编号  货源信息表实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【增加成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【增加成功】编码,ResultMode.errMsg为相应【增加成功】描述；
     * 否则ResultMode.succeed=false【增加失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【增加失败】编码,ResultMode.errMsg为相应【增加失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsOfferAddDown", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsOfferAddDown(@RequestBody @ApiParam LsdsGoodsVo model) {

        ResultMode<String> resultModel = new ResultMode<String>();
        if (null != model.getGoodsOfferList() && model.getGoodsOfferList().size() > 0) {
            for (LsdsGoodsOffer offer : model.getGoodsOfferList()) {
                //#region 判断验证数据
                if (StringUtils.isEmpty(offer.getGoodsId())) {
                    resultModel.getModel().add("goodsId:" + "不能为空字符");
                    resultModel.setSucceed(false);
                }
                if (StringUtils.isEmpty(offer.getOfferRound()) || offer.getOfferRound() == 0) {
                    resultModel.getModel().add("offerRound:" + "数据格式不能为零");
                    resultModel.setSucceed(false);
                }
                if (StringUtils.isEmpty(offer.getCompanyId())) {
                    resultModel.getModel().add("companyId:" + "不能为空字符");
                    resultModel.setSucceed(false);
                }
                if (StringUtils.isEmpty(offer.getSortNode()) || offer.getSortNode() == 0) {
                    resultModel.getModel().add("sortNode:" + "不能为空字符");
                    resultModel.setSucceed(false);
                }

                if (!resultModel.getSucceed()) {
                    resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
                    resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
                    resultModel.setSucceed(false);
                }
                //#endregion
            }
            resultModel = lsdsGoodsOfferTran.lsdsGoodsOfferAddDown(model, "");
        }
        return resultModel;
    }

    /**
     * 根据对货源报价ID 货源报价 我准备要接单进行承运进行报价。货主（老板）审核评估你的价格OK了再进行下单给你承运.主键删除记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param offerId 货源报价ID
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【删除成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【删除成功】编码,ResultMode.errMsg为相应【删除成功】描述；
     * 否则ResultMode.succeed=false【删除失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【删除失败】编码,ResultMode.errMsg为相应【删除失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsOfferDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsOfferDel(@RequestParam("offerId") String offerId) {
        //try {
        return lsdsGoodsOfferTran.lsdsGoodsOfferDel(offerId, "");
        // 要替换  return CommonParaBLL.getInstance().lsdsGoodsOfferDel(offerId,"");
        //} catch (Exception ex) {
        //	LogHelper.writeError(ex.getMessage(), ex);
        //}
        //return false;
    }

    /**
     * 根据对货源报价ID 货源报价 我准备要接单进行承运进行报价。货主（老板）审核评估你的价格OK了再进行下单给你承运.实体修改记录，内部以ID主键修改
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param model 货源编号  货源信息表实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【修改成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【修改成功】编码,ResultMode.errMsg为相应【修改成功】描述；
     * 否则ResultMode.succeed=false【修改失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【修改失败】编码,ResultMode.errMsg为相应【修改失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsOfferUpdate", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsOfferUpdate(@RequestBody @ApiParam LsdsGoodsOffer model) {
        //try {
        ResultMode<String> resultModel = new ResultMode<String>();
        //#region 判断验证数据
        if (StringUtils.isEmpty(model.getOfferId())) {
            resultModel.getModel().add("offerId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getGoodsId())) {
            resultModel.getModel().add("goodsId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (model.getOfferRound() != 0) {
            resultModel.getModel().add("offerRound:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getCompanyId())) {
            resultModel.getModel().add("companyId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getCompanyShortName())) {
            resultModel.getModel().add("companyShortName:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getTransportationType())) {
            resultModel.getModel().add("transportationType:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (model.getEnquiryTypeBasePrice() == null || model.getEnquiryTypeBasePrice().equals(BigDecimal.ZERO)) {
            resultModel.getModel().add("enquiryTypeBasePrice:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (model.getEnquiryTypeBaseTaxRate() == null || model.getEnquiryTypeBaseTaxRate().equals(BigDecimal.ZERO)) {
            resultModel.getModel().add("enquiryTypeBaseTaxRate:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (model.getEnquiryTypeBaseOpenTicket() == null || model.getEnquiryTypeBaseOpenTicket().equals(BigDecimal.ZERO)) {
            resultModel.getModel().add("enquiryTypeBaseOpenTicket:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (model.getOfferWeight() == null || model.getOfferWeight().equals(BigDecimal.ZERO)) {
            resultModel.getModel().add("offerWeight:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (model.getStatus() != 0) {
            resultModel.getModel().add("status:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getRemark())) {
            resultModel.getModel().add("remark:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (model.getSortNode() != 0) {
            resultModel.getModel().add("sortNode:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getCreateBy())) {
            resultModel.getModel().add("createBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getModifyBy())) {
            resultModel.getModel().add("modifyBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }


        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        }
        //#endregion


        resultModel = lsdsGoodsOfferTran.lsdsGoodsOfferUpdate(model, "");
        return resultModel;
        // 要替换	return CommonParaBLL.getInstance().lsdsGoodsOfferUpdate(model,"");
        //} catch (Exception ex) {
        //	LogHelper.writeError(ex.getMessage(), ex);
        //}
        //return false;
    }

    /**
     * 承运人货源报价列表
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoodsOffer>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源报价实体类LsdsGoodsOffer列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsOfferPaging", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsOffer> lsdsGoodsOfferPaging(@RequestBody @ApiParam PagingInfo<LsdsGoodsOfferFilter> pageInfo) {
        ResultMode<LsdsGoodsOffer> resultModel = new ResultMode<LsdsGoodsOffer>();
        StringBuffer errsb = new StringBuffer();

        //#region 判断验证数据
        if (StringUtils.isEmpty(pageInfo.filterModel.getCompanyId())) {
            errsb.append(String.format(" %s 不能为空字符", "companyId"));
            resultModel.setSucceed(false);
        }
        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        }
        //#endregion

        resultModel = lsdsGoodsOfferTran.lsdsGoodsOfferPaging(pageInfo, "");
        return resultModel;

    }

    /**
     * 根据货源ID查询货源详细信息（包含货源主信息、货源分段信息、承运商信息）
     * 创建者：cgb
     * 创建时间：2019/11/20
     *
     * @param model 货源编号  货源信息表实体类
     * @return {@code  ResultMode<LsdsGoodsVo>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoodsVo数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsGetDetails", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsVo> lsdsGoodsGetDetails(@RequestBody @ApiParam LsdsGoods model) {
        ResultMode<LsdsGoodsVo> resultModel = new ResultMode<LsdsGoodsVo>();
        //#region 判断验证数据
        if (null != model) {
            if (StringUtils.isEmpty(model.getGoodsId())) {
                resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
                resultModel.setErrMsg("goodsId:不能为空!");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(model.getCompanyId())) {
                resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
                resultModel.setErrMsg("companyId:不能为空!");
                resultModel.setSucceed(false);
            }
            if (null == model.getOfferCurrentRounds() || model.getOfferCurrentRounds() == 0) {
                resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
                resultModel.setErrMsg("offerCurrentRounds:不能为空!");
                resultModel.setSucceed(false);
            }
        }
        if (resultModel.getSucceed()) {
            resultModel = lsdsGoodsOfferTran.lsdsGoodsGetDetails(model, "");
        }
        return resultModel;
    }

    /**
     * 根据货源ID查询货源详细信息（包含货源主信息、货源分段信息、承运商信息）
     * 创建者：cgb
     * 创建时间：2019/11/20
     *
     * @param model 货源编号  货源信息表实体类
     * @return {@code  ResultMode<LsdsGoodsVo>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoodsVo数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsGetDetailsDown", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsVo> lsdsGoodsGetDetailsDown(@RequestBody @ApiParam LsdsGoods model) {
        ResultMode<LsdsGoodsVo> resultModel = new ResultMode<LsdsGoodsVo>();
        //#region 判断验证数据
        if (null != model) {
            if (StringUtils.isEmpty(model.getGoodsId())) {
                resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
                resultModel.setErrMsg("goodsId:不能为空!");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(model.getCompanyId())) {
                resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
                resultModel.setErrMsg("companyId:不能为空!");
                resultModel.setSucceed(false);
            }
        }
        if (resultModel.getSucceed()) {
            resultModel = lsdsGoodsOfferTran.lsdsGoodsGetDetailsDown(model, "");
        }
        return resultModel;
    }

    @RequestMapping(value = "/lsdsGoodsDetails", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> lsdsGoodsDetails(@RequestBody @ApiParam LsdsGoods model) {
        return lsdsGoodsOfferTran.lsdsGoodsDetails(model);
    }

    /**
     * 进行下一轮报价
     * 创建者：cgb
     * 创建时间：2019/11/27
     *
     * @param model 货源编号  货源信息表实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【报价成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【报价成功】编码,ResultMode.errMsg为相应【报价成功】描述；
     * 否则ResultMode.succeed=false【报价失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【报价失败】编码,ResultMode.errMsg为相应【报价失败】描述。
     */
    @RequestMapping(value = "/offerToNetRound", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> offerToNetRound(@RequestBody @ApiParam LsdsGoodsVo model) {
        ResultMode<String> resultModel = new ResultMode<String>();
        if (StringUtils.isEmpty(model.getLsdsGoods().getGoodsId())) {
            resultModel.getModel().add("goodsId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getLsdsGoods().getOfferRound())) {
            resultModel.getModel().add("offerRound:" + "不能为空字符");
            resultModel.setSucceed(false);
        }

        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        } else {
            resultModel = lsdsGoodsOfferTran.offerToNetRound(model, "");
        }

        return resultModel;
    }


    /**
     * 货源列表(我要承运)
     * 创建者：cgb
     * 创建时间：2019/11/30
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/getGoodsListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> getGoodsListPage(@RequestBody @ApiParam PagingInfo<LsdsGoodsFilter> pageInfo) {
        return lsdsGoodsOfferTran.getGoodsListPage(pageInfo);
    }

    /**
     * App货源列表(找货)
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     * <AUTHOR>
     * 创建时间 2019/11/29 14:43
     */
    @RequestMapping(value = "/getGoodsListPageForApp", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> getGoodsListPageForApp(@RequestBody @ApiParam PagingInfo<LsdsGoodsFilter> pageInfo) {
        return lsdsGoodsOfferTran.getGoodsListPageForAPP(pageInfo);
    }

    /**
     * 货源信息分页查询
     * 创建者：cgb
     * 创建时间：2019/11/30
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/getGoodsOfferListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> getGoodsOfferListPage(@RequestBody @ApiParam PagingInfo<LsdsGoodsFilter> pageInfo) {
        ResultMode<LsdsGoods> resultModel = new ResultMode<LsdsGoods>();
        if (null != pageInfo) {
            if (StringUtils.isEmpty(pageInfo.filterModel) || StringUtils.isEmpty(pageInfo.filterModel.getCompanyId())) {
                resultModel.setErrMsg("companyId:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
        } else {
            resultModel.setSucceed(false);
        }
        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
        } else {
            resultModel = lsdsGoodsOfferTran.getGoodsOfferListPage(pageInfo);
        }
        return resultModel;
    }

    /**
     * 下游询价时，平台3pl使用下游承运商的报价作为自己给发货人的报价
     * 创建者: cgb
     * 创建时间: 2019/12/17
     *
     * @param model
     * @returns ResultMode<String>
     */
    @RequestMapping(value = "/lsdsGoodsOfferToSender", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsOfferToSender(@RequestBody @ApiParam LsdsGoodsVo model) {
        ResultMode<String> resultModel = new ResultMode<String>();
        if (StringUtils.isEmpty(model.getLsdsGoods().getGoodsId())) {
            resultModel.getModel().add("goodsId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (null == model.getGoodsOfferList() || model.getGoodsOfferList().size() == 0) {
            resultModel.getModel().add("goodsOfferList:不能为空");
            resultModel.setSucceed(false);
        }
        if (resultModel.getSucceed()) {
            resultModel = lsdsGoodsOfferTran.lsdsGoodsOfferToSender(model, "");
        }

        return resultModel;
    }


    /**
     * 根据货源ID查询货源详细信息（包含货源主信息、货源分段信息、承运商信息）
     * 创建者：cgb
     * 创建时间：2019/11/20
     *
     * @param goodsId 货源ID
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsGet", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> lsdsGoodsGet(@RequestBody String goodsId) {
        ResultMode<LsdsGoods> resultModel = new ResultMode<LsdsGoods>();
        if (StringUtils.isEmpty(goodsId)) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg("goodsId:不能为空字符！");
            resultModel.setSucceed(false);
        } else {
            resultModel = lsdsGoodsOfferTran.lsdsGoodsGet(goodsId);
        }
        return resultModel;
    }

    /**
     * 查询工作台承运方货源统计信息
     * 创建者：cgb
     *
     * @param lsdsGoods 货源实体
     * @return {@code ResultMode<Map<String, Object>>}
     */
    @RequestMapping(value = "/lsdsGoodsSupplierView", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<Map<String, String>> lsdsGoodsSupplierView(@RequestBody @ApiParam LsdsGoods lsdsGoods) {
        ResultMode<Map<String, String>> resultModel = new ResultMode<Map<String, String>>();
        resultModel = lsdsGoodsOfferTran.lsdsGoodsSupplierView(lsdsGoods);
        return resultModel;
    }

    /**
     * 查询工作台货源大厅货源列表信息
     * 创建者：cgb
     * 创建时间：2020/3/24
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoods>}
     */
    @RequestMapping(value = "/getBenchGoodsListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> getBenchGoodsListPage(@RequestBody @ApiParam PagingInfo<LsdsGoodsFilter> pageInfo) {
        ResultMode<LsdsGoods> resultModel = new ResultMode<LsdsGoods>();
        resultModel = lsdsGoodsOfferTran.getBenchGoodsListPage(pageInfo);
        return resultModel;
    }

    /**
     * 导出我要承运-报价列表信息
     *
     * @param filter 查询条件
     * <AUTHOR>
     * 创建时间 2020/6/12
     */
    @RequestMapping(value = "/exportLsdsGoodsOfferList", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> exportLsdsGoodsOfferList(@RequestBody @ApiParam LsdsGoodsFilter filter) throws IOException {
        ResultMode<String> returnModel = new ResultMode<String>();
        if (null != filter) {
            if (StringUtils.isEmpty(filter.getCompanyId())) {
                returnModel.setErrMsg("companyId: 不能为空字符");
                returnModel.setSucceed(false);
            }
        } else {
            returnModel.setSucceed(false);
        }
        if (!returnModel.getSucceed()) {
            return returnModel;
        } else {
            List<LsdsGoods> goodsList = lsdsGoodsOfferTran.getExportGoodsOfferList(filter);
            InputStream is = null;
            ClassPathResource resource = null;
            try {
                HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
                HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getResponse();
                if ("2".equals(filter.getCompanyId())) {
                    resource = new ClassPathResource("verifyxml/ExportLsdsGoodsOfferAdminMode.xml");
                } else {
                    resource = new ClassPathResource("verifyxml/ExportLsdsGoodsOfferMode.xml");
                }


                is = resource.getInputStream();
                ExcelUtil.exportDataToExcel(request, response, "报价管理", is, goodsList, "报价管理", JwtUtil.getInstance().getUsernameByToken() + "(" + JwtUtil.getInstance().getLoginNameByToken() + ")");
            } catch (Exception e) {
                LogHelper.writeError("导出报价管理信息异常：", e);
            } finally {
                if (null != is) {
                    is.close();
                }
            }
        }
        return returnModel;
    }

    /**
     * 根据报价id查询成交报价信息
     */
    @RequestMapping(value = "/getOfferByOfferId", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsOffer> getOfferByOfferId(@RequestParam("offerId") String offerId) {
        if (StrUtil.isBlank(offerId)) {
            return ResultMode.fail("报价id不允许为空");
        }

        LsdsGoodsOffer info = lsdsGoodsOfferTran.getOfferByOfferId(offerId);

        return ResultMode.success(info);
    }
}
