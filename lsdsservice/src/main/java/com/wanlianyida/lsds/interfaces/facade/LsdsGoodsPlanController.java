
package com.wanlianyida.lsds.interfaces.facade;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsPlan;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsPlanFilter;
import com.wanlianyida.lsds.application.service.LsdsGoodsPlanAppService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 创建描述：方案ID控制器 类的后面Controller其实可以不用，只是为了容易区分
 *
 * <AUTHOR>
 * 创建时间 2019/11/15 14:56:33
 */
@RequestMapping(value = "/LsdsGoodsPlan")
@RestController
public class LsdsGoodsPlanController   {
    @Resource
    private LsdsGoodsPlanAppService lsdsGoodsPlanBusiness;
    /**
     * 根据实体方案ID添加记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param model 方案ID实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【增加成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【增加成功】编码,ResultMode.errMsg为相应【增加成功】描述；
     * 否则ResultMode.succeed=false【增加失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【增加失败】编码,ResultMode.errMsg为相应【增加失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsPlanAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsPlanAdd(@RequestBody LsdsGoodsPlan model) {
        //try {

        ResultMode<String> resultModel = new ResultMode<String>();

        //#region 判断验证数据
        if (StringUtils.isNotBlank(model.getPlanId())) {
            resultModel.getModel().add("planId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getOfferSplitId())) {
            resultModel.getModel().add("offerSplitId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSiteCityCode())) {
            resultModel.getModel().add("startSiteCityCode:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSiteCityName())) {
            resultModel.getModel().add("startSiteCityName:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSiteAddress())) {
            resultModel.getModel().add("startSiteAddress:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndSiteCityCode())) {
            resultModel.getModel().add("endSiteCityCode:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndSiteCityName())) {
            resultModel.getModel().add("endSiteCityName:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndSiteAddress())) {
            resultModel.getModel().add("endSiteAddress:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getTransportationType())) {
            resultModel.getModel().add("transportationType:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCreateBy())) {
            resultModel.getModel().add("createBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getModifyBy())) {
            resultModel.getModel().add("modifyBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem1())) {
            resultModel.getModel().add("item1:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem2())) {
            resultModel.getModel().add("item2:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem3())) {
            resultModel.getModel().add("item3:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem4())) {
            resultModel.getModel().add("item4:" + "不能为空字符");
            resultModel.setSucceed(false);
        }


        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        }
        //#endregion


        resultModel = lsdsGoodsPlanBusiness.lsdsGoodsPlanAdd(model, "");
        return resultModel;

        // 要替换 return CommonParaBLL.getInstance().lsdsGoodsPlanAdd(model,"");
        //} catch (Exception ex) {
        //	LogHelper.writeError(ex.getMessage(), ex);
        //}
        //return false;
    }

    /**
     * 根据方案ID主键删除记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param planId 方案ID主键
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【删除成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【删除成功】编码,ResultMode.errMsg为相应【删除成功】描述；
     * 否则ResultMode.succeed=false【删除失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【删除失败】编码,ResultMode.errMsg为相应【删除失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsPlanDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsPlanDel(@RequestParam String planId) {
        //try {
        return lsdsGoodsPlanBusiness.lsdsGoodsPlanDel(planId, "");
        // 要替换  return CommonParaBLL.getInstance().lsdsGoodsPlanDel(planId,"");
        //} catch (Exception ex) {
        //	LogHelper.writeError(ex.getMessage(), ex);
        //}
        //return false;
    }

    /**
     * 根据方案ID实体修改记录，内部以ID主键修改
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param model 方案ID实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【修改成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【修改成功】编码,ResultMode.errMsg为相应【修改成功】描述；
     * 否则ResultMode.succeed=false【修改失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【修改失败】编码,ResultMode.errMsg为相应【修改失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsPlanUpdate", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsPlanUpdate(@RequestBody LsdsGoodsPlan model) {
        //try {
        ResultMode<String> resultModel = new ResultMode<String>();
        //#region 判断验证数据
        if (StringUtils.isNotBlank(model.getPlanId())) {
            resultModel.getModel().add("planId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getOfferSplitId())) {
            resultModel.getModel().add("offerSplitId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSiteCityCode())) {
            resultModel.getModel().add("startSiteCityCode:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSiteCityName())) {
            resultModel.getModel().add("startSiteCityName:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSiteAddress())) {
            resultModel.getModel().add("startSiteAddress:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndSiteCityCode())) {
            resultModel.getModel().add("endSiteCityCode:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndSiteCityName())) {
            resultModel.getModel().add("endSiteCityName:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndSiteAddress())) {
            resultModel.getModel().add("endSiteAddress:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getTransportationType())) {
            resultModel.getModel().add("transportationType:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCreateBy())) {
            resultModel.getModel().add("createBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getModifyBy())) {
            resultModel.getModel().add("modifyBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem1())) {
            resultModel.getModel().add("item1:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem2())) {
            resultModel.getModel().add("item2:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem3())) {
            resultModel.getModel().add("item3:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem4())) {
            resultModel.getModel().add("item4:" + "不能为空字符");
            resultModel.setSucceed(false);
        }


        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        }
        //#endregion


        resultModel = lsdsGoodsPlanBusiness.lsdsGoodsPlanUpdate(model, "");
        return resultModel;
        // 要替换	return CommonParaBLL.getInstance().lsdsGoodsPlanUpdate(model,"");
        //} catch (Exception ex) {
        //	LogHelper.writeError(ex.getMessage(), ex);
        //}
        //return false;
    }

    /**
     * 方案ID分页查询
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoodsPlan>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为方案ID实体类LsdsGoodsPlan列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsPlanPaging", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsPlan> lsdsGoodsPlanPaging(@RequestBody PagingInfo<LsdsGoodsPlanFilter> pageInfo) {
        ResultMode<LsdsGoodsPlan> resultModel = new ResultMode<LsdsGoodsPlan>();
        StringBuffer errsb = new StringBuffer();

        //#region 判断验证数据
        if (StringUtils.isNotBlank(pageInfo.filterModel.getPlanId())) {
            errsb.append(String.format(" %s 不能为空字符", "planId"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getOfferSplitId())) {
            errsb.append(String.format(" %s 不能为空字符", "offerSplitId"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getStartSiteCityCode())) {
            errsb.append(String.format(" %s 不能为空字符", "startSiteCityCode"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getStartSiteCityName())) {
            errsb.append(String.format(" %s 不能为空字符", "startSiteCityName"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getStartSiteAddress())) {
            errsb.append(String.format(" %s 不能为空字符", "startSiteAddress"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getEndSiteCityCode())) {
            errsb.append(String.format(" %s 不能为空字符", "endSiteCityCode"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getEndSiteCityName())) {
            errsb.append(String.format(" %s 不能为空字符", "endSiteCityName"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getEndSiteAddress())) {
            errsb.append(String.format(" %s 不能为空字符", "endSiteAddress"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getCreateBy())) {
            errsb.append(String.format(" %s 不能为空字符", "createBy"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getModifyBy())) {
            errsb.append(String.format(" %s 不能为空字符", "modifyBy"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getItem1())) {
            errsb.append(String.format(" %s 不能为空字符", "item1"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getItem2())) {
            errsb.append(String.format(" %s 不能为空字符", "item2"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getItem3())) {
            errsb.append(String.format(" %s 不能为空字符", "item3"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getItem4())) {
            errsb.append(String.format(" %s 不能为空字符", "item4"));
            resultModel.setSucceed(false);
        }


        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        }
        //#endregion


        resultModel = lsdsGoodsPlanBusiness.lsdsGoodsPlanPaging(pageInfo, "");
        // 要替换         resultModel = CommonParaBLL.getInstance().lsdsGoodsPlanPaging(pageInfo,"");

        return resultModel;

    }


}
