//=========================================================
//===       此类是由代码工具生成，框架开发者
//===       框架开发者Create By: 李健华
//===       Create Date: 2019/11/15 14:56:33
//=========================================================
package com.wanlianyida.lsds.interfaces.facade;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsSplit;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsSplitFilter;
import com.wanlianyida.lsds.application.service.LsdsGoodsSplitAppService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 创建描述：货物拆单id 货源拆单信息表控制器 类的后面Controller其实可以不用，只是为了容易区分
 *
 * <AUTHOR>
 * 创建时间 2019/11/15 14:56:33
 */
@RequestMapping(value = "/LsdsGoodsSplit")
@RestController
public class LsdsGoodsSplitController   {
    @Resource
    private LsdsGoodsSplitAppService lsdsGoodsSplitBusiness;
    /**
     * 根据实体货物拆单id 货源拆单信息表添加记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param model 货物拆单id 货源拆单信息表实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【增加成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【增加成功】编码,ResultMode.errMsg为相应【增加成功】描述；
     * 否则ResultMode.succeed=false【增加失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【增加失败】编码,ResultMode.errMsg为相应【增加失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsSplitAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsSplitAdd(@RequestBody LsdsGoodsSplit model) {
        //try {

        ResultMode<String> resultModel = new ResultMode<String>();

        //#region 判断验证数据
        if (StringUtils.isNotBlank(model.getGoodsSplitId())) {
            resultModel.getModel().add("goodsSplitId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getGoodsId())) {
            resultModel.getModel().add("goodsId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (model.getSplitType() != 0) {
            resultModel.getModel().add("splitType:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSiteCityCode())) {
            resultModel.getModel().add("startSiteCityCode:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSiteCityName())) {
            resultModel.getModel().add("startSiteCityName:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSiteAddress())) {
            resultModel.getModel().add("startSiteAddress:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSendLinker())) {
            resultModel.getModel().add("startSendLinker:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSendPhoneNumber())) {
            resultModel.getModel().add("startSendPhoneNumber:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndSiteCityCode())) {
            resultModel.getModel().add("endSiteCityCode:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndSiteCityName())) {
            resultModel.getModel().add("endSiteCityName:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndSiteAddress())) {
            resultModel.getModel().add("endSiteAddress:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndReceiveLinker())) {
            resultModel.getModel().add("endReceiveLinker:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndReceivePhoneNumber())) {
            resultModel.getModel().add("endReceivePhoneNumber:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getTransportationType())) {
            resultModel.getModel().add("transportationType:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getSplitAmount())) {
            resultModel.getModel().add("splitAmount:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (model.getStatus() != 0) {
            resultModel.getModel().add("status:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getRemark())) {
            resultModel.getModel().add("remark:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (model.getSortNode() != 0) {
            resultModel.getModel().add("sortNode:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCreateBy())) {
            resultModel.getModel().add("createBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getModifyBy())) {
            resultModel.getModel().add("modifyBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }


        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        }
        //#endregion


        resultModel = lsdsGoodsSplitBusiness.lsdsGoodsSplitAdd(model, "");
        return resultModel;

        // 要替换 return CommonParaBLL.getInstance().lsdsGoodsSplitAdd(model,"");
        //} catch (Exception ex) {
        //	LogHelper.writeError(ex.getMessage(), ex);
        //}
        //return false;
    }

    /**
     * 根据货物拆单id 货源拆单信息表主键删除记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param goodsSplitId 货物拆单id 货源拆单信息表主键
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【删除成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【删除成功】编码,ResultMode.errMsg为相应【删除成功】描述；
     * 否则ResultMode.succeed=false【删除失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【删除失败】编码,ResultMode.errMsg为相应【删除失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsSplitDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsSplitDel(@RequestParam String goodsSplitId) {
        //try {
        return lsdsGoodsSplitBusiness.lsdsGoodsSplitDel(goodsSplitId, "");
        // 要替换  return CommonParaBLL.getInstance().lsdsGoodsSplitDel(goodsSplitId,"");
        //} catch (Exception ex) {
        //	LogHelper.writeError(ex.getMessage(), ex);
        //}
        //return false;
    }

    /**
     * 根据货物拆单id 货源拆单信息表实体修改记录，内部以ID主键修改
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param model 货物拆单id 货源拆单信息表实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【修改成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【修改成功】编码,ResultMode.errMsg为相应【修改成功】描述；
     * 否则ResultMode.succeed=false【修改失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【修改失败】编码,ResultMode.errMsg为相应【修改失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsSplitUpdate", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsSplitUpdate(@RequestBody LsdsGoodsSplit model) {
        //try {
        ResultMode<String> resultModel = new ResultMode<String>();
        //#region 判断验证数据
        if (StringUtils.isNotBlank(model.getGoodsSplitId())) {
            resultModel.getModel().add("goodsSplitId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getGoodsId())) {
            resultModel.getModel().add("goodsId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (model.getSplitType() != 0) {
            resultModel.getModel().add("splitType:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSiteCityCode())) {
            resultModel.getModel().add("startSiteCityCode:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSiteCityName())) {
            resultModel.getModel().add("startSiteCityName:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSiteAddress())) {
            resultModel.getModel().add("startSiteAddress:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSendLinker())) {
            resultModel.getModel().add("startSendLinker:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getStartSendPhoneNumber())) {
            resultModel.getModel().add("startSendPhoneNumber:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndSiteCityCode())) {
            resultModel.getModel().add("endSiteCityCode:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndSiteCityName())) {
            resultModel.getModel().add("endSiteCityName:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndSiteAddress())) {
            resultModel.getModel().add("endSiteAddress:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndReceiveLinker())) {
            resultModel.getModel().add("endReceiveLinker:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getEndReceivePhoneNumber())) {
            resultModel.getModel().add("endReceivePhoneNumber:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getTransportationType())) {
            resultModel.getModel().add("transportationType:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getSplitAmount())) {
            resultModel.getModel().add("splitAmount:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (model.getStatus() != 0) {
            resultModel.getModel().add("status:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getRemark())) {
            resultModel.getModel().add("remark:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (model.getSortNode() != 0) {
            resultModel.getModel().add("sortNode:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCreateBy())) {
            resultModel.getModel().add("createBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getModifyBy())) {
            resultModel.getModel().add("modifyBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }


        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        }
        //#endregion


        resultModel = lsdsGoodsSplitBusiness.lsdsGoodsSplitUpdate(model, "");
        return resultModel;
        // 要替换	return CommonParaBLL.getInstance().lsdsGoodsSplitUpdate(model,"");
        //} catch (Exception ex) {
        //	LogHelper.writeError(ex.getMessage(), ex);
        //}
        //return false;
    }

    /**
     * 货物拆单id 货源拆单信息表分页查询
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoodsSplit>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货物拆单id 货源拆单信息表实体类LsdsGoodsSplit列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsSplitPaging", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsSplit> lsdsGoodsSplitPaging(@RequestBody PagingInfo<LsdsGoodsSplitFilter> pageInfo) {
        ResultMode<LsdsGoodsSplit> resultModel = new ResultMode<LsdsGoodsSplit>();
        StringBuffer errsb = new StringBuffer();

        //#region 判断验证数据
        if (StringUtils.isNotBlank(pageInfo.filterModel.getGoodsSplitId())) {
            errsb.append(String.format(" %s 不能为空字符", "goodsSplitId"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getGoodsId())) {
            errsb.append(String.format(" %s 不能为空字符", "goodsId"));
            resultModel.setSucceed(false);
        }
        if (pageInfo.filterModel.getSplitType() != 0) {
            errsb.append(String.format(" %s 数据格式不能为零", "splitType"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getStartSiteCityCode())) {
            errsb.append(String.format(" %s 不能为空字符", "startSiteCityCode"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getStartSiteCityName())) {
            errsb.append(String.format(" %s 不能为空字符", "startSiteCityName"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getStartSiteAddress())) {
            errsb.append(String.format(" %s 不能为空字符", "startSiteAddress"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getStartSendLinker())) {
            errsb.append(String.format(" %s 不能为空字符", "startSendLinker"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getStartSendPhoneNumber())) {
            errsb.append(String.format(" %s 不能为空字符", "startSendPhoneNumber"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getEndSiteCityCode())) {
            errsb.append(String.format(" %s 不能为空字符", "endSiteCityCode"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getEndSiteCityName())) {
            errsb.append(String.format(" %s 不能为空字符", "endSiteCityName"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getEndSiteAddress())) {
            errsb.append(String.format(" %s 不能为空字符", "endSiteAddress"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getEndReceiveLinker())) {
            errsb.append(String.format(" %s 不能为空字符", "endReceiveLinker"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getEndReceivePhoneNumber())) {
            errsb.append(String.format(" %s 不能为空字符", "endReceivePhoneNumber"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getSplitAmount())) {
            errsb.append(String.format(" %s 不能为空字符", "splitAmount"));
            resultModel.setSucceed(false);
        }
        if (pageInfo.filterModel.getStatus() != 0) {
            errsb.append(String.format(" %s 数据格式不能为零", "status"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getRemark())) {
            errsb.append(String.format(" %s 不能为空字符", "remark"));
            resultModel.setSucceed(false);
        }
        if (pageInfo.filterModel.getSortNode() != 0) {
            errsb.append(String.format(" %s 数据格式不能为零", "sortNode"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getCreateBy())) {
            errsb.append(String.format(" %s 不能为空字符", "createBy"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getModifyBy())) {
            errsb.append(String.format(" %s 不能为空字符", "modifyBy"));
            resultModel.setSucceed(false);
        }


        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        }
        //#endregion


        resultModel = lsdsGoodsSplitBusiness.lsdsGoodsSplitPaging(pageInfo, "");
        // 要替换         resultModel = CommonParaBLL.getInstance().lsdsGoodsSplitPaging(pageInfo,"");

        return resultModel;

    }


}
