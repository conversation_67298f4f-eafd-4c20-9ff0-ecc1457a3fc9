package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.lsds.application.model.command.GoodsIdCommand;
import com.wanlianyida.lsds.application.model.command.audit.GoodsSourceAuditCommand;
import com.wanlianyida.lsds.application.model.command.publish.PublishGoodsSourceAgainCommand;
import com.wanlianyida.lsds.application.model.command.publish.PublishGoodsSourceCommand;
import com.wanlianyida.lsds.application.model.command.publish.PublishOneClickCommand;
import com.wanlianyida.lsds.application.model.dto.GoodsSourceDetailDTO;
import com.wanlianyida.lsds.application.model.dto.GoodsSourceListDTO;
import com.wanlianyida.lsds.application.model.dto.PublishGoodsSourceDTO;
import com.wanlianyida.lsds.application.model.query.GoodsSourceListQuery;
import com.wanlianyida.lsds.application.model.query.GoodsSourceQuery;
import com.wanlianyida.lsds.application.service.GoodsSourceAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月12日 14:35
 */
@Slf4j
@Api("货源")
@RestController
@RequestMapping("/goodsSource")
public class GoodsSourceController {

    @Resource
    private GoodsSourceAppService goodsSourceAppService;

    /**
     * 发布货源（新增）
     */
    @PostMapping("/publish")
    public ResultMode<PublishGoodsSourceDTO> publish(@RequestBody @Validated PublishGoodsSourceCommand command) {
        log.info("goodsSource publish入数：{}", JSONUtil.toJsonStr(command));
        return goodsSourceAppService.publish(command);
    }

    /**
     * 发布货源（修改）
     */
    @PostMapping("/publishAgain")
    public ResultMode<PublishGoodsSourceDTO> publishAgain(@RequestBody @Validated PublishGoodsSourceAgainCommand command) {
        log.info("goodsSource publishAgain入数：{}", JSONUtil.toJsonStr(command));
        return goodsSourceAppService.publishAgain(command);
    }


    /**
     * 保存草稿箱后列表一键发布
     */
    @PostMapping("/publishOneClick")
    public ResultMode<String> publishOneClick(@RequestBody @Validated PublishOneClickCommand command) {
        log.info("goodsSource publishOneClick：{}", JSONUtil.toJsonStr(command));
        return goodsSourceAppService.publishOneClick(command);
    }

    @ApiOperation("删除货源")
    @PostMapping("/deleteGoods")
    public ResultMode<?> deleteGoods(@RequestBody @Validated GoodsIdCommand command) {
        return goodsSourceAppService.deleteGoods(command.getGoodsId());
    }

    @ApiOperation("关闭货源")
    @PostMapping("/closeGoods")
    public ResultMode<?> closeGoods(@RequestBody @Validated GoodsIdCommand command) {
        return goodsSourceAppService.closeGoods(command.getGoodsId());
    }

    @ApiOperation("货源审核")
    @PostMapping("/audit")
    public ResultMode<?> audit(@RequestBody @Validated GoodsSourceAuditCommand command) {
        return goodsSourceAppService.audit(command);
    }

    /**
     * 查询货源详情
     */
    @PostMapping(value = "/queryDetail")
    public ResultMode<GoodsSourceDetailDTO> queryDetail(@RequestBody @Validated GoodsSourceQuery query) {
        GoodsSourceDetailDTO dto = goodsSourceAppService.queryDetail(query);
        if (ObjUtil.isNull(dto)) {
            return ResultMode.success();
        }

        return ResultMode.success(dto);
    }


    /**
     * 获取货源列表
     */
    @PostMapping(value = "/queryPage")
    public ResultMode<GoodsSourceListDTO> queryPage(@RequestBody PagingInfo<GoodsSourceListQuery> query) {
        return goodsSourceAppService.queryPage(query);
    }

}
