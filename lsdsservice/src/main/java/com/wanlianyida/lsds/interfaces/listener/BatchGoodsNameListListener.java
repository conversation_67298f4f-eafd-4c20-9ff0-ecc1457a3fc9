package com.wanlianyida.lsds.interfaces.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.UtilityClass;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsName;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsNameRelation;
import com.isoftstone.hig.lsds.api.mvcvo.BatchGoodsNameAddExcelReq;
import com.isoftstone.hig.lsds.api.mvcvo.BatchGoodsNameAddImportReq;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsNameCreateVo;
import com.wanlianyida.lsds.application.service.LsdsGoodsNameAppService;
import com.wanlianyida.lsds.domain.service.LsdsGoodsNameRelationDomainService;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsGoodsNameMapper;
import com.wanlianyida.lsds.infrastructure.util.NumberGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

/**
 * 批量导入名称管理数据
 */
@Slf4j
@Component
public class BatchGoodsNameListListener extends AnalysisEventListener<BatchGoodsNameAddExcelReq> {

    @Resource
    private NumberGenerator numberGenerator;

    /**
     * 每隔100条存储数据库
     */
    private static final int BATCH_COUNT = 100;
    private final LsdsGoodsNameAppService lsdsGoodsNameService;
    private final LsdsGoodsNameRelationDomainService lsdsGoodsNameRelationService;
    private final LsdsGoodsNameMapper lsdsGoodsNameMapper;
    private final BatchGoodsNameAddImportReq batchGoodsNameAddImportReq;
    /**
     * 错误信息
     */
    private final List<String> respErrMsgList = new ArrayList<>(16);
    List<BatchGoodsNameAddExcelReq> goodsNameReqList = new ArrayList<>();
    /**
     * 总记录行数
     */
    private int totalDataNum = 0;
    /**
     * 成功记录行数
     */
    private int sucDataNum = 0;

    /**
     * 数据库保存成功行数
     */
    private int saveSucDataNum = 0;

    /**
     * 构造注入
     *
     * @param goodsNameService           货物名称管理 服务接口
     * @param goodsNameRelationService   货物名称关系表 服务接口
     * @param lsdsGoodsNameMapper        货物名称管理 Mapper 接口
     * @param batchGoodsNameAddImportReq 货物名称管理批量导入返回结果
     */
    @Autowired
    public BatchGoodsNameListListener(LsdsGoodsNameAppService goodsNameService, LsdsGoodsNameRelationDomainService goodsNameRelationService, LsdsGoodsNameMapper lsdsGoodsNameMapper, BatchGoodsNameAddImportReq batchGoodsNameAddImportReq) {
        this.lsdsGoodsNameService = goodsNameService;
        this.lsdsGoodsNameRelationService = goodsNameRelationService;
        this.lsdsGoodsNameMapper = lsdsGoodsNameMapper;
        this.batchGoodsNameAddImportReq = batchGoodsNameAddImportReq;
    }

    /**
     * 解析每一行数据时调用，用于处理解析出的数据；
     *
     * @param batchGoodsNameAddExcelReq 模板数据导入实体
     * @param analysisContext           上下文监听对象
     */
    @Override
    public void invoke(BatchGoodsNameAddExcelReq batchGoodsNameAddExcelReq, AnalysisContext analysisContext) {
        int rowIndex = analysisContext.readRowHolder().getRowIndex() + 1;
        log.info("BatchGoodsNameListListener#invoke 解析行数：{}行   入参：{}", rowIndex, JSONObject.toJSONString(batchGoodsNameAddExcelReq));
        if (batchGoodsNameAddExcelReq != null && isNumber(batchGoodsNameAddExcelReq.getId())) {
            totalDataNum++;
            List<String> msgList = checkData(rowIndex, batchGoodsNameAddExcelReq);
            if (!IterUtil.isEmpty(msgList)) {
                respErrMsgList.addAll(msgList);
                return;
            }
            goodsNameReqList.add(batchGoodsNameAddExcelReq);
            sucDataNum++;
        }
        /*
        if (totalDataNum > 100) {
            //导入excel表格数据量最大支持100条数据
            throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_IMPORT_ERR, "导入excel表格数据量最大支持100条数据！");
        }*/
        try {
            if (goodsNameReqList.size() >= BATCH_COUNT) {
                log.info(String.valueOf(sucDataNum));
                log.info("BatchGoodsNameListListener#invoke   数据解析{}条 开始存储数据库！", BATCH_COUNT);
                saveGoodsNameData(analysisContext);
                goodsNameReqList.clear();
            }
        } catch (Exception e) {
            log.error("批量导入名称管理数据失败", e);
            throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_IMPORT_ERR, "批量导入名称管理数据失败！");
        }
    }

    /**
     * 所有数据解析完成后调用
     *
     * @param analysisContext 上下文监听对象
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("BatchGoodsNameListListener#doAfterAllAnalysed   所有数据解析完成！");
        try {
            saveGoodsNameData(analysisContext);
            batchGoodsNameAddImportReq.setSucNum(sucDataNum);
            batchGoodsNameAddImportReq.setFailNum(totalDataNum - sucDataNum);
            batchGoodsNameAddImportReq.setSaveSucDataNum(saveSucDataNum);
            batchGoodsNameAddImportReq.setErrMsgList(respErrMsgList);
            log.info("BatchGoodsNameListListener#doAfterAllAnalysed   所有数据处理完成！");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("批量导入名称管理数据失败");
            throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_IMPORT_ERR, "批量导入名称管理数据失败");
        }
    }

    /**
     * 是否为数值
     *
     * @param str 字符判定
     * @return 是否为数值类型
     */
    private boolean isNumber(String str) {
        if (StrUtil.isEmpty(str)) {
            return false;
        }
        String reg = "^[0-9]+(.[0-9]+)?$";
        return str.matches(reg);
    }


    /**
     * 检查数据合法性
     *
     * @param lineNum              数据总量
     * @param goodsNameAddExcelReq 货物名称管理批量导入
     */
    private List<String> checkData(int lineNum, BatchGoodsNameAddExcelReq goodsNameAddExcelReq) {
        List<String> errMsg = new ArrayList<>();

        ResultMode resultMode;

        //序号
        resultMode = checkStringData(true, goodsNameAddExcelReq.getId(), lineNum, "序号");
        if (!resultMode.isSucceed()) {
            errMsg.add(resultMode.getErrMsg());
        } else {
            if (StrUtil.isEmpty(goodsNameAddExcelReq.getId())) {
                errMsg.add(StrUtil.format("第{}行：【{}】字段填写不正确，值应该为数字!!", lineNum, "序号"));
            }
        }
        //货物名称
        goodsNameAddExcelReq.setGoodsName(replaceAllSpaces(goodsNameAddExcelReq.getGoodsName()));
        resultMode = checkStringData(true, goodsNameAddExcelReq.getGoodsName(), lineNum, "货物名称");
        if (!resultMode.isSucceed()) {
            errMsg.add(resultMode.getErrMsg());
        }

        //货物类型名称
        goodsNameAddExcelReq.setGoodsTypeName(replaceAllSpaces(goodsNameAddExcelReq.getGoodsTypeName()));
        resultMode = checkStringData(true, goodsNameAddExcelReq.getGoodsTypeName(), lineNum, "货物类型名称");
        if (!resultMode.isSucceed()) {
            errMsg.add(resultMode.getErrMsg());
        }

        //上级货物类型名称
        goodsNameAddExcelReq.setGoodsTypeParentName(replaceAllSpaces(goodsNameAddExcelReq.getGoodsTypeParentName()));
        resultMode = checkStringData(true, goodsNameAddExcelReq.getGoodsTypeParentName(), lineNum, "上级货物类型名称");
        if (!resultMode.isSucceed()) {
            errMsg.add(resultMode.getErrMsg());
        }

        //企业ID
        resultMode = checkStringData(true, goodsNameAddExcelReq.getCompanyId(), lineNum, "  企业ID");
        if (!resultMode.isSucceed()) {
            errMsg.add(resultMode.getErrMsg());
        }

        //企业名称（简称）
        goodsNameAddExcelReq.setCompanyShortName(replaceAllSpaces(goodsNameAddExcelReq.getCompanyShortName()));
        resultMode = checkStringData(true, goodsNameAddExcelReq.getCompanyShortName(), lineNum, "企业名称（简称）");
        if (!resultMode.isSucceed()) {
            errMsg.add(resultMode.getErrMsg());
        }


        //货物类型code码
        goodsNameAddExcelReq.setGoodsTypeCode(replaceAllSpaces(goodsNameAddExcelReq.getGoodsTypeCode()));
        resultMode = checkStringData(true, goodsNameAddExcelReq.getGoodsTypeCode(), lineNum, "货物类型code码");
        if (!resultMode.isSucceed()) {
            errMsg.add(resultMode.getErrMsg());
        }


        //上级货物类型code码
        goodsNameAddExcelReq.setGoodsTypeParentCode(replaceAllSpaces(goodsNameAddExcelReq.getGoodsTypeParentCode()));
        resultMode = checkStringData(true, goodsNameAddExcelReq.getGoodsTypeParentCode(), lineNum, "上级货物类型code码");
        if (!resultMode.isSucceed()) {
            errMsg.add(resultMode.getErrMsg());
        }

        return errMsg;
    }


    /**
     * 替换前中后空格
     *
     * @param str str
     * @return {@link String}
     */
    private String replaceAllSpaces(String str) {
        if (StrUtil.isEmpty(str)) {
            return null;
        }
        return str.replaceAll(" ", "");
    }


    /**
     * 检查字符串数据
     *
     * @param checkIsEmpty 检查是空
     * @param strData      str数据
     * @param lineNum      行号
     * @param fieldName    字段名
     * @return {@link ResultMode}
     */
    private ResultMode checkStringData(boolean checkIsEmpty, String strData, int lineNum, String fieldName) {
        if (checkIsEmpty) {
            if (StrUtil.isEmpty(strData)) {
                return ResultMode.fail(StrUtil.format("第{}行：【{}】字段不能为空", lineNum, fieldName));
            }
        } else {
            if (StrUtil.isEmpty(strData)) {
                return ResultMode.success();
            }
        }
        if (isSpecialChar(strData)) {
            return ResultMode.fail(StrUtil.format("第{}行：【{}】字段含有特殊字符", lineNum, fieldName));
        }
        return ResultMode.success();
    }

    /**
     * 特殊字符判断
     *
     * @param str str
     * @return boolean
     */
    private boolean isSpecialChar(String str) {
        if (StrUtil.isEmpty(str)) {
            return false;
        }
        String regEx = "[ _`~!@#$%^&*+=|{}':;'\\[\\]<>?~！@#￥%……&*——+|{}【】‘；：”“’。、？]|\n|\r|\t";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.find();
    }


    /**
     * 数据保存
     *
     * @param analysisContext 上下文监听对象
     */
    public void saveGoodsNameData(AnalysisContext analysisContext) {
        log.info("BatchGoodsNameListListener#saveData   {}条数据，开始存储数据库！", goodsNameReqList.size());
        if (goodsNameReqList.isEmpty()) {
            return;
        }
        List<LsdsGoodsNameCreateVo> goodsNameCreateVoList = new ArrayList<>();
        List<LsdsGoodsNameRelation> goodsNameRelationList = new ArrayList<>();
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        if (tokenInfo == null) {
            log.error("BatchGoodsNameListListener#saveData   获取token异常失败");
            throw new LsdsWlydException(CommonStatusCodeEnum.USER_ERROR_EXPIRE);
        }
        goodsNameReqList.forEach(excelData -> {
            String goodsNameId;
            LsdsGoodsName lsdsGoodsName = lsdsGoodsNameMapper.findGoodsNameByName(excelData.getGoodsName());
            if (lsdsGoodsName != null) {
                goodsNameId = lsdsGoodsName.getGoodsNameId();
                int rows = lsdsGoodsNameRelationService.countGoodsNameByGoodsNameId(excelData.getCompanyId(), lsdsGoodsName.getGoodsNameId());
                if (rows > 0) {
                    log.info("BatchGoodsNameListListener#saveData   \n 货物名称已经存在：{} 货品ID：{}",
                        lsdsGoodsName.getGoodsName(), lsdsGoodsName.getGoodsNameId());
                    return;
                }
            } else {
                //货物名称管理表
                LsdsGoodsNameCreateVo goodsNameCreateVo = new LsdsGoodsNameCreateVo();
                //主键
                goodsNameId = UtilityClass.uuid();
                goodsNameCreateVo.setGoodsNameId(goodsNameId);
                //货物名称
                goodsNameCreateVo.setGoodsName(excelData.getGoodsName());
                //状态0待审核、1审核拒绝、2启用、3禁用
                goodsNameCreateVo.setStatus(LsdsEnum.GoodsNameStatusEnum.ENABLE_STATUS.getCode());
                //删除标记(默认0，1已删除)
                goodsNameCreateVo.setDeleteFlag(LsdsEnum.GoodsDeleteFlagEnum.NOT_DELETED_FLAG.getCode());
                //创建企业id
                goodsNameCreateVo.setCreateCompanyId(excelData.getCompanyId());
                //创建企业(简称)
                goodsNameCreateVo.setCreateCompanyName(excelData.getCompanyShortName());
                //货物类型编码
                goodsNameCreateVo.setGoodsType(excelData.getGoodsTypeCode());
                //货物类型上级编码
                goodsNameCreateVo.setGoodsParentCode(excelData.getGoodsTypeParentCode());
                //创建人ID
                goodsNameCreateVo.setCreateBy(tokenInfo.getUserBaseId());
                //创建人名称
                goodsNameCreateVo.setCreateName(tokenInfo.getUsername());
                // 生成货品编码
                goodsNameCreateVo.setGoodsNameNo(numberGenerator.generateIncr("GN", 8));
                goodsNameCreateVoList.add(goodsNameCreateVo);
            }

            //货物名称关系表
            LsdsGoodsNameRelation goodsNameRelation = new LsdsGoodsNameRelation();
            //企业ID
            goodsNameRelation.setCompanyId(excelData.getCompanyId());
            //企业简称
            goodsNameRelation.setCompanyName(excelData.getCompanyShortName());
            //创建时间
            goodsNameRelation.setCreateDate(new Date());
            //创建人
            goodsNameRelation.setCreateName(tokenInfo.getUserBaseId());
            //删除标记(默认0，1已删除)
            goodsNameRelation.setDeleteFlag(LsdsEnum.GoodsDeleteFlagEnum.NOT_DELETED_FLAG.getCode());
            //货物名称ID
            goodsNameRelation.setGoodsNameId(goodsNameId);
            //主键
            goodsNameRelation.setGoodsNameRelationId(UtilityClass.uuid());
            //描述
            //goodsNameRelation.setGoodsDesc();
            //包装方式
            //goodsNameRelation.setPackType();
            goodsNameRelationList.add(goodsNameRelation);
        });
        //批量插入
        batchSaveGoodsNameData(goodsNameCreateVoList, goodsNameRelationList);
        log.info("BatchGoodsNameListListener#saveData   {}条数据，数据库存储完成！", saveSucDataNum);
    }


    /**
     * 批量插入货品名称数据
     *
     * @param goodsNameCreateVoList 货品名称数据集
     * @param goodsNameRelationList 货品名称关系表数据集
     */
    public void batchSaveGoodsNameData(List<LsdsGoodsNameCreateVo> goodsNameCreateVoList, List<LsdsGoodsNameRelation> goodsNameRelationList) {
        //批量插入货物名称
        if (IterUtil.isNotEmpty(goodsNameCreateVoList)) {
            log.info("批量插入货物名称：{}条", goodsNameCreateVoList.size());
            CollectionUtil.split(goodsNameCreateVoList, BATCH_COUNT).forEach(lsdsGoodsNameService::batchAddGoodsName);
        }
        //批量插入货物名称关系表
        if (IterUtil.isNotEmpty(goodsNameRelationList)) {
            log.info("批量插入货物名称关系表：{}条", goodsNameRelationList.size());
            saveSucDataNum += goodsNameRelationList.size();
            CollectionUtil.split(goodsNameRelationList, BATCH_COUNT).forEach(lsdsGoodsNameRelationService::batchAddGoodsName);
        }
    }


}
