package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsAttention;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsAttentionFilter;
import com.isoftstone.hig.lsds.api.mvcvo.GetGoodsAttentionListVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsAttentionCreateVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsAttentionVO;
import com.wanlianyida.lsds.application.service.LsdsGoodsAttentionAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 货源关注Controller
 *
 * <AUTHOR>
 * @date 2023-03-03
 */
@RequestMapping(value = "/goodsAttention")
@Slf4j
@RestController
public class LsdsGoodsAttentionController {
    @Autowired
    private LsdsGoodsAttentionAppService lsdsGoodsAttentionServiceTran;

    /**
     * 查询货源关注列表
     */
    @RequestMapping("/pagelist")
    public ResultMode<LsdsGoodsAttentionVO> pagelist(@RequestBody PagingInfo<LsdsGoodsAttentionFilter> pageInfo)
    {

        ResultMode<LsdsGoodsAttentionVO> resultMode = lsdsGoodsAttentionServiceTran.selectPageLsdsGoodsAttentionData(pageInfo);
        return resultMode;
    }


    /**
     * 获取货源关注详细信息
     */
    @GetMapping("/getInfo")
    public ResultMode getInfo(@PathVariable("attentionId") String attentionId)
    {
        return ResultMode.success(lsdsGoodsAttentionServiceTran.selectLsdsGoodsAttentionByAttentionId(attentionId));
    }

    /**
     * 新增货源关注
     */
    @RequestMapping("/addGoodsAttention")
    public ResultMode addGoodsAttention(@RequestBody LsdsGoodsAttentionCreateVo lsdsGoodsAttention)
    {
        return lsdsGoodsAttentionServiceTran.addGoodsAttention(lsdsGoodsAttention);
    }

    /**
     * 修改货源关注
     */
    @PutMapping("/edit")
    public ResultMode edit(@RequestBody LsdsGoodsAttention lsdsGoodsAttention)
    {
        return ResultMode.success(lsdsGoodsAttentionServiceTran.updateLsdsGoodsAttention(lsdsGoodsAttention));
    }

    /**
     * 删除货源关注
     */
    @DeleteMapping("/remove")
    public ResultMode remove(@PathVariable String[] attentionIds)
    {
        return ResultMode.success(lsdsGoodsAttentionServiceTran.deleteLsdsGoodsAttentionByAttentionIds(attentionIds));
    }

    @RequestMapping("/longTermAttentionGoods")
    public ResultMode longTermAttentionGoods(@RequestBody LsdsGoodsAttention lsdsGoodsAttention) {
        return lsdsGoodsAttentionServiceTran.longTermAttentionGoods(lsdsGoodsAttention);
    }

    @RequestMapping("/cancelGoodsAttention")
    public ResultMode cancelGoodsAttention(@RequestBody LsdsGoodsAttention lsdsGoodsAttention) {
        return lsdsGoodsAttentionServiceTran.cancelGoodsAttention(lsdsGoodsAttention);
    }

    /**
     * 查询货源关注列表
     * @param getGoodsAttentionListVo
     * @return
     */
    @PostMapping("/getGoodsAttentionInfoList")
    public ResultMode<LsdsGoodsAttention> getGoodsAttentionInfoList(@RequestBody GetGoodsAttentionListVo getGoodsAttentionListVo) {
        try {
            log.info("查询货物关注列表->getGoodsAttentionInfoList请求参数：{}", JSONUtil.toJsonStr(getGoodsAttentionListVo));
            if(CollUtil.isEmpty(getGoodsAttentionListVo.getGoodsIds()) && CollUtil.isEmpty(getGoodsAttentionListVo.getOrderIds())){
                log.info("goodsIds || orderIds 为空！");
                return ResultMode.fail("goodsIds || orderIds 不能为空！");
            }
            List<LsdsGoodsAttention> goodsAttentionInfoList = lsdsGoodsAttentionServiceTran.getGoodsAttentionInfoList(getGoodsAttentionListVo);
            ResultMode success = ResultMode.success();
            success.setModel(goodsAttentionInfoList);
            return success;
        }catch (Exception e){
            log.error("查询货物关注列表出现异常：",e);
            return ResultMode.fail("查询货物关注列表出现异常！");
        }
    }
}
