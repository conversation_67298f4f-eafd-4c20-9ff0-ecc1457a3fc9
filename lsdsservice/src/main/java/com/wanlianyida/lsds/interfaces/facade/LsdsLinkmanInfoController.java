package com.wanlianyida.lsds.interfaces.facade;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.lsds.api.command.LsdsLinkmanInfoCommand;
import com.isoftstone.hig.lsds.api.dto.LsdsLinkmanInfoDTO;
import com.isoftstone.hig.lsds.api.query.LsdsLinkmanInfoQuery;
import com.wanlianyida.lsds.domain.service.LsdsLinkmanInfoDomainService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年11月14日 19:48
 */
@RequestMapping("/linkman")
@RestController
public class LsdsLinkmanInfoController   {

    @Resource
    private LsdsLinkmanInfoDomainService linkmanInfoService;
    @ApiOperation("添加联系人")
    @PostMapping("/add")
    public ResultMode add(@RequestBody LsdsLinkmanInfoCommand command) {
        linkmanInfoService.add(command);
        return ResultMode.success();
    }

    @ApiOperation("联系人列表")
    @PostMapping("/queryList")
    public ResultMode<LsdsLinkmanInfoDTO> queryList(@Validated @RequestBody LsdsLinkmanInfoQuery query) {
        query.setCompanyId(JwtUtil.getInstance().getCompanyIdByToken());
        List<LsdsLinkmanInfoDTO> result = linkmanInfoService.queryList(query);
        return ResultMode.successList(result);
    }
}
