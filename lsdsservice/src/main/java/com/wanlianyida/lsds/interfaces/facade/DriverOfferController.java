package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.isoftstone.hig.annotations.OverloadCheck;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.common.utils.exception.WlydException;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.DriverOffer;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.filter.DriverOfferFilter;
import com.isoftstone.hig.lsds.api.mvcvo.PrepayReqVO;
import com.isoftstone.hig.lsds.api.mvcvo.PrepayResVO;
import com.isoftstone.hig.platform.api.entity.PlatformCmDictionary;
import com.isoftstone.hig.platform.api.entity.PlatformCmPlatformParameter;
import com.isoftstone.hig.platform.api.inter.PlatformCmDictionaryInter;
import com.isoftstone.hig.platform.api.inter.PlatformCmPlatformParameterInter;
import com.isoftstone.hig.tms.api.common.Result;
import com.isoftstone.hig.tms.api.entity.TmsEnum;
import com.isoftstone.hig.tms.api.entity.TmsWaybill;
import com.isoftstone.hig.tms.api.inter.TmsWaybillInter;
import com.isoftstone.hig.tms.api.query.TmsWaybillFilter;
import com.wanlianyida.lsds.application.service.DriverGoodsAppService;
import com.wanlianyida.lsds.application.service.DriverGoodsOfferAppService;
import com.wanlianyida.lsds.application.service.DriverOfferAppService;
import com.wanlianyida.lsds.application.service.PrepayAppService;
import com.wanlianyida.lsds.infrastructure.config.FormsAuthTrader;
import com.wanlianyida.lsds.infrastructure.exchange.TcsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 司机报价Controller
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@RequestMapping("/driverOffer")
@RestController
@Slf4j
public class DriverOfferController   {

    @Resource
    private DriverOfferAppService driverOfferService;
    @Resource
    private PlatformCmPlatformParameterInter platformCmPlatformParameterInter;
    @Resource
    private TmsWaybillInter tmsWaybillInter;
    @Resource
    private DriverGoodsAppService driverGoodsService;
    @Resource
    private PrepayAppService prepayService;
    @Resource
    private PlatformCmDictionaryInter platformCmDictionaryInter;
    @Resource
    private DriverGoodsOfferAppService driverGoodsOfferService;
    @Resource
    private TcsExchangeService tcsExchangeService;
    /**
     * 查询司机报价
     *
     * @param driverOfferFilter 司机报价ID
     * @return 司机报价
     */
    @PostMapping("/getById")
    public ResultMode<DriverOffer> getById(@RequestBody DriverOfferFilter driverOfferFilter) {
        return new ResultMode(driverOfferService.getById(driverOfferFilter.getId()));
    }

    /**
     * 查询司机报价列表
     *
     * @param driverOfferFilter 司机报价
     * @return 司机报价集合
     */
    @PostMapping("/listByEntity")
    public ResultMode<List<DriverOffer>> listByEntity(@RequestBody DriverOfferFilter driverOfferFilter) {
        log.info("请求参数为：{}", JSONUtil.toJsonStr(driverOfferFilter));

        if (StringUtils.isBlank(driverOfferFilter.getWaybillId())) {
            return ResultMode.fail("运单号不能为空！！");
        }
        TmsWaybillFilter filter = new TmsWaybillFilter();
        filter.setWaybillId(driverOfferFilter.getWaybillId());
        ResultMode<TmsWaybill> info = tmsWaybillInter.info(filter);
        BigDecimal advancePayment = new BigDecimal(0);
        if (!CollectionUtils.isEmpty(info.getModel())) {
            log.info("info:{}", JSONUtil.toJsonStr(info.getModel()));
            advancePayment = info.getModel().get(0).getAdvancePayment();
            log.info("报价信息，查询预付费：{}", advancePayment);
        }

        List<DriverOffer> driverOffers = driverOfferService.listByEntity(driverOfferFilter);
        if (!CollectionUtils.isEmpty(driverOffers)) {
            driverOffers.get(0).setAdvancePayment(advancePayment);
            log.info("报价信息：{}", driverOffers);
            return new ResultMode(driverOffers);
        }
        return ResultMode.fail();

    }

    /**
     * 新增司机报价
     *
     * @param driverOffer 司机报价
     * @return 结果
     */
    @PostMapping(value = "/save")
    public ResultMode save(@RequestBody DriverOffer driverOffer) {
        return new ResultMode(driverOfferService.save(driverOffer));
    }

    /**
     * 修改司机报价
     *
     * @param offer 司机报价
     * @return 结果
     */
    @PostMapping(value = "/updateById")
    public ResultMode updateById(@RequestBody DriverOffer offer) {
        return new ResultMode(driverOfferService.updateById(offer));
    }

    /**
     * 批量删除司机报价
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @PostMapping(value = "/removeByIds")
    public ResultMode removeByIds(String[] ids) {
        return new ResultMode(driverOfferService.removeByIds(ids));
    }

    /**
     * 删除司机报价信息
     *
     * @param id 司机报价ID
     * @return 结果
     */
    @PostMapping(value = "/removeById")
    public ResultMode removeById(String id) {
        return new ResultMode(driverOfferService.removeById(id));
    }

    /**
     * 分页查询司机报价列表
     *
     * @param pageInfo 司机报价
     * @return 司机报价集合
     */
    @PostMapping(value = "/page")
    public ResultMode<DriverOffer> page(@RequestBody PagingInfo<DriverOfferFilter> pageInfo) {
        log.info("报价列表参数详情:pageInfo:{}" , JSONObject.toJSONString(pageInfo));
        ResultMode<DriverOffer> ResultMode = new ResultMode<>();
        //设置分页参数
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        //列表查询
        List<DriverOffer> list = driverOfferService.page(pageInfo);
        DriverGoods driverGood = driverGoodsService.getByGoodsId(pageInfo.getFilterModel().getGoodsId());


        //非短倒货源的进行预付费校验
        if (driverGood != null && org.springframework.util.StringUtils.hasText(driverGood.getTransportationType()) && !StringUtils.equals(driverGood.getTransportationType(), UtilityEnum.TransportationAllTypeEnum.INVERT_SHORT.getLsdsCode())) {
            //预付费相关
            if ("1".equalsIgnoreCase(pageInfo.getFilterModel().getOfferFlag())) {
                List<String> paraCodesDefaultLoad = new ArrayList();
                paraCodesDefaultLoad.add("1052");
                ResultMode<PlatformCmPlatformParameter> resultModeDefaultLoad = platformCmPlatformParameterInter.getByParaCodes(paraCodesDefaultLoad);
                List<PlatformCmPlatformParameter> platformCmPlatformParameters = resultModeDefaultLoad.getModel();
                if (!org.springframework.util.CollectionUtils.isEmpty(platformCmPlatformParameters)) {
                    if ("1".equalsIgnoreCase(platformCmPlatformParameters.get(0).getParaValue())) {

                        log.info("报价列表参数详情:driverGood" + JSONObject.toJSONString(driverGood));
                        if (null != driverGood.getAdvancePayment() && StrUtil.equals(driverGood.getAdvancePaymentFlag(), "0")) {
                            list.stream().map(lt -> {
                                lt.setAdvancePayment(driverGood.getAdvancePayment());
                                PrepayReqVO prepayReqVO = PrepayReqVO.of();
                                //结算类型【radio:10-按柜,20-按重量,30-按车】
                                prepayReqVO.setChargeType(driverGood.getTotalQuantityUnits());
                                //预付费金额
                                if (driverGood.getAdvancePayment() != null) {
                                    prepayReqVO.setPrePayMoney(driverGood.getAdvancePayment().toString());
                                }
                                //询价方式【radio:10-公开询价,20-指定单价】
                                prepayReqVO.setEnquiryType(driverGood.getEnquiryType());
                                if ("10".equalsIgnoreCase(driverGood.getEnquiryType())) {
                                    prepayReqVO.setPlaceOrderOptFlag("10");
                                }
                                prepayReqVO.setReceivingOrdersQuantity(lt.getReceivingOrdersQuantity());
                                prepayReqVO.setReceivingOrdersUnitPrice(lt.getEnquiryTypeBasePrice());
                                prepayReqVO.setEnquiryTypeBasePrice(lt.getEnquiryTypeBasePrice());

                                ResultMode<PrepayResVO> prepayResVOResultMode = prepayService.prepayCheck(prepayReqVO);
                                if (!CollectionUtils.isEmpty(prepayResVOResultMode.getModel())) {
                                    if (null != prepayResVOResultMode.getModel().get(0)) {
                                        lt.setMaxadvancePayment(prepayResVOResultMode.getModel().get(0).getPrepayMax());
                                        lt.setExcessAdvancePaymentFlag(prepayResVOResultMode.getModel().get(0).getOverLimit());
                                    }
                                }

                                return lt;
                            }).collect(Collectors.toList());
                        }
                    }
                }

            }

        }
        //处理分页
        PageInfo<DriverOffer> returnPageInfo = new PageInfo<>(list);
        //处理返回数据
        ResultMode.setTotal((int) returnPageInfo.getTotal());
        ResultMode.setModel(returnPageInfo.getList());
        return ResultMode;
    }

    /**
     * @Description 司机货源报价
     * <AUTHOR>
     * @Date 2021/5/31 10:03
     * @Version 1.0
     * @Param
     * @Return
     */
    @OverloadCheck
    @PostMapping(value = "/sourceQuotation")
    public ResultMode sourceQuotation(@RequestBody DriverOfferFilter offerFilter) {
        log.info("offerFilter.............{}",JSON.toJSONString(offerFilter));
        // 挂车必填校验
        try {
            //车辆信息校验
            ResultMode resultMode = checkCarInfo(offerFilter);
            if (!resultMode.getSucceed()) {
                return resultMode;
            }
            //司机信息校验
            if(StrUtil.equals(LsdsEnum.FreightTypeEnum.FREIGHT_TYPE_2.getCode(),offerFilter.getFreightType())){
                resultMode = checkDriverInfo(offerFilter,LsdsEnum.OrderOpTypeEnum.APPQUOTATION30.getCode());
                if (!resultMode.getSucceed()) {
                    return resultMode;
                }
            }

            if (CharSequenceUtil.startWith(offerFilter.getAssignCarType(), "Q")) {
                List<String> paraCodesGuache = new ArrayList();
                paraCodesGuache.add("052");
                ResultMode<PlatformCmPlatformParameter> requiredResultMode = platformCmPlatformParameterInter.getByParaCodes(paraCodesGuache);
                List<PlatformCmPlatformParameter> requiredModel = requiredResultMode.getModel();
                log.info("平台挂车必填响应：{}", JSON.toJSONString(requiredModel));
                ResultMode fail = Result.fail();
                fail.setErrCode("301");
                if (IterUtil.isNotEmpty(requiredModel) && CharSequenceUtil.equals("1", requiredModel.get(0).getParaValue())
                        && CharSequenceUtil.isBlank(offerFilter.getTrailerPlateNumber())) {
                    String colorName = getCarColorNameByColorCode(offerFilter.getTractorPlateColor());
                    fail.setErrMsg(String.format("【%s %s】的挂车车牌需必填!", offerFilter.getTractorPlateNumber(), colorName));
                    fail.setModel(Arrays.asList(offerFilter.getTractorPlateCarId()));
                    return fail;
                }
                // 挂车有效状态校验
                if (StrUtil.isNotBlank(offerFilter.getTrailerPlateNumber()) && StrUtil.isNotBlank(offerFilter.getTrailerPlateColor())) {
                    String carStatus = tcsExchangeService.getCarStatus(offerFilter.getTrailerPlateNumber(),offerFilter.getTrailerPlateColor());
                    if (!StrUtil.equals(carStatus, "10")) {
                        fail.setErrMsg(String.format("无效的挂车【%s %s】", offerFilter.getTrailerPlateNumber(), TmsEnum.CarColorTypeEnum.getValueBykey(String.valueOf(offerFilter.getTrailerPlateColor()))));
                        fail.setModel(Arrays.asList(offerFilter.getTractorPlateCarId()));
                        return fail;
                    }
                }
            }

            return driverOfferService.sourceQuotation(offerFilter);
        } catch (WlydException e) {
            log.error("司机货源报价异常:{}",e);
            return ResultMode.fail("司机货源报价失败！");
        }
    }

    /**
     * 司机货源报价（新方法）
     */
    @OverloadCheck
    @PostMapping(value = "/goodsQuote")
    public ResultMode<DriverOffer> goodsQuote(@Validated(DriverOfferFilter.Quote.class)
                                                  @RequestBody DriverOfferFilter offerFilter) {
        return driverGoodsOfferService.goodsQuote(offerFilter);
    }

    /**
     * 车辆信息校验
     */
    private ResultMode checkCarInfo(DriverOfferFilter offerFilter) {
        if (StrUtil.isBlank(offerFilter.getTractorPlateCarId())) {
            return ResultMode.fail("车辆id为空");
        }
        if (StrUtil.isBlank(offerFilter.getTractorPlateNumber())) {
            return ResultMode.fail("车牌号为空");
        }
        if (StrUtil.isBlank(offerFilter.getTractorPlateColor())) {
            return ResultMode.fail("车牌颜色为空");
        }
        //车牌颜色枚举
        String parentDicId = "267";
        List<String> carColorCodes = FormsAuthTrader.getDictionaryEnumCodeList(platformCmDictionaryInter, parentDicId);
        if (IterUtil.isEmpty(carColorCodes)) {
            return ResultMode.fail("车牌颜色字典不存在");
        }
        if (!carColorCodes.contains(offerFilter.getTractorPlateColor())) {
            return ResultMode.fail("车牌颜色编码不存在");
        }

        //选择挂车校验
        if (StrUtil.isNotBlank(offerFilter.getTrailerPlateNumber())) {
            if (StrUtil.isBlank(offerFilter.getTrailerPlateCarId())) {
                return ResultMode.fail("挂车id为空");
            }
            if (StrUtil.isBlank(offerFilter.getTrailerPlateColor())) {
                return ResultMode.fail("挂车车牌颜色为空");
            }
            if (!carColorCodes.contains(offerFilter.getTrailerPlateColor())) {
                return ResultMode.fail("挂车车牌颜色编码不存在");
            }
        }

        return ResultMode.success();
    }

    /**
     * 司机信息校验
     * HWJC-413
     * 运单表结算id校验逻辑优化
     * 代收校验
     */

    private ResultMode checkDriverInfo(DriverOfferFilter offerFilter,String opType) {
        String message1=String.format("用户【%s】您好，因承运司机信息异常，当前操作失败，请与万联易达客服取得联系处理。客服电话：400-015-8686",offerFilter.getDriverName());
        String message2=String.format("用户【%s】您好，因收款信息异常，当前操作失败，请与万联易达客服取得联系处理。客服电话：400-015-8686",offerFilter.getDriverName());

        //1-司机id不能为空
        if(StrUtil.isEmpty(offerFilter.getDriverId())){
            return ResultMode.fail(message1);
        }
        //2-收款人身份证号不能为空
        if(StrUtil.equals(opType, LsdsEnum.OrderOpTypeEnum.PCORDER20.getCode())){
             //3PL报价下单
            if(StrUtil.isEmpty(offerFilter.getIdCard())){
                return ResultMode.fail(message2);
            }
            offerFilter.setReceiptorIdCardNo(offerFilter.getIdCard());
        }else{
            //APP大厅直接或扫码下单或报价
            if(StrUtil.isEmpty(offerFilter.getReceiptorIdCardNo())){
                return ResultMode.fail(message2);
            }
        }

        //3-如果收款人司机id为空时，通过收款人身份证号查询对应的司机id
        if(StrUtil.isEmpty(offerFilter.getReceiptorDriverId())){
            //根据身份证号反查driverId
            String driverId = tcsExchangeService.getDriverIdByIdCardNo(offerFilter.getReceiptorIdCardNo());
            if(StrUtil.isEmpty(driverId) ){
                return ResultMode.fail(message2);
            }
            offerFilter.setReceiptorDriverId(driverId);
        }
        //4-receiptorDriverld，receiptAccountName校验不能为空
        if(StrUtil.isEmpty(offerFilter.getReceiptorDriverId()) || StrUtil.isEmpty(offerFilter.getReceiptAccountName())){
            return ResultMode.fail(message2);
        }

        return ResultMode.success();
    }


    /**
     * 根据颜色编号查询颜色名称
     * @param carColor
     * @return {@link String}
     */
    private String getCarColorNameByColorCode(String carColor){
        String parentDicId = "267";
        ResultMode<PlatformCmDictionary> dictionaryResultMode = platformCmDictionaryInter.getDictByParentIdAndEnumCode(parentDicId,carColor);
        if(dictionaryResultMode!=null && !IterUtil.isEmpty(dictionaryResultMode.getModel())){
            PlatformCmDictionary dictionary = IterUtil.getFirst(dictionaryResultMode.getModel());
            if(dictionary!=null){
                return dictionary.getName();
            }
        }
        return null;
    }


    @PostMapping(value = "/generateDriverOffer")
    public ResultMode generateDriverOffer(@RequestBody DriverGoods goods) {
        driverOfferService.generateDriverOffer(goods);
        return null;
    }

    /**
     * @Description 获取报价单列表
     * <AUTHOR>
     * @Date 2021/9/8 17:33
     * @Version .0
     * @Pram a
     * @Return a
     */
    @PostMapping(value = "/getDriverOfferList")
    public ResultMode<DriverOffer> getDriverOfferList(@RequestBody PagingInfo<DriverOfferFilter> pageInfo) {
        ResultMode<DriverOffer> ResultMode = new ResultMode<>();
        //设置分页参数
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        //列表查询
        List<DriverOffer> list = driverOfferService.getDriverOfferList(pageInfo);
        //处理分页
        PageInfo<DriverOffer> returnPageInfo = new PageInfo<DriverOffer>(list);
        //处理返回数据
        ResultMode.setTotal((int) returnPageInfo.getTotal());
        ResultMode.setModel(returnPageInfo.getList());
        return ResultMode;
    }

    @PostMapping(value = "/getDriverGoodsPriceByGoodsId")
    public ResultMode<DriverGoods> getDriverGoodsPriceByGoodsId(@RequestBody DriverGoods goods) {
        ResultMode<DriverGoods> driverGoodsResultMode = new ResultMode<DriverGoods>();
        String goodsId = goods.getGoodsId();
        BigDecimal enquiryTypeBaseOpenTicket = goods.getEnquiryTypeBaseOpenTicket();
        try {
            DriverGoods source = driverGoodsService.getById(goodsId);
            DriverGoods driverGoods = driverOfferService.getDriverGoods(enquiryTypeBaseOpenTicket, source);
            log.info("货源【{}】指定的开票价【{}】重新计算司机基价,司机开票价,发货方基价成功!计算前【{}】计算后【{}】", goodsId, enquiryTypeBaseOpenTicket, JSONObject.toJSONString(source), JSONObject.toJSONString(driverGoods));
            driverGoodsResultMode.getModel().add(driverGoods);
        } catch (Exception e) {
            driverGoodsResultMode.setSucceed(false);
            log.error("货源【{}】指定的开票价【{}】重新计算司机基价,司机开票价,发货方基价失败【{}】", goodsId, enquiryTypeBaseOpenTicket, e.getMessage());
        }
        return driverGoodsResultMode;
    }


    @PostMapping("/getTractorPlateCarIdByWaybillId")
    public ResultMode<DriverOffer> getTractorPlateCarIdByWaybillId(@RequestBody TmsWaybillFilter tmsWaybillFilter) {
        ResultMode<DriverOffer> resultMode = new ResultMode<DriverOffer>();
        if (StringUtils.isBlank(tmsWaybillFilter.getWaybillId())) {
            resultMode.setSucceed(false);
            resultMode.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultMode.setErrMsg("运单号为空");
            return resultMode;
        }
        List<DriverOffer> driverOffers = driverOfferService.getTractorPlateCarIdByWaybillId(tmsWaybillFilter.getWaybillId());
        if (CollectionUtils.isEmpty(driverOffers)) {
            resultMode.setSucceed(false);
            return ResultMode.fail("根据运单号" + tmsWaybillFilter.getWaybillId() + "获取的报价对象为空");
        }
        resultMode.setSucceed(true);
        resultMode.setModel(driverOffers);
        return resultMode;
    }

    @PostMapping("/getDriverOfferByWaybillId")
    public ResultMode<DriverOffer> getDriverOfferByWaybillId(@RequestBody DriverOfferFilter filter) {
        ResultMode<DriverOffer> resultMode = new ResultMode<DriverOffer>();
        if (StringUtils.isBlank(filter.getWaybillId())) {
            resultMode.setSucceed(false);
            resultMode.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultMode.setErrMsg("运单号为空");
            return resultMode;
        }
        List<DriverOffer> driverOffers = driverOfferService.getDriverOfferByWaybillId(filter.getWaybillId());
        if (CollectionUtils.isEmpty(driverOffers)) {
            resultMode.setSucceed(false);
            return ResultMode.fail("根据运单号" + filter.getWaybillId() + "获取的报价对象为空");
        }
        resultMode.setSucceed(true);
        resultMode.setModel(driverOffers);
        return resultMode;
    }

    @PostMapping("/getDriverOfferByOrderId")
    public ResultMode<DriverOffer> getDriverOfferByOrderId(@RequestBody DriverOfferFilter filter) {
        ResultMode<DriverOffer> resultMode = new ResultMode();
        DriverOffer driverOffer = null;
        if (ObjectUtils.isEmpty(filter) || ObjectUtils.isEmpty(filter.getOrderId())) {
            resultMode.setSucceed(false);
            resultMode.setErrMsg("订单ID无效");
        } else {
            resultMode.setSucceed(true);
            driverOffer = this.driverOfferService.getDriverOfferByOrderId(filter.getOrderId());
            List<DriverOffer> list = Lists.newArrayList();
            list.add(driverOffer);
            resultMode.setModel(list);
        }
        return resultMode;
    }

    /**
     * 根绝waybillIds集合查询接单重量数据
     * 创建者：cgb
     * 创建时间：2023/08/11 19:30
     */
    @RequestMapping(value = "/getDriverOfferInfoByQuery", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<DriverOffer> getDriverOfferInfoByQuery(@RequestBody List<String> waybillIds) {
        return driverOfferService.getDriverOfferInfoByQuery(waybillIds);
    }

    /**
     * 查询报价信息
     * @return
     */
    @PostMapping("/getDriverOfferInfo")
    public ResultMode<DriverOffer> getDriverOfferInfo(@RequestBody DriverOfferFilter filter) {
        if(StrUtil.isBlank(filter.getDriverId())){
            ResultMode.fail("司机id不能为空");
        }
        if(StrUtil.isBlank(filter.getGoodsId())){
            ResultMode.fail("货源号不能为空");
        }
        List<DriverOffer> driverOfferList = driverOfferService.listByEntity(filter);
        ResultMode success = ResultMode.success();
        success.setModel(driverOfferList);
        return success;
    }

    @RequestMapping(value = "/queryDriverOfferByGoodsIds", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<DriverOffer> queryDriverOfferByGoodsIds(@RequestBody List<String> goodsIds) {
        ResultMode<DriverOffer> resultMode = new ResultMode<DriverOffer>();
        if (IterUtil.isEmpty(goodsIds)) {
            resultMode.setSucceed(false);
            resultMode.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultMode.setErrMsg("货源号为空");
            return resultMode;
        }
        List<DriverOffer> driverOffers = driverOfferService.queryDriverOfferByGoodsIds(goodsIds);
        if (IterUtil.isEmpty(driverOffers)) {
            resultMode.setSucceed(true);
            resultMode.setModel(new ArrayList<>());
        }
        resultMode.setSucceed(true);
        resultMode.setModel(driverOffers);
        return resultMode;
    }

    @PostMapping(value = "/getUnacceptedOrdersByGoodsId")
    public ResultMode<DriverOffer> getUnacceptedOrdersByGoodsId(@RequestBody PagingInfo<DriverOfferFilter> pageInfo) {
        log.info("分页查询显示未接单车辆:pageInfo:{}" , JSONObject.toJSONString(pageInfo));
        ResultMode<DriverOffer> ResultMode = new ResultMode<>();

        List<DriverOffer> list = driverOfferService.getUnacceptedOrdersByGoodsId(pageInfo);
        //处理分页
        PageInfo<DriverOffer> returnPageInfo = new PageInfo<>(list);
        //处理返回数据
        ResultMode.setTotal((int) returnPageInfo.getTotal());
        ResultMode.setModel(returnPageInfo.getList());
        return ResultMode;
    }

    /**
     * 批量查询司机报价
     */
    @PostMapping(value = "/getByIds")
    public ResultMode<DriverOffer> getByIds(@RequestBody List<String> ids) {
        List<DriverOffer> list = driverOfferService.getByIds(ids);
        if (IterUtil.isEmpty(list)) {
            return ResultMode.success();
        }

        return ResultMode.successList(list);
    }
}
