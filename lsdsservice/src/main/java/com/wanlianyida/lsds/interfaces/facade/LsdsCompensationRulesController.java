package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.LsdsGoods;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsDeductible;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsCompensationRulesFilter;
import com.wanlianyida.lsds.application.service.DriverGoodsAppService;
import com.wanlianyida.lsds.application.service.GoodsDeductibleAppService;
import com.wanlianyida.lsds.application.service.LsdsGoodsAppService;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequestMapping("/compensationRules")
@Log4j2
@RestController
public class LsdsCompensationRulesController {

    @Resource
    private GoodsDeductibleAppService goodsDeductibleService;

    @Resource
    private DriverGoodsAppService driverGoodsService;
    @Resource
    private LsdsGoodsAppService lsdsGoodsBusiness;

    @PostMapping(value = "/getLsdsGoodsCompensationRulesById")
    public ResultMode<LsdsGoodsDeductible> getLsdsGoodsCompensationRulesById(@RequestBody LsdsGoodsCompensationRulesFilter filter) {
        ResultMode<LsdsGoodsDeductible> result = new ResultMode<>();
        if (StrUtil.isEmpty(filter.getGoodsId())) {
            result.setErrMsg("货源id不能为空");
            result.setTotal(0);
            result.setSucceed(false);
            return result;
        }

        if (filter.getGoodsId().startsWith("DFQ")) {
            DriverGoods driverGoods = driverGoodsService.getByGoodsId(filter.getGoodsId());
            filter.setOtherKuiTonsRatio(driverGoods.getOtherKuiTonsRatio());
        } else {
            LsdsGoods lsdsGoods = lsdsGoodsBusiness.lsdsGoodsGet(filter.getGoodsId(), "");
            filter.setOtherKuiTonsRatio(lsdsGoods.getOtherKuiTonsRatio());
        }
        result.setModel(Lists.newArrayList(goodsDeductibleService.wrapGoodsDeductible(filter.getOtherKuiTonsRatio(), filter.getGoodsId())));
        result.setTotal(1);
        result.setSucceed(true);
        if (result.getModel().size() < 1) {
            result.setErrMsg("无法获取免赔数据");
            result.setTotal(0);
            result.setSucceed(false);
        }
        return result;
    }
}
