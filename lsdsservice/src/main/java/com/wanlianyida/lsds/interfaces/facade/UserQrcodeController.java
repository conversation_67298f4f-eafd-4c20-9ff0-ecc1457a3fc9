package com.wanlianyida.lsds.interfaces.facade;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.entity.UserQrcode;
import com.isoftstone.hig.lsds.api.filter.UserQrcodeFilter;
import com.wanlianyida.lsds.domain.service.UserQrcodeDomainService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 用户和货源码的关系Controller
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@RequestMapping("/userQrcode")
@RestController
public class UserQrcodeController {
    @Resource
    private UserQrcodeDomainService userQrcodeService;


    /**
     * 查询用户和货源码的关系
     *
     * @param userQrcodeFilter 用户和货源码的关系ID
     * @return 用户和货源码的关系
     */
    @PostMapping("/getById")
    public ResultMode<UserQrcode> getById(@RequestBody UserQrcodeFilter userQrcodeFilter) {
        return new ResultMode(userQrcodeService.getById(userQrcodeFilter.getId()));
    }

    /**
     * 查询用户和货源码的关系列表
     *
     * @param userQrcodeFilter 用户和货源码的关系
     * @return 用户和货源码的关系集合
     */
    @PostMapping("/listByEntity")
    public ResultMode<List<UserQrcode>> listByEntity(@RequestBody UserQrcodeFilter userQrcodeFilter) {
        return new ResultMode(userQrcodeService.listByEntity(userQrcodeFilter));
    }

    /**
     * 新增用户和货源码的关系
     *
     * @param userQrcode 用户和货源码的关系
     * @return 结果
     */
    @PostMapping(value = "/save")
    public ResultMode save(@RequestBody UserQrcode userQrcode) {
        return new ResultMode(userQrcodeService.save(userQrcode));
    }

    /**
     * 修改用户和货源码的关系
     *
     * @param userQrcodeFilter 用户和货源码的关系
     * @return 结果
     */
    @PostMapping(value = "/updateById")
    public ResultMode updateById(@RequestBody UserQrcodeFilter userQrcodeFilter) {
        return new ResultMode(userQrcodeService.updateById(userQrcodeFilter));
    }


    /**
     * 批量删除用户和货源码的关系
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @PostMapping(value = "/removeByIds")
    public ResultMode removeByIds(String[] ids) {
        return new ResultMode(userQrcodeService.removeByIds(ids));
    }

    /**
     * 删除用户和货源码的关系信息
     *
     * @param id 用户和货源码的关系ID
     * @return 结果
     */

    @PostMapping(value = "/removeById")
    public ResultMode removeById(String id) {
        return new ResultMode(userQrcodeService.removeById(id));
    }

    /**
     * 分页查询用户和货源码的关系列表
     *
     * @param pageInfo 用户和货源码的关系
     * @return 用户和货源码的关系集合
     */
    @PostMapping(value = "/page")
    public ResultMode<UserQrcode> page(@RequestBody PagingInfo<UserQrcodeFilter> pageInfo) {
        ResultMode<UserQrcode> ResultMode = new ResultMode<>();
        //设置分页参数
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        //列表查询
        List<UserQrcode> list = userQrcodeService.page(pageInfo);
        //处理分页
        PageInfo<UserQrcode> returnPageInfo = new PageInfo<UserQrcode>(list);
        //处理返回数据
        ResultMode.setTotal((int) returnPageInfo.getTotal());
        ResultMode.setModel(returnPageInfo.getList());
        return ResultMode;
    }
}
