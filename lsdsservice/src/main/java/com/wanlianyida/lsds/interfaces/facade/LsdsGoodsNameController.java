package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.model.TokenInfo;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.framework.component.file.utils.ExcelUtil;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsName;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsNameRelation;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsNameFilter;
import com.isoftstone.hig.lsds.api.mvcvo.BatchGoodsNameAddExcelReq;
import com.isoftstone.hig.lsds.api.mvcvo.BatchGoodsNameAddImportReq;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsNameCreateVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsNameVo;
import com.wanlianyida.lsds.application.service.LsdsGoodsNameAppService;
import com.wanlianyida.lsds.domain.service.LsdsGoodsNameRelationDomainService;
import com.wanlianyida.lsds.infrastructure.exception.LsdsStatusCodeEnum;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.repository.mapper.LsdsGoodsNameMapper;
import com.wanlianyida.lsds.interfaces.listener.BatchGoodsNameListListener;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 货物名称管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 */
@RequestMapping(value = "/lsdsGoodsName")
@Slf4j
@RestController
public class LsdsGoodsNameController   {

    @Resource
    private LsdsGoodsNameAppService goodsNameService;

    @Resource
    private LsdsGoodsNameRelationDomainService goodsNameRelationService;


    @Resource
    private LsdsGoodsNameAppService lsdsGoodsNameService;

    @Resource
    private LsdsGoodsNameRelationDomainService lsdsGoodsNameRelationService;


    @Resource
    private LsdsGoodsNameMapper lsdsGoodsNameMapper;


    /**
     * 查询货品名称列表
     *
     * @param pageInfo 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    @RequestMapping(value = "/getGoodsNameListPage", method = RequestMethod.POST)
    public ResultMode<LsdsGoodsNameVo> getGoodsNameListPage(@RequestBody PagingInfo<LsdsGoodsNameFilter> pageInfo) {
        return goodsNameService.getGoodsNameListPage(pageInfo);
    }

    /**
     * 查询货物名称关系
     *
     * @param pageInfo 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    @RequestMapping(value = "/getGoodsNameRelationListPage", method = RequestMethod.POST)
    public ResultMode<LsdsGoodsNameRelation> getGoodsNameRelationListPage(@RequestBody PagingInfo<LsdsGoodsNameRelation> pageInfo){
        return goodsNameRelationService.getGoodsNameRelationListPage(pageInfo);
    }

    /**
     * 新增货物名称
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @RequestMapping(value = "/addGoodsName", method = RequestMethod.POST)
    public ResultMode addGoodsName(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo) {
        TokenInfo tokenInfo = JwtUtil.getInstance().getTokenInfo();
        goodsNameCreateVo.setCompanyId(tokenInfo.getCompanyId());
        goodsNameCreateVo.setCompanyName(tokenInfo.getCompanyShortName());
        if(StrUtil.isAllNotBlank(goodsNameCreateVo.getCreateCompanyId(),goodsNameCreateVo.getCreateCompanyName())){
            goodsNameCreateVo.setCompanyId(goodsNameCreateVo.getCreateCompanyId());
            goodsNameCreateVo.setCompanyName(goodsNameCreateVo.getCreateCompanyName());
        }
        return goodsNameService.addGoodsName(goodsNameCreateVo);
    }


    /**
     * 新增货物名称 -- 智慧场站专用
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @RequestMapping(value = "/addGoodsNameByStation", method = RequestMethod.POST)
    public ResultMode addGoodsNameByStation(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo) {
        return goodsNameService.addGoodsName(goodsNameCreateVo);
    }

    /**
     * 解除
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @RequestMapping(value = "/removeGoodsName", method = RequestMethod.POST)
    public ResultMode removeGoodsName(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo){
        return goodsNameRelationService.removeGoodsNameRelation(goodsNameCreateVo);
    }

    /**
     * 删除
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @RequestMapping(value = "/deleteGoodsName", method = RequestMethod.POST)
    public ResultMode deleteGoodsName(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo){
        return goodsNameService.deleteGoodsName(goodsNameCreateVo);
    }

    /**
     * 禁用
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @RequestMapping(value = "/disableGoodsName", method = RequestMethod.POST)
    public ResultMode disableGoodsName(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo){
        return goodsNameService.disableGoodsName(goodsNameCreateVo);
    }

    /**
     * 启用
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @RequestMapping(value = "/enableGoodsName", method = RequestMethod.POST)
    public ResultMode enableGoodsName(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo){
        return goodsNameService.enableGoodsName(goodsNameCreateVo);
    }

    /**
     * 审核
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @RequestMapping(value = "/auditGoodsName", method = RequestMethod.POST)
    public ResultMode auditGoodsName(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo){
        return goodsNameService.auditGoodsName(goodsNameCreateVo);
    }

    /**
     * 调整平台分类
     *
     * @param goodsNameCreateVo 货物名称
     * @return {@link ResultMode}
     */
    @RequestMapping(value = "/editPlatformType", method = RequestMethod.POST)
    public ResultMode editPlatformType(@RequestBody LsdsGoodsNameCreateVo goodsNameCreateVo){
        return goodsNameService.editPlatformType(goodsNameCreateVo);
    }

    /**
     * 导出货物名称
     *
     * @param goodsNameFilter 货物名称
     * @return {@link ResultMode}
     */
    @RequestMapping(value = "/exportGoodsName", method = RequestMethod.POST)
    public void exportGoodsName(@RequestBody LsdsGoodsNameFilter goodsNameFilter){
        InputStream is = null;
        try{
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getResponse();

            List<LsdsGoodsNameVo> goodsNameList = goodsNameService.exportGoodsName(goodsNameFilter);
            ClassPathResource resource = new ClassPathResource("verifyxml/ExportGoodsNameMode.xml");
            is = resource.getInputStream();
            ExcelUtil.exportDataToExcel(request,response,"货物名称",is,goodsNameList,"货物名称", JwtUtil.getInstance().getUsernameByToken() + "(" + JwtUtil.getInstance().getLoginNameByToken() + ")");
        }catch (Exception e){
            log.error("导出货物名称异常：{}", e);
        }finally {
            if(null != is){
                try {
                    is.close();
                }catch (IOException e){
                    log.error("exportGoodsName InputStream 输入流关闭异常：{}", e);
                }
            }
        }
    }

    /**
     * 服务伙伴创建-按公司id查询货品名称下拉列表
     *
     * @param goodsNameFilter 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    @RequestMapping(value = "/findGoodsNameListForPartner", method = RequestMethod.POST)
    public ResultMode<LsdsGoodsNameVo> findGoodsNameListForPartner(@RequestBody LsdsGoodsNameFilter goodsNameFilter){
        Assert.notBlank(goodsNameFilter.getCompanyId(), "托运企业ID不能为空");
        return goodsNameService.findGoodsNameListForPartner(goodsNameFilter);
    }

    /**
     * 查询货品名称列表
     *
     * @param goodsNameFilter 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    @RequestMapping(value = "/findGoodsNameDropdownList", method = RequestMethod.POST)
    public ResultMode<LsdsGoodsNameVo> findGoodsNameDropdownList(@RequestBody LsdsGoodsNameFilter goodsNameFilter){
        return goodsNameService.findGoodsNameDropdownList(goodsNameFilter);
    }

    /**
     * 查询货品名称列表
     *
     * @param goodsNameFilter 货物名称信息
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    @RequestMapping(value = "/findGoodsNameList", method = RequestMethod.POST)
    public ResultMode<LsdsGoodsNameVo> findGoodsNameList(@RequestBody LsdsGoodsNameFilter goodsNameFilter){
        return goodsNameService.findGoodsNameList(goodsNameFilter);
    }

    /**
     * 按名称查询货品名称
     *
     * @param goodsNameFilter 货物名称信息
     * @return {@link LsdsGoodsName}
     */
    @RequestMapping(value = "/findGoodsNameByName", method = RequestMethod.POST)
    public ResultMode<LsdsGoodsNameVo> findGoodsNameByName(@RequestBody LsdsGoodsNameFilter goodsNameFilter) {
        return goodsNameService.findGoodsNameByName(goodsNameFilter);
    }

    @RequestMapping(value = "/addGoodsNameRelation", method = RequestMethod.POST)
    public ResultMode<String> addGoodsNameRelation(@RequestBody LsdsGoodsNameVo goodsNameVo) {
        return goodsNameService.addGoodsNameRelation(goodsNameVo);
    }

    /**
     * 批量导入货物名称
     *
     * @param file 货物名称文件
     * @return 处理结果
     */
    @PostMapping(value = "/batchImportGoodsName", consumes = {"multipart/form-data"},
        produces = {"application/json;charset=UTF-8"})
    @ApiOperation(value = "批量导入货物名称", notes = "批量导入货物名称")
    @ResponseBody
    public ResultMode batchImportGoodsName(@RequestParam("file") MultipartFile file) {
        try {
            BatchGoodsNameAddImportReq goodsNameAddImportReq = new BatchGoodsNameAddImportReq();
            BatchGoodsNameListListener goodsNameListListener = new BatchGoodsNameListListener(lsdsGoodsNameService, lsdsGoodsNameRelationService, lsdsGoodsNameMapper, goodsNameAddImportReq);
            EasyExcel.read(file.getInputStream(), BatchGoodsNameAddExcelReq.class, goodsNameListListener).sheet().doReadSync();
            return ResultMode.success(goodsNameAddImportReq);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("批量导入货物名称失败");
            return ResultMode.fail("批量导入货物名称失败,请检查更正后重新导入");
        }
    }


    /**
     * @Description: 根据companyId和货物名称查询货物列表
     * @Author: Mindy
     * @Date: 2023/11/20
     * @Param:1
     * @return:
     */
    @RequestMapping(value = "/getGoodsNameList", method = RequestMethod.POST)
    public ResultMode<LsdsGoodsNameVo> getGoodsNameList(@RequestBody PagingInfo<LsdsGoodsNameFilter> pagingInfo) {
       log.info("LsdsGoodsNameController......getGoodsNameList......入参:{}", JSON.toJSONString(pagingInfo));
       ResultMode<LsdsGoodsNameVo> resultMode = new ResultMode();

       LsdsGoodsNameFilter goodsNameFilter = pagingInfo.getFilterModel();
       if(StrUtil.isEmpty(goodsNameFilter.getCompanyId())){
           throw new LsdsWlydException(LsdsStatusCodeEnum.BUSS_ERROR_BLSDS1019.getCode(),LsdsStatusCodeEnum.BUSS_ERROR_BLSDS1019.getMsg());
       }

        goodsNameFilter.setOpInter(LsdsEnum.OpInterEnum.OPINTER_NO01.getCode());
        goodsNameFilter.setStatus(LsdsEnum.GoodsNameStatusEnum.ENABLE_STATUS.getCode());

       //设置分页参数
       PageHelper.startPage(pagingInfo.currentPage, pagingInfo.pageLength);

       List<LsdsGoodsNameVo>  lsdsGoodsNameVoList = goodsNameService.queryGoodsNameListByCondition(goodsNameFilter);

       resultMode.setSucceed(true);
       resultMode.setModel(lsdsGoodsNameVoList);

       return resultMode;
    }

    @RequestMapping(value = "/findBatchGoodsNameByName", method = RequestMethod.POST)
    public ResultMode<LsdsGoodsNameVo> findBatchGoodsNameByName(@RequestBody LsdsGoodsNameFilter goodsNameFilter) {
        return goodsNameService.findBatchGoodsNameByName(goodsNameFilter);
    }
}
