//=========================================================
//===       此类是由代码工具生成，框架开发者
//===       框架开发者Create By: 李健华
//===       Create Date: 2019/11/15 14:56:33
//=========================================================
package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.isoftstone.hig.annotations.LogPrintPoint;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.LogHelper;
import com.isoftstone.hig.common.utils.ValidateUtils;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.crm.api.inter.CrmCompanyLineAddressInter;
import com.isoftstone.hig.framework.component.file.utils.ExcelUtil;
import com.isoftstone.hig.lsds.api.command.BidOpeningCommand;
import com.isoftstone.hig.lsds.api.dto.GoodsStatisticsDTO;
import com.isoftstone.hig.lsds.api.dto.LsdsGoodsBidOpeningDTO;
import com.isoftstone.hig.lsds.api.entity.*;
import com.isoftstone.hig.lsds.api.enums.GoodsSourceTypeEnum;
import com.isoftstone.hig.lsds.api.filter.*;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsVo;
import com.isoftstone.hig.lsds.api.mvcvo.PlanTransferToGoodsVo;
import com.isoftstone.hig.lsds.api.mvcvo.WebsiteGoods;
import com.isoftstone.hig.platform.api.client.PlatformCommonInterClient;
import com.isoftstone.hig.platform.api.filter.PlatformQrCodeFilter;
import com.isoftstone.hig.platform.api.inter.PlatformCmOperationMainBodyInter;
import com.isoftstone.hig.woa.api.vo.WoaLogisticsPlanVO;
import com.wanlianyida.lsds.application.model.command.GoodsSourceSyncDataCommand;
import com.wanlianyida.lsds.application.service.*;
import com.wanlianyida.lsds.infrastructure.enums.DealStatusEnum;
import com.wanlianyida.lsds.infrastructure.enums.GoodsPublishTypeEnum;
import com.wanlianyida.lsds.infrastructure.enums.StatusCodeEnum;
import com.wanlianyida.lsds.infrastructure.event.EventPublisher;
import com.wanlianyida.lsds.infrastructure.event.model.GoodsSourceSyncDataEvent;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.exchange.WoaExchangeService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 创建描述：货源编号  货源信息表控制器 类的后面Controller其实可以不用，只是为了容易区分
 *
 * <AUTHOR>
 * 创建时间 2019/11/15 14:56:33
 */
@RequestMapping(value = "/LsdsGoods")
@RestController
@Slf4j
public class LsdsGoodsController   {

    @Autowired
    private LsdsGoodsAppService lsdsGoodsTran;
    @Resource
    private DriverGoodsAppService driverGoodsService;
    @Autowired
    private PlatformCommonInterClient platformCommonInterClient;
    @Resource
    private CrmCompanyLineAddressInter crmCompanyLineAddressInter;
    @Autowired
    @Lazy
    private PlatformCmOperationMainBodyInter platformCmOperationMainBodyInter;

    @Resource
    private GoodsDeductibleAppService goodsDeductibleService;

    @Resource
    private WoaExchangeService woaExchangeService;

    @Resource
    private LsdsGoodsBidOpeningAppService lsdsGoodsBidOpeningService;

    @Resource
    private LsdsGoodsBidFileAppService lsdsGoodsBidFileService;

    @Resource
    private GoodsSourceAppService goodsSourceAppService;


    @Resource(name = "asyncContextAwareExecutor")
    private Executor asyncContextAwareExecutor;

    @Resource
    private EventPublisher eventPublisher;

    /***
     *
     * @param vo 实体货源编号  货源信息表
     * @Description 承运商货源自动审核
     * @return ResultMode<String>
     * <AUTHOR>
     * @date 2022/2/22 15:03
     ***/
    @RequestMapping(value = "/lsdsGoodsAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsAddWithAutoCheck(@RequestBody LsdsGoodsVo vo) {
        try {

            //检查出发地目的地是否一致
            ResultMode result = checkAddress(vo);
            if (!result.getSucceed()) {
                return result;
            }
            //检查计价类型
            LsdsGoods lGoods = vo.getLsdsGoods();
            if (StrUtil.isBlank(lGoods.getTotalQuantityUnits())) {
                return ResultMode.fail("计价类型不能为空！");
            }

            //1、货源主信息敏感词校验
            log.info("货源主信息敏感词校验>>>>>>>>");
            ResultMode<String> resultModel = lsdsGoodsTran.filterSensitiveWord(vo);

            if (!resultModel.getSucceed()) {
                return resultModel;
            }
            resultModel = lsdsGoodsTran.createCrmLine(vo);
            if (!resultModel.getSucceed()) {
                return resultModel;
            }
//            setTransportMileageByGoodsId(vo);
            //2、根据公司id查询公司和管理员信息，并且判断平台是否为3PL
            boolean flag = lsdsGoodsTran.checkPlatformIs3PL(vo);
            log.info("是否为平台3PL:{}", flag);
            if (flag) {//是平台3pl,      * @param flag 平台是否为3PL
                ResultMode<String> resultMode = lsdsGoodsTran.handle3PLAudit(vo, flag);
                if(ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && CollUtil.isNotEmpty(resultMode.getModel())){
                    syncGoodsSourceData(resultMode.getModel().get(0),vo.getLsdsGoods().getLsdsGoodsDeductible());
                }
                return resultMode;
            }
            //3、判断网络货运主体 开启自动审核开关 是否开启
            LsdsGoods lsdsGoods = vo.getLsdsGoods();
            //交易状态  20-待审核
            String networkMainBodyId = lsdsGoodsTran.checkNetworkMainBody(vo);

            if (StringUtils.hasText(networkMainBodyId)) {
                lsdsGoods.setNetworkMainBodyId(networkMainBodyId);
                vo.setLsdsGoods(lsdsGoods);
            }

            log.info("交易状态:{}", lsdsGoods.getDealStatus());
            //待审核
            if (StrUtil.equals(DealStatusEnum.TODO_AUDIT.getCode(), lsdsGoods.getDealStatus())) {
                boolean autoAudit = lsdsGoodsTran.judgeAutoAudit(lsdsGoods);
                log.info("自动审核状态：{}", autoAudit);
                if (autoAudit) {//开启自动审核
                    log.info(">>>>>>>>自动审核>>>>>>>>>>");
                    ResultMode<String> resultMode = lsdsGoodsTran.handleAutoAudit(vo);
                    log.info("货源id:{}", vo.getLsdsGoods().getGoodsId());

                    log.info("返回结果：{}", resultMode.getSucceed());
                    if (!resultMode.getSucceed()) {
                        log.info(">>>>>>>>>>>>人工审核>>>>>>>>");
                        ResultMode<String> res = lsdsGoodsTran.handleManualAudit(vo);
                        if(ObjUtil.isNotNull(res) && res.isSucceed() && CollUtil.isNotEmpty(res.getModel())){
                            syncGoodsSourceData(res.getModel().get(0),vo.getLsdsGoods().getLsdsGoodsDeductible());
                        }
                        log.info("goodsId:{}-{}", vo.getLsdsGoods().getGoodsId(), res.getModel().get(0));
                        lsdsGoodsTran.writeOperateLog(resultMode.getErrMsg(), res.getModel().get(0));
                        return res;
                    }
                    if(ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && CollUtil.isNotEmpty(resultMode.getModel())){
                        syncGoodsSourceData(resultMode.getModel().get(0),vo.getLsdsGoods().getLsdsGoodsDeductible());
                    }
                    return resultMode;
                }
                log.info(">>>>>>>>人工审核>>>>>>>>>>");
                ResultMode<String> resultMode = lsdsGoodsTran.handleManualAudit(vo);
                if(ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && CollUtil.isNotEmpty(resultMode.getModel())){
                    syncGoodsSourceData(resultMode.getModel().get(0),vo.getLsdsGoods().getLsdsGoodsDeductible());
                }
                return resultMode;
            }
            ResultMode<String> resultMode = lsdsGoodsTran.lsdsGoodsAdd(vo, "");
            if(ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && CollUtil.isNotEmpty(resultMode.getModel())){
                syncGoodsSourceData(resultMode.getModel().get(0),vo.getLsdsGoods().getLsdsGoodsDeductible());
            }
            //保存企业货源20230424
            return resultMode;
        } catch (Exception e) {
            log.error("企业货源创建异常:{}", e);
            throw new LsdsWlydException("承运商发布货源失败:" + e.getMessage(), StatusCodeEnum.DB_CREATE_ERR.getCode());
        }
    }

    @LogPrintPoint(ret = true)
    @RequestMapping(value = "/planTransferToGoods", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> planTransferToGoods(@RequestBody PlanTransferToGoodsVo vo) {
        // 计划单信息校验
        WoaLogisticsPlanVO woaLogisticsPlanVO = woaExchangeService.getLogisticsPlanByPlanId(vo.getPlanId());
        if (woaLogisticsPlanVO == null || woaLogisticsPlanVO.getPlanId() == null) {
            throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_DB_NORECORD, "计划单不存在");
        }
        // 发布货源
        ResultMode<String> resultMode = lsdsGoodsAddWithAutoCheck(vo);
        woaExchangeService.goodsIdWriteBack(vo.getPlanId(), IterUtil.getFirst(resultMode.getModel()));
        return resultMode;
    }

    /*private CrmCompanyLineAddress createLsdsGoodsLine(LsdsGoodsVo vo) {

        CrmCompanyLineAddress startCrmCompanyLineAddress = null;
        CrmCompanyLineAddress endCrmCompanyLineAddress = null;
        LsdsGoodsAddress goodsAddress = vo.getGoodsAddress();

        if (goodsAddress != null && StrUtil.isNotBlank(vo.getGoodsAddress().getSendAddrShortName())) {


            startCrmCompanyLineAddress = crmCompanyLineAddressInter.findLineByLineShortNameAndCompanyId(vo.getLsdsGoods().getSendAddrShortName(), vo.getLsdsGoods().getCompanyId()).getModel().get(0);
            vo.getLsdsGoods().setStartLineId(startCrmCompanyLineAddress.getLineId());
        }
        if (goodsAddress != null && StrUtil.isNotBlank(vo.getGoodsAddress().getReceiveAddrShortName())) {

            endCrmCompanyLineAddress = crmCompanyLineAddressInter.findLineByLineShortNameAndCompanyId(vo.getLsdsGoods().getReceiveAddrShortName(), vo.getLsdsGoods().getCompanyId()).getModel().get(0);
            vo.getLsdsGoods().setEndLineId(endCrmCompanyLineAddress.getEndLineId());
        }


        if (startCrmCompanyLineAddress == null) {
            log.info(">>>根据出发地ID：{}>>>出发地为空！！", JSONUtil.toJsonStr(goodsAddress));
            return null;
        }

        if (endCrmCompanyLineAddress == null) {
            log.info(">>>根据地址ID：{}>>>目的地为空！！", JSONUtil.toJsonStr(goodsAddress));
            return null;
        }

        //根据地址判断线路是否存在,存在返回，不存在创建线路
        CheckLineExitsFilter checkLineExitsFilter = new CheckLineExitsFilter();
        checkLineExitsFilter.setEndCrmCompanyLineAddress(endCrmCompanyLineAddress);
        checkLineExitsFilter.setStartCrmCompanyLineAddress(startCrmCompanyLineAddress);
        CrmCompanyLineAddress lineObj = crmCompanyLineAddressInter.checkLineExits(checkLineExitsFilter);
        if (lineObj != null) {
            vo.getLsdsGoods().setLineId(lineObj.getLineId());
        }
        return lineObj;
    }*/


//    private void setTransportMileageByGoodsId(LsdsGoodsVo vo){
//
//            CrmCompanyLineAddress model = new CrmCompanyLineAddress();
//            model.setSendShortName(vo.getLsdsGoods().getSendAddrShortName());
//            model.setReceiveShortName(vo.getLsdsGoods().getReceiveAddrShortName());
//            log.info("公司id为:{}",JwtUtil.getInstance().getCompanyIdByToken());
//            model.setCompanyId(JwtUtil.getInstance().getCompanyIdByToken());
//            //根据地址简称查询对应线路
//            log.info("企业货源getBySendShortNameAndReceiveShortName入参:{}", JSONObject.toJSONString(model));
//            ResultMode<CrmCompanyLineAddress> addressResult = crmCompanyLineAddressInter.getBySendShortNameAndReceiveShortName(model);
//            log.info("企业货源getBySendShortNameAndReceiveShortName出参:{}", JSONObject.toJSONString(addressResult));
//            if (addressResult.getSucceed() && !CollectionUtils.isEmpty(addressResult.getModel())) {
//                //货源距离修改
//                vo.getLsdsGoods().setTransportMileage(addressResult.getModel().get(0).getTransportMileage());
//                vo.getLsdsGoods().setTakeUpTime(addressResult.getModel().get(0).getTakeUpTime());
//            }
//
//        log.info("setTransportMileageByGoodsId返回结果:{}", JSONObject.toJSONString(vo));
//    }

    private ResultMode checkAddress(LsdsGoodsVo vo) {
        ResultMode resultMode = new ResultMode();
        LsdsGoods lsdsGoods = vo.getLsdsGoods();
//        if(lsdsGoods.getSendAddrProvince().equalsIgnoreCase(lsdsGoods.getReceiveAddrProvince())
//                &&lsdsGoods.getSendAddrCity().equalsIgnoreCase(lsdsGoods.getReceiveAddrCity())
//                &&lsdsGoods.getSendAddrArea().equalsIgnoreCase(lsdsGoods.getReceiveAddrArea())
//        ){
//            if(lsdsGoods.getSendAddrDetail().equalsIgnoreCase(lsdsGoods.getReceiveAddrDetail())){
//                return ResultMode.fail("出发地、目的地省市区县相同时，详细地址不能相同");
//            }
//        }
//        if(lsdsGoods.getSendPhoneNumber().equalsIgnoreCase(lsdsGoods.getReceivePhoneNumber())){
//            return ResultMode.fail("出发地联系电话与目的地联系电话不能相同");
//        }
        if (lsdsGoods.getIsTransaction() == null) {
            lsdsGoods.setIsTransaction(false);
        }
        //转交易场景，LineId必传
        if (lsdsGoods.getIsTransaction() && StrUtil.isBlank(lsdsGoods.getLineId())) {
            return ResultMode.fail("线路ID不能为空!");
        }

        if (StrUtil.isBlank(lsdsGoods.getLineId())) {
            if (StrUtil.isBlank(lsdsGoods.getStartLineId())) {
                return ResultMode.fail("出发地ID不能为空!");
            }
            if (StrUtil.isBlank(lsdsGoods.getEndLineId())) {
                return ResultMode.fail("目的地ID不能为空!");
            }
        }
        return ResultMode.success();
    }

    /**
     * 新增下游询价货源
     * 创建者：cgb
     * 创建时间：2020/1/15 14:56:33
     *
     * @param vo 货源实体
     * @return {@code ResultMode<String>}
     */
    @RequestMapping(value = "/lsdsGoodsDownAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsDownAdd(@RequestBody LsdsGoodsVo vo) throws Exception {
        ResultMode<String> resultModel = new ResultMode<String>();

        if (null != vo && null != vo.getLsdsGoods()) {
            //#region 判断验证数据
            if (StringUtils.isEmpty(vo.getLsdsGoods().getParentGoodsId())) {
                resultModel.setErrMsg("继承的父货源号:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            //参数校验
            lsdsGoodsTran.checkParams(vo, resultModel);

            Map<String, String> map = new HashMap<String, String>(16);
            //货源主信息敏感词校验字段
            map.put("出发地简称", vo.getLsdsGoods().getSendAddrShortName());
            map.put("出发地联系人", vo.getLsdsGoods().getSendLinker());
            map.put("出发地详细地址 ", vo.getLsdsGoods().getSendAddrDetail());
            map.put("目的地简称", vo.getLsdsGoods().getReceiveAddrShortName());
            map.put("目的地联系人", vo.getLsdsGoods().getReceiveLinker());
            map.put("目的详细地址 ", vo.getLsdsGoods().getReceiveAddrDetail());
            map.put("客户名称", vo.getLsdsGoods().getCustomerName());
            map.put("合同名称", vo.getLsdsGoods().getContractName());
            map.put("货物名称", vo.getLsdsGoods().getGoodsName());
            map.put("货物描述", vo.getLsdsGoods().getGoodsDesc());
            map.put("指定车牌", vo.getLsdsGoods().getAssignCarPlateNumber());
            map.put("运输要求", vo.getLsdsGoods().getOtherRemark());

            //货源拆段信息敏感词校验字段
            if (null != vo.getGoodsSplitList() && vo.getGoodsSplitList().size() > 0) {
                for (LsdsGoodsSplit split : vo.getGoodsSplitList()) {
                    map.put(split.getSortNode() + "-运输拆分-出发地简称", split.getSendAddrShortName());
                    map.put(split.getSortNode() + "-运输拆分-出发地详细地址 ", split.getSendAddrDetail());
                    map.put(split.getSortNode() + "-运输拆分-目的地简称", split.getReceiveAddrShortName());
                    map.put(split.getSortNode() + "-运输拆分-目的详细地址 ", split.getReceiveAddrDetail());
                }
            }
            Map<String, String> returnMap = ValidateUtils.sensitiveWordValid(map);
            if (null != returnMap && returnMap.size() > 0) {
                resultModel.setErrMsg("敏感词校验不通过:" + returnMap);
                resultModel.getModel().add("敏感词校验不通过:" + returnMap);
                resultModel.setSucceed(false);
            }

            //#endregion
        } else {
            resultModel.setSucceed(false);
        }

        if (!resultModel.getSucceed()) {
            return resultModel;
        } else {
            //设置货源发布类型标识为下游询价
            vo.getLsdsGoods().setReleaseType(2);
            resultModel = lsdsGoodsTran.lsdsGoodsDownAdd(vo, "");
            String goodsId = resultModel.getModel().get(0);
            //if("2".equals(vo.getLsdsGoods().getCompanyId())
            if (StringUtil.isNotBlank(vo.getLsdsGoods().getCompanyId()) && platformCmOperationMainBodyInter.isTransactionBody(vo.getLsdsGoods().getCompanyId())
                && "2".equals(vo.getLsdsGoods().getDealStatus()) && resultModel.getSucceed() && null != resultModel.getModel() && resultModel.getModel().size() > 0) {
                vo.getLsdsGoods().setGoodsId(resultModel.getModel().get(0));
                vo.getLsdsGoods().setDealStatus("3");
                resultModel = lsdsGoodsTran.lsdsGoodsUpdate(vo, "");
                resultModel.getModel().add(goodsId);
                //同步货源数据
                syncGoodsSourceData(goodsId,vo.getLsdsGoods().getLsdsGoodsDeductible());
            }
        }
        return resultModel;
    }


    /**
     * 根据货源编号  货源信息表主键删除记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param goodsId 货源编号  货源信息表主键
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【删除成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【删除成功】编码,ResultMode.errMsg为相应【删除成功】描述；
     * 否则ResultMode.succeed=false【删除失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【删除失败】编码,ResultMode.errMsg为相应【删除失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<?> lsdsGoodsDel(@RequestParam String goodsId) {
        ResultMode<?> resultModel = new ResultMode<String>();
        if (StringUtils.isEmpty(goodsId)) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg("货源ID: 不能为空字符！");
            resultModel.setSucceed(false);
        } else {
            resultModel = goodsSourceAppService.deleteGoods(goodsId);
//            resultModel = lsdsGoodsTran.lsdsGoodsDel(goodsId, "");
        }
        return resultModel;
    }

    /**
     * 根据货源编号  货源信息表实体修改记录，内部以ID主键修改
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param vo 实体货源编号  货源信息表
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【修改成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【修改成功】编码,ResultMode.errMsg为相应【修改成功】描述；
     * 否则ResultMode.succeed=false【修改失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【修改失败】编码,ResultMode.errMsg为相应【修改失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsUpdate", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsUpdate(@RequestBody LsdsGoodsVo vo) throws Exception {
        ResultMode<String> resultModel = new ResultMode<String>();
        if (null != vo && null != vo.getLsdsGoods()) {

            //设置原先的经营主体id 为网络交易主体id
            vo.getLsdsGoods().setOperationMainBodyId(vo.getLsdsGoods().getTransactionContractingBodyId());

            //#region 判断验证数据
            if (StringUtils.isEmpty(vo.getLsdsGoods().getGoodsId())) {
                resultModel.getModel().add("goodsId:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(vo.getLsdsGoods().getDealStatus())) {
                resultModel.getModel().add("dealStatus:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (null != vo.getLsdsGoods().getDealStatus()) {
                if (LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus2.getCode().equals(vo.getLsdsGoods().getDealStatus())) {
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrShortName())) {
                        resultModel.setErrMsg("出发地简称:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrProvince())) {
                        resultModel.setErrMsg("出发地省编码:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrCity())) {
                        resultModel.setErrMsg("出发地市编码:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
//                    if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrArea())) {
//                        resultModel.setErrMsg("出发地区/县编码:" + "不能为空字符");
//                        resultModel.setSucceed(false);
//                    }
//            if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrStreet())) {
//                resultModel.setErrMsg("sendAddrStreet:" + "不能为空字符");
//                resultModel.setSucceed(false);
//            }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrProvinceName())) {
                        resultModel.setErrMsg("出发地省名称:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrCityName())) {
                        resultModel.setErrMsg("出发地市名称:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
//                    if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrAreaName())) {
//                        resultModel.setErrMsg("出发地区/县名称:" + "不能为空字符");
//                        resultModel.setSucceed(false);
//                    }
//            if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrStreetName())) {
//                resultModel.setErrMsg("sendAddrStreetName:" + "不能为空字符");
//                resultModel.setSucceed(false);
//            }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getSendAddrDetail())) {
                        resultModel.setErrMsg("出发地详细地址 :" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getSendLinker())) {
                        resultModel.setErrMsg("出发地联系人:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getSendPhoneNumber())) {
                        resultModel.setErrMsg("出发地联系人联系方式:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrShortName())) {
                        resultModel.setErrMsg("目的地简称:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrProvince())) {
                        resultModel.setErrMsg("目的地省编码:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrCity())) {
                        resultModel.setErrMsg("目的地市编码:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
//                    if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrArea())) {
//                        resultModel.setErrMsg("目的地区/县编码:" + "不能为空字符");
//                        resultModel.setSucceed(false);
//                    }
//            if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrStreet())) {
//                resultModel.setErrMsg("receiveAddrStreet:" + "不能为空字符");
//                resultModel.setSucceed(false);
//            }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrProvinceName())) {
                        resultModel.setErrMsg("目的地省名称:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrCityName())) {
                        resultModel.setErrMsg("目的地市名称:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
//                    if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrAreaName())) {
//                        resultModel.setErrMsg("目的地区/县名称:" + "不能为空字符");
//                        resultModel.setSucceed(false);
//                    }
//            if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrStreetName())) {
//                resultModel.setErrMsg("receiveAddrStreetName:" + "不能为空字符");
//                resultModel.setSucceed(false);
//            }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveAddrDetail())) {
                        resultModel.setErrMsg("目的地详细地址:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getReceiveLinker())) {
                        resultModel.setErrMsg("目的地联系人:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getReceivePhoneNumber())) {
                        resultModel.setErrMsg("目的地联系人联系方式:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }

                    if (StringUtils.isEmpty(vo.getLsdsGoods().getGoodsName())) {
                        resultModel.setErrMsg("货物名称:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }

                    if (StringUtils.isEmpty(vo.getLsdsGoods().getGoodsType())) {
                        resultModel.setErrMsg("货物类型:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }

                    if (vo.getLsdsGoods().getWeightSum() == null || vo.getLsdsGoods().getWeightSum().equals(BigDecimal.ZERO)) {
                        resultModel.setErrMsg("总重量:" + "数据格式不能为零");
                        resultModel.setSucceed(false);
                    }
//                    if (vo.getLsdsGoods().getOtherKuiTonsRatio() == null) {
//                        resultModel.setErrMsg("亏吨免赔系数:" + "数据格式不能为空");
//                        resultModel.setSucceed(false);
//                    }
                    goodsDeductibleService.checkParams(vo.getLsdsGoods().getLsdsGoodsDeductible(), resultModel);

                    if (StringUtils.isEmpty(vo.getLsdsGoods().getTransportationType())) {
                        resultModel.setErrMsg("运输类型:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getReleaseDate())) {
                        resultModel.setErrMsg("预计发货时间:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getArriveDate())) {
                        resultModel.setErrMsg("要求到货时间:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }

                    Map<String, String> map = new HashMap<String, String>(16);
                    //货源主信息敏感词校验字段
                    map.put("出发地简称", vo.getLsdsGoods().getSendAddrShortName());
                    map.put("出发地联系人", vo.getLsdsGoods().getSendLinker());
                    map.put("出发地详细地址 ", vo.getLsdsGoods().getSendAddrDetail());
                    map.put("目的地简称", vo.getLsdsGoods().getReceiveAddrShortName());
                    map.put("目的地联系人", vo.getLsdsGoods().getReceiveLinker());
                    map.put("目的详细地址 ", vo.getLsdsGoods().getReceiveAddrDetail());
                    map.put("客户名称", vo.getLsdsGoods().getCustomerName());
                    map.put("合同名称", vo.getLsdsGoods().getContractName());
                    map.put("货物名称", vo.getLsdsGoods().getGoodsName());
                    map.put("货物描述", vo.getLsdsGoods().getGoodsDesc());
                    map.put("指定车牌", vo.getLsdsGoods().getAssignCarPlateNumber());
                    map.put("运输要求", vo.getLsdsGoods().getOtherRemark());

                    //货源拆段信息敏感词校验字段
                    if (null != vo.getGoodsSplitList() && vo.getGoodsSplitList().size() > 0) {
                        for (LsdsGoodsSplit split : vo.getGoodsSplitList()) {
                            map.put(split.getSortNode() + "-运输拆分-出发地简称", split.getSendAddrShortName());
                            map.put(split.getSortNode() + "-运输拆分-出发地详细地址 ", split.getSendAddrDetail());
                            map.put(split.getSortNode() + "-运输拆分-目的地简称", split.getReceiveAddrShortName());
                            map.put(split.getSortNode() + "-运输拆分-目的详细地址 ", split.getReceiveAddrDetail());
                        }
                    }
                    Map<String, String> returnMap = ValidateUtils.sensitiveWordValid(map);
                    if (null != returnMap && returnMap.size() > 0) {
                        resultModel.setErrMsg("敏感词校验不通过:" + returnMap);
                        resultModel.getModel().add("敏感词校验不通过:" + returnMap);
                        resultModel.setSucceed(false);
                    }
                }
                if (LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus3.getCode().equals(vo.getLsdsGoods().getDealStatus())) {
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getOperationMainBodyId())) {
                        resultModel.setErrMsg("经营主体ID:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getBodyName())) {
                        resultModel.setErrMsg("经营主体名称:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getOtherClearType())) {
                        resultModel.setErrMsg("结算方式:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getContractType()) || !"2".equals(vo.getLsdsGoods().getContractType())) {
                        //判断上浮数据是否为空
                        if (StringUtils.isEmpty(vo.getLsdsGoods().getFeeClearType())) {
                            resultModel.setErrMsg("费率上浮类型:" + "不能为空字符");
                            resultModel.setSucceed(false);
                        }
                        if (StringUtils.isEmpty(vo.getLsdsGoods().getFeeClearValue()) || (vo.getLsdsGoods().getFeeClearValue()).compareTo(BigDecimal.ZERO) < 0) {
                            resultModel.setErrMsg("上浮值:" + "必须大于等于零！");
                            resultModel.setSucceed(false);
                        }
//                        if (!StringUtils.isEmpty(vo.getLsdsGoods().getFeeClearType()) && !StringUtils.isEmpty(vo.getLsdsGoods().getFeeClearValue())) {
//                            if ("1".equals(vo.getLsdsGoods().getFeeClearType()) && (vo.getLsdsGoods().getFeeClearValue().subtract(new BigDecimal("100"))).compareTo(BigDecimal.ZERO) <= 0) {
//                                resultModel.setErrMsg("上浮值:" + "必须小于100！");
//                                resultModel.setSucceed(false);
//                            }
//                        }
                    }

                    if (!StringUtils.isEmpty(vo.getLsdsGoods().getContractType()) && "2".equals(vo.getLsdsGoods().getContractType())) {
                        if (StringUtils.isEmpty(vo.getLsdsGoods().getContractId())) {
                            resultModel.setErrMsg("合同号:" + "不能为空字符");
                            resultModel.setSucceed(false);
                        }
                        if (StringUtils.isEmpty(vo.getLsdsGoods().getContractName())) {
                            resultModel.setErrMsg("合同名称:" + "不能为空字符");
                            resultModel.setSucceed(false);
                        }
                        if (StringUtils.isEmpty(vo.getLsdsGoods().getUnitPrice())) {
                            resultModel.setErrMsg("单价:" + "值不能为空");
                            resultModel.setSucceed(false);
                        }
                    }

                    if (!StringUtils.isEmpty(vo.getLsdsGoods().getUnitPrice()) && (vo.getLsdsGoods().getUnitPrice()).compareTo(BigDecimal.ZERO) < 0) {
                        resultModel.setErrMsg("单价:" + "值必须大于等于零！");
                        resultModel.setSucceed(false);
                    }

                }
                if (LsdsEnum.LsdsgoodsDealStatusEnum.dealStatus4.getCode().equals(vo.getLsdsGoods().getDealStatus().toString())) {
                    if (StringUtils.isEmpty(vo.getLsdsGoods().getAuditRecordStatusMark())) {
                        resultModel.setErrMsg("审核不通过原因:" + "不能为空字符");
                        resultModel.setSucceed(false);
                    }
                }
            }
            //#endregion
        } else {
            resultModel.setErrMsg("货源参数为空！");
            resultModel.setSucceed(false);
        }
        if (!resultModel.getSucceed()) {
            return resultModel;
        } else {
            if (StringUtil.isNotBlank(vo.getLsdsGoods().getCompanyId()) && platformCmOperationMainBodyInter.isTransactionBody(vo.getLsdsGoods().getCompanyId())
                && "2".equals(vo.getLsdsGoods().getDealStatus())) {
                vo.getLsdsGoods().setDealStatus("3");
            }
            resultModel = lsdsGoodsTran.lsdsGoodsUpdate(vo, "");
        }
        return resultModel;
    }

    /**
     * 货源编号  货源信息表分页查询
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsPaging", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> lsdsGoodsPaging(@RequestBody PagingInfo<LsdsGoodsFilter> pageInfo) {
        ResultMode<LsdsGoods> resultModel = new ResultMode<LsdsGoods>();
        if (null != pageInfo) {
            resultModel = lsdsGoodsTran.lsdsGoodsPaging(pageInfo, "");
        } else {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg("查询参数不能为空！");
            resultModel.setSucceed(false);
        }
        return resultModel;
    }


    /**
     * 货源信息分页查询
     * 创建者：cgb
     * 创建时间：2019/11/30
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/getGoodsListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> getGoodsListPage(@RequestBody PagingInfo<LsdsGoodsFilter> pageInfo) {
        ResultMode<LsdsGoods> resultModel = new ResultMode<LsdsGoods>();
        if (null != pageInfo) {
            if (StringUtils.isEmpty(pageInfo.filterModel) || StringUtils.isEmpty(pageInfo.filterModel.getCompanyId())) {
                resultModel.setErrMsg("companyId:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
        } else {
            resultModel.setSucceed(false);
        }
        if (!resultModel.getSucceed()) {
            return resultModel;
        } else {
            resultModel = lsdsGoodsTran.getGoodsListPage(pageInfo);
        }
        return resultModel;
    }

    /**
     * 查询待审核的司机和货源条数
     * 创建者：zmf
     * 创建时间：2022/01/14
     *
     * @return {@code Map<String,String> 1为货源管理,2为司机货源}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * ResultMode.total为本次查询的总记录数据
     */
    @RequestMapping(value = "/getGoodsListAuditData", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.GET)
    public Map<String, String> getGoodsListAuditData() {
        Map<String, String> map = new HashMap<>();
        ResultMode<LsdsGoods> lsdsGoodsResultModel = new ResultMode<LsdsGoods>();
        ResultMode<DriverGoods> driverGoodsResultMode = new ResultMode<DriverGoods>();

        LsdsGoodsFilter lsdsGoodsfilterModel = new LsdsGoodsFilter();
        //待审核数据
        lsdsGoodsfilterModel.setDealStatus("2");
        lsdsGoodsResultModel = lsdsGoodsTran.getGoodsListAuditData(lsdsGoodsfilterModel);
        DriverGoodsFilter driverGoodsfilterModel = new DriverGoodsFilter();
        //待审核数据
        driverGoodsfilterModel.setDealStatus("20");
        int total = driverGoodsService.auditAuditData(driverGoodsfilterModel);
        //处理返回数据
        driverGoodsResultMode.setTotal(total);
        log.info("货源管理待审核条数为" + lsdsGoodsResultModel.getTotal());
        log.info("司机货源待审核条数为" + driverGoodsResultMode.getTotal());
        map.put("lsdsGoodsResultModel", String.valueOf(lsdsGoodsResultModel.getTotal()));
        map.put("driverGoodsResultMode", String.valueOf(driverGoodsResultMode.getTotal()));
        return map;
    }

    /**
     * 分页查询货源操作记录列表
     * 创建者：cgb
     * 创建时间：2020/6/9
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code ResultMode<LsdsGoods>}
     */
    @RequestMapping(value = "/getGoodsRecordListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsRecord> getGoodsRecordListPage(@RequestBody PagingInfo<LsdsGoodsRecordFilter> pageInfo) {
        ResultMode<LsdsGoodsRecord> resultModel = new ResultMode<LsdsGoodsRecord>();
        if (null != pageInfo) {
            if (StringUtils.isEmpty(pageInfo.filterModel) || StringUtils.isEmpty(pageInfo.filterModel.getGoodsId())) {
                resultModel.setErrMsg("goodsId:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
        } else {
            resultModel.setSucceed(false);
        }
        if (!resultModel.getSucceed()) {
            return resultModel;
        } else {
            resultModel = lsdsGoodsTran.getGoodsRecordListPage(pageInfo);
        }
        return resultModel;
    }


    /**
     * 货源信息分页查询
     * 创建者：cgb
     * 创建时间：2019/11/30
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoods>}
     */
    @LogPrintPoint
    @RequestMapping(value = "/getHomeGoodsListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<WebsiteGoods> getHomeGoodsListPage(@RequestBody PagingInfo<LsdsGoodsFilter> pageInfo) {
        return lsdsGoodsTran.getHomeGoodsListPage(pageInfo);
    }

    /**
     * 查询工作台发货方货源统计信息
     * 创建者：cgb
     *
     * @param lsdsGoods 货源实体
     * @return {@code ResultMode<Map<String, Object>>}
     */
    @RequestMapping(value = "/lsdsGoodsSenderView", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<Map<String, String>> lsdsGoodsSenderView(@RequestBody LsdsGoods lsdsGoods) {
        ResultMode<Map<String, String>> resultModel = new ResultMode<Map<String, String>>();
        resultModel = lsdsGoodsTran.lsdsGoodsSenderView(lsdsGoods);
        return resultModel;
    }

    /**
     * 根据货源ID查询货源详细信息（包含货源主信息、货源分段信息、承运商信息）
     * 创建者：cgb
     * 创建时间：2019/11/20
     *
     * @param goodsId 货源ID
     * @return {@code  ResultMode<LsdsGoods>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货源编号  货源信息表实体类LsdsGoods数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsGet", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> lsdsGoodsGet(@RequestParam(required = false) String goodsId) {
        ResultMode<LsdsGoods> resultModel = new ResultMode<LsdsGoods>();
        if (StringUtils.isEmpty(goodsId)) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg("goodsId:不能为空字符！");
            resultModel.setSucceed(false);
        } else {
            resultModel = lsdsGoodsTran.lsdsGoodsGet1(goodsId, "");

        }
        return resultModel;
    }


    /**
     * 根据货源ID查询货源详细信息（包含货源主信息、货源分段信息、承运商信息）
     * 创建者：cgb
     * 创建时间：2019/11/20
     *
     * @param goodsId 货源ID
     * @return {@code String}
     */
    @RequestMapping(value = "/lsdsGoodsGetDetails", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsVo> lsdsGoodsGetDetails(@RequestParam String goodsId) {
        ResultMode<LsdsGoodsVo> resultModel = new ResultMode<LsdsGoodsVo>();
        if (StringUtils.isEmpty(goodsId)) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg("goodsId:不能为空字符");
            resultModel.setSucceed(false);
        } else {
            resultModel = lsdsGoodsTran.lsdsGoodsGetDetails(goodsId, "");
        }
        return resultModel;
    }

    /**
     * 下游询价货源根据货源ID查询货源详细信息
     * 创建者：cgb
     * 创建时间：2020/4/20
     *
     * @param goods 货源ID
     * @return {@code String}
     */
    @RequestMapping(value = "/lsdsGoodsGetDetailsForDown", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsVo> lsdsGoodsGetDetailsForDown(@RequestBody LsdsGoods goods) {
        ResultMode<LsdsGoodsVo> resultModel = new ResultMode<LsdsGoodsVo>();
        if (StringUtils.isEmpty(goods) || StringUtils.isEmpty(goods.getGoodsId())) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg("goodsId:不能为空字符");
            resultModel.setSucceed(false);
        } else {
            resultModel = lsdsGoodsTran.lsdsGoodsGetDetailsForDown(goods);
        }
        return resultModel;
    }

    /**
     * tcs服务调用货源，查找信息
     * 创建者：maxing
     * 创建时间：2023/8/10
     */
    @RequestMapping(value = "/getGoodsInfoByQuery", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> getGoodsInfoByQuery(@RequestParam("goodsIds") List<String> goodsIds) {

        return lsdsGoodsTran.getGoodsInfoByQuery(goodsIds);
    }

    /**
     * 查询货源概况信息
     * 创建者：cgb
     * 创建时间：2019/11/27
     *
     * @return {@code ResultMode<Map<String,Object>>} 货源概况信息
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     */
    @RequestMapping(value = "/lsdsGoodsOverview", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<Map<String, Object>> lsdsGoodsOverview(@RequestParam("goodsId") String goodsId) {
        return lsdsGoodsTran.lsdsGoodsOverview(goodsId);
    }

    /**
     * 进行下一轮报价
     * 创建者：cgb
     * 创建时间：2019/11/27
     *
     * @param model 货源编号  货源信息表实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【报价成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【报价成功】编码,ResultMode.errMsg为相应【报价成功】描述；
     * 否则ResultMode.succeed=false【报价失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【报价失败】编码,ResultMode.errMsg为相应【报价失败】描述。
     */
    @RequestMapping(value = "/offerToNetRound", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> offerToNetRound(@RequestBody LsdsGoodsVo model) {
        ResultMode<String> resultModel = new ResultMode<String>();
        if (StringUtils.isEmpty(model.getLsdsGoods().getGoodsId())) {
            resultModel.setErrMsg("goodsId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isEmpty(model.getLsdsGoods().getOfferCurrentRounds())) {
            resultModel.setErrMsg("当前报价轮次:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (CollectionUtils.isEmpty(model.getAssignSupplierList()) && CollectionUtils.isEmpty(model.getGoodsNextRoundOfferList())) {
            resultModel.setErrMsg("当前轮次没有承运商报价，不允许进入下一轮报价！");
            resultModel.setSucceed(false);
        }

        if (resultModel.getSucceed()) {
            resultModel = lsdsGoodsTran.offerToNetRound(model, "");
        }

        return resultModel;
    }


    /**
     * 关闭货源
     * 创建者：cgb
     * 创建时间：2019/12/3
     *
     * @param goodsId 货源ID
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【关闭成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【关闭成功】编码,ResultMode.errMsg为相应【关闭成功】描述；
     * 否则ResultMode.succeed=false【关闭失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【关闭失败】编码,ResultMode.errMsg为相应【关闭失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsClose", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<?> lsdsGoodsClose(@RequestParam("goodsId") String goodsId) {
//        return lsdsGoodsTran.lsdsGoodsClose(goodsId);
        return goodsSourceAppService.closeGoods(goodsId);
    }


    /**
     * 下单成功后 更新报价状态与货源交易状态
     * 创建者: cgb
     * 创建时间: 2019/11/30
     *
     * @param model 货源VO
     * @returns ResultMode<String>
     */
    @RequestMapping(value = "/lsdsGoodsUpdateAfterOrder", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsUpdateAfterOrder(@RequestBody LsdsGoodsVo model) {
        ResultMode<String> resultModel = new ResultMode<String>();
        if (StringUtils.isEmpty(model.getLsdsGoods().getGoodsId())) {
            resultModel.setErrMsg("goodsId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (null == model.getOfferOrderList() || model.getOfferOrderList().size() == 0) {
            resultModel.setErrMsg("offerOrderList:不能为空");
            resultModel.setSucceed(false);
        }
        if (resultModel.getSucceed()) {
            resultModel = lsdsGoodsTran.lsdsGoodsUpdateAfterOrder(model, "");
        }

        return resultModel;
    }


    /**
     * 货源详情页面报价列表
     * 创建者: cgb
     * 创建时间: 2019/12/13
     *
     * @param pageInfo
     * @returns ResultMode<Map < String, Object>>
     */
    @RequestMapping(value = "/getOfferMapListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<Map<String, Object>> getOfferMapListPage(@RequestBody PagingInfo<LsdsGoodsFilter> pageInfo) {
        ResultMode<Map<String, Object>> returnModel = new ResultMode<Map<String, Object>>();
        if (null != pageInfo) {
            if (!StringUtils.isEmpty(pageInfo.filterModel)) {
                if (StringUtils.isEmpty(pageInfo.filterModel.getGoodsId())) {
                    returnModel.setErrMsg("goodsId: 不能为空字符");
                    returnModel.setSucceed(false);
                }
                if (StringUtils.isEmpty(pageInfo.filterModel.getOfferRound())) {
                    returnModel.setErrMsg("offerRound: 不能为空字符");
                    returnModel.setSucceed(false);
                }
            }
        } else {
            returnModel.setSucceed(false);
        }
        if (!returnModel.getSucceed()) {
            returnModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            returnModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
        } else {
            returnModel = lsdsGoodsTran.getOfferMapListPage(pageInfo);
        }
        return returnModel;
    }


    /**
     * 报价运输方案
     *
     * @param offerId
     * @return {@code  ResultMode<LsdsGoodsPlan>}
     * <AUTHOR>
     * 创建时间 2019/12/2
     */
    @RequestMapping(value = "/getPlanListByOfferId", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsPlan> getPlanListByOfferId(@RequestParam String offerId) {
        ResultMode<LsdsGoodsPlan> resultModel = new ResultMode<LsdsGoodsPlan>();
        if (StringUtils.isEmpty(offerId)) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg("offerId:不能为空字符");
            resultModel.setSucceed(false);
        } else {
            resultModel = lsdsGoodsTran.getPlanListByOfferId(offerId);
        }
        return resultModel;
    }


    /**
     * 根据报价offerId查询报价信息
     *
     * @param offerIdArr
     * @return {@code  ResultMode<LsdsGoodsOffer>}
     * <AUTHOR>
     * 创建时间 2019/12/2
     */
    @RequestMapping(value = "/getOfferListById", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsOffer> getOfferListById(@RequestBody String[] offerIdArr) {
        ResultMode<LsdsGoodsOffer> resultModel = new ResultMode<LsdsGoodsOffer>();
        if (null == offerIdArr || offerIdArr.length == 0) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg("请至少传入一个offerId");
            resultModel.setSucceed(false);
        } else {
            resultModel = lsdsGoodsTran.getOfferListById(offerIdArr);
        }
        return resultModel;
    }


    /**
     * 首页货源大厅（展示5条发布中货源信息：公开询价的货源）
     *
     * @return {@code  ResultMode<LsdsGoods>}
     * <AUTHOR>
     * 创建时间 2019/12/21
     */
    @RequestMapping(value = "/getGoodsListAuditedForHomePage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> getGoodsListAuditedForHomePage() {
        ResultMode<LsdsGoods> returnModel = new ResultMode<LsdsGoods>();
        returnModel = lsdsGoodsTran.getGoodsListAuditedForHomePage();
        return returnModel;
    }


    /**
     * 首页货源大厅（展示5条已成交货源信息）
     *
     * @return {@code  ResultMode<LsdsGoods>}
     * <AUTHOR>
     * 创建时间 2019/12/21
     */
    @RequestMapping(value = "/getGoodsListFinishedForHomePage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> getGoodsListFinishedForHomePage() {
        ResultMode<LsdsGoods> returnModel = new ResultMode<LsdsGoods>();
        returnModel = lsdsGoodsTran.getGoodsListFinishedForHomePage();
        return returnModel;
    }

    /**
     * 导出我要发货-货源列表信息
     *
     * @param filter 查询条件
     * <AUTHOR>
     * 创建时间 2020/6/10
     */
    @RequestMapping(value = "/exportLsdsGoodsList", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> exportLsdsGoodsList(@RequestBody LsdsGoodsFilter filter) throws IOException {

        ResultMode<String> returnModel = new ResultMode<String>();
        if (null != filter) {
            if (StringUtils.isEmpty(filter.getCompanyId())) {
                returnModel.setErrMsg("companyId: 不能为空字符");
                returnModel.setSucceed(false);
            }
        } else {
            returnModel.setSucceed(false);
        }
        if (!returnModel.getSucceed()) {
            return returnModel;
        } else {
            List<LsdsGoods> goodsList = lsdsGoodsTran.getExportGoodsList(filter);
            InputStream is = null;
            ClassPathResource resource = null;
            try {
                HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
                HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getResponse();
                resource = new ClassPathResource("verifyxml/ExportLsdsGoodsMode.xml");
                is = resource.getInputStream();
                String fileName = StrUtil.equals(filter.getGoodsSourceType(), GoodsSourceTypeEnum.BID_OPENING.getCode()) ? "招标管理" : "货源管理";
                ExcelUtil.exportDataToExcel(request, response, fileName, is, goodsList, fileName, JwtUtil.getInstance().getUsernameByToken() + "(" + JwtUtil.getInstance().getLoginNameByToken() + ")");
            } catch (Exception e) {
                LogHelper.writeError("导出货源管理信息异常：", e);
            } finally {
                if (null != is) {
                    is.close();
                }
            }
        }
        return returnModel;
    }

    /**
     * 返回首页货源大厅、最新车源、最新货源信息
     *
     * <AUTHOR>
     * 创建时间 2020/7/8
     */
    @RequestMapping(value = "/getGoodsCarInfoForHomeList", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsVo> getGoodsCarInfoForHomeList() {
        ResultMode<LsdsGoodsVo> returnModel = new ResultMode<LsdsGoodsVo>();
        returnModel = lsdsGoodsTran.getGoodsCarInfoForHomeList();
        return returnModel;
    }

    @GetMapping(value = "/getGoodsListByGoodIds")
    public ResultMode<LsdsGoods> getGoodsListByGoodIds(@RequestBody PlatformQrCodeFilter platformQrCodeFilter) {
        ResultMode<LsdsGoods> resultMode = new ResultMode<>();
        try {
            resultMode = lsdsGoodsTran.getGoodsListByGoodIds(platformQrCodeFilter);
        } catch (Exception e) {
            resultMode.setSucceed(false);
            resultMode.setErrMsg(e.getMessage());
            e.printStackTrace();
        }
        return resultMode;
    }


    @GetMapping(value = "/getGoodsBasePriceById")
    public ResultMode<LsdsGoods> getGoodsBasePriceById(@RequestParam String goodsId) {
        log.info("接受查询的货源ID{}", goodsId);
        return lsdsGoodsTran.getGoodsBasePriceById(goodsId);
    }

    @RequestMapping(value = "/lsdsGoodsInfo", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> lsdsGoodsInfo(@RequestParam(required = false) String goodsId) {
        ResultMode<LsdsGoods> resultModel = new ResultMode<LsdsGoods>();
        if (StringUtils.isEmpty(goodsId)) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg("goodsId:不能为空字符！");
            resultModel.setSucceed(false);
        } else {
            resultModel = lsdsGoodsTran.lsdsGoodsInfo(goodsId);
        }
        return resultModel;
    }


    @RequestMapping(value = "/getGoodsInfo", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> getGoodsInfo(@RequestBody PlatformQrCodeFilter platformQrCodeFilter) {
        ResultMode<LsdsGoods> resultModel = new ResultMode<LsdsGoods>();
        if (StringUtils.isEmpty(platformQrCodeFilter.getGoodsId())) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg("goodsId:不能为空字符！");
            resultModel.setSucceed(false);
        } else {
            resultModel = lsdsGoodsTran.lsdsGoodsInfo(platformQrCodeFilter.getGoodsId());
        }
        return resultModel;
    }


    @GetMapping(value = "/getGoodsInfoContainAddress")
    public ResultMode<LsdsGoods> getGoodsInfoContainAddress(@RequestParam("goodsId") String goodsId) {
        try {
            log.info("货源goodsId:{},查询货源信息包含货源地址信息", goodsId);
            return lsdsGoodsTran.getGoodsInfoContainAddress(goodsId);
        } catch (Exception e) {
            log.error("货源goodsId:{},查询货源信息包含货源地址信息异常", goodsId, e);
            return null;
        }
    }

    /**
     * 统计常用地址已产生的货源数
     *
     * @param lineId 地址id
     */
    @RequestMapping(value = "/countAddressInUse", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<Integer> countAddressInUse(@RequestParam("lineId")String lineId) {
        return lsdsGoodsTran.countAddressInUse(lineId);
    }

    /**
     * 统计常用路线已产生的货源数
     *
     * @param lineIds 路线id
     */
    @RequestMapping(value = "/countLineInUse", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<Integer> countLineInUse(@RequestBody List<String> lineIds) {
        return lsdsGoodsTran.countLineInUse(lineIds);
    }

    @RequestMapping(value = "/getLsdsGoodsListByGoodsIdList", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoods> getLsdsGoodsListByGoodsIdList(@RequestParam("goodsIds")List<String> goodsIds) {
        return lsdsGoodsTran.getLsdsGoodsListByGoodsIdList(goodsIds);
    }

    @PostMapping(value = "/bidOpening")
    public ResultMode bidOpening(@RequestBody @Validated BidOpeningCommand command) {
        return lsdsGoodsBidOpeningService.bidOpening(command);
    }

    @PostMapping(value = "/queryGoodsBidOpening")
    public ResultMode<LsdsGoodsBidOpeningDTO> queryGoodsBidOpening(@RequestBody @Validated BidOpeningFilter filter) {
        LsdsGoodsBidOpening condition = BeanUtil.toBean(filter, LsdsGoodsBidOpening.class);
        List<LsdsGoodsBidOpening> list = lsdsGoodsBidOpeningService.queryCondition(condition);
        if (IterUtil.isEmpty(list)) {
            return ResultMode.success();
        }

        return ResultMode.successList(BeanUtil.copyToList(list, LsdsGoodsBidOpeningDTO.class));
    }

    /**
     * 生成中标文件
     * @return
     */
    @PostMapping(value = "/createWinBidFile")
    public ResultMode createWinBidFile(@RequestBody @Validated CreateWinBidFileFilter filter) {
        lsdsGoodsBidFileService.createWinBidFile(filter);
        return ResultMode.success();
    }

    @PostMapping("/platformStatistics")
    public ResultMode<GoodsStatisticsDTO> platformStatistics(@RequestBody LsdsGoodsFilter filter) {
        GoodsStatisticsDTO result = lsdsGoodsTran.platformStatistics(filter);
        return ResultMode.success(result);
    }

    public void syncGoodsSourceData(String goodsId,LsdsGoodsDeductible goodsDeductible){
        GoodsSourceSyncDataCommand command = new GoodsSourceSyncDataCommand();
        command.setGoodId(goodsId);
        command.setPublisherType(GoodsPublishTypeEnum.COMPANY.getType());
        command.setGoodsDeductible(goodsDeductible);
        asyncContextAwareExecutor.execute(() -> eventPublisher.publishGoodsSourceSyncDataEvent(GoodsSourceSyncDataEvent.of(command)));
    }

}

