package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.lsds.application.model.command.GoodsSourceSyncDataCommand;
import com.wanlianyida.lsds.application.service.GoodsSourceSyncDataAppService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 货源数据同步
 */
@Slf4j
@Api("货源数据同步")
@RestController
@RequestMapping("/goodsSourceSync")
public class GoodsSourceSyncController {


    @Resource
    private GoodsSourceSyncDataAppService goodsSourceSyncDataAppService;

    @PostMapping("/syncData")
    public ResultMode<?> syncData(@RequestBody @Validated GoodsSourceSyncDataCommand command) {
        String goodIds = command.getGoodId();
        List<String> goodIdList = StrUtil.split(goodIds, ",");
        //组装参数
        List<GoodsSourceSyncDataCommand> commandList = goodIdList.stream().map(goodId -> {
            GoodsSourceSyncDataCommand command1 = new GoodsSourceSyncDataCommand();
            command1.setGoodId(goodId);
            command1.setPublisherType(command.getPublisherType());
            return command1;
        }).collect(Collectors.toList());
        goodsSourceSyncDataAppService.batchSyncData(commandList);
        return ResultMode.success();
    }

    /**
     * 企业货源历史数据同步
     */
    @PostMapping("/syncRFQGoodsHistoryData")
    public void syncRFQGoodsHistoryData(@RequestParam(value = "startDate") String startDate, @RequestParam(value = "endDate") String endDate) {
        goodsSourceSyncDataAppService.syncRFQGoodsHistoryData(startDate,endDate);
    }


    /**
     * 司机货源历史数据同步
     */
    @PostMapping("/syncDFQGoodsHistoryData")
    public void syncDFQGoodsHistoryData(@RequestParam(value = "startDate") String startDate, @RequestParam(value = "endDate") String endDate) {
        goodsSourceSyncDataAppService.syncDFQGoodsHistoryData(startDate,endDate);
    }

}
