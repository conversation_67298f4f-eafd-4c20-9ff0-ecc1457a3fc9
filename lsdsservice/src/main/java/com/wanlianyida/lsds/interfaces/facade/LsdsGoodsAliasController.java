package com.wanlianyida.lsds.interfaces.facade;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.command.GoodsAliasCommand;
import com.isoftstone.hig.lsds.api.command.IdCommand;
import com.isoftstone.hig.lsds.api.dto.LsdsGoodsAliasDTO;
import com.wanlianyida.lsds.application.service.LsdsGoodsAliasAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024年11月18日 20:06
 */
@RequestMapping("/goods/alias")
@RestController
public class LsdsGoodsAliasController   {

    @Resource
    private LsdsGoodsAliasAppService lsdsGoodsAliasService;

    @PostMapping("/add")
    public ResultMode add(@RequestBody @Validated  GoodsAliasCommand command) {
        lsdsGoodsAliasService.add(command);
        return ResultMode.success();
    }

    @PostMapping("/queryList")
    public ResultMode queryList() {
        List<LsdsGoodsAliasDTO> result = lsdsGoodsAliasService.queryList();
        return ResultMode.successList(result);
    }

    @PostMapping("/delete")
    public ResultMode delete(@RequestBody @Validated IdCommand command) {
        lsdsGoodsAliasService.delete(command);
        return ResultMode.success();
    }
}
