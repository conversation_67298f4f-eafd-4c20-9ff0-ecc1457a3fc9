package com.wanlianyida.lsds.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.entity.DriverGoods;
import com.isoftstone.hig.lsds.api.entity.DriverGoodsVo;
import com.isoftstone.hig.lsds.api.filter.DriverGoodsFilter;
import com.isoftstone.hig.tms.api.inter.TmsWaybillInter;
import com.isoftstone.hig.tms.api.query.TmsWaybillFilter;
import com.wanlianyida.lsds.application.service.DriverGoodsAppService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

import javax.annotation.Resource;

/**
 * 司机货源Controller
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@RequestMapping("/driverGoodsApp")
@RestController
@Slf4j
public class DriverGoodsAppController {
    @Resource
    private DriverGoodsAppService driverGoodsService;


    @Resource
    private TmsWaybillInter tmsWaybillInter;
    /**
     * 查询司机货源
     *
     * @param driverGoodsFilter 司机货源ID
     * @return 司机货源
     */
    @PostMapping("/viewGoods")
    public ResultMode<DriverGoodsVo> viewGoods(@RequestBody DriverGoodsFilter driverGoodsFilter)
    {
        return new ResultMode(driverGoodsService.viewGoods(driverGoodsFilter.getId()));
    }

    /**
     * 分页查询司机货源列表
     *
     * @param driverGoodsFilter 司机货源
     * @return 司机货源集合
     */
    @PostMapping(value = "/homeData")
    public ResultMode<DriverGoods> homeData(@RequestBody DriverGoodsFilter driverGoodsFilter)
    {
        ResultMode<DriverGoods> resultMode = new ResultMode<>();
        log.info("homeData-------1------入参{}", JSON.toJSONString(driverGoodsFilter));
        List<DriverGoods> driverGoods = driverGoodsService.homeData(driverGoodsFilter);

        log.info("homeData-------1------查询返回结果集{}", JSON.toJSONString(driverGoods));


        driverGoods.stream().forEach(e->{
            if (StringUtils.equals(e.getEnquiryType(),"10")){
                //查询运单
                TmsWaybillFilter filter =new TmsWaybillFilter();

                filter.setGoodsId(e.getGoodsId());
                if (!StringUtils.isEmpty(driverGoodsFilter.getWaybillId())){
                    filter.setWaybillId(driverGoodsFilter.getWaybillId());
                    BigDecimal prePay =  tmsWaybillInter.info(filter).getModel().get(0).getAdvancePayment();
                    e.setAdvancePayment(prePay);

                }

            }

        });


        log.info("homeData-------1------查询APP{}", JSON.toJSONString(driverGoods));

        resultMode.setModel(driverGoods);
        return resultMode;
    }
}
