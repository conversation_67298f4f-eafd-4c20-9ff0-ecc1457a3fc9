package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.NoRepeatSubmit;
import com.isoftstone.hig.common.utils.RedisUtil;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsOperationRecord;
import com.isoftstone.hig.lsds.api.filter.IdentifyCodeRelFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LongLinkReq;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsGoodsNameVo;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsIdentifyCodeRelVO;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.msg.api.entity.MsgInfo;
import com.isoftstone.hig.msg.api.inter.SystemMsgInter;
import com.wanlianyida.lsds.application.service.DriverGoodsAppService;
import com.wanlianyida.lsds.domain.service.LsdsIdentifyCodeRelDomainService;
import com.wanlianyida.lsds.domain.service.LsdsOperationRecordDomainService;
import com.wanlianyida.lsds.infrastructure.exception.LsdsStatusCodeEnum;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import com.wanlianyida.lsds.infrastructure.util.ShortUrlUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

/**
 * <p>
 * 操作记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/operationRecord")
public class LsdsOperationRecordController {
    @Resource
    private LsdsOperationRecordDomainService operationRecordService;

    @Resource
    private SystemMsgInter systemMsgInter;

    @Autowired
    private LsdsIdentifyCodeRelDomainService lsdsIdentifyCodeRelService;

    @Resource
    private ShortUrlUtils shortUrlUtils;

    @Resource
    private DriverGoodsAppService driverGoodsService;

    @Autowired
    private LsdsKafkaSender lsdsKafkaSender;

    /**
     * 查询操作记录
     *
     * @param pageInfo
     * @return {@link ResultMode}<{@link LsdsGoodsNameVo}>
     */
    @RequestMapping(value = "/getOperationRecordListPage", method = RequestMethod.POST)
    public ResultMode<LsdsOperationRecord> getOperationRecordListPage(@RequestBody PagingInfo<LsdsOperationRecord> pageInfo) {
        return operationRecordService.getOperationRecordListPage(pageInfo);
    }

    /**
     * 推荐货源下单/报价记录
     *
     * @param lsdsOperationRecord
     * @return
     */
    @NoRepeatSubmit
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResultMode save(@RequestBody LsdsOperationRecord lsdsOperationRecord) {
        String busId = lsdsOperationRecord.getBusId();
        Assert.notBlank(busId, "业务ID不能为空");
        String operationDesc = "司机推荐货源查看";
        String remark = "查看[司机货源]";
        operationRecordService.addOperationRecord(busId, LsdsEnum.OperationTypeEnum.DRIVER_LINE_SUPPLY.getCode(), operationDesc, remark);
        return ResultMode.success();
    }

    /**
     * 司机接单--身份异常验证点击确认操作记录
     *
     * @param lsdsOperationRecord
     * @return
     */
    @RequestMapping(value = "/saveDriverConfirmRecord", method = RequestMethod.POST)
    public ResultMode saveDriverConfirmRecord(@RequestBody LsdsOperationRecord lsdsOperationRecord) {
        String busId = lsdsOperationRecord.getBusId();
        Assert.notBlank(busId, "业务ID不能为空");
        String operationDesc = "司机接单收款人提醒";
        //承运司机[”姓名”]和收款人[”姓名”]不一致
        String remark = "";
        if (!StrUtil.equals(lsdsOperationRecord.getDriverName(), lsdsOperationRecord.getPayeeName())) {
            remark = StrUtil.format("承运司机[{}]和收款人[{}]不一致", lsdsOperationRecord.getDriverName(), lsdsOperationRecord.getPayeeName());
        } else {
            remark = StrUtil.format("承运司机:{},收款人:{}", lsdsOperationRecord.getDriverName(), lsdsOperationRecord.getPayeeName());
        }
        operationRecordService.addOperationRecord(busId, LsdsEnum.OperationTypeEnum.PAYEE_REMIND_OPER_TYPE.getCode(), operationDesc, remark);
        return ResultMode.success();
    }


    /**
     * 通过短链接接单/报价后，储存通过短链接接单/报价记录
     *
     * @param lsdsOperationRecord
     * @return
     */
    @RequestMapping(value = "/saveShortURLReciveOrOfferData", method = RequestMethod.POST)
    public ResultMode saveShortURLReciveOrOfferData(@RequestBody LsdsOperationRecord lsdsOperationRecord) {
        String busId = lsdsOperationRecord.getBusId();
        Assert.notBlank(busId, "业务ID不能为空");
        String operationDesc = "货源推荐下单";
        String remark = "成功通过【货源短链接】完成接单，货源编号为:" + busId;
        operationRecordService.addOperationRecord(busId, LsdsEnum.OperationTypeEnum.RECEIVE_OFFER_TYPE.getCode(), operationDesc, remark);
        return ResultMode.success();
    }

    /**
     * 打开推荐运力页面
     *
     * @param lsdsOperationRecord
     * @return
     */
    @RequestMapping(value = "/openHotCarPage", method = RequestMethod.POST)
    public ResultMode openHotCarPage(@RequestBody LsdsOperationRecord lsdsOperationRecord) {
        String busId = lsdsOperationRecord.getBusId();
        Assert.notBlank(busId, "货源ID不能为空");
        // 记录打开推荐运力页面记录
        String operationDesc = "打开推荐运力页面";
        String remark = StrUtil.format("打开推荐运力页面，货源编号为{}。", busId);
        operationRecordService.addOperationRecord(busId, LsdsEnum.OperationTypeEnum.OPEN_HOT_CAR_PAGE.getCode(), operationDesc, remark);
        return ResultMode.success();
    }

    /**
     * 发送货源营销短链接
     *
     * @param lsdsOperationRecord
     * @return
     */
    @RequestMapping(value = "/sendGoodsShortLink", method = RequestMethod.POST)
    public ResultMode sendGoodsShortLink(@RequestBody LsdsOperationRecord lsdsOperationRecord) {

        String busId = lsdsOperationRecord.getBusId();
        String driverPhone = lsdsOperationRecord.getDriverPhone();
        String goodsLink = lsdsOperationRecord.getGoodsLink();
        Assert.notBlank(busId, "货源ID不能为空");
        Assert.notBlank(driverPhone, "手机号不能为空");

        //查询司机绑定二维码记录
        IdentifyCodeRelFilter identifyCodeRelFilter = new IdentifyCodeRelFilter();
        identifyCodeRelFilter.setGoodsId(busId);
        List<LsdsIdentifyCodeRelVO> lsdsIdentifyCodeRelVOS = lsdsIdentifyCodeRelService.queryBindIdentifyCodeRel(identifyCodeRelFilter);
        if (IterUtil.isEmpty(lsdsIdentifyCodeRelVOS)) {
            //解绑二维码
            return ResultMode.fail(LsdsStatusCodeEnum.BUSS_ERROR_DISABLE);
        }


        // 300s内不可以重复发送
        String cacheKey = Constants.SEND_GOODS_SHORT_LINK_CACHE_KEY + busId + "_" + driverPhone;
        String result = RedisUtil.get(cacheKey);
        if (StrUtil.isNotBlank(result)) {
            return ResultMode.fail(LsdsStatusCodeEnum.BUSS_ERROR_SEND300.getMsg(), LsdsStatusCodeEnum.BUSS_ERROR_DISABLE.getCode());
        }

        // 获取短链
        String shortUrlKey = shortUrlUtils.getShortUrlKey(goodsLink);
        if (StrUtil.isEmpty(shortUrlKey)) {
            return ResultMode.fail(LsdsStatusCodeEnum.BUSS_ERROR_RECEIVE500);
        }

        // 发送短信
        MsgInfo msgInfo = new MsgInfo();
        msgInfo.setTemplateId(UtilityEnum.SystemMsgTemplateEnum.GOODS_SHORT_LINK.getCode());
        msgInfo.setSender(JwtUtil.getInstance().getUserBaseIdByToken());
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", shortUrlKey);
        msgInfo.setTemplateParameter(jsonObject.toString());
        Set<String> phoneList = new HashSet<>();
        phoneList.add(driverPhone);
        msgInfo.setPhoneList(phoneList);
        msgInfo.setReceiver(driverPhone);
        log.info("发送货源营销短链接消息推送短信，msgInfo：{}", JSONUtil.toJsonStr(msgInfo));
        ResultMode<String> resultMode = systemMsgInter.send(msgInfo);
        if (ObjectUtil.isEmpty(resultMode) || !resultMode.getSucceed()) {
            log.error("发送货源营销短链接消息推送短信异常：", JSONUtil.toJsonStr(msgInfo));
        }

        RedisUtil.set(cacheKey, JSONUtil.toJsonStr(msgInfo), 300);

        // 存储推送记录
        String operationDesc = "发送货源营销短链接";
        String remark = StrUtil.format("成功通过【货源短链接】推送货源信息，货源编号为{}。", busId);
        operationRecordService.addOperationRecord(busId, LsdsEnum.OperationTypeEnum.SEND_GOODS_SHORT_LINK.getCode(), operationDesc, remark);
        return ResultMode.success();
    }


    /**
     * 获取短链接
     *
     * @param linkReq
     * @return
     */
    @RequestMapping(value = "/getShortUrl", method = RequestMethod.POST)
    public ResultMode getShortUrl(@RequestBody LongLinkReq linkReq) {
        String shortUrl = shortUrlUtils.getShortUrl(linkReq.getLongLink());
        if (StrUtil.isEmpty(shortUrl)) {
            return ResultMode.fail("短链接获取失败");
        }
        return ResultMode.success("suc", shortUrl);
    }

}
