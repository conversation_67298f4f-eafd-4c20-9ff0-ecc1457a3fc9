
package com.wanlianyida.lsds.interfaces.facade;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsAssignSupplier;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsAssignSupplierFilter;
import com.wanlianyida.lsds.application.service.LsdsGoodsAssignSupplierAppService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 创建描述：货物指定物流供应商记录ID 控制器 类的后面Controller其实可以不用，只是为了容易区分
 *
 * <AUTHOR>
 * 创建时间 2019/11/15 14:56:33
 */
@RequestMapping(value = "/LsdsGoodsAssignSupplier")
@RestController
public class LsdsGoodsAssignSupplierController   {
    @Resource
    private LsdsGoodsAssignSupplierAppService lsdsGoodsAssignSupplierBusiness;
    /**
     * 根据实体货物指定物流供应商记录ID 添加记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param model 货物指定物流供应商记录ID 实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【增加成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【增加成功】编码,ResultMode.errMsg为相应【增加成功】描述；
     * 否则ResultMode.succeed=false【增加失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【增加失败】编码,ResultMode.errMsg为相应【增加失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsAssignSupplierAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsAssignSupplierAdd(@RequestBody LsdsGoodsAssignSupplier model) {
        //try {

        ResultMode<String> resultModel = new ResultMode<String>();

        //#region 判断验证数据
        if (StringUtils.isNotBlank(model.getGoodsAssignSupplierId())) {
            resultModel.getModel().add("goodsAssignSupplierId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getGoodsId())) {
            resultModel.getModel().add("goodsId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCompanyId())) {
            resultModel.getModel().add("companyId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCompanyName())) {
            resultModel.getModel().add("companyName:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCompanyCode())) {
            resultModel.getModel().add("companyCode:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCompanyLinker())) {
            resultModel.getModel().add("companyLinker:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCompanyPhoneNumber())) {
            resultModel.getModel().add("companyPhoneNumber:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (model.getSortNode() != 0) {
            resultModel.getModel().add("sortNode:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCreateBy())) {
            resultModel.getModel().add("createBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getModifyBy())) {
            resultModel.getModel().add("modifyBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem1())) {
            resultModel.getModel().add("item1:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem2())) {
            resultModel.getModel().add("item2:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem3())) {
            resultModel.getModel().add("item3:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem4())) {
            resultModel.getModel().add("item4:" + "不能为空字符");
            resultModel.setSucceed(false);
        }


        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        }
        //#endregion


        resultModel = lsdsGoodsAssignSupplierBusiness.lsdsGoodsAssignSupplierAdd(model, "");
        return resultModel;

        // 要替换 return CommonParaBLL.getInstance().lsdsGoodsAssignSupplierAdd(model,"");
        //} catch (Exception ex) {
        //	LogHelper.writeError(ex.getMessage(), ex);
        //}
        //return false;
    }

    /**
     * 根据货物指定物流供应商记录ID 主键删除记录
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param goodsAssignSupplierId 货物指定物流供应商记录ID 主键
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【删除成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【删除成功】编码,ResultMode.errMsg为相应【删除成功】描述；
     * 否则ResultMode.succeed=false【删除失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【删除失败】编码,ResultMode.errMsg为相应【删除失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsAssignSupplierDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsAssignSupplierDel(@RequestParam String goodsAssignSupplierId) {
        return lsdsGoodsAssignSupplierBusiness.lsdsGoodsAssignSupplierDel(goodsAssignSupplierId, "");

    }

    /**
     * 根据货物指定物流供应商记录ID 实体修改记录，内部以ID主键修改
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param model 货物指定物流供应商记录ID 实体类
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【修改成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【修改成功】编码,ResultMode.errMsg为相应【修改成功】描述；
     * 否则ResultMode.succeed=false【修改失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【修改失败】编码,ResultMode.errMsg为相应【修改失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsAssignSupplierUpdate", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsAssignSupplierUpdate(@RequestBody LsdsGoodsAssignSupplier model) {
        //try {
        ResultMode<String> resultModel = new ResultMode<String>();
        //#region 判断验证数据
        if (StringUtils.isNotBlank(model.getGoodsAssignSupplierId())) {
            resultModel.getModel().add("goodsAssignSupplierId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getGoodsId())) {
            resultModel.getModel().add("goodsId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCompanyId())) {
            resultModel.getModel().add("companyId:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCompanyName())) {
            resultModel.getModel().add("companyName:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCompanyCode())) {
            resultModel.getModel().add("companyCode:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCompanyLinker())) {
            resultModel.getModel().add("companyLinker:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCompanyPhoneNumber())) {
            resultModel.getModel().add("companyPhoneNumber:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (model.getSortNode() != 0) {
            resultModel.getModel().add("sortNode:" + "数据格式不能为零");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getCreateBy())) {
            resultModel.getModel().add("createBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getModifyBy())) {
            resultModel.getModel().add("modifyBy:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem1())) {
            resultModel.getModel().add("item1:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem2())) {
            resultModel.getModel().add("item2:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem3())) {
            resultModel.getModel().add("item3:" + "不能为空字符");
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(model.getItem4())) {
            resultModel.getModel().add("item4:" + "不能为空字符");
            resultModel.setSucceed(false);
        }


        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        }


        resultModel = lsdsGoodsAssignSupplierBusiness.lsdsGoodsAssignSupplierUpdate(model, "");
        return resultModel;
    }

    /**
     * 货物指定物流供应商记录ID 分页查询
     * 创建者：李健华
     * 创建时间：2019/11/15 14:56:33
     *
     * @param pageInfo 分页获取实体对象
     * @return {@code  ResultMode<LsdsGoodsAssignSupplier>}
     * 成功：只要正常执行，无服务器异常，查询记录结果不为空/0条记录，视为查询成功。
     * 失败：服务器异常，内部异常，执行sql查询语法错误等视为失败。
     * 如果ResultMode.succeed=true 【查询成功】，返回的ResultMode.model为货物指定物流供应商记录ID 实体类LsdsGoodsAssignSupplier列表数据,ResultMode.errCode为相应【查询成功】编码,ResultMode.errMsg为相应【查询成功】描述,
     * ResultMode.total为本次查询的总记录数据，当分页查询时指定了要返回页面此值为查询的总记录数据不一定与返回的记录数相等，否则与当前返回记录数据相等。；
     * 否则ResultMode.succeed=false【查询失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【查询失败】编码,ResultMode.errMsg为相应【查询失败】描述。
     */
    @RequestMapping(value = "/lsdsGoodsAssignSupplierPaging", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsAssignSupplier> lsdsGoodsAssignSupplierPaging(@RequestBody PagingInfo<LsdsGoodsAssignSupplierFilter> pageInfo) {
        ResultMode<LsdsGoodsAssignSupplier> resultModel = new ResultMode<LsdsGoodsAssignSupplier>();
        StringBuffer errsb = new StringBuffer();

        //#region 判断验证数据
        if (StringUtils.isNotBlank(pageInfo.filterModel.getGoodsAssignSupplierId())) {
            errsb.append(String.format(" %s 不能为空字符", "goodsAssignSupplierId"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getGoodsId())) {
            errsb.append(String.format(" %s 不能为空字符", "goodsId"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getCompanyId())) {
            errsb.append(String.format(" %s 不能为空字符", "companyId"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getCompanyName())) {
            errsb.append(String.format(" %s 不能为空字符", "companyName"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getCompanyCode())) {
            errsb.append(String.format(" %s 不能为空字符", "companyCode"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getCompanyLinker())) {
            errsb.append(String.format(" %s 不能为空字符", "companyLinker"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getCompanyPhoneNumber())) {
            errsb.append(String.format(" %s 不能为空字符", "companyPhoneNumber"));
            resultModel.setSucceed(false);
        }
        if (pageInfo.filterModel.getSortNode() != 0) {
            errsb.append(String.format(" %s 数据格式不能为零", "sortNode"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getCreateBy())) {
            errsb.append(String.format(" %s 不能为空字符", "createBy"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getModifyBy())) {
            errsb.append(String.format(" %s 不能为空字符", "modifyBy"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getItem1())) {
            errsb.append(String.format(" %s 不能为空字符", "item1"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getItem2())) {
            errsb.append(String.format(" %s 不能为空字符", "item2"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getItem3())) {
            errsb.append(String.format(" %s 不能为空字符", "item3"));
            resultModel.setSucceed(false);
        }
        if (StringUtils.isNotBlank(pageInfo.filterModel.getItem4())) {
            errsb.append(String.format(" %s 不能为空字符", "item4"));
            resultModel.setSucceed(false);
        }


        if (!resultModel.getSucceed()) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getMsg());
            resultModel.setSucceed(false);
        }


        resultModel = lsdsGoodsAssignSupplierBusiness.lsdsGoodsAssignSupplierPaging(pageInfo, "");

        return resultModel;

    }

    /**
     * 查询货源承运企业
     * @param pageInfo
     * @return
     */
    @RequestMapping(value = "/getLsdsGoodsAssignSupplier", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsAssignSupplier> getLsdsGoodsAssignSupplier(@RequestBody PagingInfo<LsdsGoodsAssignSupplierFilter> pageInfo) {
        return lsdsGoodsAssignSupplierBusiness.getLsdsGoodsAssignSupplier(pageInfo);
    }


}
