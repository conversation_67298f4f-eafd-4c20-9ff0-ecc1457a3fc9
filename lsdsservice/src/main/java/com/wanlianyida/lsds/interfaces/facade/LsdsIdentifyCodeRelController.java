package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.entity.LsdsIdentifyCodeRel;
import com.isoftstone.hig.lsds.api.filter.IdentifyCodeRelFilter;
import com.isoftstone.hig.lsds.api.mvcvo.LsdsIdentifyCodeRelVO;
import com.isoftstone.hig.lsds.api.util.ValidatedGroup;
import com.wanlianyida.lsds.domain.service.LsdsIdentifyCodeRelDomainService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 司机货源识别码关系 Controller
 *
 * <AUTHOR>
 * @date 2024-02-07
 */
@RequestMapping("/identifyCodeRel")
@RestController
public class LsdsIdentifyCodeRelController   {

	@Resource
    private LsdsIdentifyCodeRelDomainService lsdsidentifycoderelService;

	/**
	 * 列表查询
	 * @param filter 查询参数
	 * @return {@link ResultMode}<{@link LsdsIdentifyCodeRel}>
	 */
    @PostMapping("/bindIdentifyCodeRel")
	public ResultMode<LsdsIdentifyCodeRelVO> queryBindIdentifyCodeRel(@RequestBody @Validated IdentifyCodeRelFilter filter) {
        return ResultMode.successList(lsdsidentifycoderelService.queryBindIdentifyCodeRel(filter));
	}

    @PostMapping("/modify")
    public ResultMode<Boolean> updateById(@RequestBody @Validated({ValidatedGroup.Update.class})  IdentifyCodeRelFilter filter) {
        LsdsIdentifyCodeRel lsdsIdentifyCodeRel = BeanUtil.copyProperties(filter, LsdsIdentifyCodeRel.class);
        lsdsidentifycoderelService.updateByPrimaryKeySelective(lsdsIdentifyCodeRel);
        return ResultMode.success(Boolean.TRUE);
    }

}
