package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.command.ReceiptPayCheckCommand;
import com.isoftstone.hig.lsds.api.mvcvo.PrepayReqVO;
import com.isoftstone.hig.lsds.api.mvcvo.PrepayResVO;
import com.wanlianyida.lsds.application.service.PrepayAppService;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 预付费接口
 */
@RequestMapping("/prepayment")
@RestController
@Slf4j
public class PrepayController {


    @Autowired
    private PrepayAppService prepayService;

    @RequestMapping(value = "/prepayCheck", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<PrepayResVO> prepayCheck(@RequestBody @ApiParam PrepayReqVO prepayReqVO) {
        if (prepayReqVO != null) {
            log.info("请求参数：{}", JSONUtil.toJsonStr(prepayReqVO));
        }


        return prepayService.prepayCheck(prepayReqVO);

    }

    @RequestMapping(value = "/receiptPayCheck", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<PrepayResVO> receiptPayCheck(@RequestBody @Validated ReceiptPayCheckCommand command) {
        return prepayService.receiptPayCheck(command);
    }

}
