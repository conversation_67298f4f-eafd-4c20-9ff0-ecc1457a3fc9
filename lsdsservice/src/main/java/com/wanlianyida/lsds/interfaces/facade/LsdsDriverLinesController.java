package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.UtilityClass;
import com.isoftstone.hig.lsds.api.entity.LsdsCommonLines;
import com.isoftstone.hig.lsds.api.entity.LsdsDriverLines;
import com.wanlianyida.lsds.domain.service.LsdsCommonLinesDomainService;
import com.wanlianyida.lsds.domain.service.LsdsDriverLinesDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

import javax.annotation.Resource;

/**
 * 司机常跑路线关系表(LsdsDriverLines)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-23 15:49:26
 */
@RequestMapping("/driverLines")
@Slf4j
@RestController
public class LsdsDriverLinesController   {

    @Resource
    private LsdsDriverLinesDomainService lsdsDriverLinesService;

    @Resource
    private LsdsCommonLinesDomainService lsdsCommonLinesService;

    /**
     * APP司机端：装货/提柜异步触发，添加常跑路线
     *
     * @param lsdsDriverLines
     * @return 结果
     */
    @PostMapping(value = "/save")
    public ResultMode save(@RequestBody  LsdsDriverLines lsdsDriverLines) {
        log.info("APP司机端：装货/提柜异步触发，添加常跑路线入参：{}", JSON.toJSONString(lsdsDriverLines));
        // 添加公共常跑路线
        Date date = new Date();
        LsdsCommonLines lsdsCommonLinesFilter = BeanUtil.copyProperties(lsdsDriverLines, LsdsCommonLines.class);
        LsdsCommonLines lsdsCommonLines = lsdsCommonLinesService.queryOneByCity(lsdsCommonLinesFilter);
        if (null == lsdsCommonLines) {
            lsdsCommonLines = lsdsCommonLinesFilter;
            lsdsCommonLines.setCommonLinesId(UtilityClass.uuid());
            lsdsCommonLines.setCreateDate(date);
            lsdsCommonLines.setModifyDate(date);
            lsdsCommonLinesService.insert(lsdsCommonLines);
        }
        // 添加司机常跑路线 更新路线”更新时间”；
        lsdsDriverLines.setCommonLinesId(lsdsCommonLines.getCommonLinesId());
        LsdsDriverLines driverLines = lsdsDriverLinesService.queryOneByCommonLinesIdAndDriverId(lsdsDriverLines);
        log.info("司机长跑路线driverLines->{}", driverLines);
        if (driverLines == null) {
            driverLines = lsdsDriverLines;
            driverLines.setDriverLinesId(UtilityClass.uuid());
            driverLines.setCreateDate(date);
            driverLines.setModifyDate(date);
            driverLines.setBusinessNum(0);
            driverLines.setLineSource("10");
            driverLines.setDeleteFlag("0");
            log.info("司机长跑路线添加driverLines->{}", driverLines);
            lsdsDriverLinesService.insert(driverLines);
        } else {
            driverLines.setModifyDate(date);
            driverLines.setCarId(lsdsDriverLines.getCarId());
            driverLines.setPlateNumber(lsdsDriverLines.getPlateNumber());
            driverLines.setCarColor(lsdsDriverLines.getCarColor());
            log.info("司机长跑路线修改driverLines->{}", driverLines);
            lsdsDriverLinesService.update(driverLines);
        }
        return ResultMode.success();
    }

    /**
     * APP司机端：签收/还柜异步触发：作业数量的累加
     *
     * @param lsdsDriverLines
     * @return 结果
     */
    @PostMapping(value = "/updateByDriverIdAndCityCode")
    public ResultMode updateByDriverIdAndCityCode(@RequestBody LsdsDriverLines lsdsDriverLines) {
        log.info("APP司机端：签收/还柜异步触发：作业数量的累加入参：{}", JSON.toJSONString(lsdsDriverLines));
        LsdsCommonLines lsdsCommonLines = BeanUtil.copyProperties(lsdsDriverLines, LsdsCommonLines.class);
        lsdsCommonLines = lsdsCommonLinesService.queryOneByCity(lsdsCommonLines);
        Assert.notNull(lsdsCommonLines, "常跑路线不存在");
        lsdsDriverLines.setCommonLinesId(lsdsCommonLines.getCommonLinesId());
        LsdsDriverLines driverLines = lsdsDriverLinesService.queryOneByCommonLinesIdAndDriverId(lsdsDriverLines);
        Assert.notNull(driverLines, "司机常跑路线不存在");
        // 只需要修改作业数量，不需要修改”更新时间”
        driverLines.setBusinessNum(driverLines.getBusinessNum() + 1);
        lsdsDriverLinesService.update(driverLines);
        return ResultMode.success();
    }

}

