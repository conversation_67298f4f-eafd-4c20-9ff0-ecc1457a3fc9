package com.wanlianyida.lsds.interfaces.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.isoftstone.hig.annotations.LogPrintPoint;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.SpringContextUtil;
import com.isoftstone.hig.common.utils.UtilityEnum;
import com.isoftstone.hig.common.utils.distance.DistanceUtils;
import com.isoftstone.hig.common.utils.distance.LatLng;
import com.isoftstone.hig.common.utils.exception.CommonStatusCodeEnum;
import com.isoftstone.hig.common.utils.exception.WlydException;
import com.isoftstone.hig.common.utils.maputils.WlydMapUtil;
import com.isoftstone.hig.crm.api.entity.CrmCompanyLineAddress;
import com.isoftstone.hig.crm.api.entity.GeoGetLonLat;
import com.isoftstone.hig.crm.api.filter.CrmCompanyLineAddressFilter;
import com.isoftstone.hig.crm.api.inter.CrmCompanyLineAddressFeignInter;
import com.isoftstone.hig.crm.api.inter.CrmCompanyLineAddressInter;
import com.isoftstone.hig.eval.api.entity.EvalDriverInfo;
import com.isoftstone.hig.eval.api.inter.EvalDriverInfoInter;
import com.isoftstone.hig.lsds.api.command.LsdsMatchmakingConfigAddCommand;
import com.isoftstone.hig.lsds.api.dto.*;
import com.isoftstone.hig.lsds.api.entity.*;
import com.isoftstone.hig.lsds.api.enums.ConfigTypeEnum;
import com.isoftstone.hig.lsds.api.enums.GoodsKindEnum;
import com.isoftstone.hig.lsds.api.enums.TransportTypeEnum;
import com.isoftstone.hig.lsds.api.filter.DriverGoodsFilter;
import com.isoftstone.hig.lsds.api.filter.IdentifyCodeRelFilter;
import com.isoftstone.hig.lsds.api.mvcvo.*;
import com.isoftstone.hig.lsds.api.query.DriverGoodsExtendQuery;
import com.isoftstone.hig.lsds.api.query.LsdsMatchmakingConfigQuery;
import com.isoftstone.hig.lsds.api.util.Constants;
import com.isoftstone.hig.oms.api.entity.OmsOrder;
import com.isoftstone.hig.oms.api.entity.OmsOrderAddress;
import com.isoftstone.hig.oms.api.inter.OmsOrderAddressInter;
import com.isoftstone.hig.oms.api.inter.OmsOrderInter;
import com.isoftstone.hig.platform.api.entity.PlatformCmDictionary;
import com.isoftstone.hig.platform.api.entity.PlatformCmPlatformParameter;
import com.isoftstone.hig.platform.api.entity.PlatformUmUserbaseinfo;
import com.isoftstone.hig.platform.api.filter.PlatformQrCodeFilter;
import com.isoftstone.hig.platform.api.inter.PlatformCmDictionaryInter;
import com.isoftstone.hig.platform.api.inter.PlatformCmPlatformParameterInter;
import com.isoftstone.hig.platform.api.inter.PlatformUmUserbaseinfoInter;
import com.isoftstone.hig.platform.api.mvcvo.PlatformQrCodeRelationVO;
import com.isoftstone.hig.qrs.api.constants.QrsEnum;
import com.isoftstone.hig.tcs.api.filter.TcsCarPlateNoAndColorFilter;
import com.isoftstone.hig.tcs.api.mvcvo.TcsCarTractorVO;
import com.isoftstone.hig.tcs.api.mvcvo.TcsCarTrailerVO;
import com.isoftstone.hig.tcs.api.mvcvo.TcsDriverVO;
import com.isoftstone.hig.tms.api.common.valid.StringUtils;
import com.isoftstone.hig.tms.api.entity.TmsEnum;
import com.isoftstone.hig.tms.api.entity.TmsOrder;
import com.isoftstone.hig.tms.api.entity.TmsWaybill;
import com.isoftstone.hig.tms.api.inter.TmsWaybillInter;
import com.isoftstone.hig.tms.api.query.TmsOrderFilter;
import com.isoftstone.hig.tms.api.query.TmsWaybillFilter;
import com.isoftstone.hig.woa.api.vo.WoaLogisticsPlanVO;
import com.isoftstone.hig.woa.api.vo.WoaOfferPlanVO;
import com.isoftstone.hig.woa.api.vo.WoaWaybillClueCntVO;
import com.isoftstone.hig.woa.api.vo.WoaWaybillClueVO;
import com.isoftstone.hig.woa.filter.WoaWaybillClueFilter;
import com.wanlianyida.hig.rms.api.common.IdUtils;
import com.wanlianyida.hig.rms.api.enums.RmsEnum;
import com.wanlianyida.hig.rms.api.po.DriverGoodsPublishRuleParma;
import com.wanlianyida.hig.rms.api.po.platform.HazardousChemicalVO;
import com.wanlianyida.lsds.application.model.command.GoodsSourceSyncDataCommand;
import com.wanlianyida.lsds.application.service.*;
import com.wanlianyida.lsds.domain.service.*;
import com.wanlianyida.lsds.infrastructure.enums.*;
import com.wanlianyida.lsds.infrastructure.event.EventPublisher;
import com.wanlianyida.lsds.infrastructure.event.model.GoodsSourceSyncDataEvent;
import com.wanlianyida.lsds.infrastructure.exception.LsdsWlydException;
import com.wanlianyida.lsds.infrastructure.exchange.*;
import com.wanlianyida.lsds.infrastructure.exchange.pojo.CrmCompanyLineAddressBO;
import com.wanlianyida.lsds.infrastructure.repository.mapper.GoodsAddressMapper;
import com.wanlianyida.lsds.infrastructure.repository.mapper.GoodsMapper;
import com.wanlianyida.lsds.infrastructure.util.HttpClientUtil;
import com.wanlianyida.lsds.infrastructure.util.IdUtil;
import com.wanlianyida.lsds.infrastructure.util.LsdsKafkaSender;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jodd.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.isoftstone.hig.lsds.api.util.Constants.MATCHMAKING_GOODS_CHANGE_TOPIC;

/**
 * 司机货源Controller
 *
 * <AUTHOR>
 * @date 2021-05-28
 */
@RequestMapping("/driverGoods")
@RestController
@Slf4j
public class DriverGoodsController   {
    @Resource
    private DriverGoodsAppService driverGoodsService;

    @Resource
    private LsdsDriverLinesDomainService lsdsDriverLinesService;

    @Resource
    private PrepayAppService prepayService;

    @Resource
    private PlatformCmPlatformParameterInter platformCmPlatformParameterInter;

    @Resource
    private CrmCompanyLineAddressFeignInter crmCompanyLineAddressFeignInter;

    @Autowired
    private LsdsGoodsAppService lsdsGoodsTran;
    @Resource
    private CrmCompanyLineAddressInter crmCompanyLineAddressInter;


    @Resource
    private GoodsAddressMapper lsdsGoodsAddressRepository;

    @Lazy
    @Resource
    private OmsOrderInter omsOrderInter;

    @Lazy
    @Resource
    private TmsWaybillInter tmsWaybillInter;

    @Resource
    private WoaExchangeService woaExchangeService;
    @Resource
    private PlatformCmDictionaryInter platformCmDictionaryInter;
    @Resource
    private GoodsDeductibleAppService goodsDeductibleService;
    @Resource
    private AmountRoundingModeExchangeService amountRoundingModeService;
    @Autowired
    private EvalDriverInfoInter evalDriverInfoInter;

    @Resource
    private PlatformUmUserbaseinfoInter platformUmUserbaseinfoInter;

    @Autowired
    private LsdsGoodsAttentionAppService lsdsGoodsAttentionServiceTran;

    @Resource
    private CrmExchangeService lineAddressExchangeService;

    @Resource
    private LsdsIdentifyCodeRelDomainService lsdsIdentifyCodeRelService;

    @Resource
    private RmsExchangeService rmsExchangeService;

    @Resource
    private TmsExchangeService tmsExchangeService;

    @Resource
    private OmsOrderAddressInter omsOrderAddressInter;

    @Resource
    private DcsExchangeService dcsExchangeService;

    @Resource
    private LsdsMatchmakingDomainService lsdsMatchmakingService;

    @Resource
    private LsdsAppGoodsAppService lsdsAppGoodsService;

    @Resource
    private LsdsKafkaSender lsdsKafkaSender;
    @Resource
    private DriverGoodsExtendDomainService driverGoodsExtendBusiness;

    @Resource
    private LsdsCommonLinesDomainService lsdsCommonLinesService;

    @Resource
    private CrmExchangeService crmExchangeService;

    @Resource
    private TcsExchangeService tcsExchangeService;

    @Resource
    private PlatformExchangeService platformExchangeService;
    @Resource
    private LsdsGoodsAttentionAppService goodsAttentionServiceTran;
    @Resource
    private GoodsSourceAppService goodsSourceAppService;

    @Resource(name = "asyncContextAwareExecutor")
    private Executor asyncContextAwareExecutor;

    @Resource
    private EventPublisher eventPublisher;
    /**
     * 查询司机货源
     *
     * @param driverGoodsFilter 司机货源ID
     * @return 司机货源
     */
    @PostMapping("/getById")
    public ResultMode<DriverGoods> getById(@RequestBody DriverGoodsFilter driverGoodsFilter) {
        log.info(">>>>>getById方法，请求参数driverGoodsFilter:{}", JSONUtil.toJsonStr(driverGoodsFilter));
        try {
            DriverGoods driverGoods = null;
            if (driverGoodsFilter != null && org.apache.commons.lang.StringUtils.isNotEmpty(driverGoodsFilter.getId())) {
                log.info(">>>goodsId:{}", driverGoodsFilter.getId());
                driverGoods = getDriverGoodsInfo(driverGoodsFilter);

            }
            if (null == driverGoods) {
                log.info(">>>>>>>sdfsa>>");
                return ResultMode.success();
            }
            return ResultMode.success(driverGoods);
        } catch (Exception e) {
            log.error("查看货源发生异常！错误原因：{}", e.getMessage());
            e.printStackTrace();
        }
        return ResultMode.fail();
    }


    /**
     * @Description: 逻辑拆出，营销码当时有逻辑需求！
     * @Author: Mindy
     * @Date: 2023/4/26
     * @Param:
     * @return:
     */
    private DriverGoods getDriverGoodsInfo(@RequestBody DriverGoodsFilter driverGoodsFilter) {
        DriverGoods driverGoods;
        driverGoods = driverGoodsService.getById(driverGoodsFilter.getId());
        if (null != driverGoods) {
            String customerShortName = driverGoodsService.initCustomerShortName(driverGoods.getCustomerId());
            driverGoods.setCustomerShortName(customerShortName);
            String carAssignType = Optional.ofNullable(driverGoods.getAssignCarType()).orElse("");
            if (!"".equals(carAssignType)) {
                Map<String, String> m = new HashMap<>();
                m.put("dictionaryId", "5");
                m.put("name", "");
                ResultMode<PlatformCmDictionary> cmDictionaryResult = platformCmDictionaryInter.queryByDicAndName(m);
                log.info("cmDictionaryResult.......{}", JSON.toJSONString(cmDictionaryResult));
                if (cmDictionaryResult.getSucceed()) {
                    List<PlatformCmDictionary> cmDictionaryList = cmDictionaryResult.getModel();
                    for (PlatformCmDictionary p : cmDictionaryList) {
                        if (p.getEnumCode().equals(driverGoods.getAssignCarType())) {
                            driverGoods.setAssignCarName(p.getName());
                        }
                    }
                }
            }

            //NOTES 查询货源相关收款人二维码记录并返回司机app用于分享码的生成
            driverGoods = getGoodsQrInfo(driverGoods);
            log.info("查询货源相关收款人二维码记录: {}", JSONObject.toJSONString(driverGoods));

            // 展示亏涨吨参数配置
            LsdsGoodsDeductible lsdsGoodsDeductible = goodsDeductibleService.wrapGoodsDeductible(driverGoods.getOtherKuiTonsRatio(), driverGoods.getGoodsId());
            driverGoods.setLsdsGoodsDeductible(lsdsGoodsDeductible);

            //获取抹零方式
            driverGoods.setZeroRoundingMethod(dcsExchangeService.findZeroRoundingMethod(driverGoods.getGoodsId()));

            //查询撮合信息
            LsdsMatchmakingConfigQuery configQuery = new LsdsMatchmakingConfigQuery();
            configQuery.setBusId(driverGoods.getId());
            LsdsMatchmakingConfigDTO config = lsdsMatchmakingService.queryConfig(configQuery);
            if(ObjUtil.isNotNull(config)){
                driverGoods.setBargainName(config.getBargainName());
                driverGoods.setBargainMobile(config.getBargainMobile());
                driverGoods.setLoadName(config.getLoadName());
                driverGoods.setLoadMobile(config.getLoadMobile());
                driverGoods.setSettlementName(config.getSettlementName());
                driverGoods.setSettlementMobile(config.getSettlementMobile());
                driverGoods.setBargainConfig(config.getBargainConfig());
                driverGoods.setTrackService(config.getTrackService());
            }

            //查询货源扩展信息
            DriverGoodsExtendDTO goodsExtend = driverGoodsExtendBusiness.selectByGoodsId(driverGoods.getId());
            if(ObjUtil.isNotNull(goodsExtend)){
                driverGoods.setBatchPaymentSwitch(goodsExtend.getBatchPaymentSwitch());
                driverGoods.setSignPaymentSwitch(goodsExtend.getSignPaymentSwitch());
                driverGoods.setReceiptPaymentSwitch(goodsExtend.getReceiptPaymentSwitch());
                driverGoods.setReceiptAmount(goodsExtend.getReceiptAmount());
            }
            //填充经纬度
            CrmCompanyLineAddressBO startLine = lineAddressExchangeService.getCrmCompanyLine(driverGoods.getStartLineId());
            driverGoods.setStartLngLat(startLine.getItem1()+","+startLine.getItem2());
            CrmCompanyLineAddressBO endLine = lineAddressExchangeService.getCrmCompanyLine(driverGoods.getEndLineId());
            driverGoods.setEndLngLat(endLine.getItem1()+","+endLine.getItem2());
        }

        return driverGoods;
    }

    /**
     * @Description: 查询货源相关收款人二维码记录
     * @Author: Mindy
     * @Date: 2023/4/13
     * @Param:
     * @return:
     */
    private DriverGoods getGoodsQrInfo(DriverGoods driverGoods) {
        IdentifyCodeRelFilter filter = new IdentifyCodeRelFilter();
        filter.setGoodsId(driverGoods.getGoodsId());
//        filter.setIdentifyCodeType(QrsEnum.IdentifyCodeTypeEnum.COMPANY_RESERVE_CODE.getCode());
        List<LsdsIdentifyCodeRelVO> list = lsdsIdentifyCodeRelService.queryBindIdentifyCodeRel(filter);
        Map<String, List<LsdsIdentifyCodeRelVO>> relMap = list.stream()
            .collect(Collectors.groupingBy(LsdsIdentifyCodeRelVO::getIdentifyCodeType));
        driverGoods.setQrsCodeRelations(relMap.get(QrsEnum.IdentifyCodeTypeEnum.COMPANY_RESERVE_CODE.getCode()));
        LsdsIdentifyCodeRelVO identifyCodeRelVO = Opt.ofNullable(IterUtil.getFirst(relMap.get(QrsEnum.IdentifyCodeTypeEnum.COMPANY_GOODS_CODE.getCode())))
            .orElseGet(LsdsIdentifyCodeRelVO::new);
        driverGoods.setGoodsCodeNumber(identifyCodeRelVO.getIdentifyCode());
        return driverGoods;
    }

    /**
     * 查询司机货源列表
     *
     * @param driverGoodsFilter 司机货源
     * @return 司机货源集合
     */
    @PostMapping("/listByEntity")
    public ResultMode<DriverGoods> listByEntity(@RequestBody DriverGoodsFilter driverGoodsFilter)
    {
        ResultMode<DriverGoods> driverGoodsResultMode = new ResultMode<>();
        driverGoodsResultMode.setModel(driverGoodsService.listByEntity(driverGoodsFilter));
        return driverGoodsResultMode;

    }


    /**
     * 通过货源IDS查询绑定的货源列表
     *
     * @param pageInfo 货源IDS
     * @return {@code ResultMode<DriverGoods>}
     */
    @PostMapping("/getBindSupplyListByIds")
    public ResultMode<DriverGoods> getBindSupplyListByIds(@RequestBody @ApiParam PlatformQrCodeFilter pageInfo) {
        ResultMode<DriverGoods> resultMode = new ResultMode<>();
        try {
            log.info("货源IDS：{}", JSONObject.toJSONString(pageInfo));
            return driverGoodsService.getBindSupplyListByIds(pageInfo);
        } catch (Exception e) {
            resultMode.setSucceed(false);
            resultMode.setErrMsg(e.getMessage());
            e.printStackTrace();
        }
        return resultMode;
    }

    /**
     * 新增司机货源
     *
     * @param driverGoods 司机货源
     * @return 结果
     */
    @PostMapping(value = "/save")
    public ResultMode<DriverGoods> save(@RequestBody DriverGoods driverGoods) throws Exception {
        log.info("保存货源....save..入参:{}", JSON.toJSONString(driverGoods));

        if (org.apache.commons.lang3.StringUtils.equals(driverGoods.getEnquiryRange(), "50")) {

            if (IterUtil.isEmpty(driverGoods.getQrsCodeRelations())) {
                ResultMode.fail("询价范围为自有司机,货源码为必填！");
            }
            LsdsIdentifyCodeRelVO codeRelVO = driverGoods.getQrsCodeRelations().stream().filter(item ->
                item.getIdentifyCodeType().equals(QrsEnum.IdentifyCodeTypeEnum.COMPANY_GOODS_CODE.getCode())).findFirst().orElse(null);
            if (codeRelVO == null) {
                ResultMode.fail("询价范围为自有司机,货源码为必填！");
            }

        }

        ResultMode<DriverGoods> resultModel = new ResultMode<>();
        if(StrUtil.isBlank(driverGoods.getTotalQuantityUnits())){
            return ResultMode.fail("计价类型不能为空!");
        }
        if (driverGoods.getFreightType().equals(Constants.FREIGHT_TYPE_NETWORK) && driverGoods.getFreightSurvivalRate() == null) {
            return ResultMode.fail("网络货运主体运费留存率不能为空!");
        }

        if (driverGoods.getFreightType().equals(Constants.FREIGHT_TYPE_NETWORK) && StringUtils.isBlank(driverGoods.getNetworkMainBodyName())) {
            return ResultMode.fail("网络货运主体名称不能为空!");
        }

        if (driverGoods.getTotalQuantity() == null) {
            return ResultMode.fail("总数量不能为空!");
        }
        if (driverGoods.getEnquiryRange().equals(Constants.ENQUIRY_RANGE_DRIVERS) && StringUtils.isBlank(driverGoods.getDriverIds())) {
            return ResultMode.fail("指定司机数据不能为空!");
        }
        if(StrUtil.isEmpty(driverGoods.getStartSiteCityName())){
            return ResultMode.fail("出发地简称不能为空!");
        }
        if(StrUtil.isEmpty(driverGoods.getEndSiteCityName())){
            return ResultMode.fail("目的地简称不能为空!");
        }
        if(StrUtil.isBlank(driverGoods.getTransportationType())){
            return ResultMode.fail("运输类型不能为空!");
        }
        if(StrUtil.isBlank(driverGoods.getLineId())){
            if(StrUtil.isBlank(driverGoods.getStartLineId())){
                return ResultMode.fail("发出地线路ID不能为空!");
            }
            if(StrUtil.isBlank(driverGoods.getEndLineId())){
                return ResultMode.fail("目的地线路ID不能为空!");
            }
        }
        // 地址信息更新补充
    /*    if (org.apache.commons.lang3.StringUtils.equals(driverGoods.getTransportationType(), "112")) {
            // 如果无法获取起始地和目的地的地址信息则不可以发布货源
            log.info("startLineId:{}, endLineId:{}", driverGoods.getStartLineId(), driverGoods.getEndLineId());
            if (StringUtils.isEmpty(driverGoods.getEndLineId()) || StringUtils.isEmpty(driverGoods.getStartLineId())) {
                return ResultMode.fail(INVERT_SHORT_TIPS);
            } else {
                // 起始地目的地相同判断
                if (org.apache.commons.lang3.StringUtils.equals(driverGoods.getStartLineId(), driverGoods.getEndLineId())) {
                    return ResultMode.fail("货源起始地和目的地不可以相同!");
                }
            }
        }*/
        ResultMode resultMode = createCrmLine(driverGoods);
        if (!resultMode.getSucceed()){
            return resultMode;
        }

        CrmCompanyLineAddressBO crmCompanyLineAddressBO = lineAddressExchangeService.getCrmCompanyLine(driverGoods.getLineId());
        if(crmCompanyLineAddressBO==null){
            resultModel.setSucceed(false);
            resultModel.setErrMsg("未找到对应的线路");
            return resultModel;
        }
        if(StringUtil.equals(crmCompanyLineAddressBO.getItem3(),"1")){
            resultModel.setSucceed(false);
            resultModel.setErrMsg("该线路已经被删除");
            return resultModel;
        }
        CrmCompanyLineAddressBO startAddress = lineAddressExchangeService.getCrmCompanyLine(crmCompanyLineAddressBO.getSendAddrId());
        if(startAddress==null){
            resultModel.setSucceed(false);
            resultModel.setErrMsg("未找到发出地地址");
            return resultModel;
        }
        CrmCompanyLineAddressBO endAddress = lineAddressExchangeService.getCrmCompanyLine(crmCompanyLineAddressBO.getReceiveAddrId());
        if(endAddress==null){
            resultModel.setSucceed(false);
            resultModel.setErrMsg("未找到目的地地址");
            return resultModel;
        }

        driverGoodsService.initAddressInfo(driverGoods,startAddress,endAddress, crmCompanyLineAddressBO);
        if (StrUtil.equals(driverGoods.getTransportationType(), "112")) {
            checkInvertShortParam(startAddress, endAddress);
        }
        //非短倒货源的进行预付费校验
        if (org.apache.commons.lang3.StringUtils.equals(driverGoods.getAdvancePaymentFlag(), "0")) {
            prepayCheck(driverGoods, resultModel);
            if (!resultModel.getSucceed()) {
                return resultModel;
            }
        }

        // 亏涨吨校验
        goodsDeductibleService.checkParams(driverGoods.getLsdsGoodsDeductible(), resultModel);
        if (!resultModel.getSucceed()) {
            return resultModel;
        }
        //敏感词过滤
        ResultMode filterResultMode = driverGoodsService.filterSensitiveWord(driverGoods);
        if (!filterResultMode.getSucceed()) {
            return filterResultMode;
        }

        //风控校验网络货运货物是否危化品
        String errTipMsg = hazardousChemicalsCalculate(driverGoods);
        if (StrUtil.isNotBlank(errTipMsg)) {
            return ResultMode.fail(errTipMsg);
        }
        //公开询价、传统货运、非公路整车不支持使用优享服务
        if(StrUtil.equals(driverGoods.getEnquiryType(), LsdsEnum.LsdsgoodsEnquiryTypeEnum.enquirytype1.getCode())
            || StrUtil.equals(driverGoods.getFreightType(), FreightTypeEnum.TRADITIONAL_MODE.getCode())
            || !StrUtil.equals(driverGoods.getTransportationType(), "110") ){
            if(StrUtil.equals(driverGoods.getPremiumServStatus(),"10")){
                return ResultMode.fail("公开询价、传统货运、非公路整车不支持使用优享服务");
            }
        }
        //撮合货源填充撮合信息
        this.appendMatchmaking(driverGoods);
        ResultMode<DriverGoods> save = driverGoodsService.save(driverGoods);
        if(ObjUtil.isNotNull(save) && save.isSucceed() && CollUtil.isNotEmpty(save.getModel())){
            lsdsKafkaSender.send(MATCHMAKING_GOODS_CHANGE_TOPIC,save.getModel().get(0).getGoodsId());
            GoodsSourceSyncDataCommand command = new GoodsSourceSyncDataCommand();
            command.setGoodId(save.getModel().get(0).getGoodsId());
            command.setPublisherType(GoodsPublishTypeEnum.DRIVER.getType());
            command.setGoodsDeductible(driverGoods.getLsdsGoodsDeductible());
            asyncContextAwareExecutor.execute(() -> eventPublisher.publishGoodsSourceSyncDataEvent(GoodsSourceSyncDataEvent.of(command)));
        }
        return save;
    }

    private String hazardousChemicalsCalculate(DriverGoods req) {
        try {
            HazardousChemicalVO hazardousChemicalVO = new HazardousChemicalVO();
            hazardousChemicalVO.setGoodsName(req.getGoodsName());
            hazardousChemicalVO.setGoodsDesc(req.getGoodsDesc());
            hazardousChemicalVO.setFreightType(req.getFreightType());
            DriverGoodsPublishRuleParma ruleParma = new DriverGoodsPublishRuleParma();
            ruleParma.setBussSceneType(RmsEnum.BussItemEnum.LSDS_DRIVER_GOODS_PUBLISH.getCode());
            ruleParma.setRiskSceneType(RmsEnum.RiskItemEnum.PLATFORM_HAZARDOUS_CHEMICALS.getCode() + "|");
            ruleParma.setBizId(IdUtils.generateShardingId() + StrUtil.toString(IdUtil.generateId()));
            ruleParma.setBussId(JwtUtil.getInstance().getUserBaseIdByToken());
            ruleParma.setHazardousChemicalVO(hazardousChemicalVO);
            // 调风控接口
            return rmsExchangeService.driverGoodsPublishCalculate(ruleParma);
        } catch (Exception e) {
            log.error("校验异常：", e);
            return "网络货运货物危化品校验异常";
        }
    }

    @LogPrintPoint(ret = true)
    @SneakyThrows
    @RequestMapping(value = "/planTransferToGoods", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<DriverGoods> planTransferToGoods(@RequestBody @ApiParam PlanTransferToDriverGoodsVo vo) {
        // 计划单信息校验
        WoaLogisticsPlanVO woaLogisticsPlanVO = woaExchangeService.getLogisticsPlanByPlanId(vo.getPlanId());
        if (woaLogisticsPlanVO == null || woaLogisticsPlanVO.getPlanId() == null) {
            throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_DB_NORECORD, "计划单不存在");
        }
        // 发布货源
        DriverGoods dg = new DriverGoods();
        BeanUtil.copyProperties(vo,dg, CopyOptions.create().ignoreError().ignoreNullValue());
        ResultMode<DriverGoods> resultMode = save(dg);
        DriverGoods driverGoods = IterUtil.getFirst(resultMode.getModel());

        woaExchangeService.goodsIdWriteBack(vo.getPlanId(),driverGoods.getGoodsId());
        return resultMode;
    }

    @LogPrintPoint(ret = true)
    @SneakyThrows
    @RequestMapping(value = "/offerPlanTransferToGoods", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<DriverGoods> offerPlanTransferToGoods(@RequestBody @ApiParam PlanTransferToDriverGoodsVo vo) {
        // 报价计划单信息校验
        WoaOfferPlanVO woaOfferPlanVO = woaExchangeService.getOfferPlanByPlanId(vo.getOfferPlanId());
        if (woaOfferPlanVO == null || woaOfferPlanVO.getId() == null) {
            throw new LsdsWlydException(CommonStatusCodeEnum.BUSS_ERROR_DB_NORECORD, "计划单不存在");
        }
        // 发布货源
        DriverGoods dg = new DriverGoods();
        BeanUtil.copyProperties(vo,dg, CopyOptions.create().ignoreError().ignoreNullValue());
        ResultMode<DriverGoods> resultMode = save(dg);
        DriverGoods driverGoods = IterUtil.getFirst(resultMode.getModel());

        woaExchangeService.goodsIdWriteBackToOfferPlan(vo.getOfferPlanId(),driverGoods.getGoodsId(), TmsEnum.GoodsSourceTypeEnum.DRIVER.getCode());
        return resultMode;
    }

    private ResultMode createCrmLine(DriverGoods driverGoods) {
        ResultMode<String> resultModel = new ResultMode<String>();
        if(StringUtil.isBlank(driverGoods.getLineId())){
            String lineShortName = driverGoods.getStartSiteCityName()+"-"+driverGoods.getEndSiteCityName();
            String companyId = JwtUtil.getInstance().getCompanyIdByToken();
            resultModel = lineAddressExchangeService.createCrmCompanyLine(
                lineShortName, driverGoods.getStartLineId(), driverGoods.getEndLineId(),
                LineSourceEnum.RELEASE_GOODS.getCode(), companyId);
            if(!resultModel.getSucceed()){
                return resultModel;
            }
            String lineId = resultModel.getModel().get(0);
            driverGoods.setLineId(lineId);
        }
        return resultModel;
    }

    private ResultMode getLineAddressId(DriverGoods driverGoods){
        if(StrUtil.isEmpty(driverGoods.getStartLineId())){
            ResultMode<CrmCompanyLineAddress> companyLineAddressResultMode = crmCompanyLineAddressInter.findLineByLineShortNameAndCompanyId(driverGoods.getStartSiteCityName(),driverGoods.getCompanyId());
            if(companyLineAddressResultMode!=null && !IterUtil.isEmpty(companyLineAddressResultMode.getModel())){
                CrmCompanyLineAddress companyLineAddress = IterUtil.getFirst(companyLineAddressResultMode.getModel());
                if(companyLineAddress!=null){
                    driverGoods.setStartLineId(companyLineAddress.getLineId());
                }
            }
            if(StrUtil.isEmpty(driverGoods.getStartLineId())){
                return ResultMode.fail("没有找到出发地地址!");
            }
        }
        if(StrUtil.isEmpty(driverGoods.getEndLineId())){
            ResultMode<CrmCompanyLineAddress> companyLineAddressResultMode = crmCompanyLineAddressInter.findLineByLineShortNameAndCompanyId(driverGoods.getEndSiteCityName(),driverGoods.getCompanyId());
            if(companyLineAddressResultMode!=null && !IterUtil.isEmpty(companyLineAddressResultMode.getModel())){
                CrmCompanyLineAddress companyLineAddress = IterUtil.getFirst(companyLineAddressResultMode.getModel());
                if(companyLineAddress!=null){
                    driverGoods.setEndLineId(companyLineAddress.getLineId());
                }
            }
            if(StrUtil.isEmpty(driverGoods.getEndLineId())){
                return ResultMode.fail("没有找到目的地地址!");
            }
        }
        return ResultMode.success();
    }

    private ResultMode checkAddress(DriverGoods driverGoods) {
        if (driverGoods.getSendAddrProvince().equalsIgnoreCase(driverGoods.getReceiveAddrProvince())
            && driverGoods.getSendAddrCity().equalsIgnoreCase(driverGoods.getReceiveAddrCity())
            && driverGoods.getSendAddrArea().equalsIgnoreCase(driverGoods.getReceiveAddrArea())
        ) {
            if (driverGoods.getStartSiteAddress().equalsIgnoreCase(driverGoods.getEndSiteAddress())) {
                return ResultMode.fail("出发地、目的地省市区县相同时，详细地址不能相同");
            }
        }
        if (driverGoods.getStartSendPhoneNumber().equalsIgnoreCase(driverGoods.getEndReceivePhoneNumber())) {
            return ResultMode.fail("出发地联系电话与目的地联系电话不能相同");
        }
        return ResultMode.success();
    }

    private GeoGetLonLat getStartAddressDeail(String address, String queryUrl) {
        log.info("地址解析 address:{}", address);
        Map<String, String> map = new HashMap<>();
        map.put("key", "cf4776eec334747ca1c7634a11506cd9");  // 高德key
        map.put("address", address);  // 地址
        String jsonStrPath = HttpClientUtil.doGet(queryUrl, map);
        JSONObject jsonObject = JSONObject.parseObject(jsonStrPath);
        String status = jsonObject.getString("status");
        GeoGetLonLat geoGetLonLat = new GeoGetLonLat();
        geoGetLonLat.setAddress(address);
        if (org.apache.commons.lang3.StringUtils.equals("1", status)) {
            jsonObject.getJSONArray("geocodes").forEach(f -> {
                JSONObject path = JSONObject.parseObject(f.toString());
                String[] lonLat = path.getString("location").split(",");
                log.info("解析后的经纬度: {},{}", lonLat[0], lonLat[1]);
                geoGetLonLat.setLon(lonLat[0]);
                geoGetLonLat.setLat(lonLat[1]);
            });
        }
        return geoGetLonLat;
    }


    private void prepayCheck(DriverGoods driverGoods, ResultMode<DriverGoods> resultModel) {
        if (driverGoods != null && org.springframework.util.StringUtils.hasText(driverGoods.getTransportationType()) && !org.apache.commons.lang3.StringUtils.equals(driverGoods.getTransportationType(), UtilityEnum.TransportationAllTypeEnum.INVERT_SHORT.getLsdsCode())) {
            //预付费的逻辑
            if ("0".equalsIgnoreCase(driverGoods.getAdvancePaymentFlag())) {
                List<String> paraCodesDefaultLoad = new ArrayList();
                paraCodesDefaultLoad.add("1052");
                ResultMode<PlatformCmPlatformParameter> resultModeDefaultLoad = platformCmPlatformParameterInter.getByParaCodes(paraCodesDefaultLoad);
                List<PlatformCmPlatformParameter> platformCmPlatformParameters = resultModeDefaultLoad.getModel();
                if (!org.springframework.util.CollectionUtils.isEmpty(platformCmPlatformParameters)) {
                    if ("1".equalsIgnoreCase(platformCmPlatformParameters.get(0).getParaValue())) {
                        PrepayResVO prepayResVO = checkPrepay(driverGoods);
                        if (null != prepayResVO) {
                            resultModel.setErrMsg("预付费用超额,最大值为" + prepayResVO.getPrepayMax() + ",请重新输入");
                            resultModel.setSucceed(false);
                        }
                    }
                }
            }


        } else {
            driverGoods.setAdvancePaymentFlag("1");
            driverGoods.setAdvancePayment(new BigDecimal(0));
        }
    }


    PrepayResVO checkPrepay(DriverGoods driverGoods) {
        boolean flag = false;
        PrepayReqVO prepayReqVO = PrepayReqVO.of();
        //结算类型【radio:10-按柜,20-按重量,30-按车】
        prepayReqVO.setChargeType(driverGoods.getTotalQuantityUnits());
        //基价
        prepayReqVO.setEnquiryTypeBasePrice(driverGoods.getEnquiryTypeBasePrice());
        //预付费金额
        if (driverGoods.getAdvancePayment() != null) {
            prepayReqVO.setPrePayMoney(driverGoods.getAdvancePayment().toString());
        }
        //询价方式【radio:10-公开询价,20-指定单价】
        prepayReqVO.setEnquiryType(driverGoods.getEnquiryType());
        if ("10".equalsIgnoreCase(driverGoods.getEnquiryType())) {
            prepayReqVO.setPlaceOrderOptFlag("20");
        }
        ResultMode<PrepayResVO> prepayResVOResultMode = prepayService.prepayCheck(prepayReqVO);
        if (!CollectionUtils.isEmpty(prepayResVOResultMode.getModel())) {
            if (prepayResVOResultMode.getModel().get(0).getOverLimit()) {
                return prepayResVOResultMode.getModel().get(0);
            }
        }
        return null;
    }

    /**
     * @return com.wanlianyida.framework.lgicommon.entity.ResultMode
     * <AUTHOR>
     * @Description 司机货源审核
     * @Date 2021/6/2
     * @Param [driverGoods]
     **/
    @PostMapping(value = "/audit")
    @ApiOperation(value = "司机货源审核")
    public ResultMode audit(@RequestBody DriverGoods driverGoods) throws Exception {
        ResultMode<String> resultModel = new ResultMode<String>();
        if (driverGoods.getFreightType().equals(Constants.FREIGHT_TYPE_NETWORK) && driverGoods.getFreightSurvivalRate() == null) {
            return ResultMode.fail("网络货运主体运费留存率不能为空!");
        }
        if (driverGoods.getFreightType().equals(Constants.FREIGHT_TYPE_NETWORK) && StringUtils.isBlank(driverGoods.getNetworkMainBodyName())) {
            return ResultMode.fail("网络货运主体名称不能为空!");
        }
        if (!resultModel.getSucceed()) {
            return resultModel;
        }
        ResultMode audit = driverGoodsService.audit(driverGoods);
        if(ObjUtil.isNotNull(audit) && audit.isSucceed() && CollUtil.isNotEmpty(audit.getModel())){
            lsdsKafkaSender.send(MATCHMAKING_GOODS_CHANGE_TOPIC,driverGoods.getGoodsId());
        }
        return audit;
    }

    /**
     * 修改司机货源
     *
     * @param driverGoods 司机货源
     * @return 结果
     */
    @PostMapping(value = "/updateById")
    public ResultMode updateById(@RequestBody DriverGoods driverGoods) {
        return new ResultMode(driverGoodsService.updateById(driverGoods));
    }


    /**
     * 批量删除司机货源
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @PostMapping(value = "/removeByIds")
    public ResultMode removeByIds(String[] ids) {
        if (ArrayUtil.isEmpty(ids)) {
            return ResultMode.fail("货源ID不能为空");
        }
        for (String id : ids) {
            goodsSourceAppService.deleteGoods(id);
        }
        return new ResultMode();
    }

    /**
     * 删除司机货源信息
     *
     * @param id 司机货源ID
     * @return 结果
     */

    @PostMapping(value = "/removeById")
    public ResultMode removeById(String id) {
//        return new ResultMode(driverGoodsService.removeById(id));
        if (StrUtil.isBlank(id)) {
            return ResultMode.fail("货源ID不能为空");
        }
        return goodsSourceAppService.deleteGoods(id);
    }

    /**
     * 删除司机货源信息feign接口
     *
     * @param id 司机货源ID
     * @return 结果
     */

    @PostMapping(value = "/removeByIdFeign/{id}")
    public ResultMode removeByIdFeign(@PathVariable("id")  String id) {
        log.info("删除司机货源信息->{}", id);
//        return new ResultMode(driverGoodsService.removeById(id));
        if (StrUtil.isBlank(id)) {
            return ResultMode.fail("货源ID不能为空");
        }
        return goodsSourceAppService.deleteGoods(id);
    }


    /**
     * 分页查询司机货源列表
     *
     * @param pageInfo 司机货源
     * @return 司机货源集合
     */
    @PostMapping(value = "/page")
    public ResultMode<DriverGoods> page(@RequestBody PagingInfo<DriverGoodsFilter> pageInfo) {
        ResultMode<DriverGoods> ResultMode = new ResultMode<>();
        DriverGoodsFilter filterModel = pageInfo.getFilterModel();
        //查询、导出参数
        driverGoodsService.appendFilter(filterModel);
        //列表查询
        List<DriverGoods> list = driverGoodsService.page(pageInfo);
        //填充货源录单员
        fillOperator(list);
        //填充货源扩展信息
        fillGoodsExtendInfo(list);
        //log.info("list.............{}", JSON.toJSONString(list));
        //处理分页
        PageInfo<DriverGoods> returnPageInfo = new PageInfo<DriverGoods>(list);
        //查询是否有绑定收款码传入数据ids 返回绑定的业务数据id下的二维码集
        //NOTES 过滤掉 70 80的货源
        List<DriverGoods> apgeGoodList = returnPageInfo.getList();
        apgeGoodList = apgeGoodList.stream()
            .filter(goods -> !StrUtil.equalsAny(goods.getDealStatus(), "70", "80") ||
                goods.getRemainingQuantity().equals(BigDecimal.ZERO))
            .collect(Collectors.toList());
        Set<String> goodsIds = apgeGoodList.stream().map(DriverGoods::getGoodsId).collect(Collectors.toSet());
        //log.info("开始查询单据二维id" + JSONObject.toJSONString(goodsIds));
        // 查司机货源识别码关系
        IdentifyCodeRelFilter identifyCodeRelFilter = new IdentifyCodeRelFilter();
        identifyCodeRelFilter.setGoodsIds(goodsIds);
        identifyCodeRelFilter.setCompanyId(JwtUtil.getInstance().getCompanyIdByToken());
        List<LsdsIdentifyCodeRelVO> lsdsIdentifyCodeRelVOs = lsdsIdentifyCodeRelService.queryListByGoodsIds(identifyCodeRelFilter);
        if (IterUtil.isNotEmpty(lsdsIdentifyCodeRelVOs)) {
            Map<String, List<LsdsIdentifyCodeRelVO>> relMap = lsdsIdentifyCodeRelVOs.stream()
                .collect(Collectors.groupingBy(LsdsIdentifyCodeRelVO::getGoodsId));
            returnPageInfo.getList().stream().forEach(good -> {
                if (IterUtil.isNotEmpty(relMap.get(good.getGoodsId()))) {
                    good.setQrsCodeRelations(relMap.get(good.getGoodsId()));
                }
            });
            //log.info("收款人二维码排序结果: {} ", JSONObject.toJSONString(returnPageInfo.getList()));
        }
        //推荐运力增值服务统计
        cntWoaChannelOrder(returnPageInfo.getList());

        if (CollectionUtils.isNotEmpty(list)) {
            list.stream().forEach(good -> {
                //NOTES 过滤掉 70 80的记录不显示货源码
                if (StrUtil.equalsAny(good.getDealStatus(), "70", "80") ||
                    good.getRemainingQuantity().equals(BigDecimal.ZERO)) {
                    good.setGoodsCode(null).setGoodsCode(null).setGoodsCodeNumber(null).setGoodsCodeName(null);
                }
            });
        }
        //填充撮合信息
        if(GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(filterModel.getGoodsKind())) {
            list.forEach(l ->{
                String goodsId = l.getGoodsId();
                LsdsMatchmakingConfigQuery query = new LsdsMatchmakingConfigQuery();
                query.setBusId(goodsId);
                LsdsMatchmakingConfigDTO config = lsdsMatchmakingService.queryConfig(query);
                if(ObjUtil.isNotNull(config)){
                    BeanUtil.copyProperties(config,l);
                    l.setId(goodsId);
                }

            });
        }
        //log.info("returnPageInfo.getList()........{}", JSON.toJSONString(returnPageInfo.getList()));
        //处理返回数据
        ResultMode.setTotal((int) returnPageInfo.getTotal());
        ResultMode.setModel(returnPageInfo.getList());
        return ResultMode;
    }

    private void cntWoaChannelOrder(List<DriverGoods> goodsList) {
        if (IterUtil.isEmpty(goodsList)){
            return;
        }
        List<String> goodsIds = goodsList.stream()
            .filter(goods -> StrUtil.isNotBlank(goods.getGoodsId()))
            .map(DriverGoods::getGoodsId)
            .collect(Collectors.toList());
        if (IterUtil.isEmpty(goodsIds)){
            return;
        }
        List<WoaWaybillClueCntVO> cntList = woaExchangeService.cntByBizIds(goodsIds);
        if (IterUtil.isEmpty(cntList)){
            return;
        }
        //cntList按busId转map，按goodsList中货源号匹配cnt
        Map<String, WoaWaybillClueCntVO> cntMap = cntList.stream()
            .collect(Collectors.toMap(WoaWaybillClueCntVO::getBusId, Function.identity()));
        for (DriverGoods goods : goodsList) {
            WoaWaybillClueCntVO cntVO = cntMap.get(goods.getGoodsId());
            if (cntVO == null) {
                continue;
            }
            goods.setWoaChannelOrdercnt(cntVO.getCnt());
        }
    }

    /**
     *  填充货源的录单号
     * @param driverGoodsList
     */
    private void fillOperator(List<DriverGoods> driverGoodsList){
        if(IterUtil.isEmpty(driverGoodsList)){
            return;
        }
        Set<String> userIds = driverGoodsList.stream().filter(item->!StrUtil.isEmpty(item.getCreateBy())).map(item->item.getCreateBy()).collect(Collectors.toSet());
        ResultMode<PlatformUmUserbaseinfo> userbaseinfoResultMode = platformUmUserbaseinfoInter.getUserbaseinfoByUserBaseIds(new ArrayList<>(userIds));
        if(userbaseinfoResultMode==null || IterUtil.isEmpty(userbaseinfoResultMode.getModel())){
            return;
        }
        List<PlatformUmUserbaseinfo> userbaseinfoList = userbaseinfoResultMode.getModel();
        Map<String,String> userbaseinfoMap = userbaseinfoList.stream().collect(Collectors.toMap(item->item.getUserBaseId(),v->v.getExUserNameAndTelephone()));
        driverGoodsList.forEach(item->{
            if(StrUtil.isEmpty(item.getCreateBy())){
                return;
            }
            item.setExUserNameAndTelephone(userbaseinfoMap.get(item.getCreateBy()));
        });

    }


    /**
     * 填充货源扩展信息
     */
    private void fillGoodsExtendInfo(List<DriverGoods> driverGoodsList) {
        if (CollUtil.isEmpty(driverGoodsList)) {
            return;
        }
        List<String> goodsIds = driverGoodsList.stream().map(item -> item.getGoodsId()).collect(Collectors.toList());
        List<DriverGoodsExtendDTO> goodsExtendDTOList = driverGoodsExtendBusiness.selectByGoodsIds(goodsIds);
        if (CollUtil.isEmpty(goodsExtendDTOList)) {
            return;
        }

        Map<String, DriverGoodsExtendDTO> goodsExtendMap = goodsExtendDTOList.stream().collect(Collectors.toMap(DriverGoodsExtendDTO::getGoodsId, Function.identity()));
        driverGoodsList.forEach(item -> {
            DriverGoodsExtendDTO goodsExtend = goodsExtendMap.get(item.getGoodsId());
            if (ObjUtil.isNotNull(goodsExtend)) {
                item.setBatchPaymentSwitch(goodsExtend.getBatchPaymentSwitch());
                item.setSignPaymentSwitch(goodsExtend.getSignPaymentSwitch());
                item.setReceiptPaymentSwitch(goodsExtend.getReceiptPaymentSwitch());
                item.setReceiptAmount(goodsExtend.getReceiptAmount());
            }
        });
    }
    /**
     * 分页查询司机货源列表
     *
     * @param pageInfo 司机货源
     * @return 司机货源集合
     */
    @PostMapping(value = "/pageBindQr")
    public ResultMode<DriverGoods> pageBindQr(@RequestBody PagingInfo<DriverGoodsFilter> pageInfo) {

        ResultMode<DriverGoods> resultMode = new ResultMode<>();
        //查询3PL数据
        pageInfo.getFilterModel().setQueryType(10);
        pageInfo.getFilterModel().setGoodsKind(10);
        //设置分页参数
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        //列表查询
        List<DriverGoods> list = driverGoodsService.getUnBindList(pageInfo);
        if(IterUtil.isEmpty(list)){
            return resultMode;
        }

        //处理分页
        PageInfo<DriverGoods> returnPageInfo = new PageInfo<DriverGoods>(list);
        //处理返回数据
        resultMode.setTotal((int) returnPageInfo.getTotal());
        resultMode.setModel(returnPageInfo.getList());
        return resultMode;
    }


    /**
     * 货主货源信息分页查询
     * 货主首页业务逻辑:
     * 待发布  包含待发布(10)、待审核(20)     LsdsGoods.dealStatus in (10,20)
     * 发布中  包含发布中(30)、部分成交(60)   LsdsGoods.dealStatus in (30,60)
     * 运输中  显示订单信息 包含执行中、已签收
     * 待结算  显示运单信息 包含待结算，待核对，审核不通过，对账不通过
     * 和3pl的逻辑区别: sql有变化
     * <p>
     * 创建者：Mindy
     */
    @PostMapping(value = "/pageShipper")
    public ResultMode<DriverGoods> pageShipper(@RequestBody PagingInfo<DriverGoodsFilter> pageInfo) {

        ResultMode<DriverGoods> resultMode = new ResultMode<>();

        //1 参数校验
        if (CollectionUtils.isEmpty(pageInfo.getFilterModel().getStatusList())) {

            resultMode.setSucceed(false);
            resultMode.setErrMsg("货源参数不能为空！");

            return resultMode;
        }

        //2 查询3PL数据
//        pageInfo.getFilterModel().setQueryType(10);

        //列表查询
        List<DriverGoods> list = driverGoodsService.page(pageInfo);
        //log.info("list.............{}", JSON.toJSONString(list));

        //处理分页
        PageInfo<DriverGoods> returnPageInfo = new PageInfo<DriverGoods>(list);

        //查询是否有绑定收款码传入数据ids 返回绑定的业务数据id下的二维码集
        //NOTES 过滤掉 70 80的货源
        List<DriverGoods> apgeGoodList = returnPageInfo.getList();
        apgeGoodList = apgeGoodList.stream()
            .filter(goods -> !StrUtil.equalsAny(goods.getDealStatus(), "70", "80") ||
                goods.getRemainingQuantity().equals(BigDecimal.ZERO))
            .collect(Collectors.toList());
        Set<String> goodsIds = apgeGoodList.stream().map(DriverGoods::getGoodsId).collect(Collectors.toSet());
        //log.info("开始查询单据二维id" + JSONObject.toJSONString(goodsIds));
        // 查司机货源识别码关系
        IdentifyCodeRelFilter identifyCodeRelFilter = new IdentifyCodeRelFilter();
        identifyCodeRelFilter.setGoodsIds(goodsIds);
        identifyCodeRelFilter.setCompanyId(JwtUtil.getInstance().getCompanyIdByToken());
//        identifyCodeRelFilter.setIdentifyCodeType(QrsEnum.IdentifyCodeTypeEnum.COMPANY_RESERVE_CODE.getCode());
        List<LsdsIdentifyCodeRelVO> lsdsIdentifyCodeRelVOs = lsdsIdentifyCodeRelService.queryListByGoodsIds(identifyCodeRelFilter);
        if (IterUtil.isNotEmpty(lsdsIdentifyCodeRelVOs)) {
            Map<String, List<LsdsIdentifyCodeRelVO>> relMap = lsdsIdentifyCodeRelVOs.stream()
                .collect(Collectors.groupingBy(LsdsIdentifyCodeRelVO::getGoodsId));
            returnPageInfo.getList().forEach(good -> {
                if (IterUtil.isNotEmpty(relMap.get(good.getGoodsId()))) {
                    good.setQrsCodeRelations(relMap.get(good.getGoodsId()));
                }
            });
        }

        //log.info("returnPageInfo.getList()........{}", JSON.toJSONString(returnPageInfo.getList()));
        //处理返回数据
        resultMode.setTotal((int) returnPageInfo.getTotal());
        resultMode.setModel(returnPageInfo.getList());

        return resultMode;
    }


    /**
     * @return com.wanlianyida.framework.lgicommon.entity.ResultMode<com.isoftstone.hig.lsds.api.entity.DriverGoods>
     * <AUTHOR>
     * @Description 货源审核分页列表
     * @Date 2021/6/24
     * @Param [pageInfo]
     **/
    @PostMapping(value = "/auditPage")
    @ApiOperation(value = "货源审核分页列表")
    public ResultMode<DriverGoods> auditPage(@RequestBody PagingInfo<DriverGoodsFilter> pageInfo) {
        ResultMode<DriverGoods> ResultMode = new ResultMode<>();
        //查询4PL数据
        pageInfo.getFilterModel().setQueryType(20);
        //设置分页参数
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength);
        //列表查询
        List<DriverGoods> list = driverGoodsService.auditPage(pageInfo);
        //处理分页
        PageInfo<DriverGoods> returnPageInfo = new PageInfo<DriverGoods>(list);
        //处理返回数据
        ResultMode.setTotal((int) returnPageInfo.getTotal());
        ResultMode.setModel(returnPageInfo.getList());
        return ResultMode;
    }

    /**
     * 司机端找货司机货源列表
     * 司机端首页货源推荐
     * @param pageInfo 司机货源
     * @return 司机货源集合
     */
    @PostMapping("/findGoods")
    @ApiOperation(value = "司机端找货司机货源列表")
    public ResultMode<DriverGoods> findGoods(@RequestBody PagingInfo<DriverGoodsFilter> pageInfo) {
        TimeInterval timer = new TimeInterval();
        TimeInterval timer1 = new TimeInterval();
        log.info("findGoods#司机端货源大厅列表入参：{}", JSONUtil.toJsonStr(pageInfo));
        ResultMode<DriverGoods> resultMode = new ResultMode<>();
        List<DriverGoods> driverGoodsList = new ArrayList<>();
        String driverId = JwtUtil.getInstance().getAppDriverIdByToken();
        if (StrUtil.isBlank(driverId)) {
            ResultMode<String> userbaseinfo = platformUmUserbaseinfoInter.getDriverIdByUserBaseId();
            if (ObjectUtil.isNotNull(userbaseinfo) && userbaseinfo.getSucceed() && IterUtil.isNotEmpty(userbaseinfo.getModel())) {
                driverId = userbaseinfo.getModel().get(0);
            } else {
                //log.error("findGoods#没有拿到司机id,尝试获取司机id结果:{}", JSON.toJSONString(resultMode));
            }
        }
        // 新注册的无资质的用户只显示定位匹配货源
        if (StrUtil.isNotBlank(driverId)) {
            pageInfo.getFilterModel().setDriverIds(driverId);
            // 查询进行中的运单
            PagingInfo<TmsWaybillFilter> pageInfoWaybill = new PagingInfo<>();
            pageInfoWaybill.setCurrentPage(1);
            pageInfoWaybill.setPageLength(1);
            pageInfoWaybill.setCountTotal(false);
            pageInfoWaybill.setFilterModel(new TmsWaybillFilter());
            ResultMode<TmsWaybill> doingWaybillMode = tmsWaybillInter.queryDoingWaybill(pageInfoWaybill);
            log.info("findGoods#doingWaybillMode：{}", JSONUtil.toJsonStr(doingWaybillMode));
            if (ObjectUtil.isNull(doingWaybillMode) || !doingWaybillMode.getSucceed()) {
                //log.error("查询进行中的运单异常，不阻断，继续！");
            }
            List<TmsWaybill> waybills = doingWaybillMode.getModel();
            log.info("findGoods#queryDoingWaybill#耗时：{}", timer1.intervalRestart());
            if (IterUtil.isNotEmpty(waybills)) {
                // 热货推荐最多10条：依赖最后一次进行中运单id
                List<DriverGoods> driverGoods = queryDriverGoodsRecommend(pageInfo, IterUtil.getFirst(waybills).getWaybillId(), driverId);
                if (IterUtil.isNotEmpty(driverGoods)) {
                    driverGoodsList.addAll(driverGoods);
                }
                log.info("findGoods#goodsRecommend#耗时：{}", timer1.intervalRestart());
            } else {
                // 常跑路线推荐货源最多3条
                queryDriverLineSupply(pageInfo, driverGoodsList, driverId);
                log.info("findGoods#queryDriverLineSupply#耗时：{}", timer1.intervalRestart());
                if (IterUtil.isNotEmpty(driverGoodsList)) {
                    driverGoodsList.forEach(driverGoods -> driverGoods.setOperType("0"));
                }
            }
            //推荐货源有进行中运单设置是进行中标记为true
            if (IterUtil.isNotEmpty(waybills)) {
                driverGoodsList.forEach(driverGoods -> driverGoods.setHasDoingWaybill(true));
            } else {
                driverGoodsList.forEach(driverGoods -> driverGoods.setHasDoingWaybill(false));
            }
            //推荐货源设置推荐标记为true
            driverGoodsList.forEach(driverGoods -> driverGoods.setIsRecommendedGoods(true));
            //条件司机货源列表查询：driverGoodsList中已存在的不再查询
            Set<String> driverGoodsIds = driverGoodsList.stream()
                .filter(driverGoods -> (StrUtil.equals(driverGoods.getOperType(), "0")))
                .map(DriverGoods::getGoodsId).collect(Collectors.toSet());
            pageInfo.getFilterModel().setGoodsIds(driverGoodsIds);
        }
        log.info("findGoods#pageInfo：{}", JSONUtil.toJsonStr(pageInfo));
        List<DriverGoods> list = new ArrayList<>();
        //处理分页
        int headSize = driverGoodsList.size();
        PageInfo<DriverGoods> returnPageInfo = new PageInfo<>(list);
        //从司机端首页的请求判断推荐货源够10条了就不再查询司机货源，否则补查，提升响应
        if (BooleanUtil.isFalse(pageInfo.getFilterModel().getFromDriverAppHome()) ||
            (BooleanUtil.isTrue(pageInfo.getFilterModel().getFromDriverAppHome()) && driverGoodsList.size() < pageInfo.pageLength)) {
            if (BooleanUtil.isTrue(pageInfo.getFilterModel().getFromDriverAppHome())) {
                //设置分页参数：地址匹配
                PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength - driverGoodsList.size(), false);
            } else {
                //设置分页参数：地址匹配
                PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength, pageInfo.getCountTotal());
            }
            log.info("findGoods#pageInfo2：{}", JSONUtil.toJsonStr(pageInfo));
            returnPageInfo = lsdsAppGoodsService.list(pageInfo);
            list = returnPageInfo.getList();
            log.info("findGoods#list#耗时：{}", timer1.intervalRestart());
            if (IterUtil.isNotEmpty(list)) {
                list.forEach(driverGoods -> driverGoods.setOperType("0"));
                driverGoodsList.addAll(list);
            }
        }
        Set<String> driverGoodsIds = driverGoodsList.stream()
            .filter(driverGoods -> (StrUtil.equals(driverGoods.getOperType(), "0")))
            .map(DriverGoods::getGoodsId).collect(Collectors.toSet());
        //批量查询司机货源绑码关系：
        queryDriverGoodsBindCodeRel(driverGoodsIds, driverGoodsList);
        log.info("findGoods#lsdsIdentifyCodeRelVOs#耗时：{}", timer1.intervalRestart());

        //统一剩余重量单位
        driverGoodsList.forEach(driverGoods -> driverGoods.setPlanQuantityUnit(driverGoods.getTotalQuantityUnits()));
        //处理返回数据
        resultMode.setTotal((int) returnPageInfo.getTotal()+headSize);
        //检查是否存在关注列表
        this.checkGoodsAttention(driverId,driverGoodsList);
        log.info("findGoods#checkGoodsAttention#耗时：{}", timer1.intervalRestart());
        //找货第一页数据查出发地和目的地地址的经纬度（crm_company_line_address）
        if (BooleanUtil.isFalse(pageInfo.getFilterModel().getFromDriverAppHome()) &&
                pageInfo.getCurrentPage() == 1) {
            setLngLat(driverGoodsList);
        }
        //查询是否三方企业
        String contractLimitDicId = "20250307000000000001";
        List<PlatformCmDictionary> platformDicList = platformExchangeService.getByParentDic(contractLimitDicId);
        List<String> collect = platformDicList.stream().map(p -> p.getEnumCode()).collect(Collectors.toList());
        //填充撮合类型
        for (DriverGoods driverGoods : driverGoodsList) {
            if(ObjUtil.isNull(driverGoods.getSourceType())){
                Integer sourceType = lsdsAppGoodsService.convertSourceTypeFromDriverGoods(driverGoods,collect);
                driverGoods.setSourceType(sourceType);
            }
        }
        resultMode.setModel(driverGoodsList);
        log.info("findGoods#总耗时：{}", timer.intervalRestart());
        return resultMode;
    }

    /**
     * 查询设置出发地目的地地址的经纬度
     * @param driverGoodsList
     */
    private void setLngLat(List<DriverGoods> driverGoodsList) {
        try {
            CollUtil.split(driverGoodsList, 4).parallelStream().forEach(driverGoodses -> driverGoodses.forEach(driverGoods -> {
                CrmCompanyLineAddressBO crmCompanyLineStartAddressBO = lineAddressExchangeService.getCrmCompanyLine(driverGoods.getStartLineId());
                if (ObjectUtil.isNotNull(crmCompanyLineStartAddressBO)) {
                    driverGoods.setSendAddrLon(crmCompanyLineStartAddressBO.getItem1());
                    driverGoods.setSendAddrLat(crmCompanyLineStartAddressBO.getItem2());
                }
                CrmCompanyLineAddressBO crmCompanyLineEndAddressBO = lineAddressExchangeService.getCrmCompanyLine(driverGoods.getEndLineId());
                if (ObjectUtil.isNotNull(crmCompanyLineEndAddressBO)) {
                    driverGoods.setReceiveAddrLon(crmCompanyLineEndAddressBO.getItem1());
                    driverGoods.setReceiveAddrLat(crmCompanyLineEndAddressBO.getItem2());
                }
            }));
        } catch (Exception e) {
            log.error("查询设置出发地目的地地址的经纬度异常");
        }
    }

    /**
     * 司机端找货司机货源列表
     * 发货地地址不匹配
     * @param pageInfo 司机货源
     * @return 司机货源集合
     */
    @PostMapping("/findGoodsMismatchLocation")
    @ApiOperation(value = "司机端找货司机货源地址不匹配列表")
    public ResultMode<DriverGoods> findGoodsMismatchLocation(@RequestBody PagingInfo<DriverGoodsFilter> pageInfo) {
        TimeInterval timer = new TimeInterval();
        TimeInterval timer1 = new TimeInterval();
        log.info("findGoodsMismatchLocation#司机端货源大厅地址不匹配列表入参：{}", JSONUtil.toJsonStr(pageInfo));
        ResultMode<DriverGoods> resultMode = new ResultMode<>();
        List<DriverGoods> driverGoodsList = new ArrayList<>();
        String driverId = JwtUtil.getInstance().getAppDriverIdByToken();
        if (StrUtil.isBlank(driverId)) {
            ResultMode<String> userbaseinfo = platformUmUserbaseinfoInter.getDriverIdByUserBaseId();
            if (ObjectUtil.isNotNull(userbaseinfo) && userbaseinfo.getSucceed() && IterUtil.isNotEmpty(userbaseinfo.getModel())) {
                driverId = userbaseinfo.getModel().get(0);
            } else {
                //log.info("findGoods#没有拿到司机id,尝试获取司机id结果:{}", JSON.toJSONString(resultMode));
            }
        }
        if (StrUtil.isNotBlank(driverId)) {
            pageInfo.getFilterModel().setDriverIds(driverId);
        }
        //已推荐的不再查询（前端入参过滤出已推荐的货源列表）
        log.info("findGoodsMismatchLocation#pageInfo：{}", JSONUtil.toJsonStr(pageInfo));
        //设置分页参数：地址不匹配
        PageHelper.startPage(pageInfo.currentPage, pageInfo.pageLength, pageInfo.getCountTotal());
        List<DriverGoods> list = driverGoodsService.findGoodsForDriverAppMismatchLocation(pageInfo);
        log.info("findGoodsMismatchLocation#list#耗时：{}", timer1.intervalRestart());
        if (IterUtil.isNotEmpty(list)) {
            list.forEach(driverGoods -> driverGoods.setOperType("0"));
            driverGoodsList.addAll(list);
        }
        Set<String> driverGoodsIds = driverGoodsList.stream()
            .filter(driverGoods -> (StrUtil.equals(driverGoods.getOperType(), "0")))
            .map(DriverGoods::getGoodsId).collect(Collectors.toSet());
        //批量查询司机货源绑码关系：
        queryDriverGoodsBindCodeRel(driverGoodsIds, driverGoodsList);
        log.info("findGoodsMismatchLocation#lsdsIdentifyCodeRelVOs#耗时：{}", timer1.intervalRestart());

        //统一剩余重量单位
        driverGoodsList.forEach(driverGoods -> driverGoods.setPlanQuantityUnit(driverGoods.getTotalQuantityUnits()));
        //处理分页
        PageInfo<DriverGoods> returnPageInfo = new PageInfo<>(list);
        //处理返回数据
        resultMode.setTotal((int) returnPageInfo.getTotal());
        //检查是否存在关注列表
        this.checkGoodsAttention(driverId,driverGoodsList);
        log.info("findGoodsMismatchLocation#checkGoodsAttention#耗时：{}", timer1.intervalRestart());

        resultMode.setModel(driverGoodsList);
        log.info("findGoodsMismatchLocation#总耗时：{}", timer.intervalRestart());
        return resultMode;
    }

    /**
     * 司机端意向货源分页查询（首页、货源大厅）
     * 基于身份证号
     * 仅7天有效（当前时间小于等于【接单时间+7*24小时】时，展示；当前时间大于【接单时间+7*24小时】时，不展示）
     *
     * @param pageInfo 分页参数
     * @return 货源集合
     */
    @PostMapping("/appChannelQueryPage")
    @ApiOperation(value = "司机端意向货源分页查询")
    public ResultMode<DriverGoods> appChannelQueryPage(@RequestBody PagingInfo<WoaWaybillClueFilter> pageInfo) {
        TimeInterval timer = new TimeInterval();
        TimeInterval timer1 = new TimeInterval();
        log.info("appChannelQueryPage#司机端意向货源分页查询入参：{}", JSONUtil.toJsonStr(pageInfo));
        String driverId = JwtUtil.getInstance().getAppDriverIdByToken();
        if (StrUtil.isBlank(driverId)) {
            log.info("appChannelQueryPage#driverId为空，不处理");
            return ResultMode.success();
        }
        log.info("appChannelQueryPage#driverId->{}", driverId);
        // 查woa司机下渠道接单的运单线索
        String idCardNo = pageInfo.filterModel.getIdCardNo();
        if (StrUtil.isBlank(idCardNo)) {
            log.info("appChannelQueryPage#司机身份证号为空，不处理");
            return ResultMode.success();
        }
        log.info("appChannelQueryPage#idCardNo->{}", idCardNo);
        pageInfo.filterModel.setFromChannelGoods(true);
        List<WoaWaybillClueVO> woaWaybillClueList = woaExchangeService.appChannelQueryPage(pageInfo);
        log.info("appChannelQueryPage#查woa的司机下渠道接单的运单线索#耗时：{}", timer1.intervalRestart());
        if (IterUtil.isEmpty(woaWaybillClueList)) {
            log.info("appChannelQueryPage#查woa司机下渠道接单的运单线索为空，idCardNo->{}", idCardNo);
            return ResultMode.success();
        }
        // 从woa司机下渠道接单的运单线索中渠道货源（司机货源、绑码订单），继续统一结构处理前端所需数据
        ResultMode<DriverGoods> resultMode = new ResultMode<>();
        List<DriverGoods> goodsList = new ArrayList<>();
        // 查司机货源
        Set<String> driverGoodsIds = woaWaybillClueList.stream().filter(woaWaybillClueVO -> StrUtil.equals(woaWaybillClueVO.getBusType(), "10"))
            .map(WoaWaybillClueVO::getBusId).collect(Collectors.toSet());
        log.info("appChannelQueryPage#过滤司机货源结果，driverGoodsIds->{}", JSONUtil.toJsonStr(driverGoodsIds));
        if (IterUtil.isNotEmpty(driverGoodsIds)) {
            DriverGoodsFilter driverGoodsFilter = new DriverGoodsFilter();
            driverGoodsFilter.setGoodsIds(driverGoodsIds);
            List<DriverGoods> driverGoodsList = driverGoodsService.findChannelGoodsForDriverApp(driverGoodsFilter);
            log.info("appChannelQueryPage#查司机货源#耗时：{}", timer1.intervalRestart());
            if (IterUtil.isNotEmpty(driverGoodsList)) {
                goodsList.addAll(driverGoodsList);
            } else {
                log.info("appChannelQueryPage#查司机货源为空，driverGoodsIds->{}", JSONUtil.toJsonStr(driverGoodsIds));
            }
            // 查司机货源绑码关系
            if (IterUtil.isNotEmpty(goodsList)) {
                queryDriverGoodsBindCodeRel(driverGoodsIds, goodsList);
                log.info("appChannelQueryPage#查司机货源绑码关系#耗时：{}", timer1.intervalRestart());
            } else {
                log.info("appChannelQueryPage#查司机货源绑码关系为空，driverGoodsIds->{}", JSONUtil.toJsonStr(driverGoodsIds));
            }
        } else {
            log.info("appChannelQueryPage#接单司机货源不存在，idCardNo->{}", idCardNo);
        }
        // 查订单
        Set<String> orderIds = woaWaybillClueList.stream().filter(woaWaybillClueVO -> StrUtil.equals(woaWaybillClueVO.getBusType(), TmsEnum.GoodsSourceTypeEnum.COMPANY.getCode()))
            .map(WoaWaybillClueVO::getBusId).collect(Collectors.toSet());
//        // 非绑货源码订单
//        Set<String> orderNotBindIds = woaWaybillClueList.stream().filter(woaWaybillClueVO -> StrUtil.equals(woaWaybillClueVO.getBusType(), TmsEnum.GoodsSourceTypeEnum.COMPANY.getCode()) &&
//                StrUtil.isBlank(woaWaybillClueVO.getGoodsIdentityCode()))
//            .map(WoaWaybillClueVO::getBusId).collect(Collectors.toSet());
//        // 绑货源码订单
//        Set<String> orderBindIds = woaWaybillClueList.stream().filter(woaWaybillClueVO -> StrUtil.equals(woaWaybillClueVO.getBusType(), TmsEnum.GoodsSourceTypeEnum.COMPANY.getCode()) &&
//                StrUtil.isNotBlank(woaWaybillClueVO.getGoodsIdentityCode()))
//            .map(WoaWaybillClueVO::getBusId).collect(Collectors.toSet());
//        log.info("appChannelQueryPage#过滤订单结果，orderIds->{}，orderNotBindIds->{}，orderBindIds->{}", JSONUtil.toJsonStr(orderIds),JSONUtil.toJsonStr(orderNotBindIds),JSONUtil.toJsonStr(orderBindIds));
        if (IterUtil.isNotEmpty(orderIds)) {
            TmsOrderFilter tmsOrderIdFilter = new TmsOrderFilter();
            tmsOrderIdFilter.setOrderIds(new ArrayList<>(orderIds));
            List<TmsOrder> orderList = tmsExchangeService.getOrderListByOrderIds(tmsOrderIdFilter);
            log.info("appChannelQueryPage#查订单#耗时：{}", timer1.intervalRestart());
            if (IterUtil.isNotEmpty(orderList)) {
                // 查询绑货源码订单的有效调价
//                List<TmsOrder> orderBindList = orderList.stream().filter(order -> orderBindIds.contains(order.getOrderId())).collect(Collectors.toList());
                if (IterUtil.isNotEmpty(orderList)) {
                    List<LsdsGoodsAttentionVO> orderReserveList = lsdsGoodsAttentionServiceTran.getChannelGoodsBindOrderInfo(orderList, driverId);
                    log.info("appChannelQueryPage#查询绑码货源码订单的有效调价#耗时：{}", timer1.intervalRestart());
                    if (IterUtil.isNotEmpty(orderReserveList)) {
                        // 找到绑码有效订单号，绑码无效订单号加入到orderNotBindIds中
//                        Set<String> orderBindValidIds = orderReserveList.stream().map(LsdsGoodsAttentionVO::getBusinessId).collect(Collectors.toSet());
                        // orderBindIds和orderBindValidIds取差，得到无效订单号
//                        orderNotBindIds.addAll(orderBindIds.stream().filter(orderBindId -> !orderBindValidIds.contains(orderBindId)).collect(Collectors.toSet()));
//                        log.info("appChannelQueryPage#过滤出非绑码订单结果，orderBindValidIds->{}，orderNotBindIds->{}", JSONUtil.toJsonStr(orderBindValidIds),JSONUtil.toJsonStr(orderNotBindIds));
                        // orderReserveList按businessId转map
                        Map<String, LsdsGoodsAttentionVO> orderReserveMap = orderReserveList.stream().collect(Collectors.toMap(LsdsGoodsAttentionVO::getBusinessId, v -> v, (v1, v2) -> v2));
                        // 前端回显订单数据后无法跳转：把此处关注货源列表数据结构完全补上传出
                        woaWaybillClueList.forEach(waybillClue -> {
                            if (StrUtil.equals(waybillClue.getBusType(), TmsEnum.GoodsSourceTypeEnum.COMPANY.getCode())) {
                                LsdsGoodsAttentionVO attentionVO = orderReserveMap.get(waybillClue.getBusId());
                                if (attentionVO != null) {
                                    DriverGoods orderGoods = changeVO(attentionVO);
                                    goodsList.add(orderGoods);
                                }
                            }
                        });
                    } else {
//                        log.info("appChannelQueryPage#查询绑码订单的有效调价为空，orderBindIds->{}", JSONUtil.toJsonStr(orderBindIds));
//                        // 全是绑码无效订单号加入到orderNotBindIds中
//                        orderNotBindIds.addAll(orderBindIds);
//                        log.info("appChannelQueryPage#过滤非出绑码订单结果，orderNotBindIds->{}", JSONUtil.toJsonStr(orderNotBindIds));
                    }
                }
//                // 封装转换非绑码订单到司机货源结构
//                List<TmsOrder> orderNotBindList = orderList.stream().filter(order -> orderNotBindIds.contains(order.getOrderId())).collect(Collectors.toList());
//                if (IterUtil.isNotEmpty(orderNotBindList)) {
//                    goodsList.addAll(dealNoneQrCodes(orderNotBindList));
//                }
            } else {
                log.info("appChannelQueryPage#查订单为空，orderIds->{}", JSONUtil.toJsonStr(orderIds));
            }
        } else {
            log.info("appChannelQueryPage#接单订单不存在，idCardNo->{}", idCardNo);
        }
        if (IterUtil.isNotEmpty(goodsList)) {
            // 统一剩余重量单位
            goodsList.forEach(driverGoods -> driverGoods.setPlanQuantityUnit(driverGoods.getTotalQuantityUnits()));
            // 检查是否存在关注列表
            this.checkGoodsAttention(driverId, goodsList);
            log.info("appChannelQueryPage#检查是否存在关注列表#耗时：{}", timer1.intervalRestart());
        }
        PageInfo<DriverGoods> returnPageInfo = new PageInfo<>(goodsList);
        resultMode.setTotal((int) returnPageInfo.getTotal());
        resultMode.setModel(goodsList);
        log.info("appChannelQueryPage#司机端意向货源分页查询#总耗时：{}", timer.intervalRestart());
        return resultMode;
    }

    private List<DriverGoods> dealNoneQrCodes(List<TmsOrder> orderNotBindList) {
        List<DriverGoods> goodsList = new ArrayList<>();
        for (TmsOrder tmsOrder : orderNotBindList) {
            // 查oms订单地址
            ResultMode<OmsOrderAddress> omsOrderAddressResultMode = omsOrderAddressInter.getByOrderId(tmsOrder.getOrderId());
            if (!omsOrderAddressResultMode.getSucceed() || IterUtil.isEmpty(omsOrderAddressResultMode.getModel()) ||
                IterUtil.getFirst(omsOrderAddressResultMode.getModel()) == null) {
                continue;
            }
            OmsOrderAddress address = IterUtil.getFirst(omsOrderAddressResultMode.getModel());
            // 无奈的将新表的字段硬塞进原来的出参对象中
            PlatformQrCodeRelationVO qrCode = new PlatformQrCodeRelationVO();
            qrCode.setSendAddrProvinceName(address.getSendAddrProvinceName());
            qrCode.setSendAddrCityName(address.getSendAddrCityName());
            qrCode.setSendAddrAreaName(address.getSendAddrAreaName());
            qrCode.setReceiveAddrProvinceName(address.getReceiveAddrProvinceName());
            qrCode.setReceiveAddrCityName(address.getReceiveAddrCityName());
            qrCode.setReceiveAddrAreaName(address.getReceiveAddrAreaName());
            qrCode.setGoodsName(tmsOrder.getGoodsName());
            qrCode.setGoodsId(tmsOrder.getGoodsId());
            qrCode.setTransportationType(tmsOrder.getTransportationType());
            qrCode.setShipperCompanyId(tmsOrder.getShipperCompanyId());
            qrCode.setShipperName(tmsOrder.getShipperName());
            if (tmsOrder.getWeightSum() != null) {
                qrCode.setWeightSum(String.valueOf(tmsOrder.getWeightSum()));
            }
            if (tmsOrder.getVolumeSum() != null) {
                qrCode.setVolumeSum(String.valueOf(tmsOrder.getVolumeSum()));
            }
            if (tmsOrder.getTotalGoods() != null) {
                qrCode.setTotalGoods(String.valueOf(tmsOrder.getTotalGoods()));
            }
            qrCode.setReleaseDate(tmsOrder.getReleaseDate());
            qrCode.setArriveDate(tmsOrder.getArriveDate());
            qrCode.setOrderStatus(tmsOrder.getOrderStatus());
            qrCode.setOtherKuiTonsRatio(tmsOrder.getOtherKuiTonsRatio());
            qrCode.setOtherRemark(tmsOrder.getOtherRemark());
            //指定信息
            qrCode.setContainerType(tmsOrder.getContainerType());
            qrCode.setContainerNumber(tmsOrder.getContainerNumber());
            qrCode.setAssignCarType(tmsOrder.getAssignCarType());
            if (tmsOrder.getAssignCarLength() != null) {
                qrCode.setAssignCarLength(String.valueOf(tmsOrder.getAssignCarLength()));
            }
            qrCode.setAssignCarPlateNumber(tmsOrder.getAssignCarPlateNumber());
            qrCode.setPackType(tmsOrder.getPackType());
            // 查oms订单
            OmsOrder omsOrder = omsOrderInter.getOmsOrderByOrderId(tmsOrder.getOrderId());
            if (omsOrder != null) {
                qrCode.setEnquiryTypeBasePrice(omsOrder.getEnquiryTypeBasePrice());
            }
            qrCode.setTotalQuantityUnits(tmsOrder.getOrderQuantityUnits());
            qrCode.setRemainingQuantity(tmsOrder.getRemainingQuantity());
            if (StrUtil.equals("1", tmsOrder.getEnquiryType())) {
                qrCode.setEnquiryType(TmsEnum.EnquiryTypeEnum.TEN.getCode());
            }
            if (StrUtil.equals("2", tmsOrder.getEnquiryType())) {
                qrCode.setEnquiryType(TmsEnum.EnquiryTypeEnum.TWENTY.getCode());
            }
            qrCode.setStartSiteCityName(address.getSendAddrShorthand());
            qrCode.setEndSiteCityName(address.getReceiveAddrShorthand());
            qrCode.setValidityDate(tmsOrder.getOrderExpireDate());
            qrCode.setBusinessId(tmsOrder.getOrderId());
            qrCode.setBusinessType(TmsEnum.GoodsSourceTypeEnum.COMPANY.getCode());
            qrCode.setBindStatus(QrsEnum.BindStatusEnum.UNBIND.getCode());

            LsdsGoodsAttentionVO attentionVO = WlydMapUtil.map(qrCode, LsdsGoodsAttentionVO.class);
            //统一剩余重量单位
            attentionVO.setCreateDate(tmsOrder.getCreateDate());
            attentionVO.setOperType(LsdsEnum.LsdsgoodsAttentionGoodsType.GOODS_TYPE_ORDER.getCode());
            attentionVO.setAttentionType(LsdsEnum.LsdsgoodsAttentionType.ATTENTION_TYPE_00.getCode());
            DriverGoods orderGoods = changeVO(attentionVO);
            goodsList.add(orderGoods);
        }
        return goodsList;
    }

    private DriverGoods changeVO(LsdsGoodsAttentionVO attentionVO) {
        LsdsGoodsAttentionChangeVO changeVO = new LsdsGoodsAttentionChangeVO();
        BeanUtils.copyProperties(attentionVO, changeVO);
        Opt.ofNullable(attentionVO.getReleaseDate()).ifPresent(releaseDate -> changeVO.setReleaseDate(DateUtil.format(releaseDate, DatePattern.NORM_DATETIME_PATTERN)));
        Opt.ofNullable(attentionVO.getArriveDate()).ifPresent(arriveDate -> changeVO.setArriveDate(DateUtil.format(arriveDate, DatePattern.NORM_DATETIME_PATTERN)));
        Opt.ofNullable(attentionVO.getPriceStartTime()).ifPresent(priceStartTime -> changeVO.setPriceStartTime(DateUtil.format(priceStartTime, DatePattern.NORM_DATETIME_PATTERN)));
        Opt.ofNullable(attentionVO.getPriceEndTime()).ifPresent(priceEndTime -> changeVO.setPriceEndTime(DateUtil.format(priceEndTime, DatePattern.NORM_DATETIME_PATTERN)));
        attentionVO.setAttentionObj(JSONUtil.parseObj(changeVO));
        DriverGoods orderGoods = BeanUtil.toBean(attentionVO, DriverGoods.class, CopyOptions.create().ignoreNullValue().ignoreError());
        return orderGoods;
    }

    /**
     * 批量查询司机货源绑码关系
     * @param goodsIds
     * @param driverGoodsList
     */
    private void queryDriverGoodsBindCodeRel(Set<String> goodsIds, List<DriverGoods> driverGoodsList) {
        if (IterUtil.isNotEmpty(goodsIds)) {
            IdentifyCodeRelFilter identifyCodeRelFilter = new IdentifyCodeRelFilter();
            identifyCodeRelFilter.setGoodsIds(goodsIds);
            List<LsdsIdentifyCodeRelVO> lsdsIdentifyCodeRelVOs = lsdsIdentifyCodeRelService.queryListByGoodsIds(identifyCodeRelFilter);
            if (IterUtil.isNotEmpty(lsdsIdentifyCodeRelVOs)) {
                Map<String, List<LsdsIdentifyCodeRelVO>> relMap = lsdsIdentifyCodeRelVOs.stream()
                    .collect(Collectors.groupingBy(LsdsIdentifyCodeRelVO::getGoodsId));
                driverGoodsList.stream().forEach(good -> {
                    if (IterUtil.isNotEmpty(relMap.get(good.getGoodsId()))) {
                        good.setQrsCodeRelations(relMap.get(good.getGoodsId()));
                    }
                });
            }
        }
    }

    /**
     * 热货推荐10条
     * @param pageInfo
     * @param waybillId
     * @param driverId
     */
    private List<DriverGoods> queryDriverGoodsRecommend(PagingInfo<DriverGoodsFilter> pageInfo, String waybillId, String driverId) {
        if (pageInfo.getCurrentPage() == 1 && StrUtil.isNotBlank(driverId) && StrUtil.isNotBlank(waybillId)) {
            GoodsRecommendReqVo recommendReq = new GoodsRecommendReqVo();
            recommendReq.setWaybillId(waybillId);
            recommendReq.setDriverId(driverId);
            recommendReq.setSceneType(10);
            // 司机端货源大厅区分是否选择查询条件的出发地或目的地：选择了就用该条件过滤数据
            Boolean searchFlag = pageInfo.getFilterModel().getSearchFlag();
            if (BooleanUtil.isTrue(searchFlag)) {
                if (StrUtil.isNotEmpty(pageInfo.getFilterModel().getSendAddrCity())) {
                    recommendReq.setSendAddrCity(pageInfo.getFilterModel().getSendAddrCity());
                }
                if (StrUtil.isNotEmpty(pageInfo.getFilterModel().getReceiveAddrCity())) {
                    recommendReq.setReceiveAddrCity(pageInfo.getFilterModel().getReceiveAddrCity());
                }
                if (IterUtil.isNotEmpty(pageInfo.getFilterModel().getGoodsTypeList())) {
                    recommendReq.setGoodsTypeList(pageInfo.getFilterModel().getGoodsTypeList());
                }
            }
            ResultMode<LsdsGoodsAttentionVO> goodsRecommend = goodsAttentionServiceTran.getGoodsRecommendInfo(recommendReq);
            if (ObjectUtil.isNotNull(goodsRecommend) && IterUtil.isNotEmpty(goodsRecommend.getModel())) {
                //找货和首页回显订单数据后无法跳转：把此处关注货源列表数据结构完全补上传出
                goodsRecommend.getModel().forEach(attentionVO -> {
                    if (StrUtil.equals(LsdsEnum.LsdsgoodsAttentionGoodsType.GOODS_TYPE_ORDER.getCode(), attentionVO.getOperType())) {
                        LsdsGoodsAttentionChangeVO changeVO = new LsdsGoodsAttentionChangeVO();
                        BeanUtils.copyProperties(attentionVO, changeVO);
                        Opt.ofNullable(attentionVO.getReleaseDate()).ifPresent(releaseDate -> changeVO.setReleaseDate(DateUtil.format(releaseDate, DatePattern.NORM_DATETIME_PATTERN)));
                        Opt.ofNullable(attentionVO.getArriveDate()).ifPresent(arriveDate -> changeVO.setArriveDate(DateUtil.format(arriveDate, DatePattern.NORM_DATETIME_PATTERN)));
                        Opt.ofNullable(attentionVO.getPriceStartTime()).ifPresent(priceStartTime -> changeVO.setPriceStartTime(DateUtil.format(priceStartTime, DatePattern.NORM_DATETIME_PATTERN)));
                        Opt.ofNullable(attentionVO.getPriceEndTime()).ifPresent(priceEndTime -> changeVO.setPriceEndTime(DateUtil.format(priceEndTime, DatePattern.NORM_DATETIME_PATTERN)));
                        attentionVO.setAttentionObj(JSONUtil.parseObj(changeVO));
                    }
                });
                return BeanUtil.copyToList(goodsRecommend.getModel(), DriverGoods.class, CopyOptions.create().ignoreNullValue().ignoreError());
            }
            return null;
        }
        return null;
    }

    /**
     * 查询常跑路线推荐货源
     *
     * @param pageInfo
     * @param driverGoodsList
     * @param driverId
     */
    private void queryDriverLineSupply(PagingInfo<DriverGoodsFilter> pageInfo, List<DriverGoods> driverGoodsList, String driverId) {
        if (pageInfo.getCurrentPage() == 1 && StrUtil.isNotBlank(driverId)) {
            List<DriverGoods> driverGoodsList1 = new ArrayList<>();
            // 按照“更新时间”最新1条进行匹配
            LsdsDriverLines lsdsDriverLinesFilter = new LsdsDriverLines();
            lsdsDriverLinesFilter.setDriverId(driverId);
            lsdsDriverLinesFilter.setSendAddrCity(pageInfo.getFilterModel().getSendAddrCity());
            lsdsDriverLinesFilter.setReceiveAddrCity(pageInfo.getFilterModel().getReceiveAddrCity());
            log.info("第一条常跑路线入参：{}", JSON.toJSONString(lsdsDriverLinesFilter));
            LsdsDriverLines lsdsDriverLines = lsdsDriverLinesService.queryOneByDriverIdOrderByModifyDateDesc(lsdsDriverLinesFilter);
            log.info("第一条常跑路线结果：{}", JSON.toJSONString(lsdsDriverLines));
            if (null == lsdsDriverLines) {
                return;
            }
            int limitCount = 3;
            Date modifyDate = lsdsDriverLines.getModifyDate();
            PagingInfo<DriverGoodsFilter> pageInfo1 = new PagingInfo<>();
            DriverGoodsFilter filter = new DriverGoodsFilter();
            filter.setGoodsTypeList(pageInfo.getFilterModel().getGoodsTypeList());
            filter.setDriverIds(driverId);
            pageInfo1.setFilterModel(filter);
            // 更新时间早于当前时间12小时内 货源创建时间取最新2条-主推回城货
            if (DateUtil.between(new Date(), modifyDate, DateUnit.HOUR) < 12) {
                PageHelper.startPage(1, 2);
                pageInfo1.getFilterModel().setSendAddrCity(lsdsDriverLines.getReceiveAddrCity());
                pageInfo1.getFilterModel().setReceiveAddrCity(lsdsDriverLines.getSendAddrCity());
                //列表查询
                List<DriverGoods> list = driverGoodsService.findGoods(pageInfo1);
                if (IterUtil.isNotEmpty(list)) {
                    limitCount -= list.size();
                    driverGoodsList1.addAll(list);
                }
            }
            PageHelper.startPage(1, limitCount);
            pageInfo1.getFilterModel().setSendAddrCity(lsdsDriverLines.getSendAddrCity());
            pageInfo1.getFilterModel().setReceiveAddrCity(lsdsDriverLines.getReceiveAddrCity());
            List<DriverGoods> list = driverGoodsService.findGoods(pageInfo1);
            if (IterUtil.isNotEmpty(list)) {
                driverGoodsList1.addAll(list);
            }
            //log.info("第一条常跑路线 货源结果：{}", JSON.toJSONString(driverGoodsList1));
            // 匹配，按照货源创建时间倒序；上一个结果大于0
            if (IterUtil.isNotEmpty(driverGoodsList1)) {
                driverGoodsList1.forEach(item -> item.setLineSupply("1"));
                driverGoodsList1.sort(Comparator.comparing(DriverGoods::getCreateDate).reversed());
                driverGoodsList.addAll(driverGoodsList1);
                return;
            }
            // 不匹配（1阶段）上一个结果是0
            // 关联所有常用路线，按常用路线的更新时间倒序，取前3条
            List<DriverGoods> goodsForLine = driverGoodsService.findGoodsForLine(pageInfo);
            //log.info("所有常跑路线 货源结果：{}", JSON.toJSONString(goodsForLine));
            if (IterUtil.isNotEmpty(goodsForLine)) {
                driverGoodsList.addAll(goodsForLine);
            }
        }
    }

    /**
     * 推荐运力列表
     * 匹配规则
     * 按常用路线更新时间倒序，累计总查询数量，最大取100条；
     * 1 货源（出发地-目的地），匹配，司机常用路线（出发地-目的地），过滤删除状态
     * 2 货源（出发地-目的地），匹配，司机常用路线（目的地-出发地），过滤删除状态
     * 3 货源（出发地），匹配，司机常用路线（出发地），过滤删除状态
     */
    @RequestMapping(value = "/queryHotCar", method = RequestMethod.POST)
    public ResultMode<DriverGoodsHotCar> queryHotCar(@RequestBody DriverGoodsHotCar driverGoodsHotCar) {
        String goodsId = driverGoodsHotCar.getGoodsId();
        Assert.notBlank(goodsId, "货源ID不能为空");
        List<DriverGoodsHotCar> resultList = new ArrayList<>();
        DriverGoodsFilter driverGoodsFilter = new DriverGoodsFilter();
        driverGoodsFilter.setGoodsId(goodsId);
        // 查询货源出发地、目的地code
        DriverGoods driverGoods = driverGoodsService.findGoodsCityById(goodsId);
        if (driverGoods == null) {
            return new ResultMode<>(resultList);
        }
        int needQueryNum = 100;
        List<String> nearbyDriverIds = null;
        //查询附件运力
        List<DriverGoodsHotCar> driverGoodsHotCars = lsdsCommonLinesService.queryNearbyTransport(driverGoods);
        if(CollUtil.isNotEmpty(driverGoodsHotCars)){
            //车辆类型处理
            assembleDriverGoodsHotCar(driverGoodsHotCars);
            needQueryNum = needQueryNum-driverGoodsHotCars.size();
            if(needQueryNum == 0){
                return new ResultMode(driverGoodsHotCars);
            }
            resultList.addAll(driverGoodsHotCars);
            nearbyDriverIds = driverGoodsHotCars.stream().map(d ->d.getDriverId()).collect(Collectors.toList());
        }
        long stTime = System.currentTimeMillis();
        List<LsdsDriverLines> driverLines = getRecommendDriverLines(driverGoods.getSendAddrCity(), driverGoods.getReceiveAddrCity(),needQueryNum,nearbyDriverIds);
        log.info("queryHotCar#getRecommendDriverLines size={},time={}", driverLines.size(), System.currentTimeMillis() - stTime);
        //组装数据
        assembleDriverLines(resultList, driverLines,goodsId);
        log.info("queryHotCar#assembleDriverLines size={},time={}", driverLines.size(), System.currentTimeMillis() - stTime);

        return new ResultMode(resultList);
    }
    /**
     * 查询推荐司机线路
     *
     * @param sendAddrCity    发货城市
     * @param receiveAddrCity 收货城市
     * @param needQueryNum
     * @param nearbyDriverIds
     * @return {@link List}<{@link LsdsDriverLines}>
     */
    private List<LsdsDriverLines> getRecommendDriverLines(String sendAddrCity, String receiveAddrCity, int needQueryNum, List<String> nearbyDriverIds) {
        if (StrUtil.isEmpty(sendAddrCity) || StrUtil.isEmpty(receiveAddrCity)) {
            return new ArrayList<>();
        }
        LsdsDriverLines lsdsDriverLines = new LsdsDriverLines();
        lsdsDriverLines.setSendAddrCity(sendAddrCity);
        lsdsDriverLines.setReceiveAddrCity(receiveAddrCity);
        lsdsDriverLines.setNeedQueryNum(needQueryNum);
        lsdsDriverLines.setNearbyDriverIds(nearbyDriverIds);
        return lsdsDriverLinesService.queryHotCar(lsdsDriverLines);
    }

    // 组装返回 运力列表
    private void assembleDriverLines(List<DriverGoodsHotCar> resultList, List<LsdsDriverLines> driverLines, String goodsId) {
        DriverGoodsHotCar hotCar;
        if (IterUtil.isNotEmpty(driverLines)) {
            List<TcsCarPlateNoAndColorFilter> query = driverLines.stream().map(item->{
                TcsCarPlateNoAndColorFilter filter=new TcsCarPlateNoAndColorFilter();
                filter.setPlateNo(item.getPlateNumber());
                filter.setPlateColor(item.getCarColor());
                item.setDriverId(item.getDriverId());
                return filter;
            }).collect(Collectors.toList());
            DriverGoods driverGoods = driverGoodsService.getByGoodsId(goodsId);
            String startLineId = driverGoods.getStartLineId();
            CrmCompanyLineAddressBO crmCompanyLine = crmExchangeService.getCrmCompanyLine(startLineId);

            List<String> driversIds = driverLines.stream().map(LsdsDriverLines::getDriverId).collect(Collectors.toList());

            Map<String, TcsCarTractorVO> carMap =  tcsExchangeService.queryCarByPlateNoAndColors(query);

            //根据司机ID集合获取司机信息
            Map<String, TcsDriverVO> driverMap = tcsExchangeService.getDriverMapByDriverIds(driversIds);

            // 根据司机编号获取司机评级
            ResultMode<EvalDriverInfo> evalList = evalDriverInfoInter.selectByDriverIds(driversIds);

            for (LsdsDriverLines driverLine : driverLines) {
                hotCar = new DriverGoodsHotCar();
                //根据车辆ID获取司机信息
                TcsCarTractorVO car = carMap.get(driverLine.getPlateNumber()+driverLine.getCarColor());
                if (car != null) {
                    hotCar.setCarPlateNo(car.getPlateNo());
                    hotCar.setCarType(car.getCarType());
                    hotCar.setCarColor(car.getPlateColor());
                    TcsCarTrailerVO tcsCarTrailerVO = car.getTcsCarTrailerVO();
                    if (tcsCarTrailerVO != null) {
                        hotCar.setTrailerCarPlateNo(tcsCarTrailerVO.getPlateNo());
                        hotCar.setTrailerCarType(tcsCarTrailerVO.getCarType());
                    }
                }
                //根据司机ID获取司机信息
                TcsDriverVO driver = driverMap.get(driverLine.getDriverId());
                if (driver != null) {
                    hotCar.setDriverId(driverLine.getDriverId());
                    hotCar.setDriverName(driver.getDriverName());
                    hotCar.setDriverPhone(driver.getDriverPhoneNumber());
                }

                // 根据司机编号获取司机评级
                if (evalList.getSucceed() && CollectionUtils.isNotEmpty(evalList.getModel())) {
                    Double driverRating = evalList.getModel().stream().filter(eval -> driverLine.getDriverId().equals(eval.getDriverId())).map(EvalDriverInfo::getEvalDriverLevel)
                        .findAny()
                        .orElse(null);
                    hotCar.setDriverRating(driverRating);
                }
                hotCar.setSendAddrCityName(driverLine.getSendAddrCityName());
                hotCar.setReceiveAddrCityName(driverLine.getReceiveAddrCityName());
                hotCar.setModifyDate(driverLine.getModifyDate());
                hotCar.setBusinessNum(driverLine.getBusinessNum());
                hotCar.setTransportType(TransportTypeEnum.TYPE_20.getType());
                LatLng fromPoint = new LatLng(Double.valueOf(crmCompanyLine.getItem1()),Double.valueOf(crmCompanyLine.getItem2()));

                if(StrUtil.isAllNotBlank(driverLine.getReceiveLng(),driverLine.getReceiveLat())){
                    LatLng toPoint = new LatLng(Double.valueOf(driverLine.getReceiveLng()),Double.valueOf(driverLine.getReceiveLat()));
                    long distance = DistanceUtils.measureDistance(fromPoint, toPoint);
                    BigDecimal dis = new BigDecimal(distance).divide(new BigDecimal(1000), 3, RoundingMode.HALF_UP).stripTrailingZeros();
                    hotCar.setXcDistance(dis.toPlainString());
                }
                hotCar.setXcAddress(driverLine.getReceiveAddrProvinceName()+driverLine.getReceiveAddrCityName());
                resultList.add(hotCar);
            }
            Comparator<DriverGoodsHotCar> comparator = Comparator
                .comparing(DriverGoodsHotCar::getModifyDate, Comparator.nullsLast(Comparator.reverseOrder()))
                .thenComparing(DriverGoodsHotCar::getXcDistance, Comparator.nullsLast(Comparator.naturalOrder()));

            resultList = resultList.stream()
                .sorted(comparator).collect(Collectors.toList());
        }
    }

    @RequestMapping(value = "/exportDriverGoods", method = RequestMethod.POST)
    public void exportDriverGoods(@RequestBody @ApiParam  DriverGoodsFilter driverGoodsFilter) {
        driverGoodsService.exportDriverGoods(driverGoodsFilter);
    }

    @RequestMapping(value = "/exportWayBill", method = RequestMethod.POST)
    public void exportWayBill(@RequestBody @ApiParam DriverGoodsFilter driverGoodsFilter) {
        driverGoodsService.exportWayBill(driverGoodsFilter);
    }

    /**
     * @return
     * <AUTHOR>
     * @Description 关闭货源
     * @Date 2021/6/17
     * @Param
     **/
    @RequestMapping(value = "/closeDriverGoods", method = RequestMethod.POST)
    public ResultMode closeDriverGoods(@RequestBody @ApiParam DriverGoodsFilter driverGoodsFilter) {
//        return driverGoodsService.closeDriverGoods(driverGoodsFilter);
        if (StrUtil.isBlank(driverGoodsFilter.getGoodsId())) {
            return ResultMode.fail("货源ID不能为空！！");
        }
        return goodsSourceAppService.closeGoods(driverGoodsFilter.getGoodsId());
    }


    @PostMapping("/getByGoodsId")
    public ResultMode<DriverGoods> getByGoodsId(@RequestBody DriverGoodsFilter driverGoodsFilter) {
        log.info("goodsId:{}", driverGoodsFilter.getGoodsId());
        if (org.springframework.util.StringUtils.hasText(driverGoodsFilter.getGoodsId())) {
            DriverGoods driverGoods = driverGoodsService.getByGoodsId(driverGoodsFilter.getGoodsId());
            if (!ObjectUtils.isEmpty(driverGoods)) {
                return ResultMode.success(driverGoods);
            }
        }
        return ResultMode.fail("根据货源id查询司机货源失败！！");

    }

    @RequestMapping(value = "/getDriverGoodsListByGoodsIdList", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public List<DriverGoods> getDriverGoodsListByGoodsIdList(@RequestBody List<String> goodsIdList) {
        return driverGoodsService.getDriverGoodsListByGoodsIdList(goodsIdList);
    }

    @PostMapping("/getGoodsBasePriceById")
    public ResultMode<DriverGoods> getGoodsBasePriceById(@RequestParam String goodsId) {
        try {
            List<DriverGoods> driverGoods = driverGoodsService.getGoodsBasePriceById(goodsId);
            if (driverGoods.size() > 0 && driverGoods.get(0) != null) {
                return ResultMode.success(driverGoods.get(0));
            }
        } catch (Exception e) {
            log.error("获取司机货源基价失败,原因：{}", e.getMessage());
            e.printStackTrace();
        }
        return ResultMode.fail("获取司机货源基价失败");
    }


    /**
     * 校验常用地址中保存的地址经纬度有效性
     * <p>
     * 1.起始地目的地不可以相同
     * 2.经纬度有效
     * 3.半径距离之和大于经纬度距离
     *
     * @param startLineId crm库中的常用地址Id
     * @param endLineId   crm库中的常用地址Id
     * @return trur/false
     */
    private void checkCrmCompanyLineAddress(DriverGoods driverGoods, String startLineId, String endLineId) throws WlydException{
        if (org.apache.commons.lang3.StringUtils.equals(startLineId, endLineId)) {
            log.info("起始地目的地地址相同!");
            throw new WlydException("起始地目的地地址相同!", "500");
        }

        // 校验地址经纬度谁否可用
        Double[] startGeo = null;
        Double[] endGeo = null;

        CrmCompanyLineAddressFilter addressFilter = new CrmCompanyLineAddressFilter();
        addressFilter.setLineId(startLineId);
        ResultMode<CrmCompanyLineAddress> startResult = crmCompanyLineAddressFeignInter.getLineAddressByLineId(addressFilter);

        addressFilter = new CrmCompanyLineAddressFilter();
        addressFilter.setLineId(endLineId);
        ResultMode<CrmCompanyLineAddress> endResult = crmCompanyLineAddressFeignInter.getLineAddressByLineId(addressFilter);
        try {
                startGeo = getCrmCompanyLineAddress(startResult);
                endGeo = getCrmCompanyLineAddress(endResult);
                if (startGeo[0] == endGeo[0] && startGeo[1] == endGeo[1]) {
                    log.info("常用地址信息重复，请到常用地址中维护相关信息!");
                    throw new WlydException("常用地址信息重复，请到常用地址中维护相关信息!", "500");
                }

            //短倒，出发地。目的地距离不能小于1KM
            if (StrUtil.equals(driverGoods.getTransportationType(), "112")) {
                //单位米
                long shortDistance = 1000;
                ResultMode<PlatformCmPlatformParameter> paraCodes = platformCmPlatformParameterInter.getByParaCodes(Collections.singletonList(PlatformParamEnum.SHORT_TIPS.getCode()));
                if (!Objects.isNull(paraCodes) && CollectionUtils.isNotEmpty(paraCodes.getModel())) {
                    String paraValue = paraCodes.getModel().get(0).getParaValue();
                    shortDistance = StringUtils.isBlank(paraValue) ? shortDistance : Long.parseLong(paraValue)*1000;
                }

                LatLng startLng = new LatLng(startGeo[0], startGeo[1]);
                LatLng endLng = new LatLng(endGeo[0], endGeo[1]);
                long measureDistance = DistanceUtils.measureDistance(startLng, endLng);
                    if (measureDistance < shortDistance) {
                        String errorMsg =  StrUtil.format(StatusCodeEnum.DISTANCE_TRAN_ERROR.getTipMsg(),shortDistance/1000);
                        log.error(errorMsg);
                        throw new WlydException(errorMsg, StatusCodeEnum.DISTANCE_TRAN_ERROR.getCode());
                    }
            }
        } catch (WlydException e) {
            throw e;
        }
    }

    private Double[] getCrmCompanyLineAddress(ResultMode<CrmCompanyLineAddress> addressResultMode) throws WlydException {
        CrmCompanyLineAddress address = null;
        if (null != addressResultMode
            || !addressResultMode.getSucceed()
            || null != addressResultMode.getModel()
            || addressResultMode.getModel().size() > 0
            || null != addressResultMode.getModel().get(0)) {
            address = addressResultMode.getModel().get(0);
            if(address==null){
                throw new WlydException("常用地址信息获取失败", "500");
            }
        } else {
            log.info("常用地址信息获取失败");
            throw new WlydException("常用地址信息获取失败", "500");
        }

        return getCrmCompanyLineAddress(address);
    }

    private Double[] getCrmCompanyLineAddress(CrmCompanyLineAddress address) throws WlydException {
        Double longitude = 0.0;
        Double latitude = 0.0;
        Double[] result = new Double[2];
        String exception = String.format("常用地址 %s 经纬度为空，请在常用地址列中维护经纬度后再发布货源！", address.getLineShortName());
        try {
            if (null == address.getItem1() || address.getItem1().trim().length() < 1) {
                log.info("常用地址\"{}\"的经度 - 无法获取", address.getLineShortName());
                throw new WlydException(exception, "500");
            }
            if (null == address.getItem2() || address.getItem2().trim().length() < 1) {
                log.info("常用地址\"{}\"的纬度 - 无法获取", address.getLineShortName());
                throw new WlydException(exception, "500");
            }

            longitude = Double.parseDouble(address.getItem1());
            log.info("常用地址{}的经度 - 正常获取 :{}", address.getLineShortName(), longitude);
            latitude = Double.parseDouble(address.getItem2());
            log.info("常用地址{}的纬度 - 正常获取: {}", address.getLineShortName(), latitude);

            result[0] = longitude;
            result[1] = latitude;
        } catch (NumberFormatException e) {
            log.info(exception);
            throw new WlydException(exception, "500");
        }
        return result;
    }

    @RequestMapping(value = "/commonLineTransportmileageRepair", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> commonLineTransportmileageRepair() {
        //查询所有运输距离和耗时为空的数据
        try {
            driverGoodsDataFix();

            lsdsGoodsFix();

            powOrderFix();
            return ResultMode.success();
        } catch (Exception e) {
            log.error("commonLineTransportmileageRepair-修复运输距离和耗时为空的数据接口异常！！");
            e.printStackTrace();
        }

        return ResultMode.fail("commonLineTransportmileageRepair-修复运输距离和耗时为空的数据接口异常");
    }

    @RequestMapping(value = "/getRoundingMode", method = RequestMethod.POST)
    public ResultMode<Map<String, Integer>> getRoundingMode(@RequestBody List<String> busiIdList) {
        if (IterUtil.isEmpty(busiIdList)) {
            return ResultMode.success();
        }
        Map<String, Integer> roundingModeMap = new HashMap<>();
        if (busiIdList.size() == 1) {
            //查询1个业务id的情况
            int roundingMode = amountRoundingModeService.getRoundingMode(busiIdList.get(0));
            roundingModeMap.put(busiIdList.get(0), roundingMode);
            return new ResultMode<>(roundingModeMap);
        }
        roundingModeMap = amountRoundingModeService.getRoundingMode(busiIdList);
        for (String busiId : busiIdList) {
            if (!roundingModeMap.containsKey(busiId)) {
                roundingModeMap.put(busiId, BigDecimal.ROUND_UP);
            }
        }
        return new ResultMode<>(roundingModeMap);
    }

    private void powOrderFix() {
        List<OmsOrder> omsOrderList = omsOrderInter.getTransportmileageOrTakeTimeNullData();
        if (CollectionUtils.isNotEmpty(omsOrderList)) {
            omsOrderList.stream().forEach(e -> {
                OmsOrderAddress omsOrderAddress = omsOrderInter.getOmsOrderAddressInfo(e.getOrderId());
                String sendAddress = String.format("%s%s%s%s%s", omsOrderAddress.getSendAddrProvinceName(), omsOrderAddress.getSendAddrCityName(), omsOrderAddress.getSendAddrAreaName(), omsOrderAddress.getsendAddrStreetName(), omsOrderAddress.getSendAddrDetail());
                String receiveAddress = String.format("%s%s%s%s%s", omsOrderAddress.getReceiveAddrProvinceName(), omsOrderAddress.getReceiveAddrCityName(), omsOrderAddress.getReceiveAddrAreaName(), omsOrderAddress.getReceiveAddrStreetName(), omsOrderAddress.getReceiveAddrDetail());
                log.info("sendAddress3:{},-----receiveAddress3:{}", sendAddress, receiveAddress);
                //调用高德
                if (!org.apache.commons.lang3.StringUtils.isAnyEmpty(sendAddress, receiveAddress)) {
                    Map<String, String> mapRes = getDistanceAndTakeTimes(sendAddress.trim(), receiveAddress.trim());

                    if (MapUtils.isNotEmpty(mapRes)) {
                        String distance = (String) mapRes.get("distance");
                        String duration = (String) mapRes.get("duration");
                        log.info(">>>>>>>>>>>>>distance:{},duration:{}", distance, duration);
                        //更新货源
                        if (!StringUtils.isEmpty(distance)) {
                            e.setTransportMileage(Integer.parseInt(distance));
                        }
                        if (!StringUtils.isEmpty(duration)) {
                            e.setTakeUpTime(Integer.parseInt(duration));
                        }
                        omsOrderInter.updateOrderTransportMleage(e);
                        log.info(">>>>>>>>>>>>>>>>>>>>耗时更新完成！！！！！>>>>>>>>>>>>>>>>>>>");
//                        TmsWaybill queryC = new TmsWaybill();
//                        List<String> updateList = new ArrayList();
//                        updateList.add(e.getOrderId());
//                        queryC.setGoodsIds(updateList);
//                        queryC.setTransportMileage(Integer.parseInt(distance));
//                        tmsWaybillInter.updateTransportmileageByOrderId(queryC);
                        if (!StringUtils.isEmpty(distance)) {
                            tmsWaybillInter.transportMileageUpdateByOrderId(e.getOrderId(), distance);
                        }


                    }
                }


            });
        }
    }

    private void lsdsGoodsFix() {
        List<LsdsGoods> lsdsGoods = lsdsGoodsTran.getTransportmileageOrTakeTimeNullData();
        if (CollectionUtils.isNotEmpty(lsdsGoods)) {
            lsdsGoods.stream().forEach(e -> {
                List<String> list = new ArrayList<>();
                list.add(e.getGoodsAddressId());
                List<LsdsGoodsAddress> lsdsGoodsAddressL = lsdsGoodsAddressRepository.findLsdsGoodsAddressByIds(list);
                if (CollectionUtils.isNotEmpty(lsdsGoodsAddressL)) {
                    LsdsGoodsAddress lsdsGoodsAddress = lsdsGoodsAddressL.get(0);
                    String sendAddress = String.format("%s%s%s%s%s", lsdsGoodsAddress.getSendAddrProvinceName(), lsdsGoodsAddress.getSendAddrCityName(), lsdsGoodsAddress.getSendAddrAreaName(), lsdsGoodsAddress.getSendAddrStreetName(), lsdsGoodsAddress.getSendAddrDetail());
                    String receiveAddress = String.format("%s%s%s%s%s", lsdsGoodsAddress.getReceiveAddrProvinceName(), lsdsGoodsAddress.getReceiveAddrCityName(), lsdsGoodsAddress.getReceiveAddrAreaName(), lsdsGoodsAddress.getReceiveAddrStreetName(), lsdsGoodsAddress.getReceiveAddrDetail());
                    log.info("sendAddress2:{},-----receiveAddress2:{}", sendAddress, receiveAddress);
                    //调用高德
                    if (!org.apache.commons.lang3.StringUtils.isAnyEmpty(sendAddress, receiveAddress)) {

                        Map<String, String> mapRes = getDistanceAndTakeTimes(sendAddress.trim(), receiveAddress.trim());
                        if (MapUtils.isNotEmpty(mapRes)) {
                            String distance = (String) mapRes.get("distance");
                            String duration = (String) mapRes.get("duration");
                            log.info(">>>>>>>>>>>>>distance:{},duration:{}", distance, duration);
                            //更新货源
                            if (!StringUtils.isEmpty(distance)) {
                                e.setTransportMileage(Integer.parseInt(distance));
                            }
                            if (!StringUtils.isEmpty(duration)) {
                                e.setTakeUpTime(Integer.parseInt(duration));
                            }
                            GoodsMapper dao = SpringContextUtil.getBeanByClass(GoodsMapper.class);
                            dao.updateTransportmileage(e);

                            TmsWaybill queryC = new TmsWaybill();
                            List<String> updateList = new ArrayList();
                            updateList.add(e.getGoodsId());
                            queryC.setGoodsIds(updateList);
                            queryC.setTransportMileage(Integer.parseInt(distance));
                            //根据更新运单
                            tmsWaybillInter.updateTransportmileageByGoodsId(queryC);
                        }

                    }
                }


            });
        }
    }

    private void driverGoodsDataFix() {
        List<DriverGoods> driverGoods = driverGoodsService.getTransportmileageOrTakeTimeNullData();
        if (CollectionUtils.isNotEmpty(driverGoods)) {
            driverGoods.stream().forEach(e -> {
                //拼接发货地址
                // uploadResUrl = String.format("%s%s",sufferRuL,fileName);
                String sendAddress = String.format("%s%s%s%s", e.getSendAddrProvinceName(), e.getSendAddrCityName(), e.getSendAddrAreaName(), e.getStartSiteAddress());
                String receiveAddress = String.format("%s%s%s%s", e.getReceiveAddrProvinceName(), e.getReceiveAddrCityName(), e.getReceiveAddrAreaName(), e.getEndSiteAddress());
                log.info("sendAddress1:{},-----receiveAddress1:{}", sendAddress, receiveAddress);
                //调用高德
                if (!org.apache.commons.lang3.StringUtils.isAnyEmpty(sendAddress, receiveAddress)) {
                    Map<String, String> mapRes = getDistanceAndTakeTimes(sendAddress.trim(), receiveAddress.trim());
                    if (MapUtils.isNotEmpty(mapRes)) {
                        String distance = (String) mapRes.get("distance");
                        String duration = (String) mapRes.get("duration");
                        log.info(">>>>>>>>>>>>>distance:{},duration:{}", distance, duration);
                        //更新货源
                        if (!StringUtils.isEmpty(distance)) {
                            e.setTransportMileage(Integer.parseInt(distance));
                        }
                        if (!StringUtils.isEmpty(duration)) {
                            e.setTakeUpTime(Integer.parseInt(duration));
                        }
                        driverGoodsService.updateTransportmileage(e);

                        TmsWaybill queryC = new TmsWaybill();
                        List<String> updateList = new ArrayList();
                        updateList.add(e.getGoodsId());
                        queryC.setGoodsIds(updateList);
                        queryC.setTransportMileage(Integer.parseInt(distance));
                        //根据更新运单
                        tmsWaybillInter.updateTransportmileageByGoodsId(queryC);
                    }

                }

            });
        }
    }

    private Map<String, String> getDistanceAndTakeTimes(String sendAddress, String receiveAddress) {
        if (org.apache.commons.lang3.StringUtils.isAnyEmpty(sendAddress, receiveAddress) || org.apache.commons.lang3.StringUtils.isAllEmpty(sendAddress, receiveAddress)) {
            return new HashMap<String, String>();
        }
        String sendAddressIngAndLat = crmCompanyLineAddressInter.getIngAndLatByAddressDetailName(sendAddress);
        String receiveAddressIngAndLat = crmCompanyLineAddressInter.getIngAndLatByAddressDetailName(receiveAddress);
        log.info(">>>>>>>>>>sendAddressIngAndLat:{},receiveAddressIngAndLat:{}", sendAddressIngAndLat, receiveAddressIngAndLat);

        if (org.apache.commons.lang3.StringUtils.isAnyEmpty(sendAddressIngAndLat, receiveAddressIngAndLat) || org.apache.commons.lang3.StringUtils.isAllEmpty(sendAddressIngAndLat, receiveAddressIngAndLat)) {
            log.info(">>>>>>>>>>>>>俩地址的经纬度未能全部获取到>>>>>>>>>>>>>>>>>>>>>>");
            return new HashMap<String, String>();
        }
        return crmCompanyLineAddressInter.getDistanceByTwoAddressIngAndLat(sendAddressIngAndLat, receiveAddressIngAndLat);
    }

    /**
     * 根据司机货源编号  司机货源信息表主键删除记录
     * 创建者：柳鹏
     * 创建时间：2019/11/15 14:56:33
     *
     * @param goodsId 司机货源编号  司机货源信息表主键
     * @return {@code ResultMode<String>}
     * 如果ResultMode.succeed=true 【删除成功】，返回的ResultMode.model为相应所需要的数据,ResultMode.errCode为相应【删除成功】编码,ResultMode.errMsg为相应【删除成功】描述；
     * 否则ResultMode.succeed=false【删除失败】，返回的ResultMode.model为空或者null,ResultMode.errCode为相应【删除失败】编码,ResultMode.errMsg为相应【删除失败】描述。
     */
    @RequestMapping(value = "/driverGoodsDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> driverGoodsDel(@RequestParam String goodsId) {
        ResultMode<String> resultModel = new ResultMode<String>();
        if (org.springframework.util.StringUtils.isEmpty(goodsId)) {
            resultModel.setErrCode(CommonStatusCodeEnum.USER_ERROR_PARAM_CHECK.getCode());
            resultModel.setErrMsg("司机货源ID: 不能为空字符！");
            resultModel.setSucceed(false);
        } else {
            resultModel = driverGoodsService.driverGoodsDel(goodsId);
        }
        return resultModel;
    }


    /**
     * 检查司机是否存在关注货源
     * @param driverId
     * @param list
     */
    private void checkGoodsAttention(String driverId, List<DriverGoods> list) {
        //当前页下是否又关注过的运单
        if(ObjectUtils.isEmpty(list)) {
            log.info("货源列表为空不处理！");
            return;
        }
        List<String> goodsIds = list.stream().map(dg -> dg.getGoodsId()).collect(Collectors.toList());
        GetGoodsAttentionListVo getGoodsAttentionListVo = new GetGoodsAttentionListVo()
            .setDriverId(driverId)
            .setGoodsIds(goodsIds);
        List<LsdsGoodsAttention> goodsAttentionInfoList = lsdsGoodsAttentionServiceTran.getGoodsAttentionInfoList(getGoodsAttentionListVo);
        if(ObjectUtils.isEmpty(goodsAttentionInfoList)) {
            log.info("当前司机关注货源列表为空不处理！");
            return;
        }
        Map<String, List<LsdsGoodsAttention>> groupByGoodsIdMap = goodsAttentionInfoList.stream().collect(Collectors.groupingBy(LsdsGoodsAttention::getGoodsId));
        //填充是否关注标记
        list.forEach(dg ->{
            if(groupByGoodsIdMap.containsKey(dg.getGoodsId())){
                dg.setAttentionType(AttentionTypeEnum.NON_LONG_ATTENTION.getCode());
            }
        });
    }

    private void checkInvertShortParam(CrmCompanyLineAddressBO startAddress, CrmCompanyLineAddressBO endAddress) {
        //短倒，出发地。目的地距离不能小于1KM
        //单位米
        long shortDistance = 1000;
        ResultMode<PlatformCmPlatformParameter> paraCodes = platformCmPlatformParameterInter.getByParaCodes(Collections.singletonList(PlatformParamEnum.SHORT_TIPS.getCode()));
        if (!Objects.isNull(paraCodes) && CollectionUtils.isNotEmpty(paraCodes.getModel())) {
            String paraValue = paraCodes.getModel().get(0).getParaValue();
            shortDistance = StringUtils.isBlank(paraValue) ? shortDistance : Long.parseLong(paraValue) * 1000;
        }

        // LatLng startLng = new LatLng(startGeo[0], startGeo[1]);
        LatLng startLng = new LatLng(NumberUtil.parseDouble(startAddress.getItem1()), NumberUtil.parseDouble(startAddress.getItem2()));
        LatLng endLng = new LatLng(NumberUtil.parseDouble(endAddress.getItem1()), NumberUtil.parseDouble(endAddress.getItem2()));
        long measureDistance = DistanceUtils.measureDistance(startLng, endLng);
        if (measureDistance < shortDistance) {
            String errorMsg = StrUtil.format(StatusCodeEnum.DISTANCE_TRAN_ERROR.getTipMsg(), shortDistance / 1000);
            log.error(errorMsg);
            throw new WlydException(errorMsg, StatusCodeEnum.DISTANCE_TRAN_ERROR.getCode());
        }
    }

    /**
     * 撮合货源填充撮合信息
     * @param driverGoods
     */
    private void appendMatchmaking(DriverGoods driverGoods) {
        if(ObjUtil.isNull(driverGoods) || !GoodsKindEnum.MATCHMAKING_GOODS.getKind().equals(driverGoods.getGoodsKind())){
            return;
        }
        LsdsMatchmakingConfigAddCommand command = new LsdsMatchmakingConfigAddCommand();
        command.setPrice(driverGoods.getEnquiryTypeBasePrice());
        command.setBargainConfig(driverGoods.getBargainConfig());
        command.setBargainName(driverGoods.getBargainName());
        command.setBargainMobile(driverGoods.getBargainMobile());
        command.setLoadName(driverGoods.getLoadName());
        command.setLoadMobile(driverGoods.getLoadMobile());
        command.setSettlementName(driverGoods.getSettlementName());
        command.setSettlementMobile(driverGoods.getSettlementMobile());
        command.setExpireDate(driverGoods.getValidityDate());
        command.setTrackService(driverGoods.getTrackService());
        command.setTransportRequire(driverGoods.getOtherRemark());
        command.setConfigType(ConfigTypeEnum.GOODS.getConfigType());
        command.setBusId(driverGoods.getGoodsId());
        driverGoods.setMatchmakingConfigAddCommand(command);

        driverGoods.setTransportationType("110");
        driverGoods.setFreightType(driverGoods.getFreightType());
        driverGoods.setEnquiryTypeBaseTaxRate(BigDecimal.ZERO);
        driverGoods.setEnquiryRange("30");
        driverGoods.setAdvancePayment(BigDecimal.ZERO);
        driverGoods.setAdvancePaymentFlag("1");
        driverGoods.setOtherClearType("10");
        driverGoods.setOtherClearWeight("");
        driverGoods.setOtherReceiptType("11");
    }

    @PostMapping("/platformStatistics")
    public ResultMode<GoodsStatisticsDTO> platformStatistics(@RequestBody DriverGoodsFilter filter) {
        GoodsStatisticsDTO result = driverGoodsService.platformStatistics(filter);
        return ResultMode.success(result);
    }

    @PostMapping("/getDriverGoodsExtendInfo")
    public ResultMode<DriverGoodsExtendDTO> getDriverGoodsExtendInfo(@RequestBody @Valid DriverGoodsExtendQuery query) {
        return ResultMode.success(driverGoodsExtendBusiness.selectByGoodsId(query.getGoodsId()));
    }


    /**
     * 填充车辆类型
     * @param driverGoodsHotCars
     */
    private void assembleDriverGoodsHotCar(List<DriverGoodsHotCar> driverGoodsHotCars) {
        List<PlateNoAndColorDTO> collect = driverGoodsHotCars.stream().map(a -> {
            PlateNoAndColorDTO plateNoAndColorDTO = new PlateNoAndColorDTO();
            plateNoAndColorDTO.setPlateColor(a.getCarColor());
            plateNoAndColorDTO.setPlateNo(a.getCarPlateNo());
            return plateNoAndColorDTO;
        }).collect(Collectors.toList());
        //根据车辆号批量查询车辆及挂车信息
        List<TcsQueryCarDTO> tcsQueryCarDTOS = tcsExchangeService.queryBasicInfoByFilter(collect);
        if(CollUtil.isEmpty(tcsQueryCarDTOS)){
            return;
        }
        for (DriverGoodsHotCar driverGoodsHotCar : driverGoodsHotCars) {
            Optional<TcsQueryCarDTO> first = tcsQueryCarDTOS.stream().filter(c -> StrUtil.equals(c.getPlateNo(), driverGoodsHotCar.getCarPlateNo())
                && c.getPlateColor() == driverGoodsHotCar.getCarColor()).findFirst();
            if(!first.isPresent()){
                continue;
            }
            driverGoodsHotCar.setCarType(first.get().getCarType());
        }
    }
}
