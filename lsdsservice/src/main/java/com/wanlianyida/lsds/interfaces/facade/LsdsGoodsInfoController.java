package com.wanlianyida.lsds.interfaces.facade;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.isoftstone.hig.lsds.api.entity.LsdsGoodsInfo;
import com.isoftstone.hig.lsds.api.filter.LsdsGoodsInfoFilter;
import com.wanlianyida.lsds.application.service.LsdsGoodsInfoAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 货品控制类
 * <AUTHOR>
 * 创建时间 2020/6/17
 */
@RequestMapping(value = "/LsdsGoodsInfo")
@RestController
public class LsdsGoodsInfoController   {

    @Autowired
    private LsdsGoodsInfoAppService lsdsGoodsInfoTran;

    /**
     * 新增
     * 创建者：cgb
     * 创建时间：2020/6/16
     * @param model
     * @return {@code ResultMode<String>}
     */
    @RequestMapping(value = "/lsdsGoodsInfoAdd", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsInfoAdd(@RequestBody LsdsGoodsInfo model) {
        ResultMode<String> resultModel = new ResultMode<String>();

        if (null != model) {
            if (StringUtils.isEmpty(model.getCompanyId())) {
                resultModel.setErrMsg("公司ID:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(model.getGoodsName())) {
                resultModel.setErrMsg("货物名称:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(model.getGoodsType())) {
                resultModel.setErrMsg("货物类型:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
        } else {
            resultModel.setSucceed(false);
        }

        if (!resultModel.getSucceed()) {
            return resultModel;
        } else {
            resultModel = lsdsGoodsInfoTran.lsdsGoodsInfoAdd(model);
        }
        return resultModel;
    }

    /**
     * 删除
     * 创建者：cgb
     * 创建时间：2020/6/16
     * @param model
     * @return {@code ResultMode<String>}
     */
    @RequestMapping(value = "/lsdsGoodsInfoDel", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsInfoDel(@RequestBody LsdsGoodsInfo model) {
        ResultMode<String> resultModel = new ResultMode<String>();
        if (null != model) {
            if(StringUtils.isEmpty(model.getGoodsInfoId())){
                resultModel.setErrMsg("货品ID: 不能为空字符！");
                resultModel.setSucceed(false);
            }

        } else {
            resultModel.setSucceed(false);
        }

        if (!resultModel.getSucceed()) {
            return resultModel;
        } else {
            resultModel = lsdsGoodsInfoTran.lsdsGoodsInfoDel(model);
        }
        return resultModel;
    }

    /**
     * 更新
     * 创建者：cgb
     * 创建时间：2020/6/16
     * @param model
     * @return {@code ResultMode<String>}
     */
    @RequestMapping(value = "/lsdsGoodsInfoUpdate", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<String> lsdsGoodsInfoUpdate(@RequestBody LsdsGoodsInfo model) {
        ResultMode<String> resultModel = new ResultMode<String>();

        if (null != model) {
//            if(StringUtils.isEmpty(model.getCompanyId())){
//                resultModel.setErrMsg("公司ID: 不能为空字符！");
//                resultModel.setSucceed(false);
//            }
            if (StringUtils.isEmpty(model.getGoodsInfoId())) {
                resultModel.setErrMsg("货品ID:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(model.getGoodsName())) {
                resultModel.setErrMsg("货物名称:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
            if (StringUtils.isEmpty(model.getGoodsType())) {
                resultModel.setErrMsg("货物类型:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
        } else {
            resultModel.setSucceed(false);
        }

        if (!resultModel.getSucceed()) {
            return resultModel;
        } else {
            resultModel = lsdsGoodsInfoTran.LsdsGoodsInfoUpdate(model);
        }
        return resultModel;
    }

    /**
     * 分页查询
     * 创建者：cgb
     * 创建时间：2020/6/16
     * @param pageInfo
     * @return {@code ResultMode<String>}
     */
    @RequestMapping(value = "/getGoodsInfoListPage", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsInfo> getGoodsInfoListPage(@RequestBody PagingInfo<LsdsGoodsInfoFilter> pageInfo) {
        ResultMode<LsdsGoodsInfo> resultModel = new ResultMode<LsdsGoodsInfo>();
        if (null != pageInfo) {
            if (StringUtils.isEmpty(pageInfo.filterModel) || StringUtils.isEmpty(pageInfo.filterModel.getCompanyId())) {
                resultModel.setErrMsg("公司ID:" + "不能为空字符");
                resultModel.setSucceed(false);
            }
        } else {
            resultModel.setSucceed(false);
        }
        if (!resultModel.getSucceed()) {
            return resultModel;
        } else {
            resultModel = lsdsGoodsInfoTran.getGoodsInfoListPage(pageInfo);
        }
        return resultModel;
    }

    /**
     * 查询详情
     * 创建者：cgb
     * 创建时间：2020/6/16
     * @param model
     * @return {@code ResultMode<LsdsGoodsInfo>}
     */
    @RequestMapping(value = "/lsdsGoodsInfoGet", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode<LsdsGoodsInfo> lsdsGoodsInfoGet(@RequestBody LsdsGoodsInfo model) {
        ResultMode<LsdsGoodsInfo> resultModel = new ResultMode<LsdsGoodsInfo>();
        if (null != model) {
            if(StringUtils.isEmpty(model.getGoodsInfoId())){
                resultModel.setErrMsg("货品ID: 不能为空字符！");
                resultModel.setSucceed(false);
            }
        } else {
            resultModel.setSucceed(false);
        }

        if (!resultModel.getSucceed()) {
            return resultModel;
        } else {
            resultModel = lsdsGoodsInfoTran.lsdsGoodsInfoGet(model);
        }
        return resultModel;
    }
}
